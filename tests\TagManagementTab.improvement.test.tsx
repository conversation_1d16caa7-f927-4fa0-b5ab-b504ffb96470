import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { jest } from '@jest/globals'
import TagManagementTab from '../src/components/TagManagementTab'
import { tagService } from '../src/services/tagService'

// 模拟依赖
jest.mock('../src/services/tagService')
jest.mock('../src/hooks/useToast')
jest.mock('../src/hooks/useLoadingState')

const mockTagService = tagService as jest.Mocked<typeof tagService>

describe('TagManagementTab 改进测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockTagService.getAllTagsWithStats.mockResolvedValue([])
    mockTagService.syncTagsFromBookmarks.mockResolvedValue()
  })

  describe('批量操作错误处理', () => {
    it('应该正确处理批量删除的部分失败', async () => {
      // 模拟部分成功的批量删除
      const mockDeleteTag = jest.fn()
        .mockResolvedValueOnce(undefined) // 第一个成功
        .mockRejectedValueOnce(new Error('删除失败')) // 第二个失败
      
      mockTagService.deleteTag = mockDeleteTag
      
      render(<TagManagementTab />)
      
      // 等待组件加载完成
      await waitFor(() => {
        expect(screen.getByText('标签管理')).toBeInTheDocument()
      })
      
      // 这里需要触发批量删除操作
      // 具体实现取决于TagList组件的接口
    })

    it('应该正确处理同步状态', async () => {
      render(<TagManagementTab />)
      
      const syncButton = screen.getByText('手动同步')
      fireEvent.click(syncButton)
      
      // 验证同步状态
      expect(screen.getByText('同步中...')).toBeInTheDocument()
      
      await waitFor(() => {
        expect(screen.getByText('手动同步')).toBeInTheDocument()
      })
    })
  })

  describe('性能优化测试', () => {
    it('应该避免不必要的重渲染', () => {
      const renderSpy = jest.fn()
      
      // 这里需要实现渲染次数的监控
      // 可以使用React.memo和自定义hook来测试
    })
  })

  describe('错误处理统一性', () => {
    it('应该统一处理各种错误类型', async () => {
      mockTagService.getAllTagsWithStats.mockRejectedValue(new Error('网络错误'))
      
      render(<TagManagementTab />)
      
      await waitFor(() => {
        expect(screen.getByText('加载失败')).toBeInTheDocument()
        expect(screen.getByText('网络错误')).toBeInTheDocument()
      })
    })
  })
})