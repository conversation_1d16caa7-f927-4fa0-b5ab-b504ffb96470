/**
 * Chrome Storage API集成服务
 * 提供配置和设置数据的存储管理，合理使用同步存储和本地存储
 */
export class ChromeStorageService {
  
  /**
   * 存储配额限制（字节）
   */
  private static readonly SYNC_QUOTA_BYTES = 102400 // 100KB
  private static readonly SYNC_ITEM_MAX_SIZE = 8192 // 8KB per item
  private static readonly LOCAL_QUOTA_BYTES = 5242880 // 5MB
  
  /**
   * 存储键前缀
   */
  private static readonly SYNC_PREFIX = 'ub_sync_'
  private static readonly LOCAL_PREFIX = 'ub_local_'
  
  /**
   * 同步存储的键名（这些设置会在不同设备间同步）
   */
  private static readonly SYNC_KEYS = [
    'ai_config',
    'ai_providers',
    'ai_selected_models', // 添加选中模型的存储键
    'ai_default_model', // 添加旧版默认模型存储键（向后兼容）
    'default_ai_models', // 添加默认AI模型配置的存储键
    'ui_preferences',
    'sync_config',
    'privacy_settings',
    'feature_flags'
  ]

  /**
   * 本地存储的键名（这些设置只在本地设备存储）
   */
  private static readonly LOCAL_KEYS = [
    'cache_data',
    'temp_settings',
    'device_specific_config',
    'performance_data',
    'debug_logs'
  ]

  /**
   * 保存同步设置
   * @param key 设置键
   * @param value 设置值
   * @returns Promise<void>
   */
  static async saveSyncSetting(key: string, value: any): Promise<void> {
    if (!this.SYNC_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许同步存储`)
    }

    const storageKey = this.SYNC_PREFIX + key
    const serializedValue = JSON.stringify(value)
    
    // 检查大小限制
    if (serializedValue.length > this.SYNC_ITEM_MAX_SIZE) {
      throw new Error(`数据大小超过同步存储限制 (${serializedValue.length} > ${this.SYNC_ITEM_MAX_SIZE} 字节)`)
    }

    try {
      await chrome.storage.sync.set({ [storageKey]: value })
      console.log(`同步设置已保存: ${key}`)
    } catch (error) {
      console.error(`保存同步设置失败: ${key}`, error)
      throw new Error(`保存同步设置失败: ${error.message}`)
    }
  }

  /**
   * 获取同步设置
   * @param key 设置键
   * @param defaultValue 默认值
   * @returns Promise<any>
   */
  static async getSyncSetting(key: string, defaultValue: any = null): Promise<any> {
    if (!this.SYNC_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许同步存储`)
    }

    const storageKey = this.SYNC_PREFIX + key

    try {
      const result = await chrome.storage.sync.get(storageKey)
      return result[storageKey] !== undefined ? result[storageKey] : defaultValue
    } catch (error) {
      console.error(`获取同步设置失败: ${key}`, error)
      return defaultValue
    }
  }

  /**
   * 保存本地设置
   * @param key 设置键
   * @param value 设置值
   * @returns Promise<void>
   */
  static async saveLocalSetting(key: string, value: any): Promise<void> {
    if (!this.LOCAL_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许本地存储`)
    }

    const storageKey = this.LOCAL_PREFIX + key

    try {
      await chrome.storage.local.set({ [storageKey]: value })
      console.log(`本地设置已保存: ${key}`)
    } catch (error) {
      console.error(`保存本地设置失败: ${key}`, error)
      throw new Error(`保存本地设置失败: ${error.message}`)
    }
  }

  /**
   * 获取本地设置
   * @param key 设置键
   * @param defaultValue 默认值
   * @returns Promise<any>
   */
  static async getLocalSetting(key: string, defaultValue: any = null): Promise<any> {
    if (!this.LOCAL_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许本地存储`)
    }

    const storageKey = this.LOCAL_PREFIX + key

    try {
      const result = await chrome.storage.local.get(storageKey)
      return result[storageKey] !== undefined ? result[storageKey] : defaultValue
    } catch (error) {
      console.error(`获取本地设置失败: ${key}`, error)
      return defaultValue
    }
  }

  /**
   * 删除同步设置
   * @param key 设置键
   * @returns Promise<void>
   */
  static async removeSyncSetting(key: string): Promise<void> {
    if (!this.SYNC_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许同步存储`)
    }

    const storageKey = this.SYNC_PREFIX + key

    try {
      await chrome.storage.sync.remove(storageKey)
      console.log(`同步设置已删除: ${key}`)
    } catch (error) {
      console.error(`删除同步设置失败: ${key}`, error)
      throw new Error(`删除同步设置失败: ${error.message}`)
    }
  }

  /**
   * 删除本地设置
   * @param key 设置键
   * @returns Promise<void>
   */
  static async removeLocalSetting(key: string): Promise<void> {
    if (!this.LOCAL_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许本地存储`)
    }

    const storageKey = this.LOCAL_PREFIX + key

    try {
      await chrome.storage.local.remove(storageKey)
      console.log(`本地设置已删除: ${key}`)
    } catch (error) {
      console.error(`删除本地设置失败: ${key}`, error)
      throw new Error(`删除本地设置失败: ${error.message}`)
    }
  }

  /**
   * 获取所有同步设置
   * @returns Promise<Record<string, any>>
   */
  static async getAllSyncSettings(): Promise<Record<string, any>> {
    try {
      const result = await chrome.storage.sync.get(null)
      const syncSettings: Record<string, any> = {}

      if (result && typeof result === 'object') {
        for (const [storageKey, value] of Object.entries(result)) {
          if (storageKey.startsWith(this.SYNC_PREFIX)) {
            const key = storageKey.replace(this.SYNC_PREFIX, '')
            syncSettings[key] = value
          }
        }
      }

      return syncSettings
    } catch (error) {
      console.error('获取所有同步设置失败:', error)
      return {}
    }
  }

  /**
   * 获取所有本地设置
   * @returns Promise<Record<string, any>>
   */
  static async getAllLocalSettings(): Promise<Record<string, any>> {
    try {
      const result = await chrome.storage.local.get(null)
      const localSettings: Record<string, any> = {}

      if (result && typeof result === 'object') {
        for (const [storageKey, value] of Object.entries(result)) {
          if (storageKey.startsWith(this.LOCAL_PREFIX)) {
            const key = storageKey.replace(this.LOCAL_PREFIX, '')
            localSettings[key] = value
          }
        }
      }

      return localSettings
    } catch (error) {
      console.error('获取所有本地设置失败:', error)
      return {}
    }
  }

  /**
   * 清空所有同步设置
   * @returns Promise<void>
   */
  static async clearAllSyncSettings(): Promise<void> {
    try {
      const result = await chrome.storage.sync.get(null)
      if (result && typeof result === 'object') {
        const keysToRemove = Object.keys(result).filter(key => key.startsWith(this.SYNC_PREFIX))
        
        if (keysToRemove.length > 0) {
          await chrome.storage.sync.remove(keysToRemove)
          console.log(`已清空 ${keysToRemove.length} 个同步设置`)
        }
      }
    } catch (error) {
      console.error('清空同步设置失败:', error)
      throw new Error(`清空同步设置失败: ${(error as Error).message}`)
    }
  }

  /**
   * 清空所有本地设置
   * @returns Promise<void>
   */
  static async clearAllLocalSettings(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(null)
      if (result && typeof result === 'object') {
        const keysToRemove = Object.keys(result).filter(key => key.startsWith(this.LOCAL_PREFIX))
        
        if (keysToRemove.length > 0) {
          await chrome.storage.local.remove(keysToRemove)
          console.log(`已清空 ${keysToRemove.length} 个本地设置`)
        }
      }
    } catch (error) {
      console.error('清空本地设置失败:', error)
      throw new Error(`清空本地设置失败: ${(error as Error).message}`)
    }
  }

  /**
   * 获取存储使用情况
   * @returns Promise<StorageUsage>
   */
  static async getStorageUsage(): Promise<{
    sync: { used: number; total: number; percentage: number }
    local: { used: number; total: number; percentage: number }
  }> {
    try {
      const [syncUsage, localUsage] = await Promise.all([
        chrome.storage.sync.getBytesInUse(),
        chrome.storage.local.getBytesInUse()
      ])

      return {
        sync: {
          used: syncUsage,
          total: this.SYNC_QUOTA_BYTES,
          percentage: Math.round((syncUsage / this.SYNC_QUOTA_BYTES) * 100)
        },
        local: {
          used: localUsage,
          total: this.LOCAL_QUOTA_BYTES,
          percentage: Math.round((localUsage / this.LOCAL_QUOTA_BYTES) * 100)
        }
      }
    } catch (error) {
      console.error('获取存储使用情况失败:', error)
      return {
        sync: { used: 0, total: this.SYNC_QUOTA_BYTES, percentage: 0 },
        local: { used: 0, total: this.LOCAL_QUOTA_BYTES, percentage: 0 }
      }
    }
  }

  /**
   * 检查存储配额
   * @returns Promise<boolean> 是否接近配额限制
   */
  static async checkStorageQuota(): Promise<{
    syncNearLimit: boolean
    localNearLimit: boolean
    warnings: string[]
  }> {
    const usage = await this.getStorageUsage()
    const warnings: string[] = []
    
    const syncNearLimit = usage.sync.percentage > 80
    const localNearLimit = usage.local.percentage > 80

    if (syncNearLimit) {
      warnings.push(`同步存储使用率已达 ${usage.sync.percentage}%，接近配额限制`)
    }

    if (localNearLimit) {
      warnings.push(`本地存储使用率已达 ${usage.local.percentage}%，接近配额限制`)
    }

    return {
      syncNearLimit,
      localNearLimit,
      warnings
    }
  }

  /**
   * 清理过期的缓存数据
   * @param maxAge 最大保存时间（毫秒）
   * @returns Promise<void>
   */
  static async cleanupExpiredCache(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const cacheData = await this.getLocalSetting('cache_data', {})
      const now = Date.now()
      let cleanedCount = 0

      const cleanedCache: Record<string, any> = {}
      
      for (const [key, value] of Object.entries(cacheData)) {
        if (typeof value === 'object' && value !== null && 'timestamp' in value) {
          const age = now - (value as any).timestamp
          if (age <= maxAge) {
            cleanedCache[key] = value
          } else {
            cleanedCount++
          }
        } else {
          cleanedCache[key] = value
        }
      }

      if (cleanedCount > 0) {
        await this.saveLocalSetting('cache_data', cleanedCache)
        console.log(`已清理 ${cleanedCount} 个过期缓存项`)
      }
    } catch (error) {
      console.error('清理过期缓存失败:', error)
    }
  }

  /**
   * 监听存储变化
   * @param callback 变化回调函数
   * @returns 取消监听的函数
   */
  static onStorageChanged(
    callback: (changes: Record<string, chrome.storage.StorageChange>, areaName: string) => void
  ): () => void {
    const listener = (changes: Record<string, chrome.storage.StorageChange>, areaName: string) => {
      // 只处理我们的存储键
      const relevantChanges: Record<string, chrome.storage.StorageChange> = {}
      
      for (const [key, change] of Object.entries(changes)) {
        if (key.startsWith(this.SYNC_PREFIX) || key.startsWith(this.LOCAL_PREFIX)) {
          const cleanKey = key.replace(this.SYNC_PREFIX, '').replace(this.LOCAL_PREFIX, '')
          relevantChanges[cleanKey] = change
        }
      }

      if (Object.keys(relevantChanges).length > 0) {
        callback(relevantChanges, areaName)
      }
    }

    chrome.storage.onChanged.addListener(listener)

    // 返回取消监听的函数
    return () => {
      chrome.storage.onChanged.removeListener(listener)
    }
  }

  /**
   * 批量保存设置
   * @param syncSettings 同步设置
   * @param localSettings 本地设置
   * @returns Promise<void>
   */
  static async batchSaveSettings(
    syncSettings: Record<string, any> = {},
    localSettings: Record<string, any> = {}
  ): Promise<void> {
    const promises: Promise<void>[] = []

    // 保存同步设置
    for (const [key, value] of Object.entries(syncSettings)) {
      if (this.SYNC_KEYS.includes(key)) {
        promises.push(this.saveSyncSetting(key, value))
      }
    }

    // 保存本地设置
    for (const [key, value] of Object.entries(localSettings)) {
      if (this.LOCAL_KEYS.includes(key)) {
        promises.push(this.saveLocalSetting(key, value))
      }
    }

    try {
      await Promise.all(promises)
      console.log('批量保存设置完成')
    } catch (error) {
      console.error('批量保存设置失败:', error)
      throw error
    }
  }

  /**
   * 导出所有设置
   * @returns Promise<object> 所有设置数据
   */
  static async exportAllSettings(): Promise<{
    sync: Record<string, any>
    local: Record<string, any>
    exportDate: string
  }> {
    const [syncSettings, localSettings] = await Promise.all([
      this.getAllSyncSettings(),
      this.getAllLocalSettings()
    ])

    return {
      sync: syncSettings,
      local: localSettings,
      exportDate: new Date().toISOString()
    }
  }

  /**
   * 导入设置
   * @param settingsData 设置数据
   * @param options 导入选项
   * @returns Promise<void>
   */
  static async importSettings(
    settingsData: {
      sync?: Record<string, any>
      local?: Record<string, any>
    },
    options: {
      clearExisting?: boolean
      skipExisting?: boolean
    } = {}
  ): Promise<void> {
    try {
      // 如果需要清空现有设置
      if (options.clearExisting) {
        await Promise.all([
          this.clearAllSyncSettings(),
          this.clearAllLocalSettings()
        ])
      }

      // 导入同步设置
      if (settingsData.sync) {
        for (const [key, value] of Object.entries(settingsData.sync)) {
          if (this.SYNC_KEYS.includes(key)) {
            if (options.skipExisting) {
              const existing = await this.getSyncSetting(key)
              if (existing !== null) continue
            }
            await this.saveSyncSetting(key, value)
          }
        }
      }

      // 导入本地设置
      if (settingsData.local) {
        for (const [key, value] of Object.entries(settingsData.local)) {
          if (this.LOCAL_KEYS.includes(key)) {
            if (options.skipExisting) {
              const existing = await this.getLocalSetting(key)
              if (existing !== null) continue
            }
            await this.saveLocalSetting(key, value)
          }
        }
      }

      console.log('设置导入完成')
    } catch (error) {
      console.error('导入设置失败:', error)
      throw error
    }
  }

  /**
   * 获取默认设置
   * @returns 默认设置对象
   */
  static getDefaultSettings(): {
    sync: Record<string, any>
    local: Record<string, any>
  } {
    return {
      sync: {
        ai_config: {
          provider: 'openai',
          model: 'gpt-3.5-turbo',
          autoTagging: true,
          autoCategories: true
        },
        ai_providers: [],
        ai_selected_models: {},
        ui_preferences: {
          theme: 'light',
          language: 'zh-CN',
          viewMode: 'card',
          itemsPerPage: 20
        },
        sync_config: {
          enabled: false,
          provider: 'notion',
          syncFrequency: 3600000 // 1小时
        },
        privacy_settings: {
          enableAnalytics: false,
          shareUsageData: false,
          encryptSensitiveData: true
        },
        feature_flags: {
          enableFloatingWidget: true,
          enableAIAssistant: true,
          enableCloudSync: false
        }
      },
      local: {
        cache_data: {},
        temp_settings: {},
        device_specific_config: {
          deviceId: crypto.randomUUID(),
          installDate: new Date().toISOString()
        },
        performance_data: {
          lastCleanup: new Date().toISOString(),
          cacheHitRate: 0
        },
        debug_logs: []
      }
    }
  }

  /**
   * 初始化默认设置
   * @returns Promise<void>
   */
  static async initializeDefaultSettings(): Promise<void> {
    const defaults = this.getDefaultSettings()
    
    // 只设置不存在的设置项
    const promises: Promise<void>[] = []

    for (const [key, value] of Object.entries(defaults.sync)) {
      promises.push(
        this.getSyncSetting(key).then(existing => {
          if (existing === null) {
            return this.saveSyncSetting(key, value)
          }
        })
      )
    }

    for (const [key, value] of Object.entries(defaults.local)) {
      promises.push(
        this.getLocalSetting(key).then(existing => {
          if (existing === null) {
            return this.saveLocalSetting(key, value)
          }
        })
      )
    }

    await Promise.all(promises)
    console.log('默认设置初始化完成')
  }
}