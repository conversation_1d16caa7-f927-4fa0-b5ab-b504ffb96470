{"enabled": true, "name": "Locale File Sync", "description": "Monitors English locale file changes and automatically syncs translation keys to other language files, marking missing keys as NEEDS_TRANSLATION and modified keys as NEEDS_REVIEW", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/locales/en.json", "**/locale/en.json", "**/i18n/en.json", "**/lang/en.json", "**/translations/en.json"]}, "then": {"type": "askAgent", "prompt": "An English locale file has been updated. Please:\n1. Analyze the changes to identify which string keys were added or modified\n2. Check all other language files in the same directory for these keys\n3. For missing keys, add them with \"NEEDS_TRANSLATION\" as the value\n4. For keys that exist but the English source was modified, mark them as \"NEEDS_REVIEW\"\n5. Generate a summary report of all changes needed across all language files\n6. Apply the necessary updates to maintain translation consistency"}}