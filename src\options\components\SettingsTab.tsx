// 设置标签页组件
import React from 'react'
import { Settings } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { Checkbox } from '../../components/ui/checkbox'

const SettingsTab: React.FC = () => {
  return (
    <div className="p-6">
      {/* 头部区域 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <Settings className="w-6 h-6 mr-3 text-primary-600" />
            设置
          </CardTitle>
          <CardDescription className="mt-1">
            配置应用程序的各项设置，个性化您的使用体验
          </CardDescription>
        </CardHeader>
      </Card>
      
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">基础设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-foreground">界面主题</label>
                <p className="text-sm text-muted-foreground">选择您喜欢的界面主题</p>
              </div>
              <Select defaultValue="light">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择主题" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">浅色主题</SelectItem>
                  <SelectItem value="dark">深色主题</SelectItem>
                  <SelectItem value="system">跟随系统</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-foreground">语言设置</label>
                <p className="text-sm text-muted-foreground">选择界面显示语言</p>
              </div>
              <Select defaultValue="zh-CN">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh-CN">简体中文</SelectItem>
                  <SelectItem value="en-US">English</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">AI 功能设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-foreground">自动标签生成</label>
                <p className="text-sm text-muted-foreground">使用AI自动为收藏内容生成标签</p>
              </div>
              <Checkbox defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-foreground">智能分类</label>
                <p className="text-sm text-muted-foreground">自动将内容归类到合适的分类中</p>
              </div>
              <Checkbox defaultChecked />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SettingsTab