// 智能识别组件 - 可在浏览器插件弹窗中复用的智能识别功能

import React, { useState } from 'react'
import { <PERSON>rkles, Loader2, Check, X } from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'

/**
 * 智能识别请求接口
 */
interface SmartRecognitionRequest {
  title?: string
  url?: string
  content?: string
  description?: string
}

/**
 * 智能识别结果接口
 */
interface SmartRecognitionResult {
  tags: string[]
  folder?: string
  description?: string
  confidence: number
}

/**
 * 智能识别组件属性接口
 */
interface SmartRecognitionProps {
  /** 识别请求数据 */
  request: SmartRecognitionRequest
  /** 是否自动触发识别 */
  autoTrigger?: boolean
  /** 识别完成回调 */
  onRecognitionComplete?: (result: SmartRecognitionResult) => void
  /** 是否禁用 */
  disabled?: boolean
  /** 自定义样式类名 */
  className?: string
}

/**
 * 智能识别组件
 * 提供基于AI的智能识别功能，可以识别标签、文件夹和描述
 */
const SmartRecognition: React.FC<SmartRecognitionProps> = ({
  request,
  autoTrigger = false,
  onRecognitionComplete,
  disabled = false,
  className = ''
}) => {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<SmartRecognitionResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  /**
   * 执行智能识别
   */
  const performRecognition = async () => {
    if (!request.title && !request.content && !request.url) {
      setError('缺少识别内容')
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log('开始智能识别:', request)

      // 发送消息到background script
      const response = await chrome.runtime.sendMessage({
        type: 'AI_RECOMMEND_ALL',
        data: request
      })

      if (response?.success) {
        const recognitionResult: SmartRecognitionResult = {
          tags: [
            ...(response.data.tags?.existingTags || []),
            ...(response.data.tags?.newTags || [])
          ],
          folder: response.data.folders?.recommendedFolders?.[0]?.name,
          description: response.data.description?.description,
          confidence: Math.min(
            response.data.tags?.confidence || 0,
            response.data.folders?.recommendedFolders?.[0]?.confidence || 0,
            response.data.description?.confidence || 0
          )
        }

        setResult(recognitionResult)
        console.log('智能识别完成:', recognitionResult)

        // 调用回调函数
        if (onRecognitionComplete) {
          onRecognitionComplete(recognitionResult)
        }
      } else {
        throw new Error(response?.error || '智能识别失败')
      }
    } catch (error) {
      console.error('智能识别失败:', error)
      setError(error instanceof Error ? error.message : '识别失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 手动触发识别
   */
  const handleManualRecognition = () => {
    performRecognition()
  }

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50 border-green-200'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  /**
   * 获取置信度文本
   */
  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  // 自动触发识别
  React.useEffect(() => {
    if (autoTrigger && !loading && !result && !error) {
      performRecognition()
    }
  }, [autoTrigger, request.title, request.url])

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 手动触发按钮 */}
      {!autoTrigger && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Sparkles className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-foreground">智能识别</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleManualRecognition}
            disabled={loading || disabled || (!request.title && !request.content && !request.url)}
            className="h-8 text-xs"
          >
            {loading ? (
              <>
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                识别中...
              </>
            ) : (
              <>
                <Sparkles className="w-3 h-3 mr-1" />
                开始识别
              </>
            )}
          </Button>
        </div>
      )}

      {/* 加载状态 */}
      {loading && autoTrigger && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>正在进行智能识别...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 错误提示 */}
      {error && (
        <Card className="border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 text-sm text-red-600">
              <X className="w-4 h-4" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 识别结果 */}
      {result && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-green-600" />
                <span>识别完成</span>
              </div>
              <Badge 
                variant="outline" 
                className={`text-xs ${getConfidenceColor(result.confidence)}`}
              >
                置信度: {getConfidenceText(result.confidence)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* 识别的标签 */}
            {result.tags.length > 0 && (
              <div>
                <div className="text-xs text-muted-foreground mb-2">推荐标签</div>
                <div className="flex flex-wrap gap-1">
                  {result.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* 识别的文件夹 */}
            {result.folder && (
              <div>
                <div className="text-xs text-muted-foreground mb-2">推荐文件夹</div>
                <Badge variant="outline" className="text-xs">
                  {result.folder}
                </Badge>
              </div>
            )}

            {/* 识别的描述 */}
            {result.description && (
              <div>
                <div className="text-xs text-muted-foreground mb-2">生成描述</div>
                <div className="p-2 bg-muted/50 rounded-md">
                  <p className="text-xs text-foreground leading-relaxed">
                    {result.description}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default SmartRecognition
