// 虚拟滚动收藏列表组件

import React, { useMemo } from 'react'
import { useVirtualScroll, useItemHeight } from '../utils/virtualScroll'
import BookmarkRow from './BookmarkRow'
import BookmarkCompact from './BookmarkCompact'
import TruncatedTitle from './TruncatedTitle'
import { Bookmark, Settings, Trash2 } from 'lucide-react'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { cn } from '@/lib/utils'
import { useTagColors } from '../hooks/useTagColors'
import type { ViewMode } from './ViewModeSelector'

interface VirtualBookmarkListProps {
  /** 收藏列表 */
  bookmarks: any[]
  /** 视图模式 */
  viewMode: ViewMode
  /** 容器高度 */
  containerHeight: number
  /** 高亮的收藏ID */
  highlightBookmarkId?: string | null
  /** 编辑回调 */
  onEdit: (bookmark: any) => void
  /** 删除回调 */
  onDelete: (bookmark: any) => void
  /** 点击回调 */
  onClick: (bookmark: any) => void
}

/**
 * 虚拟收藏项组件
 */
interface VirtualBookmarkItemProps {
  bookmark: any
  index: number
  viewMode: ViewMode
  isHighlighted: boolean
  onEdit: (bookmark: any) => void
  onDelete: (bookmark: any) => void
  onClick: (bookmark: any) => void
  updateItemHeight: (index: number, height: number) => void
}

const VirtualBookmarkItem: React.FC<VirtualBookmarkItemProps> = React.memo(({
  bookmark,
  index,
  viewMode,
  isHighlighted,
  onEdit,
  onDelete,
  onClick,
  updateItemHeight
}) => {
  const { itemRef } = useItemHeight(index, updateItemHeight)
  const { getTagColor } = useTagColors()

  // 根据视图模式渲染不同的组件
  const renderBookmarkItem = () => {
    switch (viewMode) {
      case 'row':
        return (
          <BookmarkRow
            bookmark={bookmark}
            isHighlighted={isHighlighted}
            onEdit={onEdit}
            onDelete={onDelete}
            onClick={onClick}
          />
        )
      
      case 'compact':
        return (
          <BookmarkCompact
            bookmark={bookmark}
            isHighlighted={isHighlighted}
            onEdit={onEdit}
            onDelete={onDelete}
            onClick={onClick}
          />
        )
      
      default: // card 视图
        return (
          <div 
            className={cn(
              "border rounded-lg p-4 hover:shadow-md transition-all duration-300 cursor-pointer",
              isHighlighted 
                ? 'border-primary bg-primary/5 shadow-lg' 
                : 'border-border'
            )}
            onClick={() => onClick(bookmark)}
          >
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                {bookmark.favicon ? (
                  <img src={bookmark.favicon} alt="" className="w-6 h-6" />
                ) : (
                  <Bookmark className="w-6 h-6 text-muted-foreground" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="text-lg font-medium text-foreground">
                  <TruncatedTitle 
                    title={bookmark.title || '无标题'}
                    maxLength={60}
                    useContainerWidth={true}
                    className="text-lg font-medium text-foreground"
                  />
                </div>
                
                {bookmark.url && (
                  <a 
                    href={bookmark.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:text-primary/80 block"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <TruncatedTitle 
                      title={bookmark.url}
                      maxLength={80}
                      useContainerWidth={true}
                      className="text-sm text-primary hover:text-primary/80"
                    />
                  </a>
                )}
                
                {bookmark.description && (
                  <div className="text-sm text-muted-foreground mt-2">
                    <TruncatedTitle 
                      title={bookmark.description}
                      maxLength={120}
                      useContainerWidth={true}
                      maxLines={3}
                      className="text-sm text-muted-foreground"
                    />
                  </div>
                )}
                
                {bookmark.content && (
                  <div className="text-sm text-muted-foreground mt-2 bg-muted p-2 rounded">
                    <TruncatedTitle 
                      title={bookmark.content}
                      maxLength={150}
                      useContainerWidth={true}
                      maxLines={4}
                      className="text-sm text-muted-foreground"
                    />
                  </div>
                )}
                
                <div className="flex items-center space-x-4 mt-3 text-xs text-muted-foreground">
                  <span>分类: {bookmark.category || '未分类'}</span>
                  <span>创建时间: {new Date(bookmark.createdAt).toLocaleString()}</span>
                  {bookmark.tags && bookmark.tags.length > 0 && (
                    <div className="flex items-center space-x-1">
                      <span>标签:</span>
                      {bookmark.tags.map((tag: string) => (
                        <Badge 
                          key={tag} 
                          variant="outline" 
                          className="text-xs border"
                          style={{ 
                            borderColor: getTagColor(tag),
                            backgroundColor: `${getTagColor(tag)}15`,
                            color: getTagColor(tag)
                          }}
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex-shrink-0 flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation()
                    onEdit(bookmark)
                  }}
                  title="编辑收藏"
                  className="h-8 w-8"
                >
                  <Settings className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation()
                    onDelete(bookmark)
                  }}
                  title="删除收藏"
                  className="h-8 w-8 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div ref={itemRef} className="virtual-bookmark-item">
      {renderBookmarkItem()}
    </div>
  )
})

VirtualBookmarkItem.displayName = 'VirtualBookmarkItem'

/**
 * 虚拟滚动收藏列表组件
 */
const VirtualBookmarkList: React.FC<VirtualBookmarkListProps> = React.memo(({
  bookmarks,
  viewMode,
  containerHeight,
  highlightBookmarkId,
  onEdit,
  onDelete,
  onClick
}) => {
  // 根据视图模式计算预估项目高度
  const estimatedItemHeight = useMemo(() => {
    switch (viewMode) {
      case 'row':
        return 60 // 行视图较小
      case 'compact':
        return 120 // 紧凑视图中等
      default: // card
        return 200 // 卡片视图较大
    }
  }, [viewMode])

  // 虚拟滚动配置
  const virtualConfig = useMemo(() => ({
    containerHeight,
    itemHeight: estimatedItemHeight,
    overscan: 5,
    scrollThrottle: 16
  }), [containerHeight, estimatedItemHeight])

  // 使用虚拟滚动
  const {
    containerRef,
    virtualState,
    handleScroll,
    updateItemHeight
  } = useVirtualScroll(bookmarks, virtualConfig)

  // 容器样式类名
  const containerClassName = useMemo(() => {
    const baseClasses = "w-full transition-all duration-300 ease-in-out"
    
    switch (viewMode) {
      case 'row':
        return cn(baseClasses, "space-y-1")
      case 'compact':
        return cn(baseClasses, "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4")
      default: // card
        return cn(baseClasses, "grid gap-4")
    }
  }, [viewMode])

  return (
    <div
      ref={containerRef}
      className="virtual-scroll-container overflow-auto"
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      {/* 总高度占位符 */}
      <div style={{ height: virtualState.totalHeight, position: 'relative' }}>
        {/* 可见项目容器 */}
        <div
          className={containerClassName}
          style={{
            transform: `translateY(${virtualState.offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {virtualState.visibleItems.map((bookmark, index) => {
            const actualIndex = virtualState.startIndex + index
            return (
              <VirtualBookmarkItem
                key={bookmark.id}
                bookmark={bookmark}
                index={actualIndex}
                viewMode={viewMode}
                isHighlighted={highlightBookmarkId === bookmark.id}
                onEdit={onEdit}
                onDelete={onDelete}
                onClick={onClick}
                updateItemHeight={updateItemHeight}
              />
            )
          })}
        </div>
      </div>
    </div>
  )
})

VirtualBookmarkList.displayName = 'VirtualBookmarkList'

export default VirtualBookmarkList

// 导出类型定义
export type { VirtualBookmarkListProps }