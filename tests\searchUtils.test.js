/**
 * 搜索工具测试
 */

import { 
  AdvancedSearch, 
  AdvancedFilter, 
  AdvancedSorter,
  SearchSuggestionGenerator,
  createAdvancedSearch,
  createAdvancedFilter,
  createAdvancedSorter,
  createSuggestionGenerator
} from '../src/utils/searchUtils'

// 测试数据
const mockBookmarks = [
  {
    id: '1',
    title: 'React 官方文档',
    description: 'React 是一个用于构建用户界面的 JavaScript 库',
    content: 'React 让你可以通过组件的方式构建用户界面',
    url: 'https://react.dev',
    category: '前端开发',
    tags: ['React', 'JavaScript', '前端'],
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    title: 'Vue.js 指南',
    description: 'Vue.js 是一个渐进式 JavaScript 框架',
    content: 'Vue.js 专注于视图层，易于学习和集成',
    url: 'https://vuejs.org',
    category: '前端开发',
    tags: ['Vue', 'JavaScript', '前端'],
    createdAt: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    title: 'Node.js 教程',
    description: 'Node.js 是一个基于 Chrome V8 引擎的 JavaScript 运行时',
    content: 'Node.js 让 JavaScript 可以在服务器端运行',
    url: 'https://nodejs.org',
    category: '后端开发',
    tags: ['Node.js', 'JavaScript', '后端'],
    createdAt: '2024-01-03T00:00:00Z'
  }
]

describe('AdvancedSearch', () => {
  let search

  beforeEach(() => {
    search = createAdvancedSearch()
  })

  test('应该创建搜索实例', () => {
    expect(search).toBeInstanceOf(AdvancedSearch)
  })

  test('应该返回所有项目当查询为空时', () => {
    const results = search.search(mockBookmarks, '')
    expect(results).toHaveLength(3)
    expect(results[0].score).toBe(0)
  })

  test('应该搜索标题中的关键词', () => {
    const results = search.search(mockBookmarks, 'React')
    expect(results).toHaveLength(1)
    expect(results[0].item.title).toBe('React 官方文档')
    expect(results[0].score).toBeGreaterThan(0)
  })

  test('应该搜索描述中的关键词', () => {
    const results = search.search(mockBookmarks, 'JavaScript')
    expect(results).toHaveLength(3)
    expect(results.every(r => r.score > 0)).toBe(true)
  })

  test('应该按分数排序结果', () => {
    const results = search.search(mockBookmarks, 'JavaScript')
    expect(results).toHaveLength(3)
    
    // 分数应该是递减的
    for (let i = 1; i < results.length; i++) {
      expect(results[i-1].score).toBeGreaterThanOrEqual(results[i].score)
    }
  })

  test('应该搜索标签', () => {
    const results = search.search(mockBookmarks, 'Vue')
    expect(results).toHaveLength(1)
    expect(results[0].item.title).toBe('Vue.js 指南')
  })

  test('应该支持不区分大小写搜索', () => {
    const results = search.search(mockBookmarks, 'react')
    expect(results).toHaveLength(1)
    expect(results[0].item.title).toBe('React 官方文档')
  })

  test('应该支持模糊搜索', () => {
    const results = search.search(mockBookmarks, '前端 JavaScript')
    expect(results.length).toBeGreaterThan(0)
  })
})

describe('AdvancedFilter', () => {
  let filter

  beforeEach(() => {
    filter = createAdvancedFilter()
  })

  test('应该创建筛选器实例', () => {
    expect(filter).toBeInstanceOf(AdvancedFilter)
  })

  test('应该返回所有项目当没有筛选条件时', () => {
    const results = filter.filter(mockBookmarks, [])
    expect(results).toHaveLength(3)
  })

  test('应该按分类筛选', () => {
    const conditions = [{
      field: 'category',
      operator: 'equals',
      value: '前端开发'
    }]
    const results = filter.filter(mockBookmarks, conditions)
    expect(results).toHaveLength(2)
    expect(results.every(r => r.category === '前端开发')).toBe(true)
  })

  test('应该支持包含操作符', () => {
    const conditions = [{
      field: 'title',
      operator: 'contains',
      value: '官方'
    }]
    const results = filter.filter(mockBookmarks, conditions)
    expect(results).toHaveLength(1)
    expect(results[0].title).toBe('React 官方文档')
  })

  test('应该支持多个筛选条件', () => {
    const conditions = [
      {
        field: 'category',
        operator: 'equals',
        value: '前端开发'
      },
      {
        field: 'title',
        operator: 'contains',
        value: 'React'
      }
    ]
    const results = filter.filter(mockBookmarks, conditions)
    expect(results).toHaveLength(1)
    expect(results[0].title).toBe('React 官方文档')
  })
})

describe('AdvancedSorter', () => {
  let sorter

  beforeEach(() => {
    sorter = createAdvancedSorter()
  })

  test('应该创建排序器实例', () => {
    expect(sorter).toBeInstanceOf(AdvancedSorter)
  })

  test('应该返回原始顺序当没有排序配置时', () => {
    const results = sorter.sort(mockBookmarks, [])
    expect(results).toEqual(mockBookmarks)
  })

  test('应该按标题排序', () => {
    const configs = [{
      field: 'title',
      direction: 'asc',
      type: 'string'
    }]
    const results = sorter.sort(mockBookmarks, configs)
    expect(results[0].title).toBe('Node.js 教程')
    expect(results[1].title).toBe('React 官方文档')
    expect(results[2].title).toBe('Vue.js 指南')
  })

  test('应该按日期排序', () => {
    const configs = [{
      field: 'createdAt',
      direction: 'desc',
      type: 'date'
    }]
    const results = sorter.sort(mockBookmarks, configs)
    expect(results[0].id).toBe('3') // 最新的
    expect(results[2].id).toBe('1') // 最旧的
  })

  test('应该支持多级排序', () => {
    const configs = [
      {
        field: 'category',
        direction: 'asc',
        type: 'string'
      },
      {
        field: 'title',
        direction: 'asc',
        type: 'string'
      }
    ]
    const results = sorter.sort(mockBookmarks, configs)
    
    // 先按分类排序，再按标题排序
    // 按字母顺序："前端开发" 在 "后端开发" 之前
    expect(results[0].category).toBe('后端开发') // Node.js 教程
    expect(results[1].category).toBe('前端开发') // React 官方文档
    expect(results[2].category).toBe('前端开发') // Vue.js 指南
    
    // 验证具体的标题
    expect(results[0].title).toBe('Node.js 教程')
    expect(results[1].title).toBe('React 官方文档')
    expect(results[2].title).toBe('Vue.js 指南')
  })
})

describe('SearchSuggestionGenerator', () => {
  let generator

  beforeEach(() => {
    generator = createSuggestionGenerator(mockBookmarks)
  })

  test('应该创建建议生成器实例', () => {
    expect(generator).toBeInstanceOf(SearchSuggestionGenerator)
  })

  test('应该返回空数组当查询太短时', () => {
    const suggestions = generator.generateSuggestions('a')
    expect(suggestions).toEqual([])
  })

  test('应该从标题生成建议', () => {
    const suggestions = generator.generateSuggestions('Re')
    expect(suggestions).toContain('React')
  })

  test('应该从标签生成建议', () => {
    const suggestions = generator.generateSuggestions('Java')
    expect(suggestions).toContain('JavaScript')
  })

  test('应该从分类生成建议', () => {
    const suggestions = generator.generateSuggestions('前端')
    expect(suggestions).toContain('前端开发')
  })

  test('应该限制建议数量', () => {
    const suggestions = generator.generateSuggestions('a', 2)
    expect(suggestions.length).toBeLessThanOrEqual(2)
  })

  test('应该更新项目列表', () => {
    const newItems = [
      {
        id: '4',
        title: 'Angular 教程',
        tags: ['Angular', 'TypeScript'],
        category: '前端开发'
      }
    ]
    
    generator.updateItems(newItems)
    const suggestions = generator.generateSuggestions('An')
    expect(suggestions).toContain('Angular')
  })
})

describe('工厂函数', () => {
  test('createAdvancedSearch 应该创建搜索实例', () => {
    const search = createAdvancedSearch()
    expect(search).toBeInstanceOf(AdvancedSearch)
  })

  test('createAdvancedFilter 应该创建筛选器实例', () => {
    const filter = createAdvancedFilter()
    expect(filter).toBeInstanceOf(AdvancedFilter)
  })

  test('createAdvancedSorter 应该创建排序器实例', () => {
    const sorter = createAdvancedSorter()
    expect(sorter).toBeInstanceOf(AdvancedSorter)
  })

  test('createSuggestionGenerator 应该创建建议生成器实例', () => {
    const generator = createSuggestionGenerator([])
    expect(generator).toBeInstanceOf(SearchSuggestionGenerator)
  })
})

describe('搜索性能测试', () => {
  test('应该在合理时间内完成大量数据的搜索', () => {
    // 生成大量测试数据
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      id: `${i}`,
      title: `测试项目 ${i}`,
      description: `这是第 ${i} 个测试项目的描述`,
      content: `内容 ${i} 包含一些测试文本`,
      url: `https://example.com/${i}`,
      category: i % 2 === 0 ? '分类A' : '分类B',
      tags: [`标签${i}`, `标签${i + 1}`],
      createdAt: new Date(2024, 0, i + 1).toISOString()
    }))

    const search = createAdvancedSearch()
    
    const startTime = performance.now()
    const results = search.search(largeDataset, '测试')
    const endTime = performance.now()
    
    expect(results.length).toBeGreaterThan(0)
    expect(endTime - startTime).toBeLessThan(100) // 应该在100ms内完成
  })
})