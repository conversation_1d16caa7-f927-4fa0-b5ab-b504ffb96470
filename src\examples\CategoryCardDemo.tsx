// CategoryCard组件演示页面

import React, { useState } from 'react'
import CategoryCard from '../components/CategoryCard'
import type { Category } from '../types'

// 演示数据
const demoCategories: Category[] = [
  {
    id: 'category-1',
    name: '技术',
    description: '编程、开发工具、技术文档等相关内容',
    color: '#3B82F6',
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-02T10:00:00Z'),
    bookmarkCount: 0
  },
  {
    id: 'category-2',
    name: '学习',
    description: '在线课程、教程、学习资源',
    color: '#10B981',
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
    bookmarkCount: 0
  },
  {
    id: 'category-3',
    name: '工具',
    description: '实用工具、软件、在线服务等',
    color: '#F59E0B',
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
    bookmarkCount: 0
  },
  {
    id: 'category-4',
    name: '娱乐',
    color: '#EF4444',
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
    bookmarkCount: 0
  },
  {
    id: 'category-5',
    name: '前端开发',
    description: 'React、Vue、Angular等前端开发相关内容',
    color: '#8B5CF6',
    parentId: 'category-1',
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
    bookmarkCount: 0
  },
  {
    id: 'category-6',
    name: '设计',
    description: '这是一个非常非常非常非常非常非常长的描述文本，用来测试文本截断功能是否正常工作，确保在有限的空间内能够正确显示内容而不会影响整体布局',
    color: '#EC4899',
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
    bookmarkCount: 0
  }
]

const CategoryCardDemo: React.FC = () => {
  const [categories, setCategories] = useState(demoCategories)
  const [bookmarkCounts] = useState<Record<string, number>>({
    'category-1': 25,
    'category-2': 18,
    'category-3': 12,
    'category-4': 8,
    'category-5': 15,
    'category-6': 0
  })

  const handleEdit = (categoryId: string) => {
    console.log('编辑分类:', categoryId)
    alert(`编辑分类: ${categories.find(c => c.id === categoryId)?.name}`)
  }

  const handleDelete = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      const confirmed = confirm(`确定要删除分类"${category.name}"吗？`)
      if (confirmed) {
        setCategories(prev => prev.filter(c => c.id !== categoryId))
        console.log('删除分类:', categoryId)
      }
    }
  }

  const handleClick = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    console.log('点击分类:', category?.name)
    alert(`查看分类: ${category?.name}`)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">CategoryCard 组件演示</h1>
          <p className="text-gray-600">
            展示分类卡片组件的各种状态和功能，包括颜色显示、悬停效果、交互反馈等。
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <CategoryCard
              key={category.id}
              category={category}
              bookmarkCount={bookmarkCounts[category.id] || 0}
              onEdit={() => handleEdit(category.id)}
              onDelete={() => handleDelete(category.id)}
              onClick={() => handleClick(category.id)}
            />
          ))}
        </div>

        <div className="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">功能说明</h2>
          <div className="space-y-4 text-sm text-gray-600">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">基本功能</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>显示分类名称、描述、颜色和书签数量</li>
                <li>支持自定义分类颜色显示</li>
                <li>显示创建时间和更新时间</li>
                <li>区分空分类和活跃分类状态</li>
                <li>支持子分类指示器</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">交互功能</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>悬停时显示编辑和删除按钮</li>
                <li>点击卡片可触发自定义操作</li>
                <li>编辑和删除按钮有独立的点击处理</li>
                <li>支持键盘导航和屏幕阅读器</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">样式特性</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>响应式设计，适配不同屏幕尺寸</li>
                <li>平滑的悬停和过渡动画</li>
                <li>长文本自动截断处理</li>
                <li>基于分类颜色的背景色调整</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CategoryCardDemo