import React, { useState, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Checkbox } from '../ui/checkbox'
import BookmarksTab from '../BookmarksTab'
import TestErrorBoundary from './TestErrorBoundary'

// 测试项目配置
const TEST_CONFIG = {
  basicFunctions: [
    { id: 'search-input', label: '搜索框输入和清除' },
    { id: 'category-filter', label: '分类筛选器选择' },
    { id: 'add-bookmark', label: '添加收藏按钮点击' },
    { id: 'refresh-button', label: '刷新按钮功能' },
    { id: 'view-mode', label: '视图模式切换' }
  ],
  shadcnIntegration: [
    { id: 'input-style', label: 'Input组件样式正确' },
    { id: 'select-interaction', label: 'Select组件交互正常' },
    { id: 'button-variants', label: 'Button组件变体显示' },
    { id: 'card-layout', label: 'Card组件布局合理' },
    { id: 'theme-colors', label: '主题颜色一致' }
  ]
} as const

/**
 * 测试清单组件
 */
interface TestChecklistProps {
  title: string
  items: readonly { id: string; label: string }[]
  testResults: Record<string, boolean>
  onToggle: (itemId: string) => void
}

interface TestProgress {
  completed: number
  total: number
  percentage: number
}

const TestChecklist: React.FC<TestChecklistProps> = ({ title, items, testResults, onToggle }) => (
  <div className="space-y-2">
    <h5 className="font-medium">{title}</h5>
    <ul className="space-y-2" role="checklist" aria-label={title}>
      {items.map((item) => (
        <li key={item.id} className="flex items-center space-x-2">
          <Checkbox
            id={item.id}
            checked={testResults[item.id] || false}
            onCheckedChange={() => onToggle(item.id)}
          />
          <label
            htmlFor={item.id}
            className={`text-sm cursor-pointer ${
              testResults[item.id] ? 'line-through text-green-600' : 'text-muted-foreground'
            }`}
          >
            {item.label}
          </label>
        </li>
      ))}
    </ul>
  </div>
)

/**
 * 组件预览区域
 */
const ComponentPreview: React.FC = () => (
  <div className="border-2 border-dashed border-muted rounded-lg p-4">
    <div className="mb-4 text-center">
      <Badge variant="secondary" className="mb-2">组件预览区域</Badge>
      <p className="text-sm text-muted-foreground">
        下方显示的是重构后的BookmarksTab组件，已完全使用shadcn/ui组件
      </p>
    </div>
    
    {/* 错误边界保护 */}
    <div className="min-h-[400px]">
      <TestErrorBoundary
        onError={(error: Error, errorInfo: React.ErrorInfo) => {
          console.error('BookmarksTab测试错误:', { error, errorInfo })
        }}
      >
        <React.Suspense fallback={
          <div className="flex items-center justify-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        }>
          <BookmarksTab />
        </React.Suspense>
      </TestErrorBoundary>
    </div>
  </div>
)

/**
 * BookmarksTab组件测试页面
 * 用于验证重构后的BookmarksTab组件的shadcn集成效果
 */
const BookmarksTabTestPage: React.FC = () => {
  // 测试结果状态管理
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})

  // 计算测试进度
  const testProgress = useMemo((): TestProgress => {
    const allItems = [...TEST_CONFIG.basicFunctions, ...TEST_CONFIG.shadcnIntegration]
    const completed = allItems.filter(item => testResults[item.id]).length
    const total = allItems.length
    return {
      completed,
      total,
      percentage: total > 0 ? Math.round((completed / total) * 100) : 0
    }
  }, [testResults])

  // 处理测试项目切换
  const handleTestToggle = useCallback((itemId: string) => {
    setTestResults((prev: Record<string, boolean>) => ({
      ...prev,
      [itemId]: !prev[itemId]
    }))
  }, [])

  // 重置所有测试结果
  const handleResetTests = useCallback(() => {
    setTestResults({})
  }, [])
  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl flex items-center gap-2">
            📋 BookmarksTab组件测试页面
            <Badge variant="secondary">shadcn重构验证</Badge>
          </CardTitle>
          <CardDescription>
            验证重构后的BookmarksTab组件是否正确使用shadcn/ui组件，包括Input、Select、Button、Card等组件的集成效果
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h3 className="font-semibold mb-2">✅ 已完成的shadcn组件重构</h3>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• shadcn Input组件 - 搜索输入框</li>
                  <li>• shadcn Select组件 - 分类筛选器</li>
                  <li>• shadcn Button组件 - 所有操作按钮</li>
                  <li>• shadcn Card组件 - 页面布局容器</li>
                  <li>• 搜索建议下拉框 - Card + Button组合</li>
                </ul>
              </Card>
              
              <Card className="p-4">
                <h3 className="font-semibold mb-2">🎯 测试重点</h3>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• 组件独立性 - 从OptionsApp分离</li>
                  <li>• shadcn样式一致性</li>
                  <li>• 交互功能完整性</li>
                  <li>• 响应式布局适配</li>
                  <li>• 主题系统集成</li>
                </ul>
              </Card>
            </div>
            
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">BookmarksTab组件实时预览</h3>
                <div className="flex gap-2">
                  <Badge variant="outline">独立组件</Badge>
                  <Badge variant="outline">shadcn集成</Badge>
                </div>
              </div>
              
              <div className="text-sm text-muted-foreground mb-4">
                <p>以下是重构后的BookmarksTab组件的实时预览。请注意观察：</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>搜索框使用shadcn Input组件，带有搜索图标和加载状态</li>
                  <li>分类筛选器使用shadcn Select组件，支持下拉选择</li>
                  <li>操作按钮使用shadcn Button组件的不同变体</li>
                  <li>整体布局使用shadcn Card组件系统</li>
                  <li>颜色和间距遵循shadcn设计系统</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试进度显示 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">📊 测试进度</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant={testProgress.percentage === 100 ? "default" : "secondary"}>
                {testProgress.completed}/{testProgress.total}
              </Badge>
              <Button variant="outline" size="sm" onClick={handleResetTests}>
                重置
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>完成进度</span>
              <span>{testProgress.percentage}%</span>
            </div>
            <div className="w-full bg-secondary rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${testProgress.percentage}%` }}
              ></div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* BookmarksTab组件预览区域 */}
      <ComponentPreview />

      {/* 测试说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">🧪 测试说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">功能测试项目：</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <TestChecklist
                  title="基础功能"
                  items={TEST_CONFIG.basicFunctions}
                  testResults={testResults}
                  onToggle={handleTestToggle}
                />
                <TestChecklist
                  title="shadcn集成"
                  items={TEST_CONFIG.shadcnIntegration}
                  testResults={testResults}
                  onToggle={handleTestToggle}
                />
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h4 className="font-semibold mb-2">预期行为：</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 组件应该正常加载并显示"加载收藏数据中..."状态</li>
                <li>• 搜索框应该有搜索图标，支持输入和建议</li>
                <li>• 分类选择器应该显示"所有分类"选项</li>
                <li>• 按钮应该有正确的shadcn样式和hover效果</li>
                <li>• 如果没有数据，应该显示空状态提示</li>
                <li>• 所有交互元素应该响应用户操作</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default BookmarksTabTestPage