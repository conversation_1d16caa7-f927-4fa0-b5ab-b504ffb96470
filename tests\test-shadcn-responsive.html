<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>shadcn组件响应式测试</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* shadcn CSS变量 */
    :root {
      --background: 0 0% 100%;
      --foreground: 222.2 84% 4.9%;
      --card: 0 0% 100%;
      --card-foreground: 222.2 84% 4.9%;
      --popover: 0 0% 100%;
      --popover-foreground: 222.2 84% 4.9%;
      --primary: 262.1 83.3% 57.8%;
      --primary-foreground: 210 40% 98%;
      --secondary: 220 14.3% 95.9%;
      --secondary-foreground: 220.9 39.3% 11%;
      --muted: 220 14.3% 95.9%;
      --muted-foreground: 220 8.9% 46.1%;
      --accent: 220 14.3% 95.9%;
      --accent-foreground: 220.9 39.3% 11%;
      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 210 40% 98%;
      --border: 220 13% 91%;
      --input: 220 13% 91%;
      --ring: 262.1 83.3% 57.8%;
      --radius: 0.75rem;
    }
    
    .dark {
      --background: 224 71.4% 4.1%;
      --foreground: 210 20% 98%;
      --card: 224 71.4% 4.1%;
      --card-foreground: 210 20% 98%;
      --popover: 224 71.4% 4.1%;
      --popover-foreground: 210 20% 98%;
      --primary: 263.4 70% 50.4%;
      --primary-foreground: 210 20% 98%;
      --secondary: 215 27.9% 16.9%;
      --secondary-foreground: 210 20% 98%;
      --muted: 215 27.9% 16.9%;
      --muted-foreground: 217.9 10.6% 64.9%;
      --accent: 215 27.9% 16.9%;
      --accent-foreground: 210 20% 98%;
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 210 20% 98%;
      --border: 215 27.9% 16.9%;
      --input: 215 27.9% 16.9%;
      --ring: 263.4 70% 50.4%;
    }
    
    /* shadcn基础样式 */
    * {
      border-color: hsl(var(--border));
    }
    
    body {
      background-color: hsl(var(--background));
      color: hsl(var(--foreground));
    }
    
    /* shadcn组件样式 */
    .shadcn-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      border-radius: calc(var(--radius) - 2px);
      font-size: 0.875rem;
      font-weight: 500;
      transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms;
      outline: 2px solid transparent;
      outline-offset: 2px;
    }
    
    .shadcn-button:focus-visible {
      outline: 2px solid hsl(var(--ring));
      outline-offset: 2px;
    }
    
    .shadcn-button:disabled {
      pointer-events: none;
      opacity: 0.5;
    }
    
    .shadcn-button-default {
      background-color: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
    }
    
    .shadcn-button-default:hover {
      background-color: hsl(var(--primary) / 0.9);
    }
    
    .shadcn-button-outline {
      border: 1px solid hsl(var(--input));
      background-color: hsl(var(--background));
    }
    
    .shadcn-button-outline:hover {
      background-color: hsl(var(--accent));
      color: hsl(var(--accent-foreground));
    }
    
    .shadcn-card {
      border-radius: calc(var(--radius));
      border: 1px solid hsl(var(--border));
      background-color: hsl(var(--card));
      color: hsl(var(--card-foreground));
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    }
    
    .shadcn-input {
      display: flex;
      height: 2.5rem;
      width: 100%;
      border-radius: calc(var(--radius) - 2px);
      border: 1px solid hsl(var(--input));
      background-color: hsl(var(--background));
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms;
    }
    
    .shadcn-input:focus-visible {
      outline: 2px solid hsl(var(--ring));
      outline-offset: 2px;
    }
    
    .shadcn-input:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
    
    /* 响应式测试样式 */
    .test-grid {
      display: grid;
      gap: 1rem;
      grid-template-columns: 1fr;
    }
    
    @media (min-width: 640px) {
      .test-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (min-width: 768px) {
      .test-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
    
    @media (min-width: 1024px) {
      .test-grid {
        grid-template-columns: repeat(4, 1fr);
      }
    }
    
    .viewport-indicator {
      position: fixed;
      top: 1rem;
      right: 1rem;
      background-color: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
      padding: 0.5rem 1rem;
      border-radius: calc(var(--radius));
      font-size: 0.75rem;
      font-weight: 600;
      z-index: 50;
    }
  </style>
</head>
<body class="p-4 sm:p-6 lg:p-8">
  <!-- 视口指示器 -->
  <div class="viewport-indicator">
    <span class="sm:hidden">XS</span>
    <span class="hidden sm:inline md:hidden">SM</span>
    <span class="hidden md:inline lg:hidden">MD</span>
    <span class="hidden lg:inline xl:hidden">LG</span>
    <span class="hidden xl:inline 2xl:hidden">XL</span>
    <span class="hidden 2xl:inline">2XL</span>
  </div>

  <div class="max-w-7xl mx-auto">
    <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold mb-6 sm:mb-8">
      shadcn组件响应式测试
    </h1>
    
    <!-- 按钮测试 -->
    <section class="mb-8 sm:mb-12">
      <h2 class="text-xl sm:text-2xl font-semibold mb-4">按钮组件</h2>
      <div class="flex flex-col sm:flex-row gap-4">
        <button class="shadcn-button shadcn-button-default px-4 py-2">
          默认按钮
        </button>
        <button class="shadcn-button shadcn-button-outline px-4 py-2">
          轮廓按钮
        </button>
        <button class="shadcn-button shadcn-button-default px-2 py-1 sm:px-4 sm:py-2">
          响应式按钮
        </button>
      </div>
    </section>
    
    <!-- 卡片网格测试 -->
    <section class="mb-8 sm:mb-12">
      <h2 class="text-xl sm:text-2xl font-semibold mb-4">卡片网格</h2>
      <div class="test-grid">
        <div class="shadcn-card p-4 sm:p-6">
          <h3 class="text-lg font-medium mb-2">卡片 1</h3>
          <p class="text-sm text-muted-foreground">这是一个响应式卡片组件的示例。</p>
        </div>
        <div class="shadcn-card p-4 sm:p-6">
          <h3 class="text-lg font-medium mb-2">卡片 2</h3>
          <p class="text-sm text-muted-foreground">在不同屏幕尺寸下会有不同的布局。</p>
        </div>
        <div class="shadcn-card p-4 sm:p-6">
          <h3 class="text-lg font-medium mb-2">卡片 3</h3>
          <p class="text-sm text-muted-foreground">测试网格系统的响应式行为。</p>
        </div>
        <div class="shadcn-card p-4 sm:p-6">
          <h3 class="text-lg font-medium mb-2">卡片 4</h3>
          <p class="text-sm text-muted-foreground">确保在所有断点下都能正常显示。</p>
        </div>
      </div>
    </section>
    
    <!-- 表单测试 */
    <section class="mb-8 sm:mb-12">
      <h2 class="text-xl sm:text-2xl font-semibold mb-4">表单组件</h2>
      <div class="shadcn-card p-4 sm:p-6 max-w-2xl">
        <form class="space-y-4">
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">姓名</label>
              <input type="text" class="shadcn-input" placeholder="请输入姓名">
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">邮箱</label>
              <input type="email" class="shadcn-input" placeholder="请输入邮箱">
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">消息</label>
            <textarea class="shadcn-input min-h-[100px] resize-none" placeholder="请输入消息"></textarea>
          </div>
          <div class="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <button type="submit" class="shadcn-button shadcn-button-default px-6 py-2 flex-1 sm:flex-none">
              提交
            </button>
            <button type="button" class="shadcn-button shadcn-button-outline px-6 py-2 flex-1 sm:flex-none">
              取消
            </button>
          </div>
        </form>
      </div>
    </section>
    
    <!-- 响应式文本测试 -->
    <section class="mb-8 sm:mb-12">
      <h2 class="text-xl sm:text-2xl font-semibold mb-4">响应式文本</h2>
      <div class="space-y-4">
        <p class="text-sm sm:text-base lg:text-lg">
          这段文本在不同屏幕尺寸下会有不同的字体大小。
        </p>
        <p class="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl">
          这段文本展示了更多的响应式断点。
        </p>
      </div>
    </section>
  </div>
  
  <script>
    // 添加一些交互功能
    document.addEventListener('DOMContentLoaded', function() {
      // 监听窗口大小变化
      function updateViewportInfo() {
        const width = window.innerWidth;
        console.log('当前视口宽度:', width + 'px');
      }
      
      window.addEventListener('resize', updateViewportInfo);
      updateViewportInfo();
      
      // 测试按钮点击
      document.querySelectorAll('.shadcn-button').forEach(button => {
        button.addEventListener('click', function(e) {
          e.preventDefault();
          console.log('按钮被点击:', this.textContent.trim());
        });
      });
    });
  </script>
</body>
</html>