// AI配置服务类 - 负责AI服务的配置管理

import { 
  AIConfig, 
  AIProvider, 
  AIProviderPreset, 
  AIConnectionTestResult,
  AIServiceError
} from '../types/ai'
import { ChromeStorageService } from '../utils/chromeStorage'

/**
 * AI配置服务类
 * 提供AI服务的配置管理、连接测试和预设管理功能
 */
export class AIConfigService {
  
  /**
   * 预设的AI服务提供商配置
   */
  private static readonly PROVIDER_PRESETS: AIProviderPreset[] = [
    {
      id: 'openai',
      name: 'OpenAI',
      description: 'OpenAI GPT系列模型，包括GPT-3.5和GPT-4',
      baseUrl: 'https://api.openai.com/v1',
      defaultModel: 'gpt-3.5-turbo',
      supportedModels: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview'],
      requiresApiKey: true,
      documentationUrl: 'https://platform.openai.com/docs',
      icon: '🤖'
    },
    {
      id: 'claude',
      name: 'Anthropic Claude',
      description: 'Anthropic的Claude系列模型',
      baseUrl: 'https://api.anthropic.com/v1',
      defaultModel: 'claude-3-sonnet-20240229',
      supportedModels: ['claude-3-sonnet-20240229', 'claude-3-opus-20240229', 'claude-3-haiku-20240307'],
      requiresApiKey: true,
      documentationUrl: 'https://docs.anthropic.com',
      icon: '🧠'
    },
    {
      id: 'gemini',
      name: 'Google Gemini',
      description: 'Google的Gemini系列模型',
      baseUrl: 'https://generativelanguage.googleapis.com/v1',
      defaultModel: 'gemini-pro',
      supportedModels: ['gemini-pro', 'gemini-pro-vision'],
      requiresApiKey: true,
      documentationUrl: 'https://ai.google.dev/docs',
      icon: '💎'
    },
    {
      id: 'local',
      name: '本地模型',
      description: '本地部署的AI模型（如Ollama）',
      baseUrl: 'http://localhost:11434/v1',
      defaultModel: 'llama2',
      supportedModels: ['llama2', 'codellama', 'mistral', 'qwen'],
      requiresApiKey: false,
      documentationUrl: 'https://ollama.ai/docs',
      icon: '🏠'
    }
  ]

  /**
   * 默认AI配置
   */
  private static readonly DEFAULT_CONFIG: AIConfig = {
    provider: 'openai',
    baseUrl: 'https://api.openai.com/v1',
    model: 'gpt-3.5-turbo',
    autoTagging: true,
    autoCategories: true,
    autoDescription: false,
    temperature: 0.7,
    maxTokens: 1000,
    timeout: 30000,
    isConnected: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  /**
   * 获取当前AI配置
   * @returns Promise<AIConfig>
   */
  async getConfig(): Promise<AIConfig> {
    try {
      const config = await ChromeStorageService.getSyncSetting('ai_config', AIConfigService.DEFAULT_CONFIG)
      
      // 确保配置包含所有必需字段
      return {
        ...AIConfigService.DEFAULT_CONFIG,
        ...config,
        updatedAt: new Date(config?.updatedAt || Date.now())
      }
    } catch (error) {
      console.error('获取AI配置失败:', error)
      return AIConfigService.DEFAULT_CONFIG
    }
  }

  /**
   * 保存AI配置
   * @param config AI配置
   * @returns Promise<void>
   */
  async saveConfig(config: Partial<AIConfig>): Promise<void> {
    try {
      const currentConfig = await this.getConfig()
      
      const updatedConfig: AIConfig = {
        ...currentConfig,
        ...config,
        updatedAt: new Date()
      }

      // 验证配置
      this.validateConfig(updatedConfig)

      await ChromeStorageService.saveSyncSetting('ai_config', updatedConfig)
      console.log('AI配置保存成功')
    } catch (error) {
      console.error('保存AI配置失败:', error)
      throw new Error(`保存AI配置失败: ${error.message}`)
    }
  }

  /**
   * 获取预设配置列表
   * @returns AIProviderPreset[]
   */
  getProviderPresets(): AIProviderPreset[] {
    return [...AIConfigService.PROVIDER_PRESETS]
  }

  /**
   * 根据提供商获取预设配置
   * @param provider 提供商类型
   * @returns AIProviderPreset | null
   */
  getProviderPreset(provider: AIProvider): AIProviderPreset | null {
    return AIConfigService.PROVIDER_PRESETS.find(preset => preset.id === provider) || null
  }

  /**
   * 应用预设配置
   * @param provider 提供商类型
   * @param apiKey API密钥（可选）
   * @returns Promise<void>
   */
  async applyPreset(provider: AIProvider, apiKey?: string): Promise<void> {
    const preset = this.getProviderPreset(provider)
    if (!preset) {
      throw new Error(`未找到提供商预设: ${provider}`)
    }

    const config: Partial<AIConfig> = {
      provider: preset.id,
      baseUrl: preset.baseUrl,
      model: preset.defaultModel,
      apiKey: apiKey,
      availableModels: preset.supportedModels,
      isConnected: false
    }

    await this.saveConfig(config)
    console.log(`已应用预设配置: ${preset.name}`)
  }

  /**
   * 测试AI服务连接
   * @param config 要测试的配置（可选，默认使用当前配置）
   * @returns Promise<AIConnectionTestResult>
   */
  async testConnection(config?: Partial<AIConfig>): Promise<AIConnectionTestResult> {
    const testConfig = config ? { ...await this.getConfig(), ...config } : await this.getConfig()
    const startTime = Date.now()

    try {
      console.log(`开始测试AI连接: ${testConfig.provider}`)

      // 根据不同提供商进行连接测试
      const result = await this.performConnectionTest(testConfig)
      
      const responseTime = Date.now() - startTime

      const testResult: AIConnectionTestResult = {
        success: result.success,
        responseTime,
        availableModels: result.availableModels,
        error: result.error,
        errorCode: result.success ? undefined : 'CONNECTION_FAILED',
        testedAt: new Date()
      }

      // 更新配置中的连接状态
      if (result.success) {
        await this.saveConfig({
          isConnected: true,
          lastTestDate: new Date(),
          availableModels: result.availableModels
        })
      } else {
        await this.saveConfig({
          isConnected: false,
          lastTestDate: new Date()
        })
      }

      console.log(`AI连接测试完成: ${result.success ? '成功' : '失败'}`)
      return testResult

    } catch (error) {
      const responseTime = Date.now() - startTime
      console.error('AI连接测试失败:', error)

      await this.saveConfig({
        isConnected: false,
        lastTestDate: new Date()
      })

      return {
        success: false,
        responseTime,
        error: error.message,
        errorCode: 'CONNECTION_FAILED',
        testedAt: new Date()
      }
    }
  }

  /**
   * 获取可用模型列表
   * @param forceRefresh 是否强制刷新
   * @returns Promise<string[]>
   */
  async getAvailableModels(forceRefresh: boolean = false): Promise<string[]> {
    const config = await this.getConfig()

    // 如果不强制刷新且有缓存的模型列表，直接返回
    if (!forceRefresh && config.availableModels && config.availableModels.length > 0) {
      return config.availableModels
    }

    try {
      // 测试连接并获取模型列表
      const testResult = await this.testConnection()
      
      if (testResult.success && testResult.availableModels) {
        return testResult.availableModels
      } else {
        // 如果测试失败，返回预设的支持模型列表
        const preset = this.getProviderPreset(config.provider)
        return preset?.supportedModels || [config.model]
      }
    } catch (error) {
      console.error('获取可用模型列表失败:', error)
      
      // 返回预设的支持模型列表
      const preset = this.getProviderPreset(config.provider)
      return preset?.supportedModels || [config.model]
    }
  }

  /**
   * 重置配置为默认值
   * @returns Promise<void>
   */
  async resetToDefault(): Promise<void> {
    try {
      await ChromeStorageService.saveSyncSetting('ai_config', {
        ...AIConfigService.DEFAULT_CONFIG,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      console.log('AI配置已重置为默认值')
    } catch (error) {
      console.error('重置AI配置失败:', error)
      throw new Error(`重置AI配置失败: ${error.message}`)
    }
  }

  /**
   * 导出配置
   * @returns Promise<AIConfig>
   */
  async exportConfig(): Promise<AIConfig> {
    const config = await this.getConfig()
    
    // 移除敏感信息
    const exportConfig = { ...config }
    if (exportConfig.apiKey) {
      exportConfig.apiKey = '***已隐藏***'
    }
    
    return exportConfig
  }

  /**
   * 导入配置
   * @param config 要导入的配置
   * @param options 导入选项
   * @returns Promise<void>
   */
  async importConfig(
    config: Partial<AIConfig>, 
    options: { 
      skipApiKey?: boolean
      validateConnection?: boolean 
    } = {}
  ): Promise<void> {
    try {
      // 过滤掉敏感信息（如果需要）
      const importConfig = { ...config }
      if (options.skipApiKey) {
        delete importConfig.apiKey
      }

      // 验证配置
      this.validateConfig(importConfig as AIConfig)

      // 保存配置
      await this.saveConfig(importConfig)

      // 如果需要验证连接
      if (options.validateConnection) {
        const testResult = await this.testConnection()
        if (!testResult.success) {
          console.warn('导入的配置连接测试失败:', testResult.error)
        }
      }

      console.log('AI配置导入成功')
    } catch (error) {
      console.error('导入AI配置失败:', error)
      throw new Error(`导入AI配置失败: ${error.message}`)
    }
  }

  /**
   * 获取配置验证状态
   * @returns Promise<{isValid: boolean, errors: string[]}>
   */
  async validateCurrentConfig(): Promise<{isValid: boolean, errors: string[]}> {
    try {
      const config = await this.getConfig()
      const errors: string[] = []

      // 基础验证
      if (!config.provider) {
        errors.push('未选择AI服务提供商')
      }

      if (!config.model) {
        errors.push('未选择AI模型')
      }

      if (!config.baseUrl) {
        errors.push('未设置API基础URL')
      }

      // 根据提供商进行特定验证
      const preset = this.getProviderPreset(config.provider)
      if (preset?.requiresApiKey && !config.apiKey) {
        errors.push('该服务提供商需要API密钥')
      }

      // URL格式验证
      if (config.baseUrl) {
        try {
          new URL(config.baseUrl)
        } catch {
          errors.push('API基础URL格式无效')
        }
      }

      // 参数范围验证
      if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
        errors.push('温度参数应在0-2之间')
      }

      if (config.maxTokens !== undefined && config.maxTokens <= 0) {
        errors.push('最大令牌数应大于0')
      }

      if (config.timeout !== undefined && config.timeout <= 0) {
        errors.push('超时时间应大于0')
      }

      return {
        isValid: errors.length === 0,
        errors
      }
    } catch (error) {
      console.error('验证AI配置失败:', error)
      return {
        isValid: false,
        errors: ['配置验证过程中发生错误']
      }
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 验证配置
   * @param config AI配置
   */
  private validateConfig(config: Partial<AIConfig>): void {
    if (config.provider && !AIConfigService.PROVIDER_PRESETS.find(p => p.id === config.provider)) {
      throw new Error(`不支持的AI服务提供商: ${config.provider}`)
    }

    if (config.baseUrl) {
      try {
        new URL(config.baseUrl)
      } catch {
        throw new Error('API基础URL格式无效')
      }
    }

    if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
      throw new Error('温度参数应在0-2之间')
    }

    if (config.maxTokens !== undefined && config.maxTokens <= 0) {
      throw new Error('最大令牌数应大于0')
    }

    if (config.timeout !== undefined && config.timeout <= 0) {
      throw new Error('超时时间应大于0')
    }
  }

  /**
   * 执行连接测试
   * @param config 测试配置
   * @returns Promise<{success: boolean, availableModels?: string[], error?: string}>
   */
  private async performConnectionTest(config: AIConfig): Promise<{
    success: boolean
    availableModels?: string[]
    error?: string
  }> {
    try {
      // 根据不同提供商执行不同的测试逻辑
      switch (config.provider) {
        case 'openai':
          return await this.testOpenAIConnection(config)
        
        case 'claude':
          return await this.testClaudeConnection(config)
        
        case 'gemini':
          return await this.testGeminiConnection(config)
        
        case 'local':
          return await this.testLocalConnection(config)
        
        case 'custom':
          return await this.testCustomConnection(config)
        
        default:
          throw new Error(`不支持的提供商: ${config.provider}`)
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 测试OpenAI连接
   */
  private async testOpenAIConnection(config: AIConfig): Promise<{
    success: boolean
    availableModels?: string[]
    error?: string
  }> {
    try {
      const response = await fetch(`${config.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        signal: AbortSignal.timeout(config.timeout || 30000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      const models = data.data?.map((model: any) => model.id) || []

      return {
        success: true,
        availableModels: models.filter((model: string) => 
          model.includes('gpt') || model.includes('text-davinci')
        )
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 测试Claude连接
   */
  private async testClaudeConnection(config: AIConfig): Promise<{
    success: boolean
    availableModels?: string[]
    error?: string
  }> {
    try {
      // Claude API测试 - 发送一个简单的消息测试
      const response = await fetch(`${config.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'x-api-key': config.apiKey || '',
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: config.model,
          max_tokens: 10,
          messages: [
            {
              role: 'user',
              content: 'Hello'
            }
          ]
        }),
        signal: AbortSignal.timeout(config.timeout || 30000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // Claude没有模型列表API，返回预设模型
      const preset = this.getProviderPreset('claude')
      return {
        success: true,
        availableModels: preset?.supportedModels || []
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 测试Gemini连接
   */
  private async testGeminiConnection(config: AIConfig): Promise<{
    success: boolean
    availableModels?: string[]
    error?: string
  }> {
    try {
      const response = await fetch(`${config.baseUrl}/models?key=${config.apiKey}`, {
        method: 'GET',
        signal: AbortSignal.timeout(config.timeout || 30000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      const models = data.models?.map((model: any) => model.name.replace('models/', '')) || []

      return {
        success: true,
        availableModels: models
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 测试本地连接
   */
  private async testLocalConnection(config: AIConfig): Promise<{
    success: boolean
    availableModels?: string[]
    error?: string
  }> {
    try {
      // 测试Ollama API
      const response = await fetch(`${config.baseUrl.replace('/v1', '')}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(config.timeout || 30000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      const models = data.models?.map((model: any) => model.name) || []

      return {
        success: true,
        availableModels: models
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 测试自定义连接
   */
  private async testCustomConnection(config: AIConfig): Promise<{
    success: boolean
    availableModels?: string[]
    error?: string
  }> {
    try {
      // 对于自定义提供商，尝试发送一个简单的请求
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...config.customHeaders
      }

      if (config.apiKey) {
        headers['Authorization'] = `Bearer ${config.apiKey}`
      }

      const response = await fetch(`${config.baseUrl}/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          model: config.model,
          messages: [
            {
              role: 'user',
              content: 'Hello'
            }
          ],
          max_tokens: 10,
          ...config.customParams
        }),
        signal: AbortSignal.timeout(config.timeout || 30000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        success: true,
        availableModels: [config.model] // 自定义提供商只返回当前配置的模型
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }
}

// 导出单例实例
export const aiConfigService = new AIConfigService()