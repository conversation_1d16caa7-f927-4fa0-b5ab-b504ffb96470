import { describe, it, expect } from 'vitest'
import { ValidationUtils } from '../src/utils/validation.ts'

describe('ValidationUtils', () => {
  describe('validateBookmarkInput', () => {
    it('应该验证有效的URL书签输入', () => {
      const input = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        description: '这是一个测试书签',
        tags: ['测试', '示例'],
        category: '工作'
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该验证有效的文本书签输入', () => {
      const input = {
        type: 'text',
        title: '重要笔记',
        content: '这是一段重要的文字内容',
        description: '笔记描述',
        tags: ['笔记'],
        category: '学习'
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该拒绝空标题', () => {
      const input = {
        type: 'url',
        title: '',
        url: 'https://example.com'
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('TITLE_REQUIRED')
    })

    it('应该拒绝过长的标题', () => {
      const input = {
        type: 'url',
        title: 'a'.repeat(501),
        url: 'https://example.com'
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('TITLE_TOO_LONG')
    })

    it('应该拒绝无效的书签类型', () => {
      const input = {
        type: 'invalid',
        title: '测试书签',
        url: 'https://example.com'
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('INVALID_TYPE')
    })

    it('应该要求URL类型书签提供URL', () => {
      const input = {
        type: 'url',
        title: '测试书签'
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('URL_REQUIRED')
    })

    it('应该验证URL格式', () => {
      const input = {
        type: 'url',
        title: '测试书签',
        url: 'invalid-url'
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('INVALID_URL')
    })

    it('应该要求文本类型书签提供内容', () => {
      const input = {
        type: 'text',
        title: '测试笔记'
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('CONTENT_REQUIRED')
    })

    it('应该拒绝过长的内容', () => {
      const input = {
        type: 'text',
        title: '测试笔记',
        content: 'a'.repeat(10001)
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('CONTENT_TOO_LONG')
    })

    it('应该拒绝过多的标签', () => {
      const input = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        tags: Array(21).fill('标签')
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('TOO_MANY_TAGS')
    })

    it('应该拒绝过长的标签', () => {
      const input = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        tags: ['a'.repeat(51)]
      }

      const result = ValidationUtils.validateBookmarkInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('TAG_TOO_LONG')
    })
  })

  describe('validateCategoryInput', () => {
    it('应该验证有效的分类输入', () => {
      const input = {
        name: '工作',
        description: '工作相关的书签',
        color: '#FF5733'
      }

      const result = ValidationUtils.validateCategoryInput(input)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该拒绝空名称', () => {
      const input = {
        name: ''
      }

      const result = ValidationUtils.validateCategoryInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('NAME_REQUIRED')
    })

    it('应该拒绝过长的名称', () => {
      const input = {
        name: 'a'.repeat(101)
      }

      const result = ValidationUtils.validateCategoryInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('NAME_TOO_LONG')
    })

    it('应该拒绝无效的颜色格式', () => {
      const input = {
        name: '测试分类',
        color: 'invalid-color'
      }

      const result = ValidationUtils.validateCategoryInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('INVALID_COLOR')
    })

    it('应该接受有效的hex颜色', () => {
      const input = {
        name: '测试分类',
        color: '#FF5733'
      }

      const result = ValidationUtils.validateCategoryInput(input)
      expect(result.isValid).toBe(true)
    })

    it('应该接受有效的rgb颜色', () => {
      const input = {
        name: '测试分类',
        color: 'rgb(255, 87, 51)'
      }

      const result = ValidationUtils.validateCategoryInput(input)
      expect(result.isValid).toBe(true)
    })
  })

  describe('validateTagInput', () => {
    it('应该验证有效的标签输入', () => {
      const input = {
        name: '重要',
        color: '#FF5733'
      }

      const result = ValidationUtils.validateTagInput(input)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该拒绝空名称', () => {
      const input = {
        name: ''
      }

      const result = ValidationUtils.validateTagInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('NAME_REQUIRED')
    })

    it('应该拒绝过长的名称', () => {
      const input = {
        name: 'a'.repeat(51)
      }

      const result = ValidationUtils.validateTagInput(input)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('NAME_TOO_LONG')
    })
  })

  describe('sanitizeBookmarkInput', () => {
    it('应该清理和标准化书签输入数据', () => {
      const input = {
        type: 'url',
        title: '  测试书签  ',
        url: '  https://example.com  ',
        description: '  描述  ',
        tags: ['  标签1  ', '', '  标签2  '],
        category: '  工作  '
      }

      const result = ValidationUtils.sanitizeBookmarkInput(input)
      expect(result.title).toBe('测试书签')
      expect(result.url).toBe('https://example.com')
      expect(result.description).toBe('描述')
      expect(result.tags).toEqual(['标签1', '标签2'])
      expect(result.category).toBe('工作')
    })

    it('应该设置默认分类', () => {
      const input = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com'
      }

      const result = ValidationUtils.sanitizeBookmarkInput(input)
      expect(result.category).toBe('default')
    })
  })

  describe('sanitizeCategoryInput', () => {
    it('应该清理和标准化分类输入数据', () => {
      const input = {
        name: '  工作  ',
        description: '  工作相关  ',
        color: '  #FF5733  '
      }

      const result = ValidationUtils.sanitizeCategoryInput(input)
      expect(result.name).toBe('工作')
      expect(result.description).toBe('工作相关')
      expect(result.color).toBe('#FF5733')
    })
  })

  describe('sanitizeTagInput', () => {
    it('应该清理和标准化标签输入数据', () => {
      const input = {
        name: '  重要  ',
        color: '  #FF5733  '
      }

      const result = ValidationUtils.sanitizeTagInput(input)
      expect(result.name).toBe('重要')
      expect(result.color).toBe('#FF5733')
    })
  })
})