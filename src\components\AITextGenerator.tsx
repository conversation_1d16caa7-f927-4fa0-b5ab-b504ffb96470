// AI文本生成组件 - 可重用的AI生成功能组件

import React, { useState } from 'react'
import { Spa<PERSON>les, Loader2, Refresh<PERSON>w, Check, X } from 'lucide-react'

// shadcn/ui组件导入
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

interface AIGenerationResult {
  content: string
  suggestions?: string[]
  metadata?: {
    model?: string
    timestamp?: string
  }
}

interface AITextGeneratorProps {
  /** 输入字段的标签 */
  label?: string
  /** 输入字段的占位符 */
  placeholder?: string
  /** 当前文本值 */
  value: string
  /** 文本变化回调 */
  onChange: (value: string) => void
  /** 生成提示词的上下文信息 */
  context?: {
    title?: string
    url?: string
    category?: string
    tags?: string[]
    [key: string]: any
  }
  /** 生成类型 */
  generationType?: 'description' | 'summary' | 'tags' | 'title' | 'notes'
  /** 是否禁用 */
  disabled?: boolean
  /** 最大行数 */
  maxRows?: number
  /** 是否显示建议 */
  showSuggestions?: boolean
  /** 自定义样式类名 */
  className?: string
}

/**
 * AI文本生成组件
 * 提供AI辅助文本生成功能，支持多种生成类型
 */
const AITextGenerator: React.FC<AITextGeneratorProps> = ({
  label,
  placeholder = '请输入内容...',
  value,
  onChange,
  context = {},
  generationType = 'description',
  disabled = false,
  maxRows = 4,
  showSuggestions = true,
  className = ''
}) => {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<string>('')
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [showPreview, setShowPreview] = useState(false)
  const [error, setError] = useState<string>('')

  /**
   * 构建AI生成的提示词
   */
  const buildPrompt = (): string => {
    const { title, url, category, tags } = context
    
    const prompts = {
      description: `请为以下内容生成一个简洁明了的描述：
标题：${title || '未知'}
网址：${url || '未知'}
分类：${category || '未知'}
标签：${tags?.join(', ') || '无'}

要求：
1. 描述应该简洁明了，不超过200字
2. 突出内容的核心价值和用途
3. 使用中文表达
4. 避免重复标题中的信息`,

      summary: `请为以下内容生成一个摘要：
标题：${title || '未知'}
内容：${value || '无'}
分类：${category || '未知'}

要求：
1. 摘要应该简洁，不超过100字
2. 提取关键信息
3. 使用中文表达`,

      tags: `请为以下内容推荐5-8个相关标签：
标题：${title || '未知'}
描述：${value || '无'}
分类：${category || '未知'}

要求：
1. 标签应该准确反映内容特征
2. 使用中文
3. 每个标签不超过4个字
4. 返回格式：标签1,标签2,标签3`,

      title: `请为以下内容生成一个合适的标题：
网址：${url || '未知'}
描述：${value || '无'}
分类：${category || '未知'}

要求：
1. 标题应该简洁明了，不超过50字
2. 准确反映内容主题
3. 使用中文表达`,

      notes: `请为以下内容生成一些有用的笔记或想法：
标题：${title || '未知'}
描述：${value || '无'}
分类：${category || '未知'}

要求：
1. 提供有价值的见解或补充信息
2. 不超过300字
3. 使用中文表达`
    }

    return prompts[generationType] || prompts.description
  }

  /**
   * 调用AI生成内容
   */
  const handleGenerate = async () => {
    if (isGenerating) return

    try {
      setIsGenerating(true)
      setError('')
      
      const prompt = buildPrompt()
      
      // 向background script发送AI请求
      const response = await chrome.runtime.sendMessage({
        type: 'AI_GENERATE_TEXT',
        data: {
          prompt,
          generationType,
          context,
          maxLength: generationType === 'title' ? 50 : generationType === 'tags' ? 100 : 500
        }
      })

      if (response?.success) {
        const result: AIGenerationResult = response.data
        setGeneratedContent(result.content)
        
        if (result.suggestions && showSuggestions) {
          setSuggestions(result.suggestions)
        }
        
        setShowPreview(true)
      } else {
        throw new Error(response?.error || 'AI生成失败')
      }
    } catch (error) {
      console.error('AI生成失败:', error)
      setError(error instanceof Error ? error.message : 'AI生成失败，请稍后重试')
    } finally {
      setIsGenerating(false)
    }
  }

  /**
   * 接受生成的内容
   */
  const handleAccept = () => {
    onChange(generatedContent)
    setShowPreview(false)
    setGeneratedContent('')
    setSuggestions([])
  }

  /**
   * 拒绝生成的内容
   */
  const handleReject = () => {
    setShowPreview(false)
    setGeneratedContent('')
    setSuggestions([])
  }

  /**
   * 重新生成
   */
  const handleRegenerate = () => {
    setShowPreview(false)
    setGeneratedContent('')
    setSuggestions([])
    handleGenerate()
  }

  /**
   * 应用建议
   */
  const handleApplySuggestion = (suggestion: string) => {
    onChange(suggestion)
    setSuggestions(prev => prev.filter(s => s !== suggestion))
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* 主输入区域 */}
      <div className="relative">
        <div className="flex items-center justify-between mb-2">
          {label && (
            <label className="text-sm font-medium text-foreground">
              {label}
            </label>
          )}
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleGenerate}
            disabled={disabled || isGenerating}
            className="h-7 px-2 text-xs"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-3 h-3 animate-spin mr-1" />
                生成中...
              </>
            ) : (
              <>
                <Sparkles className="w-3 h-3 mr-1" />
                AI生成
              </>
            )}
          </Button>
        </div>

        <Textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          rows={Math.min(maxRows, 4)}
          className="resize-none"
        />
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="text-sm text-destructive bg-destructive/10 p-2 rounded-md">
          {error}
        </div>
      )}

      {/* 生成内容预览 */}
      {showPreview && generatedContent && (
        <Card className="border-primary/20">
          <CardContent className="p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Sparkles className="w-4 h-4 text-primary mr-1" />
                <span className="text-sm font-medium">AI生成的内容</span>
              </div>
              <div className="flex items-center space-x-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleRegenerate}
                  disabled={isGenerating}
                  className="h-6 px-2 text-xs"
                >
                  <RefreshCw className="w-3 h-3" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleAccept}
                  className="h-6 px-2 text-xs text-green-600 hover:text-green-700"
                >
                  <Check className="w-3 h-3" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleReject}
                  className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            </div>
            
            <div className="text-sm text-muted-foreground bg-muted/50 p-2 rounded border">
              {generatedContent}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 建议列表 */}
      {suggestions.length > 0 && showSuggestions && (
        <div className="space-y-2">
          <Separator />
          <div>
            <div className="text-xs text-muted-foreground mb-2">AI建议：</div>
            <div className="flex flex-wrap gap-1">
              {suggestions.map((suggestion, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs cursor-pointer hover:bg-accent transition-colors"
                  onClick={() => handleApplySuggestion(suggestion)}
                >
                  + {suggestion}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AITextGenerator