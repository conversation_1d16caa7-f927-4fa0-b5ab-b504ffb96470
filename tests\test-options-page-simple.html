<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试选项页面</title>
  <style>
    body {
      font-family: system-ui, -apple-system, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .loading {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    #error-details {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      margin-top: 10px;
      font-family: monospace;
      font-size: 12px;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>选项页面加载测试</h1>
    <div id="status" class="status loading">正在加载选项页面...</div>
    <div id="error-details" style="display: none;"></div>
    
    <div id="options-root"></div>
  </div>

  <script>
    // 错误捕获
    window.addEventListener('error', function(event) {
      console.error('全局错误:', event.error)
      document.getElementById('status').className = 'status error'
      document.getElementById('status').textContent = '页面加载失败: ' + event.error.message
      
      const errorDetails = document.getElementById('error-details')
      errorDetails.style.display = 'block'
      errorDetails.textContent = `错误信息: ${event.error.message}\n\n堆栈跟踪:\n${event.error.stack}`
    })

    window.addEventListener('unhandledrejection', function(event) {
      console.error('未处理的Promise拒绝:', event.reason)
      document.getElementById('status').className = 'status error'
      document.getElementById('status').textContent = '异步操作失败: ' + event.reason
    })

    // 模拟Chrome扩展API
    if (!window.chrome) {
      window.chrome = {
        runtime: {
          sendMessage: function(message) {
            return Promise.resolve({
              success: true,
              data: []
            })
          }
        }
      }
    }

    // 尝试加载选项页面脚本
    try {
      const script = document.createElement('script')
      script.type = 'module'
      script.src = './dist/assets/options-12c55491.js'
      script.onload = function() {
        console.log('选项页面脚本加载成功')
        document.getElementById('status').className = 'status success'
        document.getElementById('status').textContent = '选项页面加载成功！'
      }
      script.onerror = function(error) {
        console.error('脚本加载失败:', error)
        document.getElementById('status').className = 'status error'
        document.getElementById('status').textContent = '脚本加载失败'
      }
      document.head.appendChild(script)
    } catch (error) {
      console.error('创建脚本标签失败:', error)
      document.getElementById('status').className = 'status error'
      document.getElementById('status').textContent = '无法创建脚本标签: ' + error.message
    }
  </script>
</body>
</html>