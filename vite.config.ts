import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { resolve } from 'path'
import { copyFileSync, mkdirSync, existsSync } from 'fs'

// 构建配置常量
const BUILD_CONFIG = {
  // 输出目录
  OUTPUT_DIR: 'dist',
  
  // 路径常量
  PATHS: {
    SRC: './src',
    POPUP: 'src/popup/index.html',
    OPTIONS: 'src/options/index.html',
    BACKGROUND: 'src/background/index.ts',
    CONTENT: 'src/content/index.ts',
    CONTENT_STYLE: 'src/content/style.css',
    PUBLIC_ICONS: 'public/icons',
    DIST_ICONS: 'dist/icons',
    MANIFEST: 'manifest.json',
    DIST_MANIFEST: 'dist/manifest.json'
  },
  
  // 输出文件名映射
  OUTPUT_PATTERNS: {
    BACKGROUND_JS: 'src/background/index.js',
    CONTENT_JS: 'src/content/index.js',
    CONTENT_CSS: 'src/content/style.css',
    DEFAULT_JS: 'assets/[name]-[hash].js',
    DEFAULT_CHUNK: 'assets/[name]-[hash].js',
    DEFAULT_ASSET: 'assets/[name]-[hash].[ext]'
  },
  
  // 特殊文件标识
  SPECIAL_FILES: {
    BACKGROUND: 'background',
    CONTENT: 'content',
    STYLE_CSS: 'style.css',
    CONTENT_STYLE: 'contentStyle'
  },
  
  // 图标尺寸配置
  ICON_SIZES: [16, 32, 48, 128]
} as const

/**
 * 解析项目路径
 * @param relativePath 相对路径
 * @returns 绝对路径
 */
const resolvePath = (relativePath: string): string => {
  return resolve(__dirname, relativePath)
}

/**
 * 确保目录存在
 * @param dirPath 目录路径
 */
const ensureDirectoryExists = (dirPath: string): void => {
  if (!existsSync(dirPath)) {
    mkdirSync(dirPath, { recursive: true })
  }
}

/**
 * 安全复制文件
 * @param src 源文件路径
 * @param dest 目标文件路径
 * @param description 文件描述
 */
const safeCopyFile = (src: string, dest: string, description: string): void => {
  if (existsSync(src)) {
    copyFileSync(src, dest)
    console.log(`已复制${description}: ${src} -> ${dest}`)
  } else {
    console.warn(`源文件不存在: ${src}`)
  }
}

/**
 * 复制图标文件的插件
 * 负责在构建完成后复制必要的静态资源
 */
const copyIconsPlugin = () => {
  return {
    name: 'copy-icons',
    writeBundle() {
      const { PATHS, ICON_SIZES } = BUILD_CONFIG
      
      // 确保图标目标目录存在
      ensureDirectoryExists(PATHS.DIST_ICONS)
      
      // 复制所有尺寸的图标文件
      ICON_SIZES.forEach(size => {
        const src = `${PATHS.PUBLIC_ICONS}/icon-${size}.png`
        const dest = `${PATHS.DIST_ICONS}/icon-${size}.png`
        safeCopyFile(src, dest, `图标 (${size}x${size})`)
      })
      
      // 复制manifest.json
      safeCopyFile(PATHS.MANIFEST, PATHS.DIST_MANIFEST, 'manifest.json')
    }
  }
}

/**
 * 创建输入配置对象
 * @returns 输入文件配置
 */
const createInputConfig = () => {
  const { PATHS } = BUILD_CONFIG
  
  return {
    popup: resolvePath(PATHS.POPUP),
    options: resolvePath(PATHS.OPTIONS),
    background: resolvePath(PATHS.BACKGROUND),
    content: resolvePath(PATHS.CONTENT),
    contentStyle: resolvePath(PATHS.CONTENT_STYLE)
  }
}

/**
 * 生成入口文件名
 * @param chunk Rollup chunk 信息
 * @returns 文件名
 */
const generateEntryFileName = (chunk: { name: string }): string => {
  const { name } = chunk
  const { SPECIAL_FILES, OUTPUT_PATTERNS } = BUILD_CONFIG
  
  // 使用映射表处理特殊文件
  const fileNameMap: Record<string, string> = {
    [SPECIAL_FILES.BACKGROUND]: OUTPUT_PATTERNS.BACKGROUND_JS,
    [SPECIAL_FILES.CONTENT]: OUTPUT_PATTERNS.CONTENT_JS
  }
  
  return fileNameMap[name] || OUTPUT_PATTERNS.DEFAULT_JS
}

/**
 * 生成资源文件名
 * @param assetInfo 资源信息
 * @returns 文件名
 */
const generateAssetFileName = (assetInfo: { name?: string }): string => {
  const { name } = assetInfo
  const { SPECIAL_FILES, OUTPUT_PATTERNS } = BUILD_CONFIG
  
  // 检查是否为内容样式文件
  const isContentStyle = name === SPECIAL_FILES.STYLE_CSS || 
                        name?.includes(SPECIAL_FILES.CONTENT_STYLE)
  
  return isContentStyle ? OUTPUT_PATTERNS.CONTENT_CSS : OUTPUT_PATTERNS.DEFAULT_ASSET
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), copyIconsPlugin()],
  
  resolve: {
    alias: {
      '@': resolvePath(BUILD_CONFIG.PATHS.SRC),
    },
  },
  
  build: {
    outDir: BUILD_CONFIG.OUTPUT_DIR,
    rollupOptions: {
      input: createInputConfig(),
      output: {
        entryFileNames: generateEntryFileName,
        chunkFileNames: BUILD_CONFIG.OUTPUT_PATTERNS.DEFAULT_CHUNK,
        assetFileNames: generateAssetFileName
      }
    }
  }
})