// useTagColors Hook 单元测试

const { describe, it, expect, beforeEach, afterEach } = require('./test-framework')

// 模拟 React hooks
let mockState = {}
let mockSetState = {}
let mockEffectCallbacks = []

const mockUseState = (initialValue) => {
  const key = Math.random().toString()
  mockState[key] = initialValue
  const setState = (newValue) => {
    mockState[key] = typeof newValue === 'function' ? newValue(mockState[key]) : newValue
  }
  mockSetState[key] = setState
  return [mockState[key], setState]
}

const mockUseEffect = (callback, deps) => {
  mockEffectCallbacks.push({ callback, deps })
  // 立即执行 effect
  callback()
}

const mockUseCallback = (callback, deps) => {
  return callback
}

// 模拟 tagService
const mockTagService = {
  getTags: async () => [
    { id: '1', name: '技术', color: '#3B82F6' },
    { id: '2', name: '学习', color: '#10B981' },
    { id: '3', name: '工作', color: '#F59E0B' },
    { id: '4', name: '生活' } // 没有颜色，应该使用默认颜色
  ]
}

// 模拟模块
global.React = {
  useState: mockUseState,
  useEffect: mockUseEffect,
  useCallback: mockUseCallback
}

// 重置模拟状态
const resetMocks = () => {
  mockState = {}
  mockSetState = {}
  mockEffectCallbacks = []
}

describe('useTagColors Hook 测试', () => {
  beforeEach(() => {
    resetMocks()
  })

  afterEach(() => {
    resetMocks()
  })

  it('应该正确初始化状态', async () => {
    // 模拟 useTagColors 的核心逻辑
    const tagColors = {}
    const loading = true
    
    // 模拟加载标签颜色
    const tags = await mockTagService.getTags()
    const colorMap = {}
    tags.forEach(tag => {
      colorMap[tag.name] = tag.color || '#6B7280'
    })
    
    expect(colorMap).toEqual({
      '技术': '#3B82F6',
      '学习': '#10B981', 
      '工作': '#F59E0B',
      '生活': '#6B7280' // 默认颜色
    })
  })

  it('应该正确获取标签颜色', async () => {
    const tags = await mockTagService.getTags()
    const colorMap = {}
    tags.forEach(tag => {
      colorMap[tag.name] = tag.color || '#6B7280'
    })
    
    // 模拟 getTagColor 函数
    const getTagColor = (tagName) => {
      return colorMap[tagName] || '#6B7280'
    }
    
    expect(getTagColor('技术')).toBe('#3B82F6')
    expect(getTagColor('学习')).toBe('#10B981')
    expect(getTagColor('工作')).toBe('#F59E0B')
    expect(getTagColor('生活')).toBe('#6B7280')
    expect(getTagColor('不存在的标签')).toBe('#6B7280') // 默认颜色
  })

  it('应该处理空标签列表', async () => {
    // 模拟空标签列表
    const mockEmptyTagService = {
      getTags: async () => []
    }
    
    const tags = await mockEmptyTagService.getTags()
    const colorMap = {}
    tags.forEach(tag => {
      colorMap[tag.name] = tag.color || '#6B7280'
    })
    
    expect(colorMap).toEqual({})
    
    const getTagColor = (tagName) => {
      return colorMap[tagName] || '#6B7280'
    }
    
    expect(getTagColor('任何标签')).toBe('#6B7280')
  })

  it('应该处理 tagService 错误', async () => {
    // 模拟 tagService 抛出错误
    const mockErrorTagService = {
      getTags: async () => {
        throw new Error('获取标签失败')
      }
    }
    
    let colorMap = {}
    let error = null
    
    try {
      const tags = await mockErrorTagService.getTags()
      tags.forEach(tag => {
        colorMap[tag.name] = tag.color || '#6B7280'
      })
    } catch (e) {
      error = e
      colorMap = {} // 错误时设置为空对象
    }
    
    expect(error).toBeTruthy()
    expect(error.message).toBe('获取标签失败')
    expect(colorMap).toEqual({})
    
    const getTagColor = (tagName) => {
      return colorMap[tagName] || '#6B7280'
    }
    
    expect(getTagColor('任何标签')).toBe('#6B7280')
  })

  it('应该正确处理标签颜色映射接口', () => {
    // 测试 TagColorMap 接口的使用
    const tagColorMap = {
      '技术': '#3B82F6',
      '学习': '#10B981',
      '工作': '#F59E0B'
    }
    
    expect(typeof tagColorMap).toBe('object')
    expect(tagColorMap['技术']).toBe('#3B82F6')
    expect(tagColorMap['学习']).toBe('#10B981')
    expect(tagColorMap['工作']).toBe('#F59E0B')
  })

  it('应该验证 Hook 返回值结构', () => {
    // 模拟 Hook 返回值
    const hookReturn = {
      tagColors: {
        '技术': '#3B82F6',
        '学习': '#10B981'
      },
      loading: false,
      getTagColor: (tagName) => '#6B7280',
      refreshTagColors: async () => {}
    }
    
    expect(hookReturn).toHaveProperty('tagColors')
    expect(hookReturn).toHaveProperty('loading')
    expect(hookReturn).toHaveProperty('getTagColor')
    expect(hookReturn).toHaveProperty('refreshTagColors')
    
    expect(typeof hookReturn.tagColors).toBe('object')
    expect(typeof hookReturn.loading).toBe('boolean')
    expect(typeof hookReturn.getTagColor).toBe('function')
    expect(typeof hookReturn.refreshTagColors).toBe('function')
  })
})