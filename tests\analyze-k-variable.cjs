// 分析变量K的使用情况

const fs = require('fs')
const path = require('path')

const distPath = path.join(__dirname, 'dist')
const optionsJsPath = path.join(distPath, 'assets')

if (fs.existsSync(optionsJsPath)) {
  const files = fs.readdirSync(optionsJsPath)
  const optionsFile = files.find(file => file.startsWith('options-') && file.endsWith('.js'))
  
  if (optionsFile) {
    console.log('分析文件:', optionsFile)
    
    const filePath = path.join(optionsJsPath, optionsFile)
    const content = fs.readFileSync(filePath, 'utf8')
    
    // 查找所有K的使用
    const kPattern = /(.{0,100})\bK\b(.{0,100})/g
    let match
    let index = 1
    
    console.log('\n=== 变量K的所有使用情况 ===')
    
    while ((match = kPattern.exec(content)) !== null) {
      const before = match[1]
      const after = match[2]
      const fullContext = before + 'K' + after
      
      console.log(`\n${index}. 位置 ${match.index}:`)
      console.log(`   上下文: ${fullContext.replace(/\s+/g, ' ').trim()}`)
      
      // 检查是否是定义
      if (before.includes('=') && !before.includes('==') && !before.includes('!=')) {
        console.log('   类型: 可能是定义')
      } else {
        console.log('   类型: 使用')
      }
      
      index++
    }
    
    // 查找可能的定义模式
    console.log('\n=== 查找变量K的定义模式 ===')
    const definitionPatterns = [
      /const\s+K\s*=/g,
      /let\s+K\s*=/g,
      /var\s+K\s*=/g,
      /\bK\s*=/g
    ]
    
    definitionPatterns.forEach((pattern, i) => {
      const matches = content.match(pattern)
      if (matches) {
        console.log(`模式 ${i + 1} (${pattern.source}): 找到 ${matches.length} 个匹配`)
      }
    })
    
    // 查找K在函数参数中的使用
    console.log('\n=== 查找K在函数参数中的使用 ===')
    const functionParamPattern = /function[^(]*\([^)]*\bK\b[^)]*\)/g
    const arrowFunctionPattern = /\([^)]*\bK\b[^)]*\)\s*=>/g
    
    let funcMatches = content.match(functionParamPattern)
    if (funcMatches) {
      console.log('在函数参数中找到K:', funcMatches.length, '次')
      funcMatches.forEach((match, i) => {
        console.log(`  ${i + 1}: ${match}`)
      })
    }
    
    funcMatches = content.match(arrowFunctionPattern)
    if (funcMatches) {
      console.log('在箭头函数参数中找到K:', funcMatches.length, '次')
      funcMatches.forEach((match, i) => {
        console.log(`  ${i + 1}: ${match}`)
      })
    }
    
  } else {
    console.log('没有找到options JavaScript文件')
  }
} else {
  console.log('dist/assets目录不存在')
}