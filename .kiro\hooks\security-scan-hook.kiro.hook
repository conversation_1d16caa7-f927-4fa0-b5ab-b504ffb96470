{"enabled": true, "name": "Security Vulnerability Scanner", "description": "Automatically reviews changed files for potential security issues including API keys, credentials, tokens, and other sensitive data exposure", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.js", "**/*.ts", "**/*.py", "**/*.java", "**/*.cs", "**/*.php", "**/*.rb", "**/*.go", "**/*.cpp", "**/*.c", "**/*.h", "**/*.json", "**/*.xml", "**/*.yml", "**/*.yaml", "**/*.env", "**/*.config", "**/*.properties", "**/*.ini", "**/*.conf"]}, "then": {"type": "askAgent", "prompt": "Please review the changed files for potential security issues. Specifically look for:\n\n1. API keys, tokens, or credentials in source code\n2. Private keys or sensitive credentials  \n3. Encryption keys or certificates\n4. Authentication tokens or session IDs\n5. Passwords or secrets in configuration files\n6. IP addresses containing sensitive data\n7. Hardcoded internal URLs\n8. Database connection credentials\n\nFor each security issue found:\n1. Highlight the specific security risk and line number\n2. Suggest a secure alternative approach (environment variables, key management systems, etc.)\n3. Recommend security best practices for that type of credential\n\nFocus on practical, actionable security recommendations."}}