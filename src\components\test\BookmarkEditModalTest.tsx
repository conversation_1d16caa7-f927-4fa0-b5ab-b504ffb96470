// BookmarkEditModal shadcn重构测试页面

import React, { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import BookmarkEditModal from '../BookmarkEditModal'
import { useTagColors } from '../../hooks/useTagColors'

/**
 * BookmarkEditModal shadcn重构测试页面
 * 用于验证重构后的BookmarkEditModal组件是否正常工作
 */
const BookmarkEditModalTest: React.FC = () => {
  const { getTagColor } = useTagColors()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [currentBookmark, setCurrentBookmark] = useState(0)

  // 测试用的收藏数据
  const testBookmarks = [
    {
      id: '1',
      title: 'React官方文档',
      url: 'https://react.dev',
      description: 'React官方文档，学习React的最佳资源。包含了完整的API参考、教程和最佳实践指南。',
      category: '学习',
      tags: ['React', '前端', '文档', 'JavaScript', 'TypeScript']
    },
    {
      id: '2',
      title: 'shadcn/ui组件库',
      url: 'https://ui.shadcn.com',
      description: 'Beautiful and accessible React components built with Radix UI and Tailwind CSS.',
      category: '工具',
      tags: ['shadcn', 'UI组件', 'React', 'Tailwind CSS', 'Radix UI']
    },
    {
      id: '3',
      title: 'GitHub',
      url: 'https://github.com',
      description: '',
      category: '工作',
      tags: ['Git', '代码托管', '开源']
    },
    {
      id: '4',
      title: '长标题测试：这是一个非常长的标题用来测试组件在处理长文本时的表现如何，包括换行和截断等功能',
      url: 'https://example-very-long-url.com/path/to/resource?param1=value1&param2=value2&param3=value3',
      description: '这是一个用于测试长文本处理的收藏项。描述内容也比较长，用来验证组件在处理大量文本时的表现。包括文本换行、滚动、以及各种边界情况的处理。这个描述会继续变得更长，以便我们能够充分测试组件的文本处理能力。',
      category: '其他',
      tags: ['测试', '长文本', 'UI测试', '边界情况', '用户体验', '文本处理', '组件测试']
    },
    {
      id: '5',
      title: '空标签测试',
      url: 'https://empty-tags-test.com',
      description: '这个收藏项没有标签，用来测试空标签的处理',
      category: '默认分类',
      tags: []
    }
  ]

  // 处理保存操作
  const handleSave = async (updatedBookmark: any) => {
    setLoading(true)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    console.log('保存收藏:', updatedBookmark)
    
    // 模拟成功/失败
    const success = Math.random() > 0.2 // 80%成功率
    
    if (success) {
      alert(`✅ 收藏保存成功！\n\n更新内容：\n标题: ${updatedBookmark.title}\nURL: ${updatedBookmark.url}\n分类: ${updatedBookmark.category}\n标签: ${updatedBookmark.tags?.join(', ') || '无'}`)
    } else {
      alert('❌ 保存失败，请重试！')
    }
    
    setLoading(false)
    if (success) {
      setIsModalOpen(false)
    }
  }

  // 处理取消操作
  const handleCancel = () => {
    setIsModalOpen(false)
  }

  // 打开编辑模态窗口
  const openEditModal = (index: number) => {
    setCurrentBookmark(index)
    setIsModalOpen(true)
  }

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            📝 BookmarkEditModal shadcn重构测试
          </CardTitle>
          <CardDescription>
            测试重构后的BookmarkEditModal组件，验证shadcn/ui组件集成效果
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">测试功能</h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>✅ shadcn Dialog组件集成</li>
                <li>✅ shadcn Form组件和react-hook-form</li>
                <li>✅ shadcn Input/Textarea组件</li>
                <li>✅ shadcn Select组件</li>
                <li>✅ shadcn Button组件</li>
                <li>✅ shadcn Badge组件（标签显示）</li>
                <li>✅ 表单验证和错误处理</li>
                <li>✅ 加载状态处理</li>
                <li>✅ 标签添加和删除功能</li>
                <li>✅ 响应式布局</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">测试场景</h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>🔸 正常收藏项编辑</li>
                <li>🔸 长标题和长URL处理</li>
                <li>🔸 长描述文本处理</li>
                <li>🔸 多标签管理</li>
                <li>🔸 空标签处理</li>
                <li>🔸 表单验证测试</li>
                <li>🔸 加载状态测试</li>
                <li>🔸 取消操作测试</li>
                <li>🔸 不同分类选择</li>
                <li>🔸 错误处理测试</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>测试收藏项列表</CardTitle>
          <CardDescription>
            点击"编辑"按钮测试不同场景下的BookmarkEditModal组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testBookmarks.map((bookmark, index) => (
              <div key={bookmark.id} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    <h4 className="font-semibold text-lg line-clamp-2">{bookmark.title}</h4>
                    <p className="text-sm text-blue-600 break-all">{bookmark.url}</p>
                    {bookmark.description && (
                      <p className="text-sm text-gray-600 line-clamp-3">{bookmark.description}</p>
                    )}
                    <div className="flex items-center gap-2 flex-wrap">
                      <Badge variant="outline">{bookmark.category}</Badge>
                      {bookmark.tags.map((tag, tagIndex) => (
                        <Badge 
                          key={tagIndex} 
                          variant="outline" 
                          className="text-xs border"
                          style={{ 
                            borderColor: getTagColor(tag),
                            backgroundColor: `${getTagColor(tag)}15`,
                            color: getTagColor(tag)
                          }}
                        >
                          {tag}
                        </Badge>
                      ))}
                      {bookmark.tags.length === 0 && (
                        <span className="text-xs text-gray-400">无标签</span>
                      )}
                    </div>
                  </div>
                  <Button 
                    onClick={() => openEditModal(index)}
                    className="ml-4 flex-shrink-0"
                  >
                    📝 编辑
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">如何测试：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
              <li>点击任意收藏项的"编辑"按钮打开编辑模态窗口</li>
              <li>验证模态窗口是否正确显示，样式是否符合shadcn设计规范</li>
              <li>测试各个表单字段的输入和验证功能</li>
              <li>测试标签的添加和删除功能</li>
              <li>测试分类选择器的功能</li>
              <li>测试表单验证（尝试清空必填字段）</li>
              <li>测试保存功能（会有2秒加载状态）</li>
              <li>测试取消功能</li>
            </ol>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">预期效果：</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>模态窗口应该有shadcn标准的外观和动画效果</li>
              <li>表单字段应该有shadcn标准的样式和交互效果</li>
              <li>验证错误应该正确显示在对应字段下方</li>
              <li>标签应该以Badge形式显示，支持删除操作</li>
              <li>保存时应该显示加载状态，按钮变为禁用状态</li>
              <li>所有交互应该流畅，无明显的样式问题</li>
            </ul>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">⚠️ 注意事项：</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-yellow-700">
              <li>保存操作有20%的概率模拟失败，用于测试错误处理</li>
              <li>保存成功后会显示详细的更新内容</li>
              <li>长文本收藏项用于测试组件的文本处理能力</li>
              <li>空标签收藏项用于测试边界情况处理</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* BookmarkEditModal组件 */}
      <BookmarkEditModal
        bookmark={testBookmarks[currentBookmark]}
        isOpen={isModalOpen}
        onSave={handleSave}
        onCancel={handleCancel}
        loading={loading}
      />
    </div>
  )
}

export default BookmarkEditModalTest