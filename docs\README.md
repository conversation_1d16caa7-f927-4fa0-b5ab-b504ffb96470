# Universe Bag 技术文档

本目录包含Universe Bag项目的完整技术文档。

## 📚 文档导航

### 🚀 快速开始
- **[开发者指南](DEVELOPER_GUIDE.md)** - 完整的开发环境设置和技术文档
- **[AI模型管理优化](ai-model-management-optimization.md)** - v2.0.0版本的核心功能优化文档
- **[发布说明 v2.0.0](release-notes-v2.0.0.md)** - 最新版本的详细发布说明

### 🔧 API文档
- **[Background Service Worker API](background-service-worker-api.md)** - 后台服务API文档
- **[BookmarkService API](bookmark-service-api.md)** - 收藏服务API文档
- **[Popup组件API](popup-components-api.md)** - 弹出窗口组件API文档
- **[性能监控工具API](performance-utils-api.md)** - 性能监控工具API文档

### 🤖 AI集成文档
- **[AI集成服务测试脚本API](test-ai-integration-script-api.md)** - AI集成测试API文档
- **[AI集成服务测试使用示例](test-ai-integration-usage-examples.md)** - AI集成测试使用示例
- **[AI集成服务测试函数签名](test-ai-integration-function-signatures.md)** - AI集成测试函数签名
- **[AI对话服务API](aiChatService-api.md)** - AI对话服务API文档
- **[AI对话服务使用示例](aiChatService-usage-examples.md)** - AI对话服务使用示例
- **[AI类型定义API](ai-types-api.md)** - AI类型定义API文档
- **[AI提供商服务OpenAI连接API](aiProviderService-openai-connection-api.md)** - OpenAI连接增强功能API文档
- **[AI提供商服务OpenRouter API](aiProviderService-openrouter-api.md)** - OpenRouter API详细文档
- **[AI提供商服务函数签名](aiProviderService-function-signatures.md)** - AI提供商服务函数签名文档
- **[AI提供商服务使用示例](aiProviderService-usage-examples.md)** - AI提供商服务使用示例

### 🧪 测试文档
- **[测试框架API](test-build-test-api.md)** - 测试框架API文档
- **[shadcn集成测试API](integration-test-shadcn-api.md)** - shadcn集成测试API文档
- **[本地AI服务测试脚本API](test-local-ai-services-script-api.md)** - 本地AI服务测试API文档
- **[本地AI服务测试使用示例](test-local-ai-services-usage-examples.md)** - 本地AI服务测试使用示例
- **[本地AI服务测试函数签名](test-local-ai-services-function-signatures.md)** - 本地AI服务测试函数签名

### 📋 项目管理文档
- **[文档总结和导航](documentation-summary.md)** - 完整的文档总结
- **[AI集成任务1完成总结](ai-integration-task1-completion-summary.md)** - AI集成任务完成总结
- **[任务完成总结](task-1-completion-summary.md)** - 任务完成总结
- **[DetailedBookmarkForm shadcn重构](task-13-detailedbookmarkform-shadcn-refactor.md)** - 组件重构文档
- **[任务13最终验证报告](task-13-final-verification-report.md)** - 最终验证报告

## 📖 文档使用指南

### 对于新开发者
1. 首先阅读 **[开发者指南](DEVELOPER_GUIDE.md)** 了解项目结构和开发环境
2. 查看 **[BookmarkService API](bookmark-service-api.md)** 了解核心业务逻辑
3. 参考 **[测试框架API](test-build-test-api.md)** 学习如何编写和运行测试

### 对于AI功能开发
1. 阅读 **[AI集成服务测试脚本API](test-ai-integration-script-api.md)** 了解AI集成架构
2. 查看 **[AI提供商服务使用示例](aiProviderService-usage-examples.md)** 学习如何集成新的AI提供商
3. 参考 **[AI对话服务API](aiChatService-api.md)** 了解AI对话功能实现

### 对于UI组件开发
1. 查看 **[Popup组件API](popup-components-api.md)** 了解组件架构
2. 参考 **[shadcn集成测试API](integration-test-shadcn-api.md)** 学习组件测试方法
3. 阅读 **[DetailedBookmarkForm shadcn重构](task-13-detailedbookmarkform-shadcn-refactor.md)** 了解组件重构最佳实践

### 对于性能优化
1. 阅读 **[性能监控工具API](performance-utils-api.md)** 了解性能监控机制
2. 查看开发者指南中的性能优化章节
3. 参考测试文档了解性能测试方法

## 🔄 文档更新

文档会随着项目的发展持续更新。如果您发现文档中的错误或需要补充的内容，请：

1. 在GitHub上提交Issue
2. 直接提交Pull Request修改文档
3. 联系项目维护者

## 📞 获取帮助

如果您在阅读文档过程中遇到问题：

- **技术问题**：查看 [开发者指南](DEVELOPER_GUIDE.md) 或在GitHub上提交Issue
- **API使用**：参考对应的API文档和使用示例
- **测试相关**：查看测试文档或运行相关测试脚本
- **AI功能**：参考AI集成相关文档

---

*最后更新：2025年1月20日*
