// BookmarkEditModal 修复测试 - 验证Sparkles图标导入

import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import BookmarkEditModal from '../src/components/BookmarkEditModal'

// Mock chrome API
global.chrome = {
  runtime: {
    sendMessage: vi.fn()
  }
} as any

describe('BookmarkEditModal 修复测试', () => {
  const mockBookmark = {
    id: '1',
    title: '测试收藏',
    url: 'https://example.com',
    description: '测试描述',
    category: '默认分类',
    tags: ['测试']
  }

  const mockProps = {
    bookmark: mockBookmark,
    isOpen: true,
    onSave: vi.fn(),
    onCancel: vi.fn(),
    loading: false
  }

  it('应该正确渲染而不出现Sparkles图标错误', () => {
    // 这个测试主要验证组件能够正常渲染，不会因为Sparkles图标导入问题而报错
    expect(() => {
      render(<BookmarkEditModal {...mockProps} />)
    }).not.toThrow()
  })

  it('应该显示智能推荐按钮', () => {
    render(<BookmarkEditModal {...mockProps} />)
    
    // 查找智能推荐按钮
    const smartRecommendButton = screen.getByText('智能推荐')
    expect(smartRecommendButton).toBeDefined()
  })

  it('应该在没有内容时禁用智能推荐按钮', () => {
    const emptyBookmark = {
      ...mockBookmark,
      title: '',
      description: '',
      url: ''
    }
    
    render(<BookmarkEditModal {...mockProps} bookmark={emptyBookmark} />)
    
    const smartRecommendButton = screen.getByText('智能推荐')
    expect(smartRecommendButton.getAttribute('disabled')).toBe('')
  })
})