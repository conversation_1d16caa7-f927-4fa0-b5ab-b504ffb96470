// 收藏状态管理服务 - 负责检测和更新收藏状态

import { bookmarkService } from './bookmarkService'

/**
 * 收藏状态管理服务类
 * 提供收藏状态的检测、更新和同步功能
 */
export class BookmarkStatusService {
  private statusCache = new Map<string, { isBookmarked: boolean; bookmarkId?: string; timestamp: number }>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  /**
   * 检查URL的收藏状态
   * @param url URL地址
   * @param useCache 是否使用缓存
   * @returns Promise<{isBookmarked: boolean, bookmarkId?: string}>
   */
  async checkBookmarkStatus(
    url: string, 
    useCache: boolean = true
  ): Promise<{isBookmarked: boolean, bookmarkId?: string}> {
    try {
      const normalizedUrl = this.normalizeUrl(url)
      
      // 检查缓存
      if (useCache) {
        const cached = this.statusCache.get(normalizedUrl)
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
          return { isBookmarked: cached.isBookmarked, bookmarkId: cached.bookmarkId }
        }
      }

      // 从数据库查询
      const status = await bookmarkService.checkBookmarkStatus(url)
      
      // 更新缓存
      this.statusCache.set(normalizedUrl, {
        ...status,
        timestamp: Date.now()
      })

      return status
    } catch (error) {
      console.error('检查收藏状态失败:', error)
      return { isBookmarked: false }
    }
  }

  /**
   * 监听标签页更新事件（已移除，由tabStatusManager统一处理）
   * 保留此方法以维持向后兼容性，但实际不执行任何操作
   */
  startTabUpdateListener(): void {
    console.log('标签页更新监听器已启动（由tabStatusManager统一处理）')
  }

  /**
   * 清除URL的状态缓存
   * @param url URL地址
   */
  clearStatusCache(url: string): void {
    const normalizedUrl = this.normalizeUrl(url)
    this.statusCache.delete(normalizedUrl)
  }

  /**
   * 清除所有状态缓存
   */
  clearAllStatusCache(): void {
    this.statusCache.clear()
  }

  /**
   * 当收藏状态发生变化时更新缓存
   * @param url URL地址
   * @param isBookmarked 是否已收藏
   * @param bookmarkId 收藏ID
   */
  updateStatusCache(url: string, isBookmarked: boolean, bookmarkId?: string): void {
    const normalizedUrl = this.normalizeUrl(url)
    this.statusCache.set(normalizedUrl, {
      isBookmarked,
      bookmarkId,
      timestamp: Date.now()
    })
  }

  /**
   * 获取当前活动标签页的收藏状态
   * @returns Promise<{tabId: number, url: string, isBookmarked: boolean, bookmarkId?: string} | null>
   */
  async getCurrentTabBookmarkStatus(): Promise<{
    tabId: number
    url: string
    isBookmarked: boolean
    bookmarkId?: string
  } | null> {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tabs.length === 0 || !tabs[0].id || !tabs[0].url) {
        return null
      }

      const tab = tabs[0]
      const status = await this.checkBookmarkStatus(tab.url)

      return {
        tabId: tab.id,
        url: tab.url,
        ...status
      }
    } catch (error) {
      console.error('获取当前标签页收藏状态失败:', error)
      return null
    }
  }

  /**
   * 监听收藏变化事件
   */
  startBookmarkChangeListener(): void {
    // 这里可以监听数据库变化事件
    // 当收藏被添加、删除或修改时，更新相关的状态缓存
    console.log('收藏变化监听器已启动')
  }

  /**
   * 处理收藏添加事件
   * @param url 收藏的URL
   * @param bookmarkId 收藏ID
   */
  async handleBookmarkAdded(url: string, bookmarkId: string): Promise<void> {
    try {
      // 更新缓存
      this.updateStatusCache(url, true, bookmarkId)
      console.log(`收藏添加事件已处理: ${url}`)
    } catch (error) {
      console.error('处理收藏添加事件失败:', error)
    }
  }

  /**
   * 处理收藏删除事件
   * @param url 删除的收藏URL
   */
  async handleBookmarkRemoved(url: string): Promise<void> {
    try {
      // 更新缓存
      this.updateStatusCache(url, false)
      console.log(`收藏删除事件已处理: ${url}`)
    } catch (error) {
      console.error('处理收藏删除事件失败:', error)
    }
  }



  /**
   * 标准化URL
   * @param url 原始URL
   * @returns 标准化后的URL
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      // 移除查询参数和片段，统一协议
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`.toLowerCase()
    } catch {
      return url.toLowerCase()
    }
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  getCacheStats(): {
    size: number
    hitRate: number
    oldestEntry: number
  } {
    const now = Date.now()
    let oldestTimestamp = now
    
    for (const entry of this.statusCache.values()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp
      }
    }

    return {
      size: this.statusCache.size,
      hitRate: 0, // 需要实际统计命中率
      oldestEntry: now - oldestTimestamp
    }
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.statusCache.entries()) {
      if (now - entry.timestamp > this.CACHE_DURATION) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.statusCache.delete(key))
    
    if (expiredKeys.length > 0) {
      console.log(`清理了 ${expiredKeys.length} 个过期缓存项`)
    }
  }

  /**
   * 启动定期清理任务
   */
  startPeriodicCleanup(): void {
    // 每10分钟清理一次过期缓存
    setInterval(() => {
      this.cleanExpiredCache()
    }, 10 * 60 * 1000)

    console.log('定期清理任务已启动')
  }
}

// 导出单例实例
export const bookmarkStatusService = new BookmarkStatusService()