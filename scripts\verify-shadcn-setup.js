#!/usr/bin/env node

/**
 * shadcn/ui 配置验证脚本
 * 验证shadcn/ui的安装和配置是否正确
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

console.log('🔍 验证 shadcn/ui 配置...\n');

let hasErrors = false;

// 检查必要的文件是否存在
const requiredFiles = [
  'components.json',
  'src/lib/utils.ts',
  'src/styles/globals.css',
  'src/components/ui/button.tsx'
];

console.log('📁 检查必要文件...');
requiredFiles.forEach(file => {
  const filePath = path.join(rootDir, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 缺失`);
    hasErrors = true;
  }
});

// 检查 package.json 中的依赖
console.log('\n📦 检查依赖包...');
const packageJsonPath = path.join(rootDir, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const requiredDeps = [
    'class-variance-authority',
    'clsx',
    'tailwind-merge',
    'tailwindcss-animate',
    '@radix-ui/react-slot'
  ];
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep} - v${packageJson.dependencies[dep]}`);
    } else if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`✅ ${dep} - v${packageJson.devDependencies[dep]} (dev)`);
    } else {
      console.log(`❌ ${dep} - 缺失`);
      hasErrors = true;
    }
  });
} else {
  console.log('❌ package.json 文件不存在');
  hasErrors = true;
}

// 检查 components.json 配置
console.log('\n⚙️ 检查 components.json 配置...');
const componentsJsonPath = path.join(rootDir, 'components.json');
if (fs.existsSync(componentsJsonPath)) {
  try {
    const componentsJson = JSON.parse(fs.readFileSync(componentsJsonPath, 'utf8'));
    
    const requiredConfig = {
      style: 'default',
      tsx: true,
      'tailwind.config': 'tailwind.config.js',
      'tailwind.css': 'src/styles/globals.css',
      'aliases.components': '@/components',
      'aliases.utils': '@/lib/utils'
    };
    
    Object.entries(requiredConfig).forEach(([key, expectedValue]) => {
      const keys = key.split('.');
      let value = componentsJson;
      
      for (const k of keys) {
        value = value?.[k];
      }
      
      if (value === expectedValue) {
        console.log(`✅ ${key}: ${expectedValue}`);
      } else {
        console.log(`❌ ${key}: 期望 "${expectedValue}", 实际 "${value}"`);
        hasErrors = true;
      }
    });
  } catch (error) {
    console.log(`❌ components.json 解析失败: ${error.message}`);
    hasErrors = true;
  }
} else {
  console.log('❌ components.json 文件不存在');
  hasErrors = true;
}

// 检查 tailwind.config.js 中的 shadcn 配置
console.log('\n🎨 检查 Tailwind 配置...');
const tailwindConfigPath = path.join(rootDir, 'tailwind.config.js');
if (fs.existsSync(tailwindConfigPath)) {
  const tailwindConfig = fs.readFileSync(tailwindConfigPath, 'utf8');
  
  const requiredConfigs = [
    'darkMode: ["class"]',
    'tailwindcss-animate',
    '--background',
    '--foreground',
    '--primary',
    '--secondary'
  ];
  
  requiredConfigs.forEach(config => {
    if (tailwindConfig.includes(config)) {
      console.log(`✅ ${config} - 已配置`);
    } else {
      console.log(`❌ ${config} - 缺失`);
      hasErrors = true;
    }
  });
} else {
  console.log('❌ tailwind.config.js 文件不存在');
  hasErrors = true;
}

// 检查 CSS 变量
console.log('\n🎨 检查 CSS 变量...');
const globalsCssPath = path.join(rootDir, 'src/styles/globals.css');
if (fs.existsSync(globalsCssPath)) {
  const globalsCss = fs.readFileSync(globalsCssPath, 'utf8');
  
  const requiredCssVars = [
    '--background',
    '--foreground',
    '--primary',
    '--secondary',
    '--border',
    '--input',
    '--ring'
  ];
  
  requiredCssVars.forEach(cssVar => {
    if (globalsCss.includes(cssVar)) {
      console.log(`✅ ${cssVar} - 已定义`);
    } else {
      console.log(`❌ ${cssVar} - 缺失`);
      hasErrors = true;
    }
  });
} else {
  console.log('❌ src/styles/globals.css 文件不存在');
  hasErrors = true;
}

// 检查 utils.ts 中的 cn 函数
console.log('\n🛠️ 检查工具函数...');
const utilsPath = path.join(rootDir, 'src/lib/utils.ts');
if (fs.existsSync(utilsPath)) {
  const utilsContent = fs.readFileSync(utilsPath, 'utf8');
  
  if (utilsContent.includes('export function cn') && 
      utilsContent.includes('twMerge') && 
      utilsContent.includes('clsx')) {
    console.log('✅ cn 函数 - 正确实现');
  } else {
    console.log('❌ cn 函数 - 实现不正确');
    hasErrors = true;
  }
} else {
  console.log('❌ src/lib/utils.ts 文件不存在');
  hasErrors = true;
}

// 总结
console.log('\n' + '='.repeat(50));
if (hasErrors) {
  console.log('❌ shadcn/ui 配置验证失败！请检查上述错误。');
  process.exit(1);
} else {
  console.log('✅ shadcn/ui 配置验证成功！所有配置都正确。');
  console.log('\n🎉 你现在可以开始使用 shadcn/ui 组件了！');
  console.log('\n📖 使用示例:');
  console.log('   npx shadcn@latest add card');
  console.log('   npx shadcn@latest add input');
  console.log('   npx shadcn@latest add dialog');
  process.exit(0);
}