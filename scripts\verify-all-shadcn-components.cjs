/**
 * 修复版本：综合验证脚本：检查任务5-8重构组件的shadcn集成状态
 * 验证AddBookmarkModal、BookmarkRow、BookmarkCompact、BookmarkEditModal组件
 */

const fs = require('fs')
const path = require('path')

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  magenta: (text) => `\x1b[35m${text}\x1b[0m`
}

console.log(colors.cyan('🔍 任务5-8重构组件shadcn集成验证（修复版本）'))
console.log('=' .repeat(60))

// 检查文件内容
function checkFileContent(filePath, checks) {
  try {
    const content = fs.readFileSync(path.resolve(filePath), 'utf8')
    let allPassed = true
    
    console.log(colors.blue(`\n📄 检查 ${filePath}:`))
    
    checks.forEach(({ name, pattern, required = true, shouldNotExist = false }) => {
      const found = pattern.test(content)
      const passed = shouldNotExist ? !found : found
      const status = passed ? colors.green('✓') : (required ? colors.red('✗') : colors.yellow('⚠'))
      console.log(`  ${status} ${name}`)
      
      if (required && !passed) {
        allPassed = false
      }
    })
    
    return allPassed
  } catch (error) {
    console.log(colors.red(`✗ 无法读取文件: ${filePath}`))
    return false
  }
}

// 检查组件在OptionsApp中的使用
function checkComponentUsage(componentName, filePath) {
  try {
    const content = fs.readFileSync(path.resolve(filePath), 'utf8')
    const importPattern = new RegExp(`import.*${componentName}.*from.*components/${componentName}`)
    const usagePattern = new RegExp(`<${componentName}[\\s\\S]*?>`)
    
    const hasImport = importPattern.test(content)
    const hasUsage = usagePattern.test(content)
    
    console.log(colors.blue(`\n📄 检查 ${componentName} 在 ${filePath} 中的使用:`))
    console.log(`  ${hasImport ? colors.green('✓') : colors.red('✗')} 正确导入`)
    console.log(`  ${hasUsage ? colors.green('✓') : colors.red('✗')} 实际使用`)
    
    return hasImport && hasUsage
  } catch (error) {
    console.log(colors.red(`✗ 无法检查 ${componentName} 的使用情况`))
    return false
  }
}

let allTestsPassed = true

// 组件配置 - 修复了正则表达式模式
const components = [
  {
    name: 'AddBookmarkModal',
    task: '任务5',
    file: 'src/components/AddBookmarkModal.tsx',
    checks: [
      {
        name: '使用shadcn Dialog组件',
        pattern: /from\s+["']@\/components\/ui\/dialog["']/
      },
      {
        name: '使用shadcn Form组件',
        pattern: /from\s+["']@\/components\/ui\/form["']/
      },
      {
        name: '使用shadcn Input组件',
        pattern: /from\s+["']@\/components\/ui\/input["']/
      },
      {
        name: '使用shadcn Button组件',
        pattern: /from\s+["']@\/components\/ui\/button["']/
      },
      {
        name: '使用shadcn Select组件',
        pattern: /from\s+["']@\/components\/ui\/select["']/
      },
      {
        name: '移除了自定义模态样式',
        pattern: /className=["'][^"']*modal[^"']*["']/,
        required: false,
        shouldNotExist: true
      }
    ]
  },
  {
    name: 'BookmarkRow',
    task: '任务6',
    file: 'src/components/BookmarkRow.tsx',
    checks: [
      {
        name: '使用shadcn Button组件',
        pattern: /from\s+["'](@\/components\/ui\/button|\.\/ui\/button)["']/
      },
      {
        name: '使用shadcn Badge组件',
        pattern: /from\s+["'](@\/components\/ui\/badge|\.\/ui\/badge)["']/
      },
      {
        name: '使用shadcn Tooltip组件',
        pattern: /from\s+["'](@\/components\/ui\/tooltip|\.\/ui\/tooltip)["']/
      },
      {
        name: '使用shadcn颜色系统',
        pattern: /text-muted-foreground|text-foreground/
      },
      {
        name: '移除了自定义按钮样式',
        pattern: /className=["'][^"']*btn[^"']*["']/,
        required: false,
        shouldNotExist: true
      }
    ]
  },
  {
    name: 'BookmarkCompact',
    task: '任务7',
    file: 'src/components/BookmarkCompact.tsx',
    checks: [
      {
        name: '使用shadcn Card组件',
        pattern: /from\s+["'](@\/components\/ui\/card|\.\/ui\/card)["']/
      },
      {
        name: '使用shadcn Button组件',
        pattern: /from\s+["'](@\/components\/ui\/button|\.\/ui\/button)["']/
      },
      {
        name: '使用shadcn Badge组件',
        pattern: /from\s+["'](@\/components\/ui\/badge|\.\/ui\/badge)["']/
      },
      {
        name: '使用shadcn Tooltip组件',
        pattern: /from\s+["'](@\/components\/ui\/tooltip|\.\/ui\/tooltip)["']/
      },
      {
        name: '使用shadcn颜色系统',
        pattern: /text-muted-foreground|text-foreground/
      },
      {
        name: '移除了自定义卡片样式',
        pattern: /className=["'][^"']*card[^"']*["']/,
        required: false,
        shouldNotExist: true
      }
    ]
  },
  {
    name: 'BookmarkEditModal',
    task: '任务8',
    file: 'src/components/BookmarkEditModal.tsx',
    checks: [
      {
        name: '使用shadcn Dialog组件',
        pattern: /from\s+["']@\/components\/ui\/dialog["']/
      },
      {
        name: '使用shadcn Form组件',
        pattern: /from\s+["']@\/components\/ui\/form["']/
      },
      {
        name: '使用shadcn Input组件',
        pattern: /from\s+["']@\/components\/ui\/input["']/
      },
      {
        name: '使用shadcn Button组件',
        pattern: /from\s+["']@\/components\/ui\/button["']/
      },
      {
        name: '使用shadcn Select组件',
        pattern: /from\s+["']@\/components\/ui\/select["']/
      },
      {
        name: '使用shadcn Badge组件',
        pattern: /from\s+["']@\/components\/ui\/badge["']/
      },
      {
        name: '移除了自定义模态样式',
        pattern: /className=["'][^"']*modal[^"']*["']/,
        required: false,
        shouldNotExist: true
      }
    ]
  }
]

// 1. 检查各组件的shadcn集成
console.log(colors.yellow('\n1. 检查各组件的shadcn集成:'))
components.forEach(component => {
  console.log(colors.magenta(`\n🔧 ${component.task}: ${component.name}`))
  if (!checkFileContent(component.file, component.checks)) {
    allTestsPassed = false
  }
})

// 2. 检查组件在OptionsApp中的使用
console.log(colors.yellow('\n2. 检查组件在OptionsApp中的使用:'))
components.forEach(component => {
  const isDirectlyUsed = checkComponentUsage(component.name, 'src/options/OptionsApp.tsx')
  // BookmarkRow 和 BookmarkCompact 通过 VirtualBookmarkList 间接使用，这是正常的
  if (!isDirectlyUsed && !['BookmarkRow', 'BookmarkCompact'].includes(component.name)) {
    allTestsPassed = false
  }
})

// 3. 检查VirtualBookmarkList中的组件使用
console.log(colors.yellow('\n3. 检查VirtualBookmarkList中的组件使用:'))
const virtualListChecks = [
  {
    name: 'BookmarkRow组件导入和使用',
    pattern: /import BookmarkRow.*from.*BookmarkRow/
  },
  {
    name: 'BookmarkCompact组件导入和使用',
    pattern: /import BookmarkCompact.*from.*BookmarkCompact/
  }
]

if (!checkFileContent('src/components/VirtualBookmarkList.tsx', virtualListChecks)) {
  allTestsPassed = false
}

// 4. 检查测试文件覆盖
console.log(colors.yellow('\n4. 检查测试文件覆盖:'))
const testFiles = [
  'tests/AddBookmarkModal.shadcn.test.tsx',
  'tests/BookmarkRow.shadcn.test.tsx', 
  'tests/BookmarkCompact.shadcn.test.tsx',
  'tests/BookmarkEditModal.shadcn.test.tsx'
]

testFiles.forEach(testFile => {
  const exists = fs.existsSync(testFile)
  console.log(`  ${exists ? colors.green('✓') : colors.red('✗')} ${testFile}`)
  if (!exists) allTestsPassed = false
})

// 5. 检查构建产物
console.log(colors.yellow('\n5. 检查构建产物:'))
const buildFiles = [
  'dist/src/options/index.html',
  'dist/manifest.json'
]

buildFiles.forEach(file => {
  const exists = fs.existsSync(file)
  console.log(`  ${exists ? colors.green('✓') : colors.red('✗')} ${file}`)
  if (!exists) allTestsPassed = false
})

// 6. 总结
console.log('\n' + '='.repeat(60))
if (allTestsPassed) {
  console.log(colors.green('🎉 所有组件的shadcn集成验证通过！'))
  console.log(colors.green('\n✅ 验证通过的组件:'))
  components.forEach(component => {
    console.log(colors.green(`  • ${component.task}: ${component.name} - shadcn集成完成`))
  })
  
  console.log(colors.cyan('\n🚀 组件使用状态:'))
  console.log(colors.cyan('  • 所有组件都已在OptionsApp中正确使用'))
  console.log(colors.cyan('  • VirtualBookmarkList正确使用BookmarkRow和BookmarkCompact'))
  console.log(colors.cyan('  • 所有组件都有对应的shadcn测试文件'))
  console.log(colors.cyan('  • 构建产物完整'))
  
  console.log(colors.magenta('\n📋 重构完成的功能:'))
  console.log(colors.magenta('  • AddBookmarkModal: Dialog + Form + Input + Button + Select'))
  console.log(colors.magenta('  • BookmarkRow: Button + Badge + Tooltip + 颜色系统'))
  console.log(colors.magenta('  • BookmarkCompact: Card + Button + Badge + Tooltip + 颜色系统'))
  console.log(colors.magenta('  • BookmarkEditModal: Dialog + Form + Input + Button + Select + Badge'))
  
} else {
  console.log(colors.green('🎉 所有组件的shadcn集成验证通过！'))
  console.log(colors.yellow('\n🔧 建议的修复步骤:'))
  console.log(colors.yellow('  1. 检查上述失败的组件shadcn集成'))
  console.log(colors.yellow('  2. 确保所有shadcn组件正确导入和使用'))
  console.log(colors.yellow('  3. 验证组件在OptionsApp中的正确使用'))
  console.log(colors.yellow('  4. 运行对应的shadcn测试'))
}

console.log(colors.blue('\n📖 相关文档:'))
console.log(colors.blue('  • 任务5文档: docs/task-5-addBookmarkModal-shadcn-refactor.md'))
console.log(colors.blue('  • 任务6文档: docs/task-6-bookmarkrow-shadcn-refactor.md'))
console.log(colors.blue('  • 任务7文档: docs/task-7-bookmarkcompact-shadcn-refactor.md'))
console.log(colors.blue('  • 任务8文档: docs/task-8-bookmarkeditmodal-shadcn-refactor.md'))

console.log(colors.cyan('\n⚡ 快速测试命令:'))
console.log(colors.cyan('  • 运行所有shadcn测试: npm test -- --run'))
console.log(colors.cyan('  • 构建项目: npm run build'))
console.log(colors.cyan('  • 验证特定组件: node scripts/verify-[component]-shadcn-refactor.cjs'))

process.exit(allTestsPassed ? 0 : 1)