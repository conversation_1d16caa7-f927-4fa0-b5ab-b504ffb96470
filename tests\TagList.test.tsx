// TagList 组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import TagList from '../src/components/TagList'
import type { TagWithStats } from '../src/components/TagList'

// 模拟 TagCard 组件
vi.mock('../src/components/TagCard', () => ({
  default: ({ tag, onEdit, onDelete, onClick }: any) => (
    <div data-testid={`tag-card-${tag.id}`}>
      <span>{tag.name}</span>
      <span>使用次数: {tag.usageCount}</span>
      <button onClick={onEdit} data-testid={`edit-${tag.id}`}>编辑</button>
      <button onClick={onDelete} data-testid={`delete-${tag.id}`}>删除</button>
      {onClick && <button onClick={onClick} data-testid={`click-${tag.id}`}>点击</button>}
    </div>
  )
}))

// 创建测试用的标签数据
const createMockTag = (id: string, name: string, usageCount: number = 0): TagWithStats => ({
  id,
  name,
  color: '#3B82F6',
  usageCount,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01')
})

const mockTags: TagWithStats[] = [
  createMockTag('1', '技术', 25),
  createMockTag('2', '学习', 18),
  createMockTag('3', '工具', 12),
  createMockTag('4', '新闻', 8),
  createMockTag('5', '娱乐', 15),
  createMockTag('6', '其他', 0)
]

describe('TagList 组件', () => {
  const defaultProps = {
    tags: mockTags,
    onTagEdit: vi.fn(),
    onTagDelete: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基本渲染', () => {
    it('应该正确渲染标签列表', () => {
      render(<TagList {...defaultProps} />)
      
      // 检查所有标签是否都被渲染
      mockTags.forEach(tag => {
        expect(screen.getByTestId(`tag-card-${tag.id}`)).toBeInTheDocument()
        expect(screen.getByText(tag.name)).toBeInTheDocument()
      })
    })

    it('应该显示标签统计信息', () => {
      render(<TagList {...defaultProps} />)
      
      // 检查统计信息
      expect(screen.getByText('6')).toBeInTheDocument() // 总标签数
      expect(screen.getByText('总标签数')).toBeInTheDocument()
      expect(screen.getByText('5')).toBeInTheDocument() // 活跃标签数
      expect(screen.getByText('活跃标签')).toBeInTheDocument()
      expect(screen.getByText('1')).toBeInTheDocument() // 未使用标签数
      expect(screen.getByText('未使用标签')).toBeInTheDocument()
    })

    it('应该显示搜索框和排序选择器', () => {
      render(<TagList {...defaultProps} />)
      
      expect(screen.getByPlaceholderText('搜索标签...')).toBeInTheDocument()
      expect(screen.getByLabelText('排序方式:')).toBeInTheDocument()
    })

    it('应该显示结果计数', () => {
      render(<TagList {...defaultProps} />)
      
      expect(screen.getByText('共 6 个标签')).toBeInTheDocument()
    })
  })

  describe('加载状态', () => {
    it('应该显示加载状态', () => {
      render(<TagList {...defaultProps} loading={true} />)
      
      expect(screen.getByText('加载标签数据中...')).toBeInTheDocument()
      expect(screen.queryByTestId('tag-card-1')).not.toBeInTheDocument()
    })
  })

  describe('空状态', () => {
    it('应该显示空状态（无标签）', () => {
      render(<TagList {...defaultProps} tags={[]} />)
      
      expect(screen.getByText('暂无标签')).toBeInTheDocument()
      expect(screen.getByText('创建您的第一个标签来更好地组织书签')).toBeInTheDocument()
    })

    it('应该显示搜索无结果状态', () => {
      render(<TagList {...defaultProps} tags={[]} searchQuery="不存在的标签" />)
      
      expect(screen.getByText('未找到匹配的标签')).toBeInTheDocument()
      expect(screen.getByText('尝试使用不同的关键词搜索，或创建新的标签')).toBeInTheDocument()
    })

    it('应该在空状态下显示创建按钮', () => {
      const onCreateTag = vi.fn()
      render(<TagList {...defaultProps} tags={[]} onCreateTag={onCreateTag} />)
      
      const createButton = screen.getByText('创建标签')
      expect(createButton).toBeInTheDocument()
      
      fireEvent.click(createButton)
      expect(onCreateTag).toHaveBeenCalledTimes(1)
    })
  })

  describe('搜索功能', () => {
    it('应该支持搜索标签', async () => {
      render(<TagList {...defaultProps} />)
      
      const searchInput = screen.getByPlaceholderText('搜索标签...')
      fireEvent.change(searchInput, { target: { value: '技术' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('tag-card-1')).toBeInTheDocument()
        expect(screen.queryByTestId('tag-card-2')).not.toBeInTheDocument()
      })
    })

    it('应该支持外部控制的搜索', () => {
      const onSearchChange = vi.fn()
      render(<TagList {...defaultProps} searchQuery="学习" onSearchChange={onSearchChange} />)
      
      const searchInput = screen.getByPlaceholderText('搜索标签...')
      fireEvent.change(searchInput, { target: { value: '工具' } })
      
      expect(onSearchChange).toHaveBeenCalledWith('工具')
    })

    it('应该显示搜索结果计数', async () => {
      render(<TagList {...defaultProps} />)
      
      const searchInput = screen.getByPlaceholderText('搜索标签...')
      fireEvent.change(searchInput, { target: { value: '技术' } })
      
      await waitFor(() => {
        expect(screen.getByText('显示 1 个标签，共 6 个')).toBeInTheDocument()
      })
    })

    it('应该支持清除搜索筛选', async () => {
      render(<TagList {...defaultProps} />)
      
      const searchInput = screen.getByPlaceholderText('搜索标签...')
      fireEvent.change(searchInput, { target: { value: '技术' } })
      
      await waitFor(() => {
        const clearButton = screen.getByText('清除筛选')
        fireEvent.click(clearButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText('共 6 个标签')).toBeInTheDocument()
      })
    })
  })

  describe('排序功能', () => {
    it('应该支持按名称排序', () => {
      const onSortChange = vi.fn()
      render(<TagList {...defaultProps} onSortChange={onSortChange} />)
      
      const sortSelect = screen.getByLabelText('排序方式:')
      fireEvent.change(sortSelect, { target: { value: 'name-desc' } })
      
      expect(onSortChange).toHaveBeenCalledWith('name-desc')
    })

    it('应该支持按使用次数排序', () => {
      render(<TagList {...defaultProps} sortBy="usage-desc" />)
      
      const sortSelect = screen.getByLabelText('排序方式:')
      expect(sortSelect).toHaveValue('usage-desc')
    })

    it('应该正确排序标签（按使用次数降序）', () => {
      render(<TagList {...defaultProps} sortBy="usage-desc" />)
      
      const tagCards = screen.getAllByTestId(/^tag-card-/)
      // 第一个应该是使用次数最多的标签（技术，25次）
      expect(tagCards[0]).toHaveAttribute('data-testid', 'tag-card-1')
    })
  })

  describe('视图模式', () => {
    it('应该支持网格和列表视图切换', () => {
      render(<TagList {...defaultProps} />)
      
      const gridButton = screen.getByTitle('网格视图')
      const listButton = screen.getByTitle('列表视图')
      
      expect(gridButton).toBeInTheDocument()
      expect(listButton).toBeInTheDocument()
      
      fireEvent.click(listButton)
      // 视图模式切换后，布局应该改变（通过CSS类名）
      // 这里我们主要测试按钮的存在和点击功能
    })
  })

  describe('交互功能', () => {
    it('应该处理标签编辑', () => {
      render(<TagList {...defaultProps} />)
      
      const editButton = screen.getByTestId('edit-1')
      fireEvent.click(editButton)
      
      expect(defaultProps.onTagEdit).toHaveBeenCalledWith(mockTags[0])
    })

    it('应该处理标签删除', () => {
      render(<TagList {...defaultProps} />)
      
      const deleteButton = screen.getByTestId('delete-1')
      fireEvent.click(deleteButton)
      
      expect(defaultProps.onTagDelete).toHaveBeenCalledWith(mockTags[0])
    })

    it('应该处理标签点击', () => {
      const onTagClick = vi.fn()
      render(<TagList {...defaultProps} onTagClick={onTagClick} />)
      
      const clickButton = screen.getByTestId('click-1')
      fireEvent.click(clickButton)
      
      expect(onTagClick).toHaveBeenCalledWith(mockTags[0])
    })
  })

  describe('边界情况', () => {
    it('应该处理空的搜索查询', () => {
      render(<TagList {...defaultProps} searchQuery="" />)
      
      // 应该显示所有标签
      expect(screen.getByText('共 6 个标签')).toBeInTheDocument()
      mockTags.forEach(tag => {
        expect(screen.getByTestId(`tag-card-${tag.id}`)).toBeInTheDocument()
      })
    })

    it('应该处理无效的排序选项', () => {
      // 使用默认排序
      render(<TagList {...defaultProps} sortBy={'invalid' as any} />)
      
      // 应该正常渲染，不会崩溃
      expect(screen.getByTestId('tag-card-1')).toBeInTheDocument()
    })

    it('应该处理标签数据中缺少字段的情况', () => {
      const incompleteTag = {
        id: '7',
        name: '不完整标签',
        usageCount: 5,
        createdAt: new Date(),
        updatedAt: new Date()
        // 缺少 color 字段
      } as TagWithStats

      render(<TagList {...defaultProps} tags={[incompleteTag]} />)
      
      expect(screen.getByTestId('tag-card-7')).toBeInTheDocument()
      expect(screen.getByText('不完整标签')).toBeInTheDocument()
    })
  })

  describe('性能优化', () => {
    it('应该使用 React.memo 优化渲染', () => {
      const { rerender } = render(<TagList {...defaultProps} />)
      
      // 重新渲染相同的props，组件应该被memo优化
      rerender(<TagList {...defaultProps} />)
      
      // 验证组件仍然正常工作
      expect(screen.getByTestId('tag-card-1')).toBeInTheDocument()
    })

    it('应该正确处理大量标签数据', () => {
      const largeTags = Array.from({ length: 100 }, (_, i) => 
        createMockTag(`tag-${i}`, `标签${i}`, i)
      )
      
      render(<TagList {...defaultProps} tags={largeTags} />)
      
      expect(screen.getByText('共 100 个标签')).toBeInTheDocument()
      expect(screen.getByText('100')).toBeInTheDocument() // 总标签数统计
    })
  })

  describe('可访问性', () => {
    it('应该有正确的ARIA标签', () => {
      render(<TagList {...defaultProps} />)
      
      const searchInput = screen.getByPlaceholderText('搜索标签...')
      const sortSelect = screen.getByLabelText('排序方式:')
      
      expect(searchInput).toBeInTheDocument()
      expect(sortSelect).toBeInTheDocument()
    })

    it('应该支持键盘导航', () => {
      render(<TagList {...defaultProps} />)
      
      const searchInput = screen.getByPlaceholderText('搜索标签...')
      
      // 测试键盘焦点
      searchInput.focus()
      expect(document.activeElement).toBe(searchInput)
    })
  })
})