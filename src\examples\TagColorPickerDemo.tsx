import React, { useState } from 'react'
import { TagColorPicker } from '../components/TagColorPicker'
import { ColorUtils } from '../utils/colorUtils'

/**
 * TagColorPicker组件演示
 */
export const TagColorPickerDemo: React.FC = () => {
  const [selectedColor1, setSelectedColor1] = useState('#3B82F6')
  const [selectedColor2, setSelectedColor2] = useState('')
  const [selectedColor3, setSelectedColor3] = useState('#10B981')

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">TagColorPicker 组件演示</h1>
      
      <div className="space-y-8">
        {/* 基本用法 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">基本用法</h2>
          <div className="max-w-md">
            <TagColorPicker
              value={selectedColor1}
              onChange={setSelectedColor1}
              placeholder="选择标签颜色"
            />
          </div>
          <div className="mt-4 p-3 bg-gray-50 rounded">
            <p className="text-sm">
              <strong>选中的颜色:</strong> {selectedColor1 || '未选择'}
            </p>
            {selectedColor1 && (
              <div className="mt-2 flex items-center space-x-2">
                <div 
                  className="w-6 h-6 rounded border"
                  style={{ backgroundColor: selectedColor1 }}
                />
                <span className="text-sm font-mono">{selectedColor1}</span>
              </div>
            )}
          </div>
        </div>

        {/* 无初始值 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">无初始值</h2>
          <div className="max-w-md">
            <TagColorPicker
              value={selectedColor2}
              onChange={setSelectedColor2}
              placeholder="请选择颜色"
            />
          </div>
          <div className="mt-4 p-3 bg-gray-50 rounded">
            <p className="text-sm">
              <strong>选中的颜色:</strong> {selectedColor2 || '未选择'}
            </p>
          </div>
        </div>

        {/* 自定义预设颜色 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">自定义预设颜色</h2>
          <div className="max-w-md">
            <TagColorPicker
              value={selectedColor3}
              onChange={setSelectedColor3}
              presetColors={ColorUtils.LIGHT_THEME_COLORS}
              placeholder="选择浅色主题颜色"
            />
          </div>
          <div className="mt-4 p-3 bg-gray-50 rounded">
            <p className="text-sm">
              <strong>选中的颜色:</strong> {selectedColor3 || '未选择'}
            </p>
          </div>
        </div>

        {/* 禁用自定义颜色 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">禁用自定义颜色</h2>
          <div className="max-w-md">
            <TagColorPicker
              value="#F59E0B"
              onChange={() => {}}
              allowCustom={false}
              placeholder="仅预设颜色"
            />
          </div>
        </div>

        {/* 禁用状态 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">禁用状态</h2>
          <div className="max-w-md">
            <TagColorPicker
              value="#EF4444"
              onChange={() => {}}
              disabled={true}
              placeholder="禁用的颜色选择器"
            />
          </div>
        </div>

        {/* 颜色工具函数演示 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">颜色工具函数演示</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 字符串生成颜色 */}
            <div>
              <h3 className="font-medium mb-2">字符串生成颜色</h3>
              <div className="space-y-2">
                {['技术', '学习', '工具', '新闻', '娱乐'].map(tag => {
                  const color = ColorUtils.generateColorFromString(tag)
                  return (
                    <div key={tag} className="flex items-center space-x-2">
                      <div 
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: color }}
                      />
                      <span className="text-sm">{tag}</span>
                      <span className="text-xs font-mono text-gray-500">{color}</span>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* 颜色变体 */}
            <div>
              <h3 className="font-medium mb-2">颜色变体</h3>
              <div className="space-y-2">
                {ColorUtils.generateColorVariants('#3B82F6', { lighterSteps: 2, darkerSteps: 2 }).map((color, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded border"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-xs font-mono">{color}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 颜色验证演示 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">颜色验证演示</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2 text-green-600">有效颜色</h3>
              <div className="space-y-1 text-sm font-mono">
                {['#ff0000', '#f00', 'rgb(255,0,0)', 'rgba(255,0,0,0.5)', 'hsl(0,100%,50%)'].map(color => (
                  <div key={color} className="flex items-center space-x-2">
                    <span className="text-green-600">✓</span>
                    <span>{color}</span>
                    <span className="text-xs text-gray-500">
                      ({ColorUtils.isValidColor(color) ? '有效' : '无效'})
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-red-600">无效颜色</h3>
              <div className="space-y-1 text-sm font-mono">
                {['#gggggg', 'rgb(256,0,0)', 'invalid', '', 'hsl(361,100%,50%)'].map(color => (
                  <div key={color || 'empty'} className="flex items-center space-x-2">
                    <span className="text-red-600">✗</span>
                    <span>{color || '(空字符串)'}</span>
                    <span className="text-xs text-gray-500">
                      ({ColorUtils.isValidColor(color) ? '有效' : '无效'})
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TagColorPickerDemo