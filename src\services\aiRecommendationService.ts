// AI推荐服务 - 专门处理标签和文件夹推荐功能

import { aiChatService } from './aiChatService'
import { tagService } from './tagService'
import { categoryService } from './categoryService'
import { indexedDBService } from '../utils/indexedDB'
import { aiService } from './aiService'

/**
 * 标签推荐请求接口
 */
export interface TagRecommendationRequest {
  title?: string
  url?: string
  content?: string
  description?: string
  maxRecommendations?: number
}

/**
 * 标签推荐响应接口
 */
export interface TagRecommendationResponse {
  existingTags: string[]  // 推荐的现有标签
  newTags: string[]       // 建议的新标签
  confidence: number      // 推荐置信度
  reasoning?: string      // 推荐理由
}

/**
 * 文件夹推荐请求接口
 */
export interface FolderRecommendationRequest {
  title?: string
  url?: string
  content?: string
  description?: string
  tags?: string[]
  maxRecommendations?: number
}

/**
 * 文件夹推荐响应接口
 */
export interface FolderRecommendationResponse {
  recommendedFolders: Array<{
    name: string
    confidence: number
    reason?: string
  }>
  reasoning?: string
}

/**
 * AI推荐服务类
 * 提供基于AI的标签和文件夹推荐功能
 */
export class AIRecommendationService {

  /**
   * 推荐标签
   * 优先推荐现有标签，只有在现有标签不足时才生成新标签
   * @param request 推荐请求
   * @returns Promise<TagRecommendationResponse>
   */
  async recommendTags(request: TagRecommendationRequest): Promise<TagRecommendationResponse> {
    try {
      console.log('开始AI标签推荐:', request.title || request.content?.substring(0, 50))

      // 获取现有标签列表
      const existingTags = await tagService.getTags()
      const existingTagNames = existingTags.map(tag => tag.name)

      // 构建内容文本
      const contentText = this.buildContentText(request)

      // 使用AI生成标签推荐
      const aiResponse = await aiChatService.generateText({
        prompt: this.buildTagRecommendationPrompt(request, existingTagNames),
        generationType: 'tags',
        context: {
          title: request.title,
          url: request.url,
          content: request.content,
          existingTags: existingTagNames
        },
        maxLength: 500
      })

      // 解析AI响应
      const parsedResponse = this.parseTagRecommendationResponse(aiResponse.content, existingTagNames)

      // 限制推荐数量
      const maxRecommendations = request.maxRecommendations || 8
      const totalRecommendations = parsedResponse.existingTags.length + parsedResponse.newTags.length

      if (totalRecommendations > maxRecommendations) {
        // 优先保留现有标签推荐
        const existingToKeep = Math.min(parsedResponse.existingTags.length, maxRecommendations)
        const newToKeep = Math.max(0, maxRecommendations - existingToKeep)

        parsedResponse.existingTags = parsedResponse.existingTags.slice(0, existingToKeep)
        parsedResponse.newTags = parsedResponse.newTags.slice(0, newToKeep)
      }

      console.log(`标签推荐完成: 现有标签 ${parsedResponse.existingTags.length} 个, 新标签 ${parsedResponse.newTags.length} 个`)

      return {
        ...parsedResponse,
        confidence: aiResponse.metadata?.model ? 0.8 : 0.6,
        reasoning: aiResponse.metadata ? `使用 ${aiResponse.metadata.model} 模型生成推荐` : '使用本地规则生成推荐'
      }

    } catch (error) {
      console.error('AI标签推荐失败:', error)
      
      // 降级到基于规则的推荐
      return await this.fallbackTagRecommendation(request)
    }
  }

  /**
   * 推荐文件夹
   * 只推荐现有的文件夹，不创建新文件夹
   * @param request 推荐请求
   * @returns Promise<FolderRecommendationResponse>
   */
  async recommendFolders(request: FolderRecommendationRequest): Promise<FolderRecommendationResponse> {
    try {
      console.log('开始AI文件夹推荐:', request.title || request.content?.substring(0, 50))

      // 获取现有分类列表
      const existingCategories = await categoryService.getCategories()
      const existingCategoryNames = existingCategories.map(cat => cat.name)

      if (existingCategoryNames.length === 0) {
        return {
          recommendedFolders: [{
            name: '默认分类',
            confidence: 0.5,
            reason: '没有现有分类，推荐默认分类'
          }],
          reasoning: '系统中暂无自定义分类'
        }
      }

      // 构建内容文本
      const contentText = this.buildContentText(request)

      // 使用AI生成文件夹推荐
      const aiResponse = await aiChatService.generateText({
        prompt: this.buildFolderRecommendationPrompt(request, existingCategoryNames),
        generationType: 'category',
        context: {
          title: request.title,
          url: request.url,
          content: request.content,
          tags: request.tags,
          existingCategories: existingCategoryNames
        },
        maxLength: 300
      })

      // 解析AI响应
      const parsedResponse = this.parseFolderRecommendationResponse(aiResponse.content, existingCategoryNames)

      // 限制推荐数量
      const maxRecommendations = request.maxRecommendations || 3
      parsedResponse.recommendedFolders = parsedResponse.recommendedFolders.slice(0, maxRecommendations)

      console.log(`文件夹推荐完成: ${parsedResponse.recommendedFolders.length} 个推荐`)

      return {
        ...parsedResponse,
        reasoning: aiResponse.metadata ? `使用 ${aiResponse.metadata.model} 模型分析内容特征` : '使用本地规则分析内容'
      }

    } catch (error) {
      console.error('AI文件夹推荐失败:', error)
      
      // 降级到基于规则的推荐
      return await this.fallbackFolderRecommendation(request)
    }
  }

  /**
   * 批量推荐（同时推荐标签和文件夹）
   * @param request 推荐请求
   * @returns Promise<{tags: TagRecommendationResponse, folders: FolderRecommendationResponse}>
   */
  async recommendBoth(request: TagRecommendationRequest & FolderRecommendationRequest): Promise<{
    tags: TagRecommendationResponse
    folders: FolderRecommendationResponse
  }> {
    try {
      // 并行执行标签和文件夹推荐
      const [tagsResponse, foldersResponse] = await Promise.all([
        this.recommendTags(request),
        this.recommendFolders(request)
      ])

      return {
        tags: tagsResponse,
        folders: foldersResponse
      }
    } catch (error) {
      console.error('批量推荐失败:', error)
      throw error
    }
  }

  /**
   * 全面推荐（同时推荐标签、文件夹和描述）
   * @param request 推荐请求
   * @returns Promise<{tags: TagRecommendationResponse, folders: FolderRecommendationResponse, description: any}>
   */
  async recommendAll(request: TagRecommendationRequest & FolderRecommendationRequest): Promise<{
    tags: TagRecommendationResponse
    folders: FolderRecommendationResponse
    description: any
  }> {
    try {
      // 并行执行标签、文件夹推荐和描述生成
      const [tagsResponse, foldersResponse, descriptionResponse] = await Promise.all([
        this.recommendTags(request),
        this.recommendFolders(request),
        aiService.generateDescription({
          title: request.title,
          url: request.url,
          content: request.content || request.description,
          maxLength: 200,
          style: 'brief'
        })
      ])

      return {
        tags: tagsResponse,
        folders: foldersResponse,
        description: descriptionResponse
      }
    } catch (error) {
      console.error('全面推荐失败:', error)
      throw error
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 构建内容文本
   * @param request 请求对象
   * @returns 合并的内容文本
   */
  private buildContentText(request: TagRecommendationRequest | FolderRecommendationRequest): string {
    const parts: string[] = []
    
    if (request.title) parts.push(request.title)
    if (request.description) parts.push(request.description)
    if (request.content) parts.push(request.content.substring(0, 1000)) // 限制内容长度
    if (request.url) parts.push(request.url)
    
    return parts.join(' ')
  }

  /**
   * 构建标签推荐提示词
   * @param request 推荐请求
   * @param existingTags 现有标签列表
   * @returns 提示词
   */
  private buildTagRecommendationPrompt(request: TagRecommendationRequest, existingTags: string[]): string {
    let prompt = `请为以下内容推荐合适的标签。优先从现有标签中选择，只有在现有标签不够准确时才建议新标签。\n\n`

    if (request.title) {
      prompt += `标题: ${request.title}\n`
    }

    if (request.url) {
      prompt += `网址: ${request.url}\n`
    }

    if (request.description) {
      prompt += `描述: ${request.description}\n`
    }

    if (request.content) {
      prompt += `内容: ${request.content.substring(0, 800)}\n`
    }

    prompt += `\n现有标签列表:\n${existingTags.join(', ')}\n\n`

    prompt += `要求:\n`
    prompt += `1. 优先从现有标签中选择最相关的标签\n`
    prompt += `2. 如果现有标签不够准确，可以建议1-2个新标签\n`
    prompt += `3. 总共推荐不超过${request.maxRecommendations || 8}个标签\n`
    prompt += `4. 标签应该简洁明了，通常1-4个字\n`
    prompt += `5. 使用中文\n\n`

    prompt += `请按以下格式返回:\n`
    prompt += `现有标签: [从现有标签中选择的标签，用逗号分隔]\n`
    prompt += `新标签: [建议的新标签，用逗号分隔，如果不需要新标签则写"无"]\n`
    prompt += `理由: [简要说明推荐理由]\n\n`

    return prompt
  }

  /**
   * 构建文件夹推荐提示词
   * @param request 推荐请求
   * @param existingCategories 现有分类列表
   * @returns 提示词
   */
  private buildFolderRecommendationPrompt(request: FolderRecommendationRequest, existingCategories: string[]): string {
    let prompt = `请从现有文件夹中推荐最适合的分类。只能从现有文件夹中选择，不要创建新的分类。\n\n`

    if (request.title) {
      prompt += `标题: ${request.title}\n`
    }

    if (request.url) {
      prompt += `网址: ${request.url}\n`
    }

    if (request.description) {
      prompt += `描述: ${request.description}\n`
    }

    if (request.content) {
      prompt += `内容: ${request.content.substring(0, 800)}\n`
    }

    if (request.tags && request.tags.length > 0) {
      prompt += `标签: ${request.tags.join(', ')}\n`
    }

    prompt += `\n现有文件夹列表:\n${existingCategories.join(', ')}\n\n`

    prompt += `要求:\n`
    prompt += `1. 只能从现有文件夹中选择，不要创建新文件夹\n`
    prompt += `2. 推荐${request.maxRecommendations || 3}个最相关的文件夹\n`
    prompt += `3. 按相关性从高到低排序\n`
    prompt += `4. 为每个推荐提供简要理由\n\n`

    prompt += `请按以下格式返回:\n`
    prompt += `推荐1: [文件夹名称] - [推荐理由]\n`
    prompt += `推荐2: [文件夹名称] - [推荐理由]\n`
    prompt += `推荐3: [文件夹名称] - [推荐理由]\n\n`

    return prompt
  }

  /**
   * 解析标签推荐响应
   * @param response AI响应文本
   * @param existingTags 现有标签列表
   * @returns 解析后的推荐结果
   */
  private parseTagRecommendationResponse(response: string, existingTags: string[]): {
    existingTags: string[]
    newTags: string[]
  } {
    const result = {
      existingTags: [] as string[],
      newTags: [] as string[]
    }

    try {
      const lines = response.split('\n')
      
      for (const line of lines) {
        if (line.includes('现有标签:') || line.includes('现有标签：')) {
          const tagsText = line.split(/[：:]/)[1]?.trim()
          if (tagsText && tagsText !== '无') {
            const tags = tagsText.split(/[,，]/).map(tag => tag.trim()).filter(Boolean)
            // 验证标签确实存在于现有标签中
            result.existingTags = tags.filter(tag => 
              existingTags.some(existing => existing.toLowerCase() === tag.toLowerCase())
            )
          }
        } else if (line.includes('新标签:') || line.includes('新标签：')) {
          const tagsText = line.split(/[：:]/)[1]?.trim()
          if (tagsText && tagsText !== '无') {
            const tags = tagsText.split(/[,，]/).map(tag => tag.trim()).filter(Boolean)
            // 确保新标签不与现有标签重复
            result.newTags = tags.filter(tag => 
              !existingTags.some(existing => existing.toLowerCase() === tag.toLowerCase()) &&
              tag.length <= 10 // 限制标签长度
            )
          }
        }
      }

      // 如果解析失败，尝试简单的逗号分割
      if (result.existingTags.length === 0 && result.newTags.length === 0) {
        const allTags = response.replace(/[：:]/g, '').split(/[,，\n]/)
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0 && tag.length <= 10)
          .slice(0, 8)

        for (const tag of allTags) {
          if (existingTags.some(existing => existing.toLowerCase() === tag.toLowerCase())) {
            result.existingTags.push(tag)
          } else {
            result.newTags.push(tag)
          }
        }
      }

    } catch (error) {
      console.error('解析标签推荐响应失败:', error)
    }

    return result
  }

  /**
   * 解析文件夹推荐响应
   * @param response AI响应文本
   * @param existingCategories 现有分类列表
   * @returns 解析后的推荐结果
   */
  private parseFolderRecommendationResponse(response: string, existingCategories: string[]): {
    recommendedFolders: Array<{
      name: string
      confidence: number
      reason?: string
    }>
  } {
    const result = {
      recommendedFolders: [] as Array<{
        name: string
        confidence: number
        reason?: string
      }>
    }

    try {
      const lines = response.split('\n')
      let confidence = 0.9

      for (const line of lines) {
        if (line.includes('推荐') && line.includes('-')) {
          const parts = line.split('-')
          if (parts.length >= 2) {
            const folderPart = parts[0].trim()
            const reason = parts.slice(1).join('-').trim()
            
            // 提取文件夹名称
            const folderMatch = folderPart.match(/推荐\d*[：:]?\s*(.+)/)
            if (folderMatch) {
              const folderName = folderMatch[1].trim()
              
              // 验证文件夹是否存在于现有分类中
              const matchingCategory = existingCategories.find(cat => 
                cat.toLowerCase() === folderName.toLowerCase()
              )
              
              if (matchingCategory) {
                result.recommendedFolders.push({
                  name: matchingCategory,
                  confidence,
                  reason
                })
                confidence -= 0.1 // 后续推荐的置信度递减
              }
            }
          }
        }
      }

      // 如果没有解析到推荐，尝试从响应中提取现有分类名称
      if (result.recommendedFolders.length === 0) {
        for (const category of existingCategories) {
          if (response.toLowerCase().includes(category.toLowerCase())) {
            result.recommendedFolders.push({
              name: category,
              confidence: 0.7,
              reason: '基于内容匹配'
            })
            
            if (result.recommendedFolders.length >= 3) break
          }
        }
      }

    } catch (error) {
      console.error('解析文件夹推荐响应失败:', error)
    }

    return result
  }

  /**
   * 标签推荐降级策略
   * @param request 推荐请求
   * @returns 降级推荐结果
   */
  private async fallbackTagRecommendation(request: TagRecommendationRequest): Promise<TagRecommendationResponse> {
    console.log('使用标签推荐降级策略')

    try {
      // 获取现有标签
      const existingTags = await tagService.getTags()
      const existingTagNames = existingTags.map(tag => tag.name)

      // 构建内容文本
      const contentText = this.buildContentText(request).toLowerCase()

      // 基于关键词匹配现有标签
      const matchedTags: string[] = []
      for (const tagName of existingTagNames) {
        if (contentText.includes(tagName.toLowerCase()) || 
            (request.title && request.title.toLowerCase().includes(tagName.toLowerCase()))) {
          matchedTags.push(tagName)
        }
      }

      // 基于规则生成新标签
      const newTags: string[] = []
      const keywordMap: Record<string, string[]> = {
        '技术': ['javascript', 'python', 'java', 'react', 'vue', 'node', 'api', '开发', '编程', '代码'],
        '学习': ['教程', '学习', '课程', '培训', '指南', '文档'],
        '工具': ['工具', 'tool', '软件', '应用', 'app'],
        '设计': ['设计', 'design', 'ui', 'ux', '界面'],
        '新闻': ['新闻', '资讯', '报道', 'news'],
        '娱乐': ['视频', '音乐', '游戏', '娱乐', 'video', 'music']
      }

      for (const [tag, keywords] of Object.entries(keywordMap)) {
        if (!matchedTags.includes(tag) && 
            keywords.some(keyword => contentText.includes(keyword))) {
          newTags.push(tag)
        }
      }

      // 基于URL域名生成标签
      if (request.url) {
        try {
          const domain = new URL(request.url).hostname
          if (domain.includes('github') && !matchedTags.includes('代码')) {
            newTags.push('代码')
          }
          if (domain.includes('youtube') && !matchedTags.includes('视频')) {
            newTags.push('视频')
          }
        } catch (error) {
          // 忽略URL解析错误
        }
      }

      return {
        existingTags: matchedTags.slice(0, 5),
        newTags: newTags.slice(0, 3),
        confidence: 0.6,
        reasoning: '使用本地规则匹配（AI服务不可用）'
      }

    } catch (error) {
      console.error('标签推荐降级策略失败:', error)
      return {
        existingTags: [],
        newTags: ['收藏'],
        confidence: 0.3,
        reasoning: '使用默认标签'
      }
    }
  }

  /**
   * 文件夹推荐降级策略
   * @param request 推荐请求
   * @returns 降级推荐结果
   */
  private async fallbackFolderRecommendation(request: FolderRecommendationRequest): Promise<FolderRecommendationResponse> {
    console.log('使用文件夹推荐降级策略')

    try {
      // 获取现有分类
      const existingCategories = await categoryService.getCategories()
      const existingCategoryNames = existingCategories.map(cat => cat.name)

      if (existingCategoryNames.length === 0) {
        return {
          recommendedFolders: [{
            name: '默认分类',
            confidence: 0.5,
            reason: '没有现有分类'
          }],
          reasoning: '系统中暂无自定义分类'
        }
      }

      // 构建内容文本
      const contentText = this.buildContentText(request).toLowerCase()

      // 基于关键词匹配现有分类
      const recommendations: Array<{
        name: string
        confidence: number
        reason: string
      }> = []

      for (const categoryName of existingCategoryNames) {
        let confidence = 0
        let reason = ''

        // 直接名称匹配
        if (contentText.includes(categoryName.toLowerCase())) {
          confidence = 0.8
          reason = '内容直接包含分类名称'
        }
        // 基于分类的常见关键词匹配
        else if (this.matchCategoryKeywords(contentText, categoryName)) {
          confidence = 0.6
          reason = '内容特征匹配分类'
        }

        if (confidence > 0) {
          recommendations.push({
            name: categoryName,
            confidence,
            reason
          })
        }
      }

      // 按置信度排序
      recommendations.sort((a, b) => b.confidence - a.confidence)

      // 如果没有匹配的分类，推荐最常用的分类
      if (recommendations.length === 0) {
        // 获取分类统计信息
        const categoriesWithStats = await categoryService.getAllCategoriesWithStats()
        const mostUsed = categoriesWithStats
          .sort((a, b) => b.bookmarkCount - a.bookmarkCount)
          .slice(0, 3)

        for (const category of mostUsed) {
          recommendations.push({
            name: category.name,
            confidence: 0.4,
            reason: '常用分类'
          })
        }
      }

      return {
        recommendedFolders: recommendations.slice(0, 3),
        reasoning: '基于内容关键词和使用频率分析'
      }

    } catch (error) {
      console.error('文件夹推荐降级策略失败:', error)
      return {
        recommendedFolders: [{
          name: '默认分类',
          confidence: 0.3,
          reason: '降级推荐'
        }],
        reasoning: '使用默认分类'
      }
    }
  }

  /**
   * 匹配分类关键词
   * @param content 内容文本
   * @param categoryName 分类名称
   * @returns 是否匹配
   */
  private matchCategoryKeywords(content: string, categoryName: string): boolean {
    const categoryKeywords: Record<string, string[]> = {
      '技术': ['技术', '开发', '编程', '代码', 'javascript', 'python', 'java', 'react', 'vue'],
      '学习': ['学习', '教程', '课程', '培训', '教育', '知识', '指南'],
      '工作': ['工作', '职业', '办公', '商务', '项目', '管理'],
      '娱乐': ['娱乐', '游戏', '视频', '音乐', '电影', '休闲'],
      '工具': ['工具', '软件', '应用', '插件', '扩展', '实用'],
      '新闻': ['新闻', '资讯', '报道', '时事', '热点'],
      '设计': ['设计', 'ui', 'ux', '界面', '视觉', '创意'],
      '生活': ['生活', '日常', '健康', '美食', '旅游', '购物']
    }

    // 检查分类名称是否有对应的关键词
    const keywords = categoryKeywords[categoryName] || [categoryName]
    
    return keywords.some(keyword => content.includes(keyword))
  }
}

// 导出单例实例
export const aiRecommendationService = new AIRecommendationService()