// 颜色工具函数单元测试

import { describe, it, expect } from 'vitest'
import { ColorUtils } from '../src/utils/colorUtils'

describe('ColorUtils', () => {
  describe('generateColorFromString', () => {
    it('应该为相同字符串生成相同颜色', () => {
      const color1 = ColorUtils.generateColorFromString('测试')
      const color2 = ColorUtils.generateColorFromString('测试')
      
      expect(color1).toBe(color2)
    })

    it('应该为不同字符串生成不同颜色', () => {
      const color1 = ColorUtils.generateColorFromString('测试1')
      const color2 = ColorUtils.generateColorFromString('测试2')
      
      expect(color1).not.toBe(color2)
    })

    it('应该生成有效的十六进制颜色', () => {
      const color = ColorUtils.generateColorFromString('测试')
      
      expect(color).toMatch(/^#[0-9A-F]{6}$/i)
    })

    it('应该支持自定义饱和度和亮度', () => {
      const color1 = ColorUtils.generateColorFromString('测试', 50, 30)
      const color2 = ColorUtils.generateColorFromString('测试', 80, 70)
      
      expect(color1).not.toBe(color2)
      expect(color1).toMatch(/^#[0-9A-F]{6}$/i)
      expect(color2).toMatch(/^#[0-9A-F]{6}$/i)
    })

    it('应该处理空字符串', () => {
      const color = ColorUtils.generateColorFromString('')
      
      expect(color).toMatch(/^#[0-9A-F]{6}$/i)
    })
  })

  describe('isValidColor', () => {
    it('应该验证有效的十六进制颜色', () => {
      const validColors = ['#ffffff', '#000000', '#FF0000', '#abc', '#ABC']
      
      validColors.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(true)
      })
    })

    it('应该拒绝无效的十六进制颜色', () => {
      const invalidColors = ['#gggggg', '#12345', '#1234567', 'ffffff', '#']
      
      invalidColors.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(false)
      })
    })

    it('应该验证有效的RGB颜色', () => {
      const validColors = ['rgb(255,255,255)', 'rgb(0,0,0)', 'rgb(255, 0, 0)']
      
      validColors.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(true)
      })
    })

    it('应该拒绝无效的RGB颜色', () => {
      const invalidColors = ['rgb(256,0,0)', 'rgb(-1,0,0)', 'rgb(255,255)', 'rgb(a,b,c)']
      
      invalidColors.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(false)
      })
    })

    it('应该验证有效的RGBA颜色', () => {
      const validColors = ['rgba(255,255,255,1)', 'rgba(0,0,0,0)', 'rgba(255, 0, 0, 0.5)']
      
      validColors.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(true)
      })
    })

    it('应该拒绝无效的RGBA颜色', () => {
      const invalidColors = ['rgba(256,0,0,1)', 'rgba(255,255,255,2)', 'rgba(255,255,255,-1)']
      
      invalidColors.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(false)
      })
    })

    it('应该验证有效的HSL颜色', () => {
      const validColors = ['hsl(360,100%,100%)', 'hsl(0,0%,0%)', 'hsl(180, 50%, 50%)']
      
      validColors.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(true)
      })
    })

    it('应该拒绝无效的HSL颜色', () => {
      const invalidColors = ['hsl(361,100%,100%)', 'hsl(0,101%,0%)', 'hsl(0,0%,101%)']
      
      invalidColors.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(false)
      })
    })

    it('应该拒绝null、undefined和非字符串值', () => {
      const invalidValues = [null, undefined, 123, {}, []]
      
      invalidValues.forEach(value => {
        expect(ColorUtils.isValidColor(value)).toBe(false)
      })
    })
  })

  describe('hexToRgb', () => {
    it('应该转换6位十六进制颜色', () => {
      const result = ColorUtils.hexToRgb('#ff0000')
      
      expect(result).toEqual({ r: 255, g: 0, b: 0 })
    })

    it('应该转换3位十六进制颜色', () => {
      const result = ColorUtils.hexToRgb('#f00')
      
      expect(result).toEqual({ r: 255, g: 0, b: 0 })
    })

    it('应该处理不带#号的十六进制颜色', () => {
      const result = ColorUtils.hexToRgb('ff0000')
      
      expect(result).toEqual({ r: 255, g: 0, b: 0 })
    })

    it('应该处理大小写混合的十六进制颜色', () => {
      const result = ColorUtils.hexToRgb('#Ff0000')
      
      expect(result).toEqual({ r: 255, g: 0, b: 0 })
    })

    it('应该对无效输入返回null', () => {
      const invalidInputs = ['#gggggg', '#12345', 'invalid', '']
      
      invalidInputs.forEach(input => {
        expect(ColorUtils.hexToRgb(input)).toBeNull()
      })
    })
  })

  describe('rgbToHex', () => {
    it('应该转换RGB值为十六进制', () => {
      const result = ColorUtils.rgbToHex(255, 0, 0)
      
      expect(result).toBe('#ff0000')
    })

    it('应该处理边界值', () => {
      expect(ColorUtils.rgbToHex(0, 0, 0)).toBe('#000000')
      expect(ColorUtils.rgbToHex(255, 255, 255)).toBe('#ffffff')
    })

    it('应该处理超出范围的值', () => {
      expect(ColorUtils.rgbToHex(-10, 300, 128)).toBe('#00ff80')
    })

    it('应该处理小数值', () => {
      const result = ColorUtils.rgbToHex(255.7, 0.3, 0.9)
      
      expect(result).toBe('#ff0001')
    })
  })

  describe('hslToHex', () => {
    it('应该转换HSL为十六进制', () => {
      const result = ColorUtils.hslToHex({ h: 0, s: 100, l: 50 })
      
      expect(result).toBe('#ff0000')
    })

    it('应该处理灰色（无饱和度）', () => {
      const result = ColorUtils.hslToHex({ h: 0, s: 0, l: 50 })
      
      expect(result).toBe('#808080')
    })

    it('应该处理黑色和白色', () => {
      expect(ColorUtils.hslToHex({ h: 0, s: 0, l: 0 })).toBe('#000000')
      expect(ColorUtils.hslToHex({ h: 0, s: 0, l: 100 })).toBe('#ffffff')
    })
  })

  describe('hslToRgb', () => {
    it('应该转换HSL为RGB', () => {
      const result = ColorUtils.hslToRgb({ h: 0, s: 100, l: 50 })
      
      expect(result).toEqual({ r: 255, g: 0, b: 0 })
    })

    it('应该处理灰色', () => {
      const result = ColorUtils.hslToRgb({ h: 0, s: 0, l: 50 })
      
      expect(result).toEqual({ r: 128, g: 128, b: 128 })
    })

    it('应该处理不同色相', () => {
      const green = ColorUtils.hslToRgb({ h: 120, s: 100, l: 50 })
      const blue = ColorUtils.hslToRgb({ h: 240, s: 100, l: 50 })
      
      expect(green).toEqual({ r: 0, g: 255, b: 0 })
      expect(blue).toEqual({ r: 0, g: 0, b: 255 })
    })
  })

  describe('rgbToHsl', () => {
    it('应该转换RGB为HSL', () => {
      const result = ColorUtils.rgbToHsl({ r: 255, g: 0, b: 0 })
      
      expect(result.h).toBe(0)
      expect(result.s).toBe(100)
      expect(result.l).toBe(50)
    })

    it('应该处理灰色', () => {
      const result = ColorUtils.rgbToHsl({ r: 128, g: 128, b: 128 })
      
      expect(result.h).toBe(0)
      expect(result.s).toBe(0)
      expect(result.l).toBe(50)
    })

    it('应该处理黑色和白色', () => {
      const black = ColorUtils.rgbToHsl({ r: 0, g: 0, b: 0 })
      const white = ColorUtils.rgbToHsl({ r: 255, g: 255, b: 255 })
      
      expect(black.l).toBe(0)
      expect(white.l).toBe(100)
    })
  })

  describe('getContrastColor', () => {
    it('应该为浅色背景返回黑色', () => {
      const result = ColorUtils.getContrastColor('#ffffff')
      
      expect(result).toBe('#000000')
    })

    it('应该为深色背景返回白色', () => {
      const result = ColorUtils.getContrastColor('#000000')
      
      expect(result).toBe('#ffffff')
    })

    it('应该为中等亮度的颜色返回合适的对比色', () => {
      const lightResult = ColorUtils.getContrastColor('#ffff00') // 黄色，亮度高
      const darkResult = ColorUtils.getContrastColor('#0000ff') // 蓝色，亮度低
      
      expect(lightResult).toBe('#000000')
      expect(darkResult).toBe('#ffffff')
    })
  })

  describe('getLuminance', () => {
    it('应该为白色返回1', () => {
      const result = ColorUtils.getLuminance('#ffffff')
      
      expect(result).toBeCloseTo(1, 1)
    })

    it('应该为黑色返回0', () => {
      const result = ColorUtils.getLuminance('#000000')
      
      expect(result).toBeCloseTo(0, 1)
    })

    it('应该为红色返回中等亮度', () => {
      const result = ColorUtils.getLuminance('#ff0000')
      
      expect(result).toBeGreaterThan(0)
      expect(result).toBeLessThan(1)
    })

    it('应该处理无效颜色', () => {
      const result = ColorUtils.getLuminance('invalid')
      
      expect(result).toBe(0)
    })
  })

  describe('getColorSimilarity', () => {
    it('应该为相同颜色返回1', () => {
      const result = ColorUtils.getColorSimilarity('#ff0000', '#ff0000')
      
      expect(result).toBe(1)
    })

    it('应该为相似颜色返回高相似度', () => {
      const result = ColorUtils.getColorSimilarity('#ff0000', '#fe0000')
      
      expect(result).toBeGreaterThan(0.9)
    })

    it('应该为完全不同的颜色返回低相似度', () => {
      const result = ColorUtils.getColorSimilarity('#ff0000', '#00ff00')
      
      expect(result).toBeLessThan(0.5)
    })

    it('应该处理无效颜色', () => {
      const result = ColorUtils.getColorSimilarity('invalid', '#ff0000')
      
      expect(result).toBe(0)
    })
  })

  describe('generateColorVariants', () => {
    it('应该生成颜色变体', () => {
      const result = ColorUtils.generateColorVariants('#ff0000')
      
      expect(result).toHaveLength(7) // 3亮 + 1原色 + 3暗
      expect(result).toContain('#ff0000') // 包含原色
    })

    it('应该支持自定义步数', () => {
      const result = ColorUtils.generateColorVariants('#ff0000', {
        lighterSteps: 2,
        darkerSteps: 2
      })
      
      expect(result).toHaveLength(5) // 2亮 + 1原色 + 2暗
    })

    it('应该支持自定义步长', () => {
      const result1 = ColorUtils.generateColorVariants('#ff0000', { stepSize: 5 })
      const result2 = ColorUtils.generateColorVariants('#ff0000', { stepSize: 20 })
      
      expect(result1).not.toEqual(result2)
    })

    it('应该处理无效颜色', () => {
      const result = ColorUtils.generateColorVariants('invalid')
      
      expect(result).toEqual(['invalid'])
    })
  })

  describe('adjustBrightness', () => {
    it('应该增加亮度', () => {
      const original = '#808080' // 中等灰色
      const brighter = ColorUtils.adjustBrightness(original, 20)
      
      expect(brighter).not.toBe(original)
      
      const originalLuminance = ColorUtils.getLuminance(original)
      const brighterLuminance = ColorUtils.getLuminance(brighter)
      
      expect(brighterLuminance).toBeGreaterThan(originalLuminance)
    })

    it('应该减少亮度', () => {
      const original = '#808080' // 中等灰色
      const darker = ColorUtils.adjustBrightness(original, -20)
      
      expect(darker).not.toBe(original)
      
      const originalLuminance = ColorUtils.getLuminance(original)
      const darkerLuminance = ColorUtils.getLuminance(darker)
      
      expect(darkerLuminance).toBeLessThan(originalLuminance)
    })

    it('应该处理边界值', () => {
      const white = ColorUtils.adjustBrightness('#ffffff', 50) // 已经是最亮
      const black = ColorUtils.adjustBrightness('#000000', -50) // 已经是最暗
      
      expect(white).toMatch(/^#[0-9A-F]{6}$/i)
      expect(black).toMatch(/^#[0-9A-F]{6}$/i)
    })

    it('应该处理无效颜色', () => {
      const result = ColorUtils.adjustBrightness('invalid', 20)
      
      expect(result).toBe('invalid')
    })
  })

  describe('adjustSaturation', () => {
    it('应该增加饱和度', () => {
      const original = '#c0c0c0' // 灰色，饱和度为0
      const moreSaturated = ColorUtils.adjustSaturation(original, 50)
      
      expect(moreSaturated).not.toBe(original)
      expect(moreSaturated).toMatch(/^#[0-9A-F]{6}$/i)
    })

    it('应该减少饱和度', () => {
      const original = '#ff0000' // 纯红色
      const lessSaturated = ColorUtils.adjustSaturation(original, -20)
      
      expect(lessSaturated).not.toBe(original)
    })

    it('应该处理无效颜色', () => {
      const result = ColorUtils.adjustSaturation('invalid', 20)
      
      expect(result).toBe('invalid')
    })
  })

  describe('getComplementaryColor', () => {
    it('应该返回互补色', () => {
      const red = '#ff0000'
      const complementary = ColorUtils.getComplementaryColor(red)
      
      expect(complementary).not.toBe(red)
      expect(complementary).toMatch(/^#[0-9A-F]{6}$/i)
    })

    it('应该为红色返回青色系', () => {
      const red = '#ff0000'
      const complementary = ColorUtils.getComplementaryColor(red)
      const rgb = ColorUtils.hexToRgb(complementary)
      
      // 互补色应该是青色系（绿色和蓝色分量较高）
      expect(rgb.r).toBeLessThan(rgb.g + rgb.b)
    })

    it('应该处理无效颜色', () => {
      const result = ColorUtils.getComplementaryColor('invalid')
      
      expect(result).toBe('invalid')
    })
  })

  describe('getAnalogousColors', () => {
    it('应该返回类似色', () => {
      const result = ColorUtils.getAnalogousColors('#ff0000', 2)
      
      expect(result).toHaveLength(2)
      result.forEach(color => {
        expect(color).toMatch(/^#[0-9A-F]{6}$/i)
      })
    })

    it('应该支持自定义数量', () => {
      const result1 = ColorUtils.getAnalogousColors('#ff0000', 1)
      const result2 = ColorUtils.getAnalogousColors('#ff0000', 3)
      
      expect(result1).toHaveLength(1)
      expect(result2).toHaveLength(3)
    })

    it('应该支持自定义色相范围', () => {
      const result1 = ColorUtils.getAnalogousColors('#ff0000', 2, 15)
      const result2 = ColorUtils.getAnalogousColors('#ff0000', 2, 45)
      
      expect(result1).not.toEqual(result2)
    })

    it('应该处理无效颜色', () => {
      const result = ColorUtils.getAnalogousColors('invalid', 2)
      
      expect(result).toEqual(['invalid'])
    })
  })

  describe('PRESET_COLORS', () => {
    it('应该包含有效的预设颜色', () => {
      expect(ColorUtils.PRESET_COLORS.length).toBeGreaterThan(0)
      
      ColorUtils.PRESET_COLORS.forEach(color => {
        expect(ColorUtils.isValidColor(color)).toBe(true)
      })
    })

    it('应该包含不同的颜色', () => {
      const uniqueColors = new Set(ColorUtils.PRESET_COLORS)
      
      expect(uniqueColors.size).toBe(ColorUtils.PRESET_COLORS.length)
    })
  })
})