#!/usr/bin/env node

/**
 * CategoryService 单元测试
 * 
 * 功能说明:
 * - 测试CategoryService类的分类管理功能
 * - 验证分类的CRUD操作
 * - 测试分类统计信息计算
 * - 验证分类名称唯一性检查
 * - 测试从书签同步分类功能
 * 
 * 测试覆盖:
 * - 分类创建、更新、删除功能
 * - 分类统计信息获取
 * - 分类名称唯一性验证
 * - 从书签同步分类
 * - 错误处理和边界情况
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// ES模块中获取当前文件路径的标准方法
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 开始测试 CategoryService 分类管理功能...\n')

/**
 * 轻量级测试框架类
 */
class TestRunner {
  constructor() {
    this.tests = []
    this.passed = 0
    this.failed = 0
    this.skipped = 0
  }

  test(name, testFn) {
    this.tests.push({ name, testFn, skip: false })
  }

  skip(name, testFn) {
    this.tests.push({ name, testFn, skip: true })
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  assertEqual(actual, expected, message) {
    if (actual !== expected) {
      throw new Error(`${message} - 期望: ${expected}, 实际: ${actual}`)
    }
  }

  assertDeepEqual(actual, expected, message) {
    if (JSON.stringify(actual) !== JSON.stringify(expected)) {
      throw new Error(`${message} - 期望: ${JSON.stringify(expected)}, 实际: ${JSON.stringify(actual)}`)
    }
  }

  async assertThrows(fn, message) {
    try {
      await fn()
      throw new Error(`${message} - 期望抛出错误但没有抛出`)
    } catch (error) {
      if (error.message.includes('期望抛出错误但没有抛出')) {
        throw error
      }
      // 正确抛出了错误
    }
  }

  async run() {
    console.log(`运行 ${this.tests.length} 个测试用例...\n`)

    for (const test of this.tests) {
      if (test.skip) {
        console.log(`⏭️  ${test.name} (跳过)`)
        this.skipped++
        continue
      }

      try {
        await test.testFn()
        console.log(`✅ ${test.name}`)
        this.passed++
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`)
        this.failed++
      }
    }

    console.log('\n' + '='.repeat(50))
    console.log(`测试结果: ${this.passed} 通过, ${this.failed} 失败, ${this.skipped} 跳过`)

    if (this.failed > 0) {
      process.exit(1)
    }
  }
}

// 创建测试运行器实例
const runner = new TestRunner()

/**
 * 模拟IndexedDB服务
 */
class MockIndexedDBService {
  constructor() {
    this.categories = new Map()
    this.bookmarks = new Map()
    this.nextCategoryId = 1
    this.nextBookmarkId = 1
  }

  // 分类相关方法
  async saveCategory(category) {
    this.categories.set(category.id, { ...category })
    return category.id
  }

  async getCategory(id) {
    return this.categories.get(id) || null
  }

  async getCategories() {
    return Array.from(this.categories.values())
  }

  async updateCategory(id, updates) {
    const category = this.categories.get(id)
    if (!category) {
      throw new Error(`分类不存在: ${id}`)
    }
    const updatedCategory = { ...category, ...updates, updatedAt: new Date() }
    this.categories.set(id, updatedCategory)
  }

  async deleteCategory(id) {
    if (!this.categories.has(id)) {
      throw new Error(`分类不存在: ${id}`)
    }
    this.categories.delete(id)
  }

  // 书签相关方法
  async saveBookmark(bookmark) {
    this.bookmarks.set(bookmark.id, { ...bookmark })
    return bookmark.id
  }

  async getBookmark(id) {
    return this.bookmarks.get(id) || null
  }

  async getBookmarks() {
    return Array.from(this.bookmarks.values())
  }

  async updateBookmark(id, updates) {
    const bookmark = this.bookmarks.get(id)
    if (!bookmark) {
      throw new Error(`书签不存在: ${id}`)
    }
    const updatedBookmark = { ...bookmark, ...updates, updatedAt: new Date() }
    this.bookmarks.set(id, updatedBookmark)
  }

  // 清空数据（用于测试）
  clear() {
    this.categories.clear()
    this.bookmarks.clear()
    this.nextCategoryId = 1
    this.nextBookmarkId = 1
  }
}

/**
 * 模拟CategoryService
 */
class MockCategoryService {
  constructor(mockIndexedDB) {
    this.indexedDBService = mockIndexedDB
  }

  // 生成分类ID
  generateCategoryId() {
    return `category_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 生成分类颜色
  generateCategoryColor(name) {
    const colors = [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
      '#06B6D4', '#F97316', '#84CC16', '#EC4899', '#6B7280'
    ]
    
    let hash = 0
    for (let i = 0; i < name.length; i++) {
      hash = ((hash << 5) - hash + name.charCodeAt(i)) & 0xffffffff
    }
    
    return colors[Math.abs(hash) % colors.length]
  }

  // 获取所有分类及其统计信息
  async getAllCategoriesWithStats() {
    try {
      const [categories, bookmarks] = await Promise.all([
        this.indexedDBService.getCategories(),
        this.indexedDBService.getBookmarks()
      ])

      const categoryStats = new Map()
      
      bookmarks.forEach(bookmark => {
        const categoryName = bookmark.category || '默认分类'
        categoryStats.set(categoryName, (categoryStats.get(categoryName) || 0) + 1)
      })

      const categoriesWithStats = categories.map(category => ({
        ...category,
        bookmarkCount: categoryStats.get(category.name) || 0
      }))

      return categoriesWithStats
    } catch (error) {
      console.error('获取分类统计信息失败:', error)
      throw new Error('获取分类统计信息失败')
    }
  }

  // 从现有书签中同步分类
  async syncCategoriesFromBookmarks() {
    try {
      const [bookmarks, existingCategories] = await Promise.all([
        this.indexedDBService.getBookmarks(),
        this.indexedDBService.getCategories()
      ])

      const existingCategoryNames = new Set(existingCategories.map(cat => cat.name))
      
      const bookmarkCategories = new Set()
      bookmarks.forEach(bookmark => {
        if (bookmark.category && bookmark.category.trim()) {
          bookmarkCategories.add(bookmark.category.trim())
        }
      })

      const newCategories = []
      const now = new Date()

      for (const categoryName of bookmarkCategories) {
        if (!existingCategoryNames.has(categoryName)) {
          const newCategory = {
            id: this.generateCategoryId(),
            name: categoryName,
            description: `从书签自动提取的分类`,
            color: this.generateCategoryColor(categoryName),
            createdAt: now,
            updatedAt: now,
            bookmarkCount: 0
          }
          
          await this.indexedDBService.saveCategory(newCategory)
          newCategories.push(newCategory)
        }
      }

      const allCategories = await this.indexedDBService.getCategories()
      return allCategories
    } catch (error) {
      console.error('同步分类失败:', error)
      throw new Error('同步分类失败')
    }
  }

  // 创建新分类
  async createCategory(categoryData) {
    try {
      const isUnique = await this.validateCategoryName(categoryData.name)
      if (!isUnique) {
        throw new Error(`分类名称 "${categoryData.name}" 已存在`)
      }

      const now = new Date()
      const newCategory = {
        id: this.generateCategoryId(),
        name: categoryData.name.trim(),
        description: categoryData.description?.trim() || '',
        color: categoryData.color || this.generateCategoryColor(categoryData.name),
        parentId: categoryData.parentId || undefined,
        createdAt: now,
        updatedAt: now,
        bookmarkCount: 0
      }

      await this.indexedDBService.saveCategory(newCategory)
      return newCategory
    } catch (error) {
      console.error('创建分类失败:', error)
      throw error
    }
  }

  // 更新分类
  async updateCategory(categoryId, updates) {
    try {
      const existingCategory = await this.indexedDBService.getCategory(categoryId)
      if (!existingCategory) {
        throw new Error(`分类不存在: ${categoryId}`)
      }

      if (updates.name && updates.name !== existingCategory.name) {
        const isUnique = await this.validateCategoryName(updates.name, categoryId)
        if (!isUnique) {
          throw new Error(`分类名称 "${updates.name}" 已存在`)
        }
      }

      const updatedCategory = {
        ...existingCategory,
        ...updates,
        id: categoryId,
        updatedAt: new Date()
      }

      if (updates.name && updates.name !== existingCategory.name) {
        await this.updateBookmarkCategories(existingCategory.name, updates.name)
      }

      await this.indexedDBService.saveCategory(updatedCategory)
      return updatedCategory
    } catch (error) {
      console.error('更新分类失败:', error)
      throw error
    }
  }

  // 删除分类
  async deleteCategory(categoryId) {
    try {
      const category = await this.indexedDBService.getCategory(categoryId)
      if (!category) {
        throw new Error(`分类不存在: ${categoryId}`)
      }

      const bookmarkCount = await this.getCategoryBookmarkCount(categoryId)
      
      if (bookmarkCount > 0) {
        await this.updateBookmarkCategories(category.name, '默认分类')
      }

      await this.indexedDBService.deleteCategory(categoryId)
    } catch (error) {
      console.error('删除分类失败:', error)
      throw error
    }
  }

  // 验证分类名称唯一性
  async validateCategoryName(name, excludeId) {
    try {
      const categories = await this.indexedDBService.getCategories()
      const trimmedName = name.trim().toLowerCase()
      
      return !categories.some(category => 
        category.name.toLowerCase() === trimmedName && 
        category.id !== excludeId
      )
    } catch (error) {
      console.error('验证分类名称失败:', error)
      return false
    }
  }

  // 获取分类的书签数量
  async getCategoryBookmarkCount(categoryId) {
    try {
      const category = await this.indexedDBService.getCategory(categoryId)
      if (!category) {
        return 0
      }

      const bookmarks = await this.indexedDBService.getBookmarks()
      return bookmarks.filter(bookmark => bookmark.category === category.name).length
    } catch (error) {
      console.error('获取分类书签数量失败:', error)
      return 0
    }
  }

  // 根据分类名称获取书签数量
  async getCategoryBookmarkCountByName(categoryName) {
    try {
      const bookmarks = await this.indexedDBService.getBookmarks()
      return bookmarks.filter(bookmark => bookmark.category === categoryName).length
    } catch (error) {
      console.error('获取分类书签数量失败:', error)
      return 0
    }
  }

  // 更新书签的分类名称
  async updateBookmarkCategories(oldCategoryName, newCategoryName) {
    try {
      const bookmarks = await this.indexedDBService.getBookmarks()
      const bookmarksToUpdate = bookmarks.filter(bookmark => bookmark.category === oldCategoryName)
      
      for (const bookmark of bookmarksToUpdate) {
        await this.indexedDBService.updateBookmark(bookmark.id, { 
          category: newCategoryName 
        })
      }
    } catch (error) {
      console.error('更新书签分类失败:', error)
      throw error
    }
  }
}

// 创建模拟服务实例
const mockIndexedDB = new MockIndexedDBService()
const mockCategoryService = new MockCategoryService(mockIndexedDB)

/**
 * 测试数据
 */
const mockCategoryInput = {
  name: '测试分类',
  description: '这是一个测试分类',
  color: '#3B82F6'
}

const mockBookmark = {
  id: 'bookmark_1',
  type: 'url',
  title: '测试书签',
  url: 'https://test.example.com',
  category: '技术',
  tags: ['测试'],
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: { aiGenerated: false }
}

/**
 * 测试用例定义
 */

// 测试组：分类创建功能
runner.test('创建分类 - 基本功能', async () => {
  mockIndexedDB.clear()
  
  const category = await mockCategoryService.createCategory(mockCategoryInput)
  
  runner.assert(category.id, '应该返回分类ID')
  runner.assert(category.id.startsWith('category_'), 'ID格式应该正确')
  runner.assertEqual(category.name, mockCategoryInput.name, '名称应该匹配')
  runner.assertEqual(category.description, mockCategoryInput.description, '描述应该匹配')
  runner.assertEqual(category.color, mockCategoryInput.color, '颜色应该匹配')
  runner.assertEqual(category.bookmarkCount, 0, '初始书签数量应该为0')
  runner.assert(category.createdAt instanceof Date, '创建时间应该是Date对象')
  runner.assert(category.updatedAt instanceof Date, '更新时间应该是Date对象')
})

runner.test('创建分类 - 自动生成颜色', async () => {
  mockIndexedDB.clear()
  
  const categoryData = {
    name: '自动颜色分类',
    description: '测试自动生成颜色'
  }
  
  const category = await mockCategoryService.createCategory(categoryData)
  
  runner.assert(category.color, '应该自动生成颜色')
  runner.assert(category.color.startsWith('#'), '颜色应该是十六进制格式')
  runner.assertEqual(category.color.length, 7, '颜色长度应该是7个字符')
})

runner.test('创建分类 - 名称唯一性验证', async () => {
  mockIndexedDB.clear()
  
  // 创建第一个分类
  await mockCategoryService.createCategory(mockCategoryInput)
  
  // 尝试创建同名分类
  await runner.assertThrows(async () => {
    await mockCategoryService.createCategory(mockCategoryInput)
  }, '重复名称应该抛出错误')
})

runner.test('创建分类 - 名称去空格处理', async () => {
  mockIndexedDB.clear()
  
  const categoryData = {
    name: '  带空格的分类  ',
    description: '  带空格的描述  '
  }
  
  const category = await mockCategoryService.createCategory(categoryData)
  
  runner.assertEqual(category.name, '带空格的分类', '名称应该去除首尾空格')
  runner.assertEqual(category.description, '带空格的描述', '描述应该去除首尾空格')
})

// 测试组：分类更新功能
runner.test('更新分类 - 基本功能', async () => {
  mockIndexedDB.clear()
  
  const category = await mockCategoryService.createCategory(mockCategoryInput)
  
  // 稍微延迟确保时间不同
  await new Promise(resolve => setTimeout(resolve, 10))
  
  const updates = {
    name: '更新后的分类',
    description: '更新后的描述',
    color: '#10B981'
  }
  
  const updatedCategory = await mockCategoryService.updateCategory(category.id, updates)
  
  runner.assertEqual(updatedCategory.name, updates.name, '名称应该已更新')
  runner.assertEqual(updatedCategory.description, updates.description, '描述应该已更新')
  runner.assertEqual(updatedCategory.color, updates.color, '颜色应该已更新')
  runner.assertEqual(updatedCategory.id, category.id, 'ID应该保持不变')
  runner.assert(updatedCategory.updatedAt >= category.updatedAt, '更新时间应该更新')
})

runner.test('更新分类 - 更新名称时的唯一性验证', async () => {
  mockIndexedDB.clear()
  
  // 创建两个分类
  const category1 = await mockCategoryService.createCategory({
    name: '分类1',
    description: '第一个分类'
  })
  
  const category2 = await mockCategoryService.createCategory({
    name: '分类2',
    description: '第二个分类'
  })
  
  // 尝试将分类2的名称改为分类1的名称
  await runner.assertThrows(async () => {
    await mockCategoryService.updateCategory(category2.id, { name: '分类1' })
  }, '更新为重复名称应该抛出错误')
})

runner.test('更新分类 - 更新书签分类名称', async () => {
  mockIndexedDB.clear()
  
  // 创建分类和书签
  const category = await mockCategoryService.createCategory({
    name: '原始分类',
    description: '原始描述'
  })
  
  const bookmark = {
    ...mockBookmark,
    category: '原始分类'
  }
  await mockIndexedDB.saveBookmark(bookmark)
  
  // 更新分类名称
  await mockCategoryService.updateCategory(category.id, { name: '新分类名称' })
  
  // 验证书签的分类也被更新
  const updatedBookmark = await mockIndexedDB.getBookmark(bookmark.id)
  runner.assertEqual(updatedBookmark.category, '新分类名称', '书签的分类应该已更新')
})

runner.test('更新不存在的分类', async () => {
  mockIndexedDB.clear()
  
  await runner.assertThrows(async () => {
    await mockCategoryService.updateCategory('non-existent-id', { name: '新名称' })
  }, '更新不存在的分类应该抛出错误')
})

// 测试组：分类删除功能
runner.test('删除分类 - 基本功能', async () => {
  mockIndexedDB.clear()
  
  const category = await mockCategoryService.createCategory(mockCategoryInput)
  
  // 确认分类存在
  let existingCategory = await mockIndexedDB.getCategory(category.id)
  runner.assert(existingCategory, '分类应该存在')
  
  // 删除分类
  await mockCategoryService.deleteCategory(category.id)
  
  // 确认分类已删除
  existingCategory = await mockIndexedDB.getCategory(category.id)
  runner.assertEqual(existingCategory, null, '分类应该已被删除')
})

runner.test('删除分类 - 处理相关书签', async () => {
  mockIndexedDB.clear()
  
  // 创建分类和书签
  const category = await mockCategoryService.createCategory({
    name: '待删除分类',
    description: '将被删除的分类'
  })
  
  const bookmark = {
    ...mockBookmark,
    category: '待删除分类'
  }
  await mockIndexedDB.saveBookmark(bookmark)
  
  // 删除分类
  await mockCategoryService.deleteCategory(category.id)
  
  // 验证书签被移动到默认分类
  const updatedBookmark = await mockIndexedDB.getBookmark(bookmark.id)
  runner.assertEqual(updatedBookmark.category, '默认分类', '书签应该被移动到默认分类')
})

runner.test('删除不存在的分类', async () => {
  mockIndexedDB.clear()
  
  await runner.assertThrows(async () => {
    await mockCategoryService.deleteCategory('non-existent-id')
  }, '删除不存在的分类应该抛出错误')
})

// 测试组：分类验证功能
runner.test('验证分类名称唯一性 - 基本功能', async () => {
  mockIndexedDB.clear()
  
  // 创建一个分类
  await mockCategoryService.createCategory(mockCategoryInput)
  
  // 验证重复名称
  const isUniqueExisting = await mockCategoryService.validateCategoryName(mockCategoryInput.name)
  runner.assertEqual(isUniqueExisting, false, '已存在的名称应该返回false')
  
  // 验证新名称
  const isUniqueNew = await mockCategoryService.validateCategoryName('新分类名称')
  runner.assertEqual(isUniqueNew, true, '新名称应该返回true')
})

runner.test('验证分类名称唯一性 - 排除指定ID', async () => {
  mockIndexedDB.clear()
  
  const category = await mockCategoryService.createCategory(mockCategoryInput)
  
  // 验证时排除自己的ID（用于更新场景）
  const isUnique = await mockCategoryService.validateCategoryName(mockCategoryInput.name, category.id)
  runner.assertEqual(isUnique, true, '排除自己ID时应该返回true')
})

runner.test('验证分类名称唯一性 - 大小写不敏感', async () => {
  mockIndexedDB.clear()
  
  await mockCategoryService.createCategory({ name: '测试分类' })
  
  const isUnique = await mockCategoryService.validateCategoryName('测试分类')
  runner.assertEqual(isUnique, false, '大小写不同的重复名称应该返回false')
})

// 测试组：分类统计功能
runner.test('获取分类统计信息 - 基本功能', async () => {
  mockIndexedDB.clear()
  
  // 创建分类
  const category1 = await mockCategoryService.createCategory({
    name: '技术',
    description: '技术相关'
  })
  
  const category2 = await mockCategoryService.createCategory({
    name: '生活',
    description: '生活相关'
  })
  
  // 创建书签
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_1',
    category: '技术'
  })
  
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_2',
    category: '技术'
  })
  
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_3',
    category: '生活'
  })
  
  // 获取统计信息
  const categoriesWithStats = await mockCategoryService.getAllCategoriesWithStats()
  
  runner.assertEqual(categoriesWithStats.length, 2, '应该返回2个分类')
  
  const techCategory = categoriesWithStats.find(cat => cat.name === '技术')
  const lifeCategory = categoriesWithStats.find(cat => cat.name === '生活')
  
  runner.assert(techCategory, '应该找到技术分类')
  runner.assert(lifeCategory, '应该找到生活分类')
  runner.assertEqual(techCategory.bookmarkCount, 2, '技术分类应该有2个书签')
  runner.assertEqual(lifeCategory.bookmarkCount, 1, '生活分类应该有1个书签')
})

runner.test('获取分类书签数量 - 按ID查询', async () => {
  mockIndexedDB.clear()
  
  const category = await mockCategoryService.createCategory({
    name: '测试分类',
    description: '用于测试'
  })
  
  // 添加书签
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_1',
    category: '测试分类'
  })
  
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_2',
    category: '测试分类'
  })
  
  const count = await mockCategoryService.getCategoryBookmarkCount(category.id)
  runner.assertEqual(count, 2, '应该返回正确的书签数量')
})

runner.test('获取分类书签数量 - 按名称查询', async () => {
  mockIndexedDB.clear()
  
  // 添加书签
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_1',
    category: '按名称查询'
  })
  
  const count = await mockCategoryService.getCategoryBookmarkCountByName('按名称查询')
  runner.assertEqual(count, 1, '应该返回正确的书签数量')
  
  const zeroCount = await mockCategoryService.getCategoryBookmarkCountByName('不存在的分类')
  runner.assertEqual(zeroCount, 0, '不存在的分类应该返回0')
})

// 测试组：从书签同步分类功能
runner.test('从书签同步分类 - 基本功能', async () => {
  mockIndexedDB.clear()
  
  // 添加书签（包含新分类）
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_1',
    category: '从书签提取1'
  })
  
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_2',
    category: '从书签提取2'
  })
  
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_3',
    category: '从书签提取1' // 重复分类
  })
  
  // 同步分类
  const categories = await mockCategoryService.syncCategoriesFromBookmarks()
  
  runner.assertEqual(categories.length, 2, '应该创建2个新分类')
  
  const categoryNames = categories.map(cat => cat.name).sort()
  runner.assertDeepEqual(categoryNames, ['从书签提取1', '从书签提取2'], '分类名称应该正确')
  
  // 验证分类属性
  const category1 = categories.find(cat => cat.name === '从书签提取1')
  runner.assert(category1.id.startsWith('category_'), 'ID格式应该正确')
  runner.assertEqual(category1.description, '从书签自动提取的分类', '描述应该正确')
  runner.assert(category1.color, '应该有颜色')
  runner.assert(category1.createdAt instanceof Date, '创建时间应该是Date对象')
})

runner.test('从书签同步分类 - 跳过已存在的分类', async () => {
  mockIndexedDB.clear()
  
  // 先创建一个分类
  await mockCategoryService.createCategory({
    name: '已存在分类',
    description: '手动创建的分类'
  })
  
  // 添加书签
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_1',
    category: '已存在分类'
  })
  
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_2',
    category: '新分类'
  })
  
  // 同步分类
  const categories = await mockCategoryService.syncCategoriesFromBookmarks()
  
  runner.assertEqual(categories.length, 2, '应该有2个分类（1个已存在，1个新创建）')
  
  const existingCategory = categories.find(cat => cat.name === '已存在分类')
  const newCategory = categories.find(cat => cat.name === '新分类')
  
  runner.assert(existingCategory, '已存在的分类应该保留')
  runner.assertEqual(existingCategory.description, '手动创建的分类', '已存在分类的描述应该保持不变')
  
  runner.assert(newCategory, '新分类应该被创建')
  runner.assertEqual(newCategory.description, '从书签自动提取的分类', '新分类的描述应该正确')
})

runner.test('从书签同步分类 - 处理空白分类名', async () => {
  mockIndexedDB.clear()
  
  // 添加包含空白分类的书签
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_1',
    category: ''
  })
  
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_2',
    category: '   '
  })
  
  await mockIndexedDB.saveBookmark({
    ...mockBookmark,
    id: 'bookmark_3',
    category: '正常分类'
  })
  
  // 同步分类
  const categories = await mockCategoryService.syncCategoriesFromBookmarks()
  
  runner.assertEqual(categories.length, 1, '应该只创建1个分类（跳过空白分类）')
  runner.assertEqual(categories[0].name, '正常分类', '应该只创建非空分类')
})

// 测试组：工具方法测试
runner.test('生成分类ID格式', () => {
  const id = mockCategoryService.generateCategoryId()
  
  runner.assert(id, '应该生成ID')
  runner.assert(id.startsWith('category_'), 'ID应该以category_开头')
  runner.assert(id.length > 10, 'ID长度应该足够')
})

runner.test('生成分类颜色 - 一致性', () => {
  const color1 = mockCategoryService.generateCategoryColor('测试分类')
  const color2 = mockCategoryService.generateCategoryColor('测试分类')
  
  runner.assertEqual(color1, color2, '相同名称应该生成相同颜色')
  
  const color3 = mockCategoryService.generateCategoryColor('不同分类')
  runner.assert(color1 !== color3, '不同名称应该生成不同颜色')
})

runner.test('生成分类颜色 - 格式验证', () => {
  const color = mockCategoryService.generateCategoryColor('任意分类')
  
  runner.assert(color.startsWith('#'), '颜色应该以#开头')
  runner.assertEqual(color.length, 7, '颜色长度应该是7个字符')
  runner.assert(/^#[0-9A-F]{6}$/i.test(color), '颜色应该是有效的十六进制格式')
})

// 测试组：错误处理和边界情况
runner.test('处理空数据库', async () => {
  mockIndexedDB.clear()
  
  const categoriesWithStats = await mockCategoryService.getAllCategoriesWithStats()
  runner.assertEqual(categoriesWithStats.length, 0, '空数据库应该返回空数组')
  
  const categories = await mockCategoryService.syncCategoriesFromBookmarks()
  runner.assertEqual(categories.length, 0, '没有书签时同步应该返回空数组')
})

runner.test('处理异常情况', async () => {
  mockIndexedDB.clear()
  
  // 测试获取不存在分类的书签数量
  const count = await mockCategoryService.getCategoryBookmarkCount('non-existent-id')
  runner.assertEqual(count, 0, '不存在的分类应该返回0个书签')
})

runner.test('分类名称边界情况', async () => {
  mockIndexedDB.clear()
  
  // 测试极短名称
  const shortCategory = await mockCategoryService.createCategory({
    name: 'A',
    description: '单字符分类'
  })
  runner.assertEqual(shortCategory.name, 'A', '应该支持单字符名称')
  
  // 测试较长名称
  const longName = 'A'.repeat(100)
  const longCategory = await mockCategoryService.createCategory({
    name: longName,
    description: '长名称分类'
  })
  runner.assertEqual(longCategory.name, longName, '应该支持长名称')
})

// 运行所有测试
runner.run().catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})