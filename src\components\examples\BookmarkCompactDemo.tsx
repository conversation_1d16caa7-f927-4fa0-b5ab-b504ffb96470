// BookmarkCompact组件演示页面 - 测试shadcn重构效果

import React, { useState } from 'react'
import BookmarkCompact from '../BookmarkCompact'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'

import type { Bookmark } from '../../types'

// 创建测试数据
const createTestBookmarks = (): Bookmark[] => [
  {
    id: '1',
    type: 'url',
    title: 'React官方文档 - 学习现代Web开发的最佳实践',
    url: 'https://react.dev',
    description: '学习React的官方文档，包含最新的Hooks、并发特性和最佳实践指南。这是每个React开发者必备的参考资料。',
    tags: ['React', 'JavaScript', '前端开发', '官方文档'],
    category: '开发文档',
    favicon: 'https://react.dev/favicon.ico',
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {
      pageTitle: 'React - The library for web and native user interfaces',
      siteName: 'React',
      aiGenerated: false
    }
  },
  {
    id: '2',
    type: 'text',
    title: '重要的代码片段',
    content: `// 这是一个有用的React Hook示例
const useLocalStorage = (key: string, initialValue: any) => {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      return initialValue;
    }
  });
  
  const setValue = (value: any) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(error);
    }
  };
  
  return [storedValue, setValue];
};`,
    description: '一个实用的localStorage Hook，可以在React组件中方便地使用本地存储',
    tags: ['React', 'Hook', 'TypeScript'],
    category: '代码片段',
    createdAt: new Date(Date.now() - 86400000), // 昨天
    updatedAt: new Date(Date.now() - 86400000),
    metadata: {
      aiGenerated: true
    }
  },
  {
    id: '3',
    type: 'url',
    title: 'shadcn/ui',
    url: 'https://ui.shadcn.com',
    description: '基于Radix UI和Tailwind CSS构建的现代化组件库',
    tags: ['UI库', 'Tailwind'],
    category: '设计系统',
    favicon: 'https://ui.shadcn.com/favicon.ico',
    createdAt: new Date(Date.now() - 172800000), // 2天前
    updatedAt: new Date(Date.now() - 172800000),
    metadata: {
      pageTitle: 'shadcn/ui',
      siteName: 'shadcn/ui',
      aiGenerated: false
    }
  },
  {
    id: '4',
    type: 'image',
    title: '设计灵感图片',
    url: 'https://example.com/design-inspiration',
    description: '一些优秀的UI设计案例和灵感收集',
    tags: ['设计', '灵感'],
    category: '设计资源',
    createdAt: new Date(Date.now() - 604800000), // 7天前
    updatedAt: new Date(Date.now() - 604800000),
    metadata: {
      pageTitle: '设计灵感',
      siteName: 'Design Gallery',
      aiGenerated: false
    }
  },
  {
    id: '5',
    type: 'url',
    title: '无分类无标签的收藏',
    url: 'https://example.com/simple',
    description: '这是一个简单的收藏项目，没有分类和标签',
    createdAt: new Date(Date.now() - 1209600000), // 14天前
    updatedAt: new Date(Date.now() - 1209600000),
    metadata: {
      pageTitle: '简单页面',
      siteName: 'Example',
      aiGenerated: false
    }
  },
  {
    id: '6',
    type: 'url',
    title: '无图标的网站',
    url: 'https://no-favicon.example.com',
    description: '这个网站没有favicon图标，会显示默认的星形图标',
    tags: ['测试'],
    category: '测试',
    createdAt: new Date(Date.now() - 2592000000), // 30天前
    updatedAt: new Date(Date.now() - 2592000000),
    metadata: {
      pageTitle: '无图标网站',
      siteName: 'No Favicon',
      aiGenerated: false
    }
  }
]

const BookmarkCompactDemo: React.FC = () => {
  const [bookmarks] = useState<Bookmark[]>(createTestBookmarks())
  const [highlightedId, setHighlightedId] = useState<string | null>(null)
  const [actionLog, setActionLog] = useState<string[]>([])

  // 添加日志
  const addLog = (message: string) => {
    setActionLog(prev => [`${new Date().toLocaleTimeString()}: ${message}`, ...prev.slice(0, 9)])
  }

  // 处理点击事件
  const handleClick = (bookmark: Bookmark) => {
    setHighlightedId(bookmark.id)
    addLog(`点击了收藏: ${bookmark.title}`)
    setTimeout(() => setHighlightedId(null), 2000)
  }

  // 处理编辑事件
  const handleEdit = (bookmark: Bookmark) => {
    addLog(`编辑收藏: ${bookmark.title}`)
  }

  // 处理删除事件
  const handleDelete = (bookmark: Bookmark) => {
    addLog(`删除收藏: ${bookmark.title}`)
  }

  // 清空日志
  const clearLog = () => {
    setActionLog([])
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">BookmarkCompact组件演示</h1>
        <p className="text-muted-foreground mb-4">
          测试重构后的BookmarkCompact组件，验证shadcn组件集成效果
        </p>
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary">shadcn Card</Badge>
          <Badge variant="secondary">shadcn Button</Badge>
          <Badge variant="secondary">shadcn Badge</Badge>
          <Badge variant="secondary">shadcn Tooltip</Badge>
          <Badge variant="outline">重构完成</Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：组件展示区域 */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>组件展示</CardTitle>
              <CardDescription>
                以下是不同类型和状态的BookmarkCompact组件示例
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {bookmarks.map((bookmark) => (
                <BookmarkCompact
                  key={bookmark.id}
                  bookmark={bookmark}
                  isHighlighted={highlightedId === bookmark.id}
                  onClick={handleClick}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  className="w-full"
                />
              ))}
            </CardContent>
          </Card>

          {/* 功能测试区域 */}
          <Card>
            <CardHeader>
              <CardTitle>功能测试</CardTitle>
              <CardDescription>
                测试组件的各种交互功能
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">高亮状态测试</h4>
                  <BookmarkCompact
                    bookmark={bookmarks[0]}
                    isHighlighted={true}
                    className="w-full"
                  />
                </div>
                <div>
                  <h4 className="font-medium mb-2">无操作按钮</h4>
                  <BookmarkCompact
                    bookmark={bookmarks[1]}
                    className="w-full"
                  />
                </div>
              </div>
              
              <hr className="border-border" />
              
              <div>
                <h4 className="font-medium mb-2">文本类型内容预览</h4>
                <BookmarkCompact
                  bookmark={bookmarks[1]}
                  onClick={handleClick}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧：操作日志和说明 */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                操作日志
                <Button variant="outline" size="sm" onClick={clearLog}>
                  清空
                </Button>
              </CardTitle>
              <CardDescription>
                记录组件的交互事件
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {actionLog.length === 0 ? (
                  <p className="text-sm text-muted-foreground">暂无操作记录</p>
                ) : (
                  actionLog.map((log, index) => (
                    <div key={index} className="text-sm p-2 bg-muted rounded text-muted-foreground">
                      {log}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>重构说明</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <h4 className="font-medium mb-1">✅ 已完成的重构</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• 使用shadcn Card组件</li>
                  <li>• 使用shadcn Button组件</li>
                  <li>• 使用shadcn Badge组件</li>
                  <li>• 使用shadcn Tooltip组件</li>
                  <li>• 移除自定义CSS样式</li>
                </ul>
              </div>
              
              <hr className="border-border" />
              
              <div>
                <h4 className="font-medium mb-1">🎨 样式改进</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• 统一的颜色系统</li>
                  <li>• 标准化的交互状态</li>
                  <li>• 更好的可访问性</li>
                  <li>• 主题支持</li>
                </ul>
              </div>
              
              <hr className="border-border" />
              
              <div>
                <h4 className="font-medium mb-1">🔧 测试功能</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• 悬停显示操作按钮</li>
                  <li>• Tooltip提示信息</li>
                  <li>• 点击高亮效果</li>
                  <li>• 不同类型显示</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default BookmarkCompactDemo