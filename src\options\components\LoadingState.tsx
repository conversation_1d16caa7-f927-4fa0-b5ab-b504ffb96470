// 加载状态组件
import React from 'react'
import { Card, CardContent } from '../../components/ui/card'

interface LoadingStateProps {
  retryCount: number
  maxRetries: number
}

const LoadingState: React.FC<LoadingStateProps> = ({ retryCount, maxRetries }) => {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <Card className="p-8">
        <CardContent className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-foreground">正在初始化收藏管理页面...</p>
          {retryCount > 0 && (
            <p className="text-sm text-muted-foreground mt-2">重试次数: {retryCount}/{maxRetries}</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default LoadingState