// 收藏行视图组件 - 实现单行纯文字视图

import React from 'react'
import { ExternalLink, Edit, Trash2, Star, Clock, Tag } from 'lucide-react'
import TruncatedTitle from './TruncatedTitle'
import { Card } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from './ui/tooltip'
import { useTagColors } from '../hooks/useTagColors'
import type { Bookmark } from '../types'

interface BookmarkRowProps {
  /** 收藏数据 */
  bookmark: Bookmark
  /** 是否高亮显示 */
  isHighlighted?: boolean
  /** 编辑回调 */
  onEdit?: (bookmark: Bookmark) => void
  /** 删除回调 */
  onDelete?: (bookmark: Bookmark) => void
  /** 点击回调 */
  onClick?: (bookmark: Bookmark) => void
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 收藏行视图组件
 * 实现单行纯文字视图，仅显示标题、URL和基本操作
 * 设计简洁，适合大量数据的快速浏览
 */
const BookmarkRow: React.FC<BookmarkRowProps> = React.memo(({
  bookmark,
  isHighlighted = false,
  onEdit,
  onDelete,
  onClick,
  className = ''
}) => {
  // 获取标签颜色
  const { getTagColor } = useTagColors()
  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    // 如果点击的是操作按钮，不触发行点击
    if ((e.target as HTMLElement).closest('.bookmark-row-actions')) {
      return
    }
    onClick?.(bookmark)
  }

  // 处理编辑按钮点击
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit?.(bookmark)
  }

  // 处理删除按钮点击
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete?.(bookmark)
  }

  // 处理外部链接点击
  const handleLinkClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (bookmark.url) {
      window.open(bookmark.url, '_blank', 'noopener,noreferrer')
    }
  }

  // 格式化时间显示
  const formatTime = (date: Date | string) => {
    // 确保日期是Date对象
    const dateObj = typeof date === 'string' ? new Date(date) : date
    
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '未知时间'
    }
    
    const now = new Date()
    const diffMs = now.getTime() - dateObj.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return dateObj.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  return (
    <TooltipProvider>
      <Card 
        className={`
          group flex items-center py-2 px-3 border-b hover:bg-accent/50 transition-colors cursor-pointer
          ${isHighlighted ? 'bg-accent border-primary' : ''}
          ${className}
        `}
        onClick={handleClick}
      >
      {/* 网站图标 */}
      <div className="flex-shrink-0 w-4 h-4 mr-3">
        {bookmark.favicon ? (
          <img 
            src={bookmark.favicon} 
            alt="" 
            className="w-4 h-4 rounded-sm"
            onError={(e) => {
              // 图标加载失败时显示默认图标
              const target = e.target as HTMLImageElement
              target.style.display = 'none'
              target.nextElementSibling?.classList.remove('hidden')
            }}
          />
        ) : null}
        <Star className={`w-4 h-4 text-muted-foreground ${bookmark.favicon ? 'hidden' : ''}`} />
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 min-w-0 flex items-center space-x-4">
        {/* 标题 */}
        <div className="flex-1 min-w-0">
          <TruncatedTitle
            title={bookmark.title || '无标题'}
            maxLength={60}
            useContainerWidth={true}
            className="text-sm font-medium text-foreground group-hover:text-primary transition-colors"
          />
        </div>

        {/* URL */}
        {bookmark.url && (
          <div className="flex-shrink-0 max-w-xs">
            <TruncatedTitle
              title={bookmark.url}
              maxLength={40}
              useContainerWidth={true}
              className="text-xs text-muted-foreground hover:text-primary transition-colors"
            />
          </div>
        )}

        {/* 标签 */}
        {bookmark.tags && bookmark.tags.length > 0 && (
          <div className="flex-shrink-0 flex items-center space-x-1 max-w-xs">
            <Tag className="w-3 h-3 text-muted-foreground flex-shrink-0" />
            <div className="flex items-center space-x-1 overflow-hidden">
              {bookmark.tags.slice(0, 2).map((tagName) => (
                <Badge 
                  key={tagName} 
                  variant="outline"
                  className="text-xs flex-shrink-0 border"
                  style={{ 
                    borderColor: getTagColor(tagName),
                    backgroundColor: `${getTagColor(tagName)}15`,
                    color: getTagColor(tagName)
                  }}
                >
                  {tagName}
                </Badge>
              ))}
              {bookmark.tags.length > 2 && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="text-xs text-muted-foreground flex-shrink-0 cursor-help">
                      +{bookmark.tags.length - 2}
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="space-y-1">
                      {bookmark.tags.slice(2).map((tagName) => (
                        <div key={tagName} className="flex items-center space-x-2">
                          <div 
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: getTagColor(tagName) }}
                          />
                          <span>{tagName}</span>
                        </div>
                      ))}
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          </div>
        )}

        {/* 分类标签 */}
        {bookmark.category && (
          <div className="flex-shrink-0">
            <Badge variant="secondary" className="text-xs">
              {bookmark.category}
            </Badge>
          </div>
        )}

        {/* 时间信息 */}
        <div className="flex-shrink-0 flex items-center text-xs text-muted-foreground">
          <Clock className="w-3 h-3 mr-1" />
          <span>{formatTime(bookmark.createdAt)}</span>
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div className="bookmark-row-actions flex-shrink-0 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity ml-4">
        {/* 外部链接按钮 */}
        {bookmark.url && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLinkClick}
                className="h-8 w-8 text-muted-foreground hover:text-primary"
                aria-label="在新标签页中打开"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>在新标签页中打开</p>
            </TooltipContent>
          </Tooltip>
        )}

        {/* 编辑按钮 */}
        {onEdit && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleEditClick}
                className="h-8 w-8 text-muted-foreground hover:text-blue-600"
                aria-label="编辑收藏"
              >
                <Edit className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>编辑收藏</p>
            </TooltipContent>
          </Tooltip>
        )}

        {/* 删除按钮 */}
        {onDelete && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleDeleteClick}
                className="h-8 w-8 text-muted-foreground hover:text-destructive"
                aria-label="删除收藏"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>删除收藏</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
      </Card>
    </TooltipProvider>
  )
})

// 设置显示名称便于调试
BookmarkRow.displayName = 'BookmarkRow'

export default BookmarkRow

// 导出类型定义
export type { BookmarkRowProps }