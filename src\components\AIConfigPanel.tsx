// AI配置面板组件

import React, { useState, useEffect } from 'react'
import { 
  AIConfig, 
  AIProvider, 
  AIProviderPreset, 
  AIConnectionTestResult 
} from '../types/ai'
import { aiConfigService } from '../services/aiConfigService'

interface AIConfigPanelProps {
  onConfigChange?: (config: AIConfig) => void
}

export const AIConfigPanel: React.FC<AIConfigPanelProps> = ({ onConfigChange }) => {
  const [config, setConfig] = useState<AIConfig | null>(null)
  const [presets, setPresets] = useState<AIProviderPreset[]>([])
  const [testResult, setTestResult] = useState<AIConnectionTestResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  // 加载配置和预设
  useEffect(() => {
    loadConfig()
    loadPresets()
  }, [])

  const loadConfig = async () => {
    try {
      setIsLoading(true)
      const currentConfig = await aiConfigService.getConfig()
      setConfig(currentConfig)
      onConfigChange?.(currentConfig)
    } catch (error) {
      console.error('加载AI配置失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadPresets = () => {
    const providerPresets = aiConfigService.getProviderPresets()
    setPresets(providerPresets)
  }

  const handleProviderChange = async (provider: AIProvider) => {
    if (!config) return

    const preset = presets.find(p => p.id === provider)
    if (preset) {
      const updatedConfig = {
        ...config,
        provider,
        baseUrl: preset.baseUrl,
        model: preset.defaultModel,
        availableModels: preset.supportedModels,
        isConnected: false
      }
      
      setConfig(updatedConfig)
      await saveConfig(updatedConfig)
    }
  }

  const handleConfigChange = (field: keyof AIConfig, value: any) => {
    if (!config) return

    const updatedConfig = {
      ...config,
      [field]: value
    }
    
    setConfig(updatedConfig)
  }

  const saveConfig = async (configToSave?: AIConfig) => {
    const targetConfig = configToSave || config
    if (!targetConfig) return

    try {
      setIsLoading(true)
      await aiConfigService.saveConfig(targetConfig)
      onConfigChange?.(targetConfig)
      
      // 清除验证错误
      setValidationErrors([])
      
      console.log('AI配置保存成功')
    } catch (error) {
      console.error('保存AI配置失败:', error)
      setValidationErrors([error.message])
    } finally {
      setIsLoading(false)
    }
  }

  const testConnection = async () => {
    if (!config) return

    try {
      setIsTesting(true)
      setTestResult(null)
      
      const result = await aiConfigService.testConnection(config)
      setTestResult(result)
      
      if (result.success) {
        // 更新配置中的连接状态和可用模型
        const updatedConfig = {
          ...config,
          isConnected: true,
          availableModels: result.availableModels || config.availableModels
        }
        setConfig(updatedConfig)
        onConfigChange?.(updatedConfig)
      }
    } catch (error) {
      console.error('连接测试失败:', error)
      setTestResult({
        success: false,
        error: error.message,
        testedAt: new Date()
      })
    } finally {
      setIsTesting(false)
    }
  }

  const validateConfig = async () => {
    try {
      const validation = await aiConfigService.validateCurrentConfig()
      setValidationErrors(validation.errors)
      return validation.isValid
    } catch (error) {
      setValidationErrors(['配置验证失败'])
      return false
    }
  }

  const resetToDefault = async () => {
    if (confirm('确定要重置为默认配置吗？这将清除所有自定义设置。')) {
      try {
        setIsLoading(true)
        await aiConfigService.resetToDefault()
        await loadConfig()
      } catch (error) {
        console.error('重置配置失败:', error)
      } finally {
        setIsLoading(false)
      }
    }
  }

  const getCurrentPreset = () => {
    return config ? presets.find(p => p.id === config.provider) : null
  }

  if (isLoading && !config) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">加载配置中...</span>
      </div>
    )
  }

  if (!config) {
    return (
      <div className="p-4 text-center text-gray-500">
        无法加载AI配置
      </div>
    )
  }

  const currentPreset = getCurrentPreset()

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">AI服务配置</h3>
        <button
          onClick={resetToDefault}
          className="text-sm text-gray-500 hover:text-gray-700"
        >
          重置为默认
        </button>
      </div>

      {/* 验证错误 */}
      {validationErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex">
            <div className="text-red-400">⚠️</div>
            <div className="ml-2">
              <h4 className="text-sm font-medium text-red-800">配置错误</h4>
              <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* 服务提供商选择 */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          AI服务提供商
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {presets.map((preset) => (
            <div
              key={preset.id}
              className={`relative rounded-lg border p-4 cursor-pointer transition-colors ${
                config.provider === preset.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleProviderChange(preset.id)}
            >
              <div className="flex items-start">
                <div className="text-2xl mr-3">{preset.icon}</div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900">
                    {preset.name}
                  </h4>
                  <p className="text-xs text-gray-500 mt-1">
                    {preset.description}
                  </p>
                  {preset.requiresApiKey && (
                    <span className="inline-block mt-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">
                      需要API密钥
                    </span>
                  )}
                </div>
                {config.provider === preset.id && (
                  <div className="text-blue-500">✓</div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 基础配置 */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900">基础配置</h4>
        
        {/* API基础URL */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            API基础URL
          </label>
          <input
            type="url"
            value={config.baseUrl || ''}
            onChange={(e) => handleConfigChange('baseUrl', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://api.example.com/v1"
          />
          {currentPreset?.documentationUrl && (
            <a
              href={currentPreset.documentationUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 hover:text-blue-800 mt-1 inline-block"
            >
              查看API文档 →
            </a>
          )}
        </div>

        {/* API密钥 */}
        {currentPreset?.requiresApiKey && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              API密钥
            </label>
            <input
              type="password"
              value={config.apiKey || ''}
              onChange={(e) => handleConfigChange('apiKey', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入您的API密钥"
            />
          </div>
        )}

        {/* 模型选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            AI模型
          </label>
          <select
            value={config.model}
            onChange={(e) => handleConfigChange('model', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {(config.availableModels || currentPreset?.supportedModels || [config.model]).map((model) => (
              <option key={model} value={model}>
                {model}
              </option>
            ))}
          </select>
        </div>

        {/* 连接测试 */}
        <div className="flex items-center space-x-3">
          <button
            onClick={testConnection}
            disabled={isTesting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isTesting ? '测试中...' : '测试连接'}
          </button>
          
          {testResult && (
            <div className={`flex items-center text-sm ${
              testResult.success ? 'text-green-600' : 'text-red-600'
            }`}>
              <span className="mr-1">
                {testResult.success ? '✅' : '❌'}
              </span>
              <span>
                {testResult.success 
                  ? `连接成功 (${testResult.responseTime}ms)`
                  : `连接失败: ${testResult.error}`
                }
              </span>
            </div>
          )}
        </div>
      </div>

      {/* 功能开关 */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900">功能开关</h4>
        
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.autoTagging}
              onChange={(e) => handleConfigChange('autoTagging', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">自动标签生成</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.autoCategories}
              onChange={(e) => handleConfigChange('autoCategories', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">自动分类建议</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.autoDescription}
              onChange={(e) => handleConfigChange('autoDescription', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">自动描述生成</span>
          </label>
        </div>
      </div>

      {/* 高级设置 */}
      <div className="space-y-4">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center text-sm text-gray-600 hover:text-gray-800"
        >
          <span className={`mr-1 transition-transform ${showAdvanced ? 'rotate-90' : ''}`}>
            ▶
          </span>
          高级设置
        </button>
        
        {showAdvanced && (
          <div className="space-y-4 pl-4 border-l-2 border-gray-200">
            {/* 温度参数 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                温度参数 ({config.temperature || 0.7})
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={config.temperature || 0.7}
                onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>保守 (0)</span>
                <span>创造性 (2)</span>
              </div>
            </div>

            {/* 最大令牌数 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                最大令牌数
              </label>
              <input
                type="number"
                min="100"
                max="4000"
                value={config.maxTokens || 1000}
                onChange={(e) => handleConfigChange('maxTokens', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 超时时间 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                超时时间 (毫秒)
              </label>
              <input
                type="number"
                min="5000"
                max="120000"
                value={config.timeout || 30000}
                onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        )}
      </div>

      {/* 保存按钮 */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          onClick={validateConfig}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          验证配置
        </button>
        <button
          onClick={() => saveConfig()}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? '保存中...' : '保存配置'}
        </button>
      </div>

      {/* 连接状态指示 */}
      <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t border-gray-100">
        <span>连接状态:</span>
        <span className={`flex items-center ${
          config.isConnected ? 'text-green-600' : 'text-red-600'
        }`}>
          <span className={`w-2 h-2 rounded-full mr-2 ${
            config.isConnected ? 'bg-green-500' : 'bg-red-500'
          }`}></span>
          {config.isConnected ? '已连接' : '未连接'}
        </span>
      </div>
    </div>
  )
}