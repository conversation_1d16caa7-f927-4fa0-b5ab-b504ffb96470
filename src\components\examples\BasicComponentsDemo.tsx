import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter,
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  AlertDialog, 
  AlertDialogContent, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogDescription,
  DialogFooter as AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter 
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';

/**
 * 基础shadcn组件演示组件
 * 展示所有已安装的基础UI组件的使用方法
 */
export const BasicComponentsDemo: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [textareaValue, setTextareaValue] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">shadcn基础组件演示</h1>
      
      {/* Button组件演示 */}
      <Card>
        <CardHeader>
          <CardTitle>Button组件</CardTitle>
          <CardDescription>展示Button组件的各种变体</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="default">默认按钮</Button>
            <Button variant="destructive">危险按钮</Button>
            <Button variant="outline">轮廓按钮</Button>
            <Button variant="secondary">次要按钮</Button>
            <Button variant="ghost">幽灵按钮</Button>
            <Button variant="link">链接按钮</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-4">
            <Button size="sm">小按钮</Button>
            <Button size="default">默认大小</Button>
            <Button size="lg">大按钮</Button>
            <Button size="icon">🔍</Button>
          </div>
        </CardContent>
      </Card>

      {/* Input和Textarea组件演示 */}
      <Card>
        <CardHeader>
          <CardTitle>输入组件</CardTitle>
          <CardDescription>展示Input和Textarea组件</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="demo-input">输入框示例</Label>
            <Input 
              id="demo-input"
              placeholder="请输入内容..." 
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="demo-textarea">文本域示例</Label>
            <Textarea 
              id="demo-textarea"
              placeholder="请输入多行内容..." 
              value={textareaValue}
              onChange={(e) => setTextareaValue(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Dialog组件演示 */}
      <Card>
        <CardHeader>
          <CardTitle>对话框组件</CardTitle>
          <CardDescription>展示Dialog和AlertDialog组件</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">打开对话框</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>对话框标题</DialogTitle>
                  <DialogDescription>
                    这是一个使用shadcn Dialog组件创建的对话框示例。
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <p>对话框内容区域</p>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setDialogOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={() => setDialogOpen(false)}>
                    确认
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">打开警告对话框</Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>确认删除</AlertDialogTitle>
                  <AlertDialogDescription>
                    此操作无法撤销。这将永久删除您的数据。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction>确认删除</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>

      {/* Card组件演示 */}
      <Card>
        <CardHeader>
          <CardTitle>卡片组件</CardTitle>
          <CardDescription>展示Card组件的结构</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>示例卡片1</CardTitle>
                <CardDescription>这是一个示例卡片的描述</CardDescription>
              </CardHeader>
              <CardContent>
                <p>卡片内容区域</p>
              </CardContent>
              <CardFooter>
                <Button size="sm">操作按钮</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>示例卡片2</CardTitle>
                <CardDescription>另一个示例卡片</CardDescription>
              </CardHeader>
              <CardContent>
                <p>更多卡片内容</p>
              </CardContent>
              <CardFooter>
                <Button size="sm" variant="outline">次要操作</Button>
              </CardFooter>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};