<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单分割线测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 模拟扩展的主题样式 */
        .primary-50 { background-color: #eff6ff; }
        .primary-700 { color: #1d4ed8; }
        .primary-200 { border-color: #bfdbfe; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-md mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-center">菜单分割线效果测试</h1>
        
        <!-- 模拟侧边栏菜单 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="mb-3">
                <h2 class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                    功能导航
                </h2>
                <p class="text-xs text-gray-400 mt-1">
                    使用 Alt + 数字键快速切换
                </p>
            </div>
            
            <ul class="space-y-2" role="tablist">
                <!-- 功能菜单项 -->
                <li>
                    <button class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-gray-700 hover:bg-gray-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                        </svg>
                        <span class="font-medium">收藏管理</span>
                        <span class="text-xs text-gray-400 ml-auto">Alt+1</span>
                    </button>
                </li>
                
                <li>
                    <button class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-gray-700 hover:bg-gray-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        <span class="font-medium">分类管理</span>
                        <span class="text-xs text-gray-400 ml-auto">Alt+2</span>
                    </button>
                </li>
                
                <li>
                    <button class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-gray-700 hover:bg-gray-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        <span class="font-medium">标签管理</span>
                        <span class="text-xs text-gray-400 ml-auto">Alt+3</span>
                    </button>
                </li>
                
                <li>
                    <button class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-gray-700 hover:bg-gray-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                        <span class="font-medium">导入导出</span>
                        <span class="text-xs text-gray-400 ml-auto">Alt+4</span>
                    </button>
                </li>
                
                <li>
                    <button class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-gray-700 hover:bg-gray-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="font-medium">设置</span>
                        <span class="text-xs text-gray-400 ml-auto">Alt+5</span>
                    </button>
                </li>
                
                <!-- 分割线 -->
                <li class="my-4">
                    <div class="border-t border-gray-200 mx-2"></div>
                </li>
                
                <!-- 关于我们和帮助中心 -->
                <li>
                    <button class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors bg-primary-50 text-primary-700 border border-primary-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium">关于我们</span>
                        <span class="text-xs text-gray-400 ml-auto">Alt+6</span>
                    </button>
                </li>
                
                <li>
                    <button class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-gray-700 hover:bg-gray-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium">帮助中心</span>
                        <span class="text-xs text-gray-400 ml-auto">Alt+7</span>
                    </button>
                </li>
            </ul>
        </div>
        
        <div class="mt-6 text-center text-sm text-gray-600">
            <p>✅ 分割线已添加在"关于我们"菜单项之前</p>
            <p>📱 在移动端视图中分割线会自动隐藏</p>
        </div>
    </div>
</body>
</html>