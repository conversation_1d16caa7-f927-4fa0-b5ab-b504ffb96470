#!/usr/bin/env node

/**
 * API 提取工具
 * 从 TypeScript 文件中提取函数签名和文档
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

console.log('🔍 提取 API 信息...')

/**
 * 提取函数信息的类
 */
class APIExtractor {
  constructor() {
    this.functions = []
    this.interfaces = []
    this.types = []
  }

  /**
   * 从文件中提取 API 信息
   * @param {string} filePath - 文件路径
   */
  extractFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const fileName = path.basename(filePath)
      
      console.log(`📄 分析文件: ${fileName}`)
      
      // 提取函数
      this.extractFunctions(content, fileName)
      
      // 提取接口
      this.extractInterfaces(content, fileName)
      
      // 提取类型定义
      this.extractTypes(content, fileName)
      
    } catch (error) {
      console.error(`❌ 分析文件失败 ${filePath}:`, error.message)
    }
  }

  /**
   * 提取函数定义
   * @param {string} content - 文件内容
   * @param {string} fileName - 文件名
   */
  extractFunctions(content, fileName) {
    // 匹配函数定义的正则表达式
    const functionRegex = /(?:async\s+)?function\s+(\w+)\s*\([^)]*\)(?:\s*:\s*[^{]+)?/g
    const arrowFunctionRegex = /(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s+)?\([^)]*\)\s*(?::\s*[^=]+)?\s*=>/g
    
    let match
    
    // 提取普通函数
    while ((match = functionRegex.exec(content)) !== null) {
      const functionName = match[1]
      const fullMatch = match[0]
      
      // 获取函数前的注释
      const beforeFunction = content.substring(0, match.index)
      const comment = this.extractComment(beforeFunction)
      
      this.functions.push({
        name: functionName,
        signature: fullMatch,
        comment: comment,
        file: fileName,
        type: 'function'
      })
    }
    
    // 提取箭头函数
    while ((match = arrowFunctionRegex.exec(content)) !== null) {
      const functionName = match[1]
      const fullMatch = match[0]
      
      // 获取函数前的注释
      const beforeFunction = content.substring(0, match.index)
      const comment = this.extractComment(beforeFunction)
      
      this.functions.push({
        name: functionName,
        signature: fullMatch,
        comment: comment,
        file: fileName,
        type: 'arrow-function'
      })
    }
  }

  /**
   * 提取接口定义
   * @param {string} content - 文件内容
   * @param {string} fileName - 文件名
   */
  extractInterfaces(content, fileName) {
    const interfaceRegex = /interface\s+(\w+)\s*{[^}]*}/g
    let match
    
    while ((match = interfaceRegex.exec(content)) !== null) {
      const interfaceName = match[1]
      const fullMatch = match[0]
      
      // 获取接口前的注释
      const beforeInterface = content.substring(0, match.index)
      const comment = this.extractComment(beforeInterface)
      
      this.interfaces.push({
        name: interfaceName,
        definition: fullMatch,
        comment: comment,
        file: fileName
      })
    }
  }

  /**
   * 提取类型定义
   * @param {string} content - 文件内容
   * @param {string} fileName - 文件名
   */
  extractTypes(content, fileName) {
    const typeRegex = /type\s+(\w+)\s*=\s*[^;\n]+/g
    let match
    
    while ((match = typeRegex.exec(content)) !== null) {
      const typeName = match[1]
      const fullMatch = match[0]
      
      // 获取类型前的注释
      const beforeType = content.substring(0, match.index)
      const comment = this.extractComment(beforeType)
      
      this.types.push({
        name: typeName,
        definition: fullMatch,
        comment: comment,
        file: fileName
      })
    }
  }

  /**
   * 提取注释
   * @param {string} beforeCode - 代码前的内容
   * @returns {string} 注释内容
   */
  extractComment(beforeCode) {
    const lines = beforeCode.split('\n')
    const commentLines = []
    
    // 从后往前查找注释
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim()
      
      if (line.startsWith('//')) {
        commentLines.unshift(line.substring(2).trim())
      } else if (line.startsWith('*') || line.startsWith('*/') || line.startsWith('/**')) {
        commentLines.unshift(line.replace(/^[\*\/\s]+/, ''))
      } else if (line === '' || line.startsWith('/*')) {
        continue
      } else {
        break
      }
    }
    
    return commentLines.join(' ').trim()
  }

  /**
   * 生成 API 文档
   * @returns {string} 文档内容
   */
  generateDocumentation() {
    let doc = '# API 提取报告\n\n'
    
    // 函数文档
    if (this.functions.length > 0) {
      doc += '## 函数列表\n\n'
      
      this.functions.forEach(func => {
        doc += `### ${func.name}\n\n`
        doc += `**文件**: ${func.file}\n`
        doc += `**类型**: ${func.type}\n`
        doc += `**签名**: \`${func.signature}\`\n\n`
        
        if (func.comment) {
          doc += `**说明**: ${func.comment}\n\n`
        }
        
        doc += '---\n\n'
      })
    }
    
    // 接口文档
    if (this.interfaces.length > 0) {
      doc += '## 接口列表\n\n'
      
      this.interfaces.forEach(iface => {
        doc += `### ${iface.name}\n\n`
        doc += `**文件**: ${iface.file}\n\n`
        
        if (iface.comment) {
          doc += `**说明**: ${iface.comment}\n\n`
        }
        
        doc += '```typescript\n'
        doc += iface.definition
        doc += '\n```\n\n'
        doc += '---\n\n'
      })
    }
    
    // 类型文档
    if (this.types.length > 0) {
      doc += '## 类型定义\n\n'
      
      this.types.forEach(type => {
        doc += `### ${type.name}\n\n`
        doc += `**文件**: ${type.file}\n\n`
        
        if (type.comment) {
          doc += `**说明**: ${type.comment}\n\n`
        }
        
        doc += '```typescript\n'
        doc += type.definition
        doc += '\n```\n\n'
        doc += '---\n\n'
      })
    }
    
    return doc
  }

  /**
   * 生成统计信息
   * @returns {object} 统计信息
   */
  generateStats() {
    return {
      functions: this.functions.length,
      interfaces: this.interfaces.length,
      types: this.types.length,
      total: this.functions.length + this.interfaces.length + this.types.length
    }
  }
}

/**
 * 主函数
 */
function main() {
  const extractor = new APIExtractor()
  
  // 要分析的文件列表
  const filesToAnalyze = [
    'src/background/index.ts',
    'src/content/index.ts',
    'src/types/index.ts'
  ]
  
  // 分析每个文件
  filesToAnalyze.forEach(file => {
    const filePath = path.resolve(rootDir, file)
    if (fs.existsSync(filePath)) {
      extractor.extractFromFile(filePath)
    } else {
      console.log(`⚠️  文件不存在: ${file}`)
    }
  })
  
  // 生成文档
  const documentation = extractor.generateDocumentation()
  const stats = extractor.generateStats()
  
  // 保存文档
  const outputPath = path.resolve(rootDir, 'docs/api-extraction.md')
  fs.writeFileSync(outputPath, documentation, 'utf8')
  
  // 输出统计信息
  console.log('\n📊 提取统计:')
  console.log(`  函数: ${stats.functions} 个`)
  console.log(`  接口: ${stats.interfaces} 个`)
  console.log(`  类型: ${stats.types} 个`)
  console.log(`  总计: ${stats.total} 个`)
  
  console.log(`\n📝 文档已生成: ${outputPath}`)
  console.log('\n✅ API 提取完成！')
}

// 运行主函数
main()