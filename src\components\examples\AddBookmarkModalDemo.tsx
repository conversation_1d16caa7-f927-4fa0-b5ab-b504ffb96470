// AddBookmarkModal shadcn重构演示组件

import React, { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import AddBookmarkModal from '../AddBookmarkModal'
import { BookmarkInput } from '../../types'

/**
 * AddBookmarkModal演示组件
 * 用于展示重构后的shadcn风格收藏模态窗口
 */
const AddBookmarkModalDemo: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [savedBookmarks, setSavedBookmarks] = useState<BookmarkInput[]>([])
  const [demoType, setDemoType] = useState<'empty' | 'prefilled'>('empty')

  // 模拟保存收藏的函数
  const handleSave = async (bookmark: BookmarkInput) => {
    setLoading(true)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 保存收藏到演示列表
    setSavedBookmarks(prev => [...prev, { ...bookmark, id: Date.now().toString() }])
    
    setLoading(false)
    setIsModalOpen(false)
    
    console.log('保存的收藏:', bookmark)
  }

  // 处理取消操作
  const handleCancel = () => {
    setIsModalOpen(false)
  }

  // 打开空白模态窗口
  const openEmptyModal = () => {
    setDemoType('empty')
    setIsModalOpen(true)
  }

  // 打开预填充数据的模态窗口
  const openPrefilledModal = () => {
    setDemoType('prefilled')
    setIsModalOpen(true)
  }

  // 预填充的演示数据
  const prefilledData: Partial<BookmarkInput> = {
    type: 'url',
    title: 'shadcn/ui - 优雅的React组件库',
    url: 'https://ui.shadcn.com',
    description: '基于Radix UI和Tailwind CSS构建的现代化React组件库，提供了丰富的可复用组件。',
    category: '工具',
    tags: ['React', 'UI组件', 'shadcn', '前端开发']
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* 标题 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">AddBookmarkModal shadcn重构演示</h1>
        <p className="text-muted-foreground">
          展示使用shadcn/ui组件重构后的收藏模态窗口效果
        </p>
      </div>

      {/* 演示控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle>演示控制</CardTitle>
          <CardDescription>
            点击下面的按钮来测试不同场景下的收藏模态窗口
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Button onClick={openEmptyModal} variant="default">
              打开空白收藏窗口
            </Button>
            <Button onClick={openPrefilledModal} variant="outline">
              打开预填充收藏窗口
            </Button>
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p><strong>空白窗口：</strong>测试从零开始创建收藏的体验</p>
            <p><strong>预填充窗口：</strong>测试编辑现有收藏的体验</p>
          </div>
        </CardContent>
      </Card>

      {/* 功能特性展示 */}
      <Card>
        <CardHeader>
          <CardTitle>shadcn重构特性</CardTitle>
          <CardDescription>
            重构后的组件采用了shadcn/ui的标准组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-sm">使用的shadcn组件</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Dialog - 模态窗口容器</li>
                <li>• Form - 表单管理和验证</li>
                <li>• Input - 文本输入框</li>
                <li>• Textarea - 多行文本输入</li>
                <li>• Button - 各种操作按钮</li>
                <li>• Select - 分类选择器</li>
                <li>• Badge - 标签显示</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-sm">保留的功能特性</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 收藏类型切换（URL/文本）</li>
                <li>• 实时表单验证</li>
                <li>• 标签管理（添加/删除）</li>
                <li>• AI建议功能</li>
                <li>• 重复检查</li>
                <li>• 加载状态显示</li>
                <li>• 键盘快捷键支持</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 已保存的收藏展示 */}
      {savedBookmarks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>演示保存的收藏</CardTitle>
            <CardDescription>
              通过模态窗口保存的收藏将显示在这里
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {savedBookmarks.map((bookmark, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold">{bookmark.title}</h4>
                    <span className="text-xs bg-secondary px-2 py-1 rounded">
                      {bookmark.type === 'url' ? '网页链接' : '文本摘录'}
                    </span>
                  </div>
                  {bookmark.url && (
                    <p className="text-sm text-blue-600 hover:underline">
                      <a href={bookmark.url} target="_blank" rel="noopener noreferrer">
                        {bookmark.url}
                      </a>
                    </p>
                  )}
                  {bookmark.description && (
                    <p className="text-sm text-muted-foreground">
                      {bookmark.description}
                    </p>
                  )}
                  {bookmark.content && (
                    <div className="bg-muted p-3 rounded text-sm">
                      {bookmark.content}
                    </div>
                  )}
                  <div className="flex items-center gap-2 text-xs">
                    <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                      {bookmark.category}
                    </span>
                    {bookmark.tags?.map((tag, tagIndex) => (
                      <span key={tagIndex} className="bg-secondary px-2 py-1 rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* AddBookmarkModal组件 */}
      <AddBookmarkModal
        isOpen={isModalOpen}
        onSave={handleSave}
        onCancel={handleCancel}
        loading={loading}
        initialData={demoType === 'prefilled' ? prefilledData : undefined}
      />
    </div>
  )
}

export default AddBookmarkModalDemo