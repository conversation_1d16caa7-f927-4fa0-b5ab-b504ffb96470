/**
 * 帮助中心内容数据
 * 包含使用指南、FAQ和故障排除信息
 */

export interface HelpSection {
  id: string
  title: string
  content: string
  category: 'guide' | 'faq' | 'troubleshooting'
  keywords: string[]
}

export interface HelpCategory {
  id: string
  name: string
  description: string
  icon: string
  order: number
}

export interface HelpContent {
  sections: HelpSection[]
  categories: HelpCategory[]
}

export interface HelpSearchResult {
  section: HelpSection
  relevance: number
  matchedKeywords: string[]
}

// 帮助内容分类
export const helpCategories: HelpCategory[] = [
  {
    id: 'guide',
    name: '使用指南',
    description: '详细的功能使用说明',
    icon: 'book',
    order: 1
  },
  {
    id: 'faq',
    name: '常见问题',
    description: '用户常见问题解答',
    icon: 'help-circle',
    order: 2
  },
  {
    id: 'troubleshooting',
    name: '故障排除',
    description: '问题诊断和解决方案',
    icon: 'tool',
    order: 3
  }
]

// 帮助内容数据
export const helpSections: HelpSection[] = [
  // 使用指南
  {
    id: 'getting-started',
    title: '快速开始',
    content: `
# 快速开始

欢迎使用 Universe Bag（乾坤袋）！这是一个智能收藏管理工具，帮助您更好地组织和管理网页收藏。

## 基本功能

1. **添加收藏**：点击浏览器工具栏中的扩展图标，或使用右键菜单快速收藏当前页面
2. **分类管理**：为收藏内容创建和管理分类，让您的收藏井然有序
3. **标签系统**：使用标签为收藏内容添加关键词，方便快速查找
4. **智能搜索**：支持全文搜索，快速找到您需要的收藏内容
5. **导入导出**：支持从其他浏览器或收藏工具导入数据，也可以导出备份

## 开始使用

1. 安装扩展后，点击工具栏图标打开弹窗界面
2. 访问设置页面进行个性化配置
3. 开始收藏您感兴趣的网页内容
    `,
    category: 'guide',
    keywords: ['开始', '入门', '使用', '基本功能', '收藏', '分类', '标签']
  },
  {
    id: 'bookmark-management',
    title: '收藏管理',
    content: `
# 收藏管理

## 添加收藏

有多种方式可以添加收藏：

1. **扩展弹窗**：点击工具栏图标，在弹窗中点击"添加收藏"
2. **右键菜单**：在网页上右键选择"添加到 Universe Bag"
3. **设置页面**：在收藏管理页面点击"添加收藏"按钮

## 编辑收藏

1. 在收藏列表中找到要编辑的项目
2. 点击编辑按钮（齿轮图标）
3. 修改标题、描述、分类或标签
4. 点击保存完成编辑

## 删除收藏

1. 在收藏列表中找到要删除的项目
2. 点击删除按钮（垃圾桶图标）
3. 确认删除操作

## 批量操作

选择多个收藏项目后，可以进行批量操作：
- 批量删除
- 批量修改分类
- 批量添加标签
    `,
    category: 'guide',
    keywords: ['收藏', '管理', '添加', '编辑', '删除', '批量操作']
  },
  {
    id: 'search-features',
    title: '搜索功能',
    content: `
# 搜索功能

Universe Bag 提供强大的搜索功能，帮助您快速找到需要的收藏内容。

## 基本搜索

在搜索框中输入关键词，系统会在以下内容中搜索：
- 收藏标题
- 网页描述
- 标签
- 分类名称
- 网页内容（如果已抓取）

## 高级搜索

支持以下搜索语法：
- **精确匹配**：使用引号 "关键词"
- **排除关键词**：使用减号 -关键词
- **分类筛选**：category:分类名
- **标签筛选**：tag:标签名
- **日期范围**：date:2024-01-01..2024-12-31

## 搜索建议

系统会根据您的输入提供搜索建议，包括：
- 历史搜索记录
- 相关标签
- 分类名称
    `,
    category: 'guide',
    keywords: ['搜索', '查找', '关键词', '高级搜索', '筛选']
  },

  // 常见问题
  {
    id: 'faq-installation',
    title: '如何安装扩展？',
    content: `
# 如何安装扩展？

## Chrome 浏览器

1. 打开 Chrome 网上应用店
2. 搜索 "Universe Bag" 或 "乾坤袋"
3. 点击"添加至 Chrome"按钮
4. 在弹出的确认对话框中点击"添加扩展程序"
5. 安装完成后，工具栏会出现扩展图标

## Edge 浏览器

1. 打开 Microsoft Edge 加载项商店
2. 搜索 "Universe Bag"
3. 点击"获取"按钮
4. 确认安装

## 手动安装

如果无法从应用商店安装，可以手动安装：
1. 下载扩展文件包
2. 解压到本地文件夹
3. 打开浏览器扩展管理页面
4. 开启"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择解压后的文件夹
    `,
    category: 'faq',
    keywords: ['安装', '扩展', 'Chrome', 'Edge', '手动安装']
  },
  {
    id: 'faq-sync',
    title: '如何同步收藏数据？',
    content: `
# 如何同步收藏数据？

## 浏览器同步

Universe Bag 支持浏览器内置的同步功能：

1. 确保您已登录浏览器账户
2. 在浏览器设置中开启扩展同步
3. 您的收藏数据会自动同步到其他设备

## 导出导入

如果需要手动备份或迁移数据：

1. **导出数据**：
   - 打开设置页面
   - 进入"导入导出"标签
   - 点击"导出收藏"
   - 选择导出格式（JSON、HTML等）

2. **导入数据**：
   - 在新设备上安装扩展
   - 打开设置页面
   - 进入"导入导出"标签
   - 选择要导入的文件
   - 点击"导入"按钮

## 注意事项

- 导入操作会与现有数据合并，不会覆盖
- 建议定期导出数据作为备份
- 大量数据同步可能需要一些时间
    `,
    category: 'faq',
    keywords: ['同步', '备份', '导出', '导入', '数据迁移']
  },

  // 故障排除
  {
    id: 'troubleshooting-not-working',
    title: '扩展无法正常工作',
    content: `
# 扩展无法正常工作

如果扩展出现问题，请尝试以下解决方案：

## 基本排查

1. **检查扩展状态**：
   - 打开浏览器扩展管理页面
   - 确认 Universe Bag 已启用
   - 查看是否有错误提示

2. **重启扩展**：
   - 在扩展管理页面禁用扩展
   - 等待几秒后重新启用
   - 刷新网页后重试

3. **清除缓存**：
   - 右键点击扩展图标
   - 选择"检查弹出窗口"
   - 在开发者工具中清除存储数据

## 高级排查

1. **检查权限**：
   - 确认扩展有必要的权限
   - 在扩展详情页面查看权限设置

2. **查看错误日志**：
   - 打开浏览器开发者工具
   - 查看控制台是否有错误信息
   - 记录错误信息以便反馈

3. **重新安装**：
   - 导出收藏数据（如果可能）
   - 卸载扩展
   - 重新安装最新版本
   - 导入之前的数据

## 联系支持

如果问题仍然存在，请联系我们：
- 邮箱：<EMAIL>
- 提供详细的错误描述和浏览器版本信息
    `,
    category: 'troubleshooting',
    keywords: ['故障', '不工作', '错误', '排查', '修复', '重启']
  }
]

// 默认帮助内容
export const defaultHelpContent: HelpContent = {
  sections: helpSections,
  categories: helpCategories
}