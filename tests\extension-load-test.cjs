/**
 * 扩展加载测试脚本
 * 验证构建后的扩展是否可以正常加载
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始扩展加载测试...\n');

// 检查dist目录
const distPath = path.join(__dirname, '..', 'dist');
if (!fs.existsSync(distPath)) {
    console.log('❌ dist目录不存在，请先运行构建命令');
    process.exit(1);
}

console.log('✅ dist目录存在\n');

// 检查manifest.json
const manifestPath = path.join(distPath, 'manifest.json');
if (fs.existsSync(manifestPath)) {
    try {
        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        console.log('✅ manifest.json 解析成功');
        console.log(`   扩展名称: ${manifest.name}`);
        console.log(`   版本: ${manifest.version}`);
        console.log(`   描述: ${manifest.description}\n`);
        
        // 检查图标路径
        if (manifest.icons) {
            console.log('📁 检查图标文件:');
            Object.entries(manifest.icons).forEach(([size, iconPath]) => {
                const fullPath = path.join(distPath, iconPath);
                if (fs.existsSync(fullPath)) {
                    const stats = fs.statSync(fullPath);
                    console.log(`✅ ${iconPath} (${stats.size} 字节)`);
                } else {
                    console.log(`❌ ${iconPath} 不存在`);
                }
            });
        }
        
    } catch (error) {
        console.log('❌ manifest.json 解析失败:', error.message);
        process.exit(1);
    }
} else {
    console.log('❌ manifest.json 不存在');
    process.exit(1);
}

// 检查必需的文件
console.log('\n📋 检查必需文件:');
const requiredFiles = [
    'src/popup/index.html',
    'src/options/index.html',
    'src/background/index.js',
    'src/content/index.js',
    'src/content/style.css'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} 不存在`);
        allFilesExist = false;
    }
});

if (allFilesExist) {
    console.log('\n🎉 扩展加载测试通过！');
    console.log('\n📋 在Chrome中加载扩展的步骤:');
    console.log('1. 打开Chrome浏览器');
    console.log('2. 访问 chrome://extensions/');
    console.log('3. 开启右上角的"开发者模式"');
    console.log('4. 点击"加载已解压的扩展程序"');
    console.log('5. 选择项目的 dist 文件夹');
    console.log('6. 扩展应该会出现在工具栏中，显示新的logo图标');
    
    console.log('\n🔧 如果遇到问题:');
    console.log('- 检查浏览器控制台是否有错误信息');
    console.log('- 确认所有文件权限正确');
    console.log('- 尝试重新构建: npm run build');
} else {
    console.log('\n❌ 扩展加载测试失败，缺少必需文件');
    process.exit(1);
}