/**
 * 智谱AI提供商服务测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'
import { AIProviderConfig } from '../src/types/ai'

// 模拟fetch
global.fetch = vi.fn()

describe('AIProviderService - 智谱AI集成', () => {
  let service: AIProviderService
  const mockFetch = global.fetch as ReturnType<typeof vi.fn>

  beforeEach(() => {
    service = new AIProviderService()
    mockFetch.mockClear()
  })

  describe('testZhipuConnection', () => {
    const baseUrl = 'https://open.bigmodel.cn/api/paas/v4'
    const apiKey = 'test-api-key'

    it('应该成功测试智谱AI连接', async () => {
      // 模拟成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            { id: 'glm-4', object: 'model' },
            { id: 'glm-4v', object: 'model' },
            { id: 'glm-3-turbo', object: 'model' }
          ]
        })
      } as Response)

      const result = await service.testZhipuConnection(baseUrl, apiKey)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(3)
      expect(result.error).toBeUndefined()

      // 验证API调用
      expect(mockFetch).toHaveBeenCalledWith(
        'https://open.bigmodel.cn/api/paas/v4/models',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Authorization': 'Bearer test-api-key',
            'Content-Type': 'application/json'
          }
        })
      )
    })

    it('应该处理空API密钥错误', async () => {
      const result = await service.testZhipuConnection(baseUrl, '')

      expect(result.success).toBe(false)
      expect(result.error).toBe('智谱AI API密钥不能为空')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理401未授权错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await service.testZhipuConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥无效或已过期')
    })

    it('应该处理403权限不足错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      } as Response)

      const result = await service.testZhipuConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥权限不足')
    })

    it('应该处理429频率限制错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response)

      const result = await service.testZhipuConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API请求频率超限，请稍后重试')
    })

    it('应该处理500服务器错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const result = await service.testZhipuConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('智谱AI服务器错误，请稍后重试')
    })

    it('应该处理网络连接超时', async () => {
      const abortError = new Error('AbortError')
      abortError.name = 'AbortError'
      mockFetch.mockRejectedValueOnce(abortError)

      const result = await service.testZhipuConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('连接超时，请检查网络连接')
    })

    it('应该处理DNS解析失败', async () => {
      mockFetch.mockRejectedValueOnce(new Error('ENOTFOUND open.bigmodel.cn'))

      const result = await service.testZhipuConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('DNS解析失败，请检查网络连接')
    })

    it('应该正确处理末尾有斜杠的baseUrl', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: [] })
      } as Response)

      await service.testZhipuConnection('https://open.bigmodel.cn/api/paas/v4/', apiKey)

      expect(mockFetch).toHaveBeenCalledWith(
        'https://open.bigmodel.cn/api/paas/v4/models',
        expect.any(Object)
      )
    })
  })

  describe('getZhipuModels', () => {
    it('应该返回智谱AI模型列表', async () => {
      const models = await service.getZhipuModels()

      expect(models).toHaveLength(5)
      
      // 验证GLM-4模型
      const glm4Model = models.find(m => m.id === 'glm-4')
      expect(glm4Model).toBeDefined()
      expect(glm4Model?.name).toBe('glm-4')
      expect(glm4Model?.displayName).toBe('GLM-4')
      expect(glm4Model?.description).toContain('智谱AI最新一代')
      expect(glm4Model?.capabilities).toContain('chat')
      expect(glm4Model?.capabilities).toContain('completion')
      expect(glm4Model?.capabilities).toContain('reasoning')
      expect(glm4Model?.capabilities).toContain('code')
      expect(glm4Model?.tags).toContain('智谱AI')
      expect(glm4Model?.tags).toContain('GLM')
      expect(glm4Model?.providerId).toBe('zhipu')
      expect(glm4Model?.isRecommended).toBe(true)
      expect(glm4Model?.isPopular).toBe(true)
      expect(glm4Model?.maxTokens).toBe(128000)
      expect(glm4Model?.contextLength).toBe(128000)

      // 验证GLM-4V模型
      const glm4vModel = models.find(m => m.id === 'glm-4v')
      expect(glm4vModel).toBeDefined()
      expect(glm4vModel?.name).toBe('glm-4v')
      expect(glm4vModel?.displayName).toBe('GLM-4V')
      expect(glm4vModel?.description).toContain('多模态模型')
      expect(glm4vModel?.capabilities).toContain('vision')
      expect(glm4vModel?.capabilities).toContain('multimodal')
      expect(glm4vModel?.tags).toContain('多模态')
      expect(glm4vModel?.tags).toContain('图像理解')
      expect(glm4vModel?.providerId).toBe('zhipu')
      expect(glm4vModel?.isRecommended).toBe(true)
      expect(glm4vModel?.isPopular).toBe(false)

      // 验证GLM-3 Turbo模型
      const glm3TurboModel = models.find(m => m.id === 'glm-3-turbo')
      expect(glm3TurboModel).toBeDefined()
      expect(glm3TurboModel?.name).toBe('glm-3-turbo')
      expect(glm3TurboModel?.displayName).toBe('GLM-3 Turbo')
      expect(glm3TurboModel?.description).toContain('高性能对话模型')
      expect(glm3TurboModel?.tags).toContain('高速')
      expect(glm3TurboModel?.providerId).toBe('zhipu')
      expect(glm3TurboModel?.isRecommended).toBe(false)
      expect(glm3TurboModel?.isPopular).toBe(true)

      // 验证ChatGLM3-6B模型
      const chatglm3Model = models.find(m => m.id === 'chatglm3-6b')
      expect(chatglm3Model).toBeDefined()
      expect(chatglm3Model?.name).toBe('chatglm3-6b')
      expect(chatglm3Model?.displayName).toBe('ChatGLM3-6B')
      expect(chatglm3Model?.description).toContain('开源对话模型')
      expect(chatglm3Model?.capabilities).toContain('function-calling')
      expect(chatglm3Model?.tags).toContain('开源')
      expect(chatglm3Model?.tags).toContain('工具调用')
      expect(chatglm3Model?.providerId).toBe('zhipu')

      // 验证CodeGeeX2-6B模型
      const codegeexModel = models.find(m => m.id === 'codegeex2-6b')
      expect(codegeexModel).toBeDefined()
      expect(codegeexModel?.name).toBe('codegeex2-6b')
      expect(codegeexModel?.displayName).toBe('CodeGeeX2-6B')
      expect(codegeexModel?.description).toContain('代码生成模型')
      expect(codegeexModel?.capabilities).toContain('code')
      expect(codegeexModel?.capabilities).toContain('generation')
      expect(codegeexModel?.tags).toContain('代码生成')
      expect(codegeexModel?.tags).toContain('编程')
      expect(codegeexModel?.providerId).toBe('zhipu')
    })

    it('应该在出错时返回空数组', async () => {
      // 模拟控制台错误输出
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // 模拟getZhipuModels内部出错（虽然当前实现不会出错）
      const originalConsoleLog = console.log
      console.log = vi.fn(() => {
        throw new Error('测试错误')
      })

      const models = await service.getZhipuModels()

      expect(models).toEqual([])
      
      // 恢复console.log
      console.log = originalConsoleLog
      consoleSpy.mockRestore()
    })
  })

  describe('集成测试', () => {
    it('应该能够通过AIProviderService.testConnection调用智谱AI测试', async () => {
      const config: AIProviderConfig = {
        id: 'zhipu-test',
        name: '智谱AI Test',
        type: 'zhipu',
        baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
        apiKey: 'test-api-key',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            { id: 'glm-4', object: 'model' },
            { id: 'glm-4v', object: 'model' }
          ]
        })
      } as Response)

      const result = await service.testConnection(config)

      expect(result.success).toBe(true)
      expect(result.providerId).toBe('zhipu-test')
      expect(result.modelCount).toBe(2)
      expect(result.responseTime).toBeGreaterThanOrEqual(0)
      expect(result.testedAt).toBeInstanceOf(Date)
    })

    it('应该能够通过AIProviderService.getModels调用智谱AI模型获取', async () => {
      const config: AIProviderConfig = {
        id: 'zhipu-test',
        name: '智谱AI Test',
        type: 'zhipu',
        baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
        apiKey: 'test-api-key',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await service.getModels(config)

      expect(models).toHaveLength(5)
      expect(models[0].providerId).toBe('zhipu')
      expect(models.every(m => m.providerId === 'zhipu')).toBe(true)
    })
  })
})