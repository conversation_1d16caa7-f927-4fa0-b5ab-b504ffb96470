# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建产物
dist/
build/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器和IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 测试覆盖率报告
coverage/
.nyc_output/

# TypeScript编译缓存
*.tsbuildinfo

# 包管理器锁文件（根据团队约定选择保留一个）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Chrome扩展开发文件
*.crx
*.pem

# .VsCodeCounter
.VsCodeCounter/
