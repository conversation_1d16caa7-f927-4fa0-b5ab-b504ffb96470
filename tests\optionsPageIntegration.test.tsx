// 选项页面集成测试

import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi } from 'vitest'
import OptionsPageErrorBoundary from '../src/components/OptionsPageErrorBoundary'
import OptionsApp from '../src/options/OptionsApp'

// 模拟Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// 设置全局Chrome对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

// 模拟window.location
const mockLocation = {
  hash: '',
  reload: vi.fn()
}

Object.defineProperty(global.window, 'location', {
  value: mockLocation,
  writable: true
})

// 模拟localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

describe('选项页面集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocation.hash = ''
    mockLocalStorage.getItem.mockReturnValue('[]')
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('应该完整加载选项页面而不出现白屏', async () => {
    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面完全加载
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      expect(screen.getByText('智能收藏管理工具')).toBeInTheDocument()
    }, { timeout: 5000 })

    // 验证主要导航元素存在
    expect(screen.getByRole('button', { name: /收藏管理/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /分类管理/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /标签管理/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /导入导出/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /设置/ })).toBeInTheDocument()

    // 验证没有错误信息
    expect(screen.queryByText('页面加载失败')).not.toBeInTheDocument()
    expect(screen.queryByText('初始化失败')).not.toBeInTheDocument()
  })

  it('应该正确处理标签页切换', async () => {
    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 默认应该显示收藏管理标签页
    const bookmarksTab = screen.getByRole('button', { name: /收藏管理/ })
    expect(bookmarksTab).toHaveClass('bg-primary-50')

    // 切换到设置标签页
    const settingsTab = screen.getByRole('button', { name: /设置/ })
    fireEvent.click(settingsTab)

    // 验证标签页切换成功
    await waitFor(() => {
      expect(settingsTab).toHaveClass('bg-primary-50')
      expect(bookmarksTab).not.toHaveClass('bg-primary-50')
    })

    // 验证设置页面内容显示
    expect(screen.getByRole('heading', { name: '设置' })).toBeInTheDocument()
  })

  it('应该正确处理收藏数据加载', async () => {
    const mockBookmarks = [
      {
        id: '1',
        title: '测试收藏1',
        url: 'https://example1.com',
        category: '测试分类',
        tags: ['测试'],
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        title: '测试收藏2',
        url: 'https://example2.com',
        category: '测试分类',
        tags: ['测试'],
        createdAt: new Date().toISOString()
      }
    ]

    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: mockBookmarks
    })

    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 验证收藏数据加载
    await waitFor(() => {
      expect(screen.getByText('共找到 2 个收藏')).toBeInTheDocument()
    }, { timeout: 3000 })

    // 验证收藏项显示
    expect(screen.getByText('测试收藏1')).toBeInTheDocument()
    expect(screen.getByText('测试收藏2')).toBeInTheDocument()
  })

  it('应该正确处理收藏数据加载失败', async () => {
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: false,
      error: '获取收藏数据失败'
    })

    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 验证空状态显示
    await waitFor(() => {
      expect(screen.getByText('暂无收藏内容')).toBeInTheDocument()
    }, { timeout: 3000 })
  })

  it('应该正确处理搜索功能', async () => {
    const mockBookmarks = [
      {
        id: '1',
        title: 'React 教程',
        url: 'https://react.example.com',
        category: '学习',
        tags: ['React', '前端'],
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        title: 'Vue 指南',
        url: 'https://vue.example.com',
        category: '学习',
        tags: ['Vue', '前端'],
        createdAt: new Date().toISOString()
      }
    ]

    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: mockBookmarks
    })

    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 等待收藏数据加载
    await waitFor(() => {
      expect(screen.getByText('共找到 2 个收藏')).toBeInTheDocument()
    })

    // 进行搜索
    const searchInput = screen.getByPlaceholderText('搜索收藏...')
    fireEvent.change(searchInput, { target: { value: 'React' } })

    // 验证搜索结果
    await waitFor(() => {
      expect(screen.getByText('React 教程')).toBeInTheDocument()
      expect(screen.queryByText('Vue 指南')).not.toBeInTheDocument()
    })
  })

  it('应该正确处理分类筛选', async () => {
    const mockBookmarks = [
      {
        id: '1',
        title: '工作文档',
        url: 'https://work.example.com',
        category: '工作',
        tags: ['文档'],
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        title: '学习资料',
        url: 'https://study.example.com',
        category: '学习',
        tags: ['资料'],
        createdAt: new Date().toISOString()
      }
    ]

    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: mockBookmarks
    })

    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 等待收藏数据加载
    await waitFor(() => {
      expect(screen.getByText('共找到 2 个收藏')).toBeInTheDocument()
    })

    // 选择工作分类
    const categorySelect = screen.getByDisplayValue('所有分类')
    fireEvent.change(categorySelect, { target: { value: '工作' } })

    // 验证筛选结果
    await waitFor(() => {
      expect(screen.getByText('工作文档')).toBeInTheDocument()
      expect(screen.queryByText('学习资料')).not.toBeInTheDocument()
    })
  })

  it('应该正确处理视图模式切换', async () => {
    const mockBookmarks = [
      {
        id: '1',
        title: '测试收藏',
        url: 'https://test.example.com',
        category: '测试',
        tags: ['测试'],
        createdAt: new Date().toISOString()
      }
    ]

    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: mockBookmarks
    })

    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 等待收藏数据加载
    await waitFor(() => {
      expect(screen.getByText('共找到 1 个收藏')).toBeInTheDocument()
    })

    // 查找视图模式选择器（如果存在）
    const viewModeButtons = screen.queryAllByRole('button')
    const compactViewButton = viewModeButtons.find(button => 
      button.getAttribute('title')?.includes('紧凑') || 
      button.textContent?.includes('紧凑')
    )

    if (compactViewButton) {
      fireEvent.click(compactViewButton)
      
      // 验证视图模式切换（这里只是确保没有错误）
      await waitFor(() => {
        expect(screen.getByText('测试收藏')).toBeInTheDocument()
      })
    }
  })

  it('应该正确处理错误边界', async () => {
    // 创建一个会抛出错误的组件
    const ErrorComponent: React.FC = () => {
      throw new Error('测试错误边界')
    }

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <OptionsPageErrorBoundary>
        <ErrorComponent />
      </OptionsPageErrorBoundary>
    )

    // 验证错误边界捕获错误
    await waitFor(() => {
      expect(screen.getByText('页面加载失败')).toBeInTheDocument()
      expect(screen.getByText('收藏管理页面遇到了问题，无法正常显示')).toBeInTheDocument()
    })

    // 验证错误详情显示
    expect(screen.getByText('未知错误')).toBeInTheDocument()
    expect(screen.getByText('测试错误边界')).toBeInTheDocument()

    // 验证操作按钮存在
    expect(screen.getByText(/重试/)).toBeInTheDocument()
    expect(screen.getByText('刷新页面')).toBeInTheDocument()
    expect(screen.getByText('返回主页')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('应该正确处理URL hash参数', async () => {
    mockLocation.hash = '#settings'

    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 验证设置标签页被激活
    const settingsTab = screen.getByRole('button', { name: /设置/ })
    expect(settingsTab).toHaveClass('bg-primary-50')
  })

  it('应该正确处理高亮参数', async () => {
    // 模拟URL搜索参数
    const originalURLSearchParams = global.URLSearchParams
    global.URLSearchParams = vi.fn().mockImplementation(() => ({
      get: vi.fn().mockReturnValue('test-bookmark-id')
    }))

    const mockBookmarks = [
      {
        id: 'test-bookmark-id',
        title: '高亮收藏',
        url: 'https://highlight.example.com',
        category: '测试',
        tags: ['高亮'],
        createdAt: new Date().toISOString()
      }
    ]

    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: mockBookmarks
    })

    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 等待收藏数据加载
    await waitFor(() => {
      expect(screen.getByText('高亮收藏')).toBeInTheDocument()
    })

    // 恢复原始URLSearchParams
    global.URLSearchParams = originalURLSearchParams
  })

  it('应该正确处理Chrome扩展API错误', async () => {
    // 临时移除Chrome API
    const originalChrome = global.chrome
    // @ts-ignore
    global.chrome = undefined

    render(
      <OptionsPageErrorBoundary>
        <OptionsApp />
      </OptionsPageErrorBoundary>
    )

    // 验证错误状态显示
    await waitFor(() => {
      expect(screen.getByText('初始化失败')).toBeInTheDocument()
      expect(screen.getByText('Chrome扩展API不可用，请确保在扩展环境中运行')).toBeInTheDocument()
    })

    // 验证重试按钮存在
    expect(screen.getByText(/重试/)).toBeInTheDocument()
    expect(screen.getByText('刷新页面')).toBeInTheDocument()

    // 恢复Chrome API
    global.chrome = originalChrome
  })
})