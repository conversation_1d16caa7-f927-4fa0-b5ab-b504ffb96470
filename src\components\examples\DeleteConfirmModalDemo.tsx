// DeleteConfirmModal shadcn重构演示组件

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import DeleteConfirmModal from '../DeleteConfirmModal'

/**
 * DeleteConfirmModal shadcn重构演示组件
 * 展示使用shadcn AlertDialog重构后的删除确认模态窗口
 */
const DeleteConfirmModalDemo: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedBookmark, setSelectedBookmark] = useState<any>(null)

  // 示例收藏数据
  const sampleBookmarks = [
    {
      id: 'url-bookmark',
      title: 'React官方文档 - 学习现代Web开发的最佳实践',
      url: 'https://react.dev/learn',
      type: 'url' as const,
      category: '技术文档',
      tags: ['React', 'JavaScript', '前端开发'],
      createdAt: new Date('2024-01-15T10:30:00Z')
    },
    {
      id: 'text-bookmark',
      title: '重要会议记录',
      content: '今天的产品会议讨论了新功能的开发计划，包括用户界面改进、性能优化和新的API集成。团队决定采用shadcn/ui作为新的UI组件库，以提高开发效率和用户体验。',
      type: 'text' as const,
      category: '工作笔记',
      tags: ['会议', '产品', '开发计划'],
      createdAt: new Date('2024-01-10T14:20:00Z')
    },
    {
      id: 'image-bookmark',
      title: '设计灵感图片',
      url: 'https://example.com/design-inspiration.jpg',
      type: 'image' as const,
      category: '设计',
      tags: ['UI设计', '灵感', '配色'],
      createdAt: new Date('2024-01-08T09:15:00Z')
    }
  ]

  // 处理删除确认
  const handleConfirm = async (bookmarkId: string) => {
    setLoading(true)
    
    // 模拟删除操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    console.log('删除收藏:', bookmarkId)
    setLoading(false)
    setIsOpen(false)
    setSelectedBookmark(null)
  }

  // 处理取消
  const handleCancel = () => {
    setIsOpen(false)
    setSelectedBookmark(null)
  }

  // 打开删除确认对话框
  const openDeleteModal = (bookmark: any) => {
    setSelectedBookmark(bookmark)
    setIsOpen(true)
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">DeleteConfirmModal shadcn重构演示</h1>
        <p className="text-muted-foreground">
          展示使用shadcn AlertDialog重构后的删除确认模态窗口，支持不同类型的收藏项目。
        </p>
      </div>

      {/* 功能特性说明 */}
      <div className="mb-8 p-4 bg-muted/50 rounded-lg border">
        <h2 className="text-lg font-semibold mb-3">shadcn重构特性</h2>
        <ul className="space-y-2 text-sm">
          <li className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>使用shadcn AlertDialog组件替换自定义模态窗口</span>
          </li>
          <li className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>使用shadcn Button组件的destructive变体</span>
          </li>
          <li className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>应用shadcn标准确认对话框模式</span>
          </li>
          <li className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>移除自定义CSS样式，使用shadcn原生样式</span>
          </li>
          <li className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>保持原有功能逻辑和用户体验</span>
          </li>
        </ul>
      </div>

      {/* 示例收藏列表 */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">示例收藏项目</h2>
        <p className="text-sm text-muted-foreground mb-4">
          点击删除按钮查看shadcn重构后的删除确认对话框
        </p>
        
        {sampleBookmarks.map((bookmark) => (
          <div key={bookmark.id} className="p-4 border rounded-lg bg-card">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-medium text-card-foreground mb-1">
                  {bookmark.title}
                </h3>
                
                {bookmark.url && (
                  <p className="text-sm text-primary mb-2">
                    {bookmark.url}
                  </p>
                )}
                
                {bookmark.content && (
                  <p className="text-sm text-muted-foreground mb-2 bg-muted/30 p-2 rounded">
                    {bookmark.content}
                  </p>
                )}
                
                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                  <span>类型: {
                    bookmark.type === 'url' ? '网页收藏' :
                    bookmark.type === 'text' ? '文本摘录' :
                    bookmark.type === 'image' ? '图片收藏' : '收藏'
                  }</span>
                  <span>分类: {bookmark.category}</span>
                  <span>创建: {bookmark.createdAt.toLocaleDateString('zh-CN')}</span>
                </div>
                
                {bookmark.tags && bookmark.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {bookmark.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary text-secondary-foreground"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
              
              <Button
                variant="destructive"
                size="sm"
                onClick={() => openDeleteModal(bookmark)}
                className="ml-4"
              >
                删除
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* 测试选项 */}
      <div className="mt-8 p-4 bg-muted/50 rounded-lg border">
        <h2 className="text-lg font-semibold mb-3">测试选项</h2>
        <div className="space-y-2">
          <Button
            variant="outline"
            onClick={() => openDeleteModal({
              ...sampleBookmarks[0],
              tags: ['标签1', '标签2', '标签3', '标签4', '标签5', '标签6']
            })}
          >
            测试多标签显示
          </Button>
          
          <Button
            variant="outline"
            onClick={() => openDeleteModal({
              ...sampleBookmarks[0],
              title: '',
              tags: []
            })}
            className="ml-2"
          >
            测试无标题无标签
          </Button>
        </div>
      </div>

      {/* DeleteConfirmModal组件 */}
      <DeleteConfirmModal
        isOpen={isOpen}
        bookmark={selectedBookmark}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        loading={loading}
        enableUndo={true}
      />
    </div>
  )
}

export default DeleteConfirmModalDemo