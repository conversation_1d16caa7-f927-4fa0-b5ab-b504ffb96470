// AI配置服务单元测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { aiConfigService } from '../src/services/aiConfigService'
import { ChromeStorageService } from '../src/utils/chromeStorage'

// Mock Chrome Storage Service
vi.mock('../src/utils/chromeStorage', () => ({
  ChromeStorageService: {
    getSyncSetting: vi.fn(),
    saveSyncSetting: vi.fn(),
    removeSyncSetting: vi.fn()
  }
}))

// Mock fetch API
global.fetch = vi.fn()

describe('AIConfigService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getConfig', () => {
    it('应该返回默认配置当存储为空时', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue(null)

      const config = await aiConfigService.getConfig()

      expect(config).toMatchObject({
        provider: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        autoTagging: true,
        autoCategories: true,
        autoDescription: false,
        isConnected: false
      })
    })

    it('应该返回存储的配置', async () => {
      const storedConfig = {
        provider: 'claude',
        baseUrl: 'https://api.anthropic.com/v1',
        model: 'claude-3-sonnet-20240229',
        apiKey: 'test-key',
        autoTagging: false,
        autoCategories: true,
        autoDescription: true,
        isConnected: true
      }

      ChromeStorageService.getSyncSetting.mockResolvedValue(storedConfig)

      const config = await aiConfigService.getConfig()

      expect(config).toMatchObject(storedConfig)
    })

    it('应该处理获取配置时的错误', async () => {
      ChromeStorageService.getSyncSetting.mockRejectedValue(new Error('存储错误'))

      const config = await aiConfigService.getConfig()

      // 应该返回默认配置
      expect(config.provider).toBe('openai')
    })
  })

  describe('saveConfig', () => {
    it('应该成功保存配置', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        model: 'gpt-3.5-turbo'
      })
      ChromeStorageService.saveSyncSetting.mockResolvedValue()

      const newConfig = {
        provider: 'claude',
        apiKey: 'new-key'
      }

      await aiConfigService.saveConfig(newConfig)

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_config',
        expect.objectContaining({
          provider: 'claude',
          apiKey: 'new-key'
        })
      )
    })

    it('应该验证配置并抛出错误对于无效配置', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({})

      const invalidConfig = {
        provider: 'invalid-provider'
      }

      await expect(aiConfigService.saveConfig(invalidConfig))
        .rejects.toThrow('不支持的AI服务提供商')
    })

    it('应该验证URL格式', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({})

      const invalidConfig = {
        baseUrl: 'invalid-url'
      }

      await expect(aiConfigService.saveConfig(invalidConfig))
        .rejects.toThrow('API基础URL格式无效')
    })

    it('应该验证温度参数范围', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({})

      const invalidConfig = {
        temperature: 3.0
      }

      await expect(aiConfigService.saveConfig(invalidConfig))
        .rejects.toThrow('温度参数应在0-2之间')
    })
  })

  describe('getProviderPresets', () => {
    it('应该返回所有预设配置', () => {
      const presets = aiConfigService.getProviderPresets()

      expect(presets).toHaveLength(4)
      expect(presets.map(p => p.id)).toEqual(['openai', 'claude', 'gemini', 'local'])
    })
  })

  describe('getProviderPreset', () => {
    it('应该返回指定提供商的预设配置', () => {
      const preset = aiConfigService.getProviderPreset('openai')

      expect(preset).toMatchObject({
        id: 'openai',
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        defaultModel: 'gpt-3.5-turbo',
        requiresApiKey: true
      })
    })

    it('应该返回null对于不存在的提供商', () => {
      const preset = aiConfigService.getProviderPreset('nonexistent')

      expect(preset).toBeNull()
    })
  })

  describe('applyPreset', () => {
    it('应该应用预设配置', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({})
      ChromeStorageService.saveSyncSetting.mockResolvedValue()

      await aiConfigService.applyPreset('claude', 'test-api-key')

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_config',
        expect.objectContaining({
          provider: 'claude',
          baseUrl: 'https://api.anthropic.com/v1',
          model: 'claude-3-sonnet-20240229',
          apiKey: 'test-api-key',
          isConnected: false
        })
      )
    })

    it('应该抛出错误对于不存在的预设', async () => {
      await expect(aiConfigService.applyPreset('nonexistent'))
        .rejects.toThrow('未找到提供商预设')
    })
  })

  describe('testConnection', () => {
    beforeEach(() => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: 'test-key',
        model: 'gpt-3.5-turbo'
      })
      ChromeStorageService.saveSyncSetting.mockResolvedValue()
    })

    it('应该成功测试OpenAI连接', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: 'gpt-3.5-turbo' },
            { id: 'gpt-4' }
          ]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const result = await aiConfigService.testConnection()

      expect(result.success).toBe(true)
      expect(result.availableModels).toEqual(['gpt-3.5-turbo', 'gpt-4'])
      expect(result.responseTime).toBeGreaterThan(0)
    })

    it('应该处理连接失败', async () => {
      fetch.mockRejectedValue(new Error('网络错误'))

      const result = await aiConfigService.testConnection()

      expect(result.success).toBe(false)
      expect(result.error).toBe('网络错误')
      expect(result.errorCode).toBe('CONNECTION_FAILED')
    })

    it('应该处理HTTP错误响应', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      }
      fetch.mockResolvedValue(mockResponse)

      const result = await aiConfigService.testConnection()

      expect(result.success).toBe(false)
      expect(result.error).toContain('HTTP 401')
    })

    it('应该测试Claude连接', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'claude',
        baseUrl: 'https://api.anthropic.com/v1',
        apiKey: 'test-key',
        model: 'claude-3-sonnet-20240229'
      })

      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          content: [{ text: 'Hello' }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const result = await aiConfigService.testConnection()

      expect(result.success).toBe(true)
      expect(fetch).toHaveBeenCalledWith(
        'https://api.anthropic.com/v1/messages',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'x-api-key': 'test-key',
            'anthropic-version': '2023-06-01'
          })
        })
      )
    })

    it('应该测试本地模型连接', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'local',
        baseUrl: 'http://localhost:11434/v1',
        model: 'llama2'
      })

      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          models: [
            { name: 'llama2' },
            { name: 'codellama' }
          ]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const result = await aiConfigService.testConnection()

      expect(result.success).toBe(true)
      expect(result.availableModels).toEqual(['llama2', 'codellama'])
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:11434/api/tags',
        expect.objectContaining({
          method: 'GET'
        })
      )
    })
  })

  describe('getAvailableModels', () => {
    it('应该返回缓存的模型列表', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        availableModels: ['gpt-3.5-turbo', 'gpt-4']
      })

      const models = await aiConfigService.getAvailableModels()

      expect(models).toEqual(['gpt-3.5-turbo', 'gpt-4'])
    })

    it('应该强制刷新模型列表', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: 'test-key',
        availableModels: ['old-model']
      })
      ChromeStorageService.saveSyncSetting.mockResolvedValue()

      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [{ id: 'gpt-4' }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const models = await aiConfigService.getAvailableModels(true)

      expect(models).toEqual(['gpt-4'])
    })

    it('应该返回预设模型列表当测试失败时', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        model: 'gpt-3.5-turbo'
      })
      fetch.mockRejectedValue(new Error('网络错误'))

      const models = await aiConfigService.getAvailableModels(true)

      expect(models).toEqual(['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview'])
    })
  })

  describe('resetToDefault', () => {
    it('应该重置配置为默认值', async () => {
      ChromeStorageService.saveSyncSetting.mockResolvedValue()

      await aiConfigService.resetToDefault()

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_config',
        expect.objectContaining({
          provider: 'openai',
          model: 'gpt-3.5-turbo',
          autoTagging: true,
          autoCategories: true,
          autoDescription: false
        })
      )
    })
  })

  describe('validateCurrentConfig', () => {
    it('应该验证有效配置', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        apiKey: 'test-key',
        temperature: 0.7,
        maxTokens: 1000,
        timeout: 30000
      })

      const result = await aiConfigService.validateCurrentConfig()

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测缺少必需字段', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: '',
        model: '',
        baseUrl: ''
      })

      const result = await aiConfigService.validateCurrentConfig()

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('未选择AI服务提供商')
      expect(result.errors).toContain('未选择AI模型')
      expect(result.errors).toContain('未设置API基础URL')
    })

    it('应该检测需要API密钥的提供商', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo'
        // 缺少apiKey
      })

      const result = await aiConfigService.validateCurrentConfig()

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('该服务提供商需要API密钥')
    })

    it('应该验证参数范围', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        apiKey: 'test-key',
        temperature: 3.0,
        maxTokens: -100,
        timeout: -1000
      })

      const result = await aiConfigService.validateCurrentConfig()

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('温度参数应在0-2之间')
      expect(result.errors).toContain('最大令牌数应大于0')
      expect(result.errors).toContain('超时时间应大于0')
    })
  })

  describe('exportConfig', () => {
    it('应该导出配置并隐藏API密钥', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        apiKey: 'secret-key',
        model: 'gpt-3.5-turbo'
      })

      const exportedConfig = await aiConfigService.exportConfig()

      expect(exportedConfig.provider).toBe('openai')
      expect(exportedConfig.model).toBe('gpt-3.5-turbo')
      expect(exportedConfig.apiKey).toBe('***已隐藏***')
    })
  })

  describe('importConfig', () => {
    it('应该导入配置', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({})
      ChromeStorageService.saveSyncSetting.mockResolvedValue()

      const configToImport = {
        provider: 'claude',
        model: 'claude-3-sonnet-20240229',
        apiKey: 'imported-key'
      }

      await aiConfigService.importConfig(configToImport)

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_config',
        expect.objectContaining(configToImport)
      )
    })

    it('应该跳过API密钥当选项设置时', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({})
      ChromeStorageService.saveSyncSetting.mockResolvedValue()

      const configToImport = {
        provider: 'claude',
        apiKey: 'should-be-skipped'
      }

      await aiConfigService.importConfig(configToImport, { skipApiKey: true })

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_config',
        expect.not.objectContaining({ apiKey: 'should-be-skipped' })
      )
    })

    it('应该验证连接当选项设置时', async () => {
      ChromeStorageService.getSyncSetting.mockResolvedValue({
        provider: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: 'test-key'
      })
      ChromeStorageService.saveSyncSetting.mockResolvedValue()

      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ data: [] })
      }
      fetch.mockResolvedValue(mockResponse)

      const configToImport = {
        provider: 'openai',
        model: 'gpt-4'
      }

      await aiConfigService.importConfig(configToImport, { validateConnection: true })

      expect(fetch).toHaveBeenCalled()
    })
  })
})