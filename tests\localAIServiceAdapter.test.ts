// 通用本地AI服务适配器测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { localAIServiceAdapter, LocalServiceConfig } from '../src/services/localAIServiceAdapter'

// Mock fetch
global.fetch = vi.fn()

describe('LocalAIServiceAdapter', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('discoverLocalServices', () => {
    it('应该发现本地AI服务', async () => {
      // Mock Ollama服务响应
      const mockOllamaResponse = {
        ok: true,
        url: 'http://localhost:11434/api/version',
        json: () => Promise.resolve({ version: '0.1.26' }),
        headers: new Map([['server', 'ollama']])
      }

      // Mock LM Studio服务响应
      const mockLMStudioResponse = {
        ok: true,
        url: 'http://localhost:1234/v1/models',
        json: () => Promise.resolve({ data: [] }),
        headers: new Map()
      }

      vi.mocked(fetch)
        .mockResolvedValueOnce(mockOllamaResponse as any)
        .mockResolvedValueOnce(mockLMStudioResponse as any)
        .mockRejectedValue(new Error('Connection refused')) // 其他端口连接失败

      const result = await localAIServiceAdapter.discoverLocalServices()

      expect(result.services.length).toBeGreaterThanOrEqual(0)
      expect(result.errors).toBeInstanceOf(Array)
    })

    it('应该扫描自定义端口', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Connection refused'))

      const customPorts = [8888, 9999]
      const result = await localAIServiceAdapter.discoverLocalServices(customPorts)

      expect(result.services).toEqual([])
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('testLocalServiceConnection', () => {
    it('应该成功测试本地服务连接', async () => {
      const mockHealthResponse = {
        ok: true,
        json: () => Promise.resolve({ status: 'ok' })
      }
      const mockModelsResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: 'model1' },
            { id: 'model2' }
          ]
        })
      }

      vi.mocked(fetch)
        .mockResolvedValueOnce(mockHealthResponse as any)
        .mockResolvedValueOnce(mockModelsResponse as any)

      const config: LocalServiceConfig = {
        name: 'Test Service',
        baseUrl: 'http://localhost:8080',
        port: 8080,
        protocol: 'http',
        healthCheckPath: '/health',
        modelsPath: '/models',
        timeout: 5000
      }

      const result = await localAIServiceAdapter.testLocalServiceConnection(config)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(result.responseTime).toBeGreaterThanOrEqual(0)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8080/health',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
    })

    it('连接失败应该返回错误结果', async () => {
      const connectionError = new Error('connect ECONNREFUSED 127.0.0.1:8080')
      vi.mocked(fetch).mockRejectedValue(connectionError)

      const config: LocalServiceConfig = {
        name: 'Test Service',
        baseUrl: 'http://localhost:8080',
        port: 8080,
        protocol: 'http',
        healthCheckPath: '/health',
        modelsPath: '/models',
        timeout: 5000
      }

      const result = await localAIServiceAdapter.testLocalServiceConnection(config)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Test Service服务未运行或端口不正确')
    })

    it('连接超时应该返回友好的错误信息', async () => {
      const timeoutError = new Error('The operation was aborted')
      timeoutError.name = 'AbortError'
      
      vi.mocked(fetch).mockRejectedValue(timeoutError)

      const config: LocalServiceConfig = {
        name: 'Test Service',
        baseUrl: 'http://localhost:8080',
        port: 8080,
        protocol: 'http',
        healthCheckPath: '/health',
        modelsPath: '/models',
        timeout: 5000
      }

      const result = await localAIServiceAdapter.testLocalServiceConnection(config)

      expect(result.success).toBe(false)
      expect(result.error).toContain('连接超时')
    })
  })

  describe('getLocalServiceModels', () => {
    it('应该获取Ollama格式的模型列表', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          models: [
            {
              name: 'llama2:7b',
              size: **********,
              details: {
                parameter_size: '7B'
              }
            },
            {
              name: 'qwen:14b',
              size: **********,
              details: {
                parameter_size: '14B'
              }
            }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: LocalServiceConfig = {
        name: 'Ollama',
        baseUrl: 'http://localhost:11434',
        port: 11434,
        protocol: 'http',
        modelsPath: '/api/tags',
        timeout: 10000
      }

      const models = await localAIServiceAdapter.getLocalServiceModels(config)

      expect(models).toHaveLength(2)
      expect(models[0].id).toBe('llama2:7b')
      expect(models[0].name).toBe('llama2:7b')
      expect(models[0].displayName).toBe('llama2:7b')
      expect(models[0].description).toBe('Ollama本地模型')
      expect(models[0].size).toBe('3.54 GB')
      expect(models[0].parameters).toBe('7B')
      expect(models[0].capabilities).toContain('chat')
      expect(models[0].tags).toContain('Ollama')
      expect(models[0].providerId).toBe('ollama')
    })

    it('应该获取OpenAI兼容格式的模型列表', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            {
              id: 'gpt-3.5-turbo',
              object: 'model',
              created: **********,
              owned_by: 'openai'
            },
            {
              id: 'text-embedding-ada-002',
              object: 'model',
              created: **********,
              owned_by: 'openai'
            }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: LocalServiceConfig = {
        name: 'OpenAI Compatible',
        baseUrl: 'http://localhost:8080',
        port: 8080,
        protocol: 'http',
        modelsPath: '/v1/models',
        timeout: 10000
      }

      const models = await localAIServiceAdapter.getLocalServiceModels(config)

      expect(models).toHaveLength(2)
      expect(models[0].id).toBe('gpt-3.5-turbo')
      expect(models[0].name).toBe('gpt-3.5-turbo')
      expect(models[0].displayName).toBe('gpt-3.5-turbo')
      expect(models[0].description).toBe('OpenAI Compatible本地模型')
      expect(models[0].capabilities).toContain('chat')
      expect(models[0].tags).toContain('OpenAI Compatible')
      expect(models[0].providerId).toBe('openai-compatible')

      expect(models[1].id).toBe('text-embedding-ada-002')
      expect(models[1].capabilities).toContain('embedding')
    })

    it('应该获取Xinference格式的模型列表', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            {
              model_uid: 'llama-2-7b-uid',
              model_name: 'llama-2-7b-chat',
              model_type: 'LLM',
              model_size_in_billions: 7,
              quantization: 'q4_0',
              model_description: 'Llama 2 7B Chat model'
            }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: LocalServiceConfig = {
        name: 'Xinference',
        baseUrl: 'http://localhost:9997',
        port: 9997,
        protocol: 'http',
        modelsPath: '/v1/models',
        timeout: 10000
      }

      const models = await localAIServiceAdapter.getLocalServiceModels(config)

      expect(models).toHaveLength(1)
      expect(models[0].id).toBe('llama-2-7b-uid')
      expect(models[0].name).toBe('llama-2-7b-chat')
      expect(models[0].displayName).toBe('llama-2-7b-chat')
      expect(models[0].description).toBe('Llama 2 7B Chat model')
      expect(models[0].size).toBe('7B')
      expect(models[0].parameters).toBe('7B')
      expect(models[0].capabilities).toContain('chat')
      expect(models[0].capabilities).toContain('instruction-following')
      expect(models[0].tags).toContain('Xinference')
      expect(models[0].tags).toContain('LLM')
      expect(models[0].tags).toContain('q4_0')
      expect(models[0].tags).toContain('7B参数')
      expect(models[0].tags).toContain('对话')
      expect(models[0].providerId).toBe('xinference')
    })

    it('网络错误应该返回空数组', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Network error'))

      const config: LocalServiceConfig = {
        name: 'Test Service',
        baseUrl: 'http://localhost:8080',
        port: 8080,
        protocol: 'http',
        modelsPath: '/models',
        timeout: 10000
      }

      const models = await localAIServiceAdapter.getLocalServiceModels(config)
      expect(models).toEqual([])
    })
  })

  describe('createCustomServiceConfig', () => {
    it('应该创建自定义服务配置', () => {
      const config = localAIServiceAdapter.createCustomServiceConfig(
        'My Custom Service',
        'http://localhost:9000',
        {
          apiPath: '/api/v2',
          timeout: 15000,
          headers: { 'Authorization': 'Bearer token' }
        }
      )

      expect(config.name).toBe('My Custom Service')
      expect(config.baseUrl).toBe('http://localhost:9000')
      expect(config.port).toBe(9000)
      expect(config.protocol).toBe('http')
      expect(config.apiPath).toBe('/api/v2')
      expect(config.timeout).toBe(15000)
      expect(config.headers).toEqual({ 'Authorization': 'Bearer token' })
    })

    it('应该处理HTTPS URL', () => {
      const config = localAIServiceAdapter.createCustomServiceConfig(
        'HTTPS Service',
        'https://example.com:8443'
      )

      expect(config.baseUrl).toBe('https://example.com:8443')
      expect(config.port).toBe(8443)
      expect(config.protocol).toBe('https')
    })

    it('应该使用默认端口', () => {
      const httpConfig = localAIServiceAdapter.createCustomServiceConfig(
        'HTTP Service',
        'http://example.com'
      )

      const httpsConfig = localAIServiceAdapter.createCustomServiceConfig(
        'HTTPS Service',
        'https://example.com'
      )

      expect(httpConfig.port).toBe(80)
      expect(httpsConfig.port).toBe(443)
    })
  })

  describe('getDefaultServices', () => {
    it('应该返回默认服务配置列表', () => {
      const services = localAIServiceAdapter.getDefaultServices()

      expect(services).toBeInstanceOf(Array)
      expect(services.length).toBeGreaterThan(0)
      
      // 检查必需的服务
      const serviceNames = services.map(s => s.name)
      expect(serviceNames).toContain('Ollama')
      expect(serviceNames).toContain('LM Studio')
      expect(serviceNames).toContain('Xinference')
      
      // 检查配置结构
      services.forEach(service => {
        expect(service).toHaveProperty('name')
        expect(service).toHaveProperty('baseUrl')
        expect(service).toHaveProperty('port')
        expect(service).toHaveProperty('protocol')
        expect(service).toHaveProperty('healthCheckPath')
        expect(service).toHaveProperty('modelsPath')
        expect(service).toHaveProperty('timeout')
      })
    })
  })

  describe('私有方法测试', () => {
    describe('formatModelDisplayName', () => {
      it('应该移除文件扩展名', () => {
        const formatName = localAIServiceAdapter['formatModelDisplayName']
        
        expect(formatName('model.gguf', 'Test')).toBe('model')
        expect(formatName('model.bin', 'Test')).toBe('model')
        expect(formatName('model.safetensors', 'Test')).toBe('model')
        expect(formatName('model.txt', 'Test')).toBe('model.txt') // 不移除未知扩展名
      })

      it('应该处理长路径', () => {
        const formatName = localAIServiceAdapter['formatModelDisplayName']
        
        const longPath = '/very/long/path/to/model/files/that/exceeds/fifty/characters/model.gguf'
        expect(formatName(longPath, 'Test')).toBe('model')
      })
    })

    describe('determineModelCapabilities', () => {
      it('应该根据模型名称确定能力', () => {
        const determineCapabilities = localAIServiceAdapter['determineModelCapabilities']
        
        const codeModel = { model_name: 'codellama-7b' }
        const capabilities = determineCapabilities(codeModel, 'Test')
        
        expect(capabilities).toContain('chat')
        expect(capabilities).toContain('completion')
        expect(capabilities).toContain('coding')
      })

      it('应该根据服务类型确定能力', () => {
        const determineCapabilities = localAIServiceAdapter['determineModelCapabilities']
        
        const embeddingModel = { model_type: 'embedding' }
        const capabilities = determineCapabilities(embeddingModel, 'Xinference')
        
        expect(capabilities).toContain('embedding')
      })
    })

    describe('isModelRecommended', () => {
      it('应该正确判断推荐模型', () => {
        const isRecommended = localAIServiceAdapter['isModelRecommended']
        
        expect(isRecommended('llama2-7b')).toBe(true)
        expect(isRecommended('qwen-14b')).toBe(true)
        expect(isRecommended('chatglm3-6b')).toBe(true)
        expect(isRecommended('unknown-model')).toBe(false)
      })
    })

    describe('isModelPopular', () => {
      it('应该正确判断热门模型', () => {
        const isPopular = localAIServiceAdapter['isModelPopular']
        
        expect(isPopular('llama2-chat')).toBe(true)
        expect(isPopular('qwen-instruct')).toBe(true)
        expect(isPopular('chatglm3-6b')).toBe(true)
        expect(isPopular('unknown-model')).toBe(false)
      })
    })

    describe('formatBytes', () => {
      it('应该正确格式化字节大小', () => {
        const formatBytes = localAIServiceAdapter['formatBytes']
        
        expect(formatBytes(0)).toBe('0 Bytes')
        expect(formatBytes(1024)).toBe('1 KB')
        expect(formatBytes(1048576)).toBe('1 MB')
        expect(formatBytes(1073741824)).toBe('1 GB')
        expect(formatBytes(**********)).toBe('3.54 GB')
      })
    })
  })
})