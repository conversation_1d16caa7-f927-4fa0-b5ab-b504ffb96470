#!/usr/bin/env node

/**
 * 性能监控工具API提取脚本
 * 从 performance.ts 文件中提取函数签名和文档
 * 
 * 功能说明:
 * - 自动提取PerformanceMonitor和MemoryMonitor类的方法签名
 * - 生成完整的API参考文档
 * - 验证文档与代码的一致性
 * - 支持中文注释和文档生成
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

console.log('🔍 提取性能监控工具API信息...')

/**
 * 性能监控工具API提取器类
 */
class PerformanceAPIExtractor {
  constructor() {
    this.classes = []
    this.functions = []
    this.exports = []
    this.imports = []
  }

  /**
   * 从文件中提取API信息
   */
  extractFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const fileName = path.basename(filePath)
      
      console.log(`📄 分析文件: ${fileName}`)
      
      this.extractClasses(content)
      this.extractFunctions(content)
      this.extractExports(content)
      this.extractImports(content)
      
    } catch (error) {
      console.error(`❌ 分析文件失败 ${filePath}:`, error.message)
    }
  }

  /**
   * 提取类信息
   */
  extractClasses(content) {
    // 匹配类定义
    const classRegex = /export\s+class\s+(\w+)\s*{[\s\S]*?^}/gm
    let match

    while ((match = classRegex.exec(content)) !== null) {
      const className = match[1]
      const classContent = match[0]
      
      const classInfo = {
        name: className,
        methods: this.extractClassMethods(classContent),
        properties: this.extractClassProperties(classContent),
        isExported: true,
        comment: this.extractClassComment(content, match.index)
      }
      
      this.classes.push(classInfo)
    }
  }

  /**
   * 提取类方法
   */
  extractClassMethods(classContent) {
    const methods = []
    
    // 匹配方法定义
    const methodRegex = /(\/\*\*[\s\S]*?\*\/)?\s*(private\s+|public\s+|static\s+)*(async\s+)?(\w+)\s*\([^)]*\)\s*:\s*([^{]+)/g
    let match

    while ((match = methodRegex.exec(classContent)) !== null) {
      const comment = match[1] || ''
      const modifiers = (match[2] || '').trim()
      const isAsync = !!match[3]
      const methodName = match[4]
      const returnType = match[5].trim()
      
      // 跳过构造函数
      if (methodName === 'constructor') continue
      
      const methodInfo = {
        name: methodName,
        modifiers: modifiers,
        isAsync: isAsync,
        returnType: returnType,
        comment: this.parseComment(comment),
        parameters: this.extractMethodParameters(match[0]),
        isStatic: modifiers.includes('static'),
        isPrivate: modifiers.includes('private')
      }
      
      methods.push(methodInfo)
    }
    
    return methods
  }

  /**
   * 提取类属性
   */
  extractClassProperties(classContent) {
    const properties = []
    
    // 匹配属性定义
    const propertyRegex = /(private\s+|public\s+|static\s+)*(\w+):\s*([^=\n]+)/g
    let match

    while ((match = propertyRegex.exec(classContent)) !== null) {
      const modifiers = (match[1] || '').trim()
      const propertyName = match[2]
      const propertyType = match[3].trim()
      
      properties.push({
        name: propertyName,
        type: propertyType,
        modifiers: modifiers,
        isStatic: modifiers.includes('static'),
        isPrivate: modifiers.includes('private')
      })
    }
    
    return properties
  }

  /**
   * 提取函数信息
   */
  extractFunctions(content) {
    // 匹配导出的函数
    const functionRegex = /export\s+function\s+(\w+)<([^>]*)>\s*\([^)]*\)\s*:\s*([^{]+)/g
    let match

    while ((match = functionRegex.exec(content)) !== null) {
      const functionName = match[1]
      const generics = match[2] || ''
      const returnType = match[3].trim()
      
      const functionInfo = {
        name: functionName,
        generics: generics,
        returnType: returnType,
        parameters: this.extractFunctionParameters(match[0]),
        comment: this.extractFunctionComment(content, match.index),
        isExported: true
      }
      
      this.functions.push(functionInfo)
    }
  }

  /**
   * 提取方法参数
   */
  extractMethodParameters(methodSignature) {
    const paramMatch = methodSignature.match(/\(([^)]*)\)/)
    if (!paramMatch || !paramMatch[1].trim()) {
      return []
    }
    
    return this.parseParameters(paramMatch[1])
  }

  /**
   * 提取函数参数
   */
  extractFunctionParameters(functionSignature) {
    const paramMatch = functionSignature.match(/\(([^)]*)\)/)
    if (!paramMatch || !paramMatch[1].trim()) {
      return []
    }
    
    return this.parseParameters(paramMatch[1])
  }

  /**
   * 解析参数字符串
   */
  parseParameters(paramString) {
    const params = []
    const paramParts = paramString.split(',')
    
    paramParts.forEach(param => {
      const trimmed = param.trim()
      if (trimmed) {
        const colonIndex = trimmed.indexOf(':')
        if (colonIndex > 0) {
          const name = trimmed.substring(0, colonIndex).trim()
          const type = trimmed.substring(colonIndex + 1).trim()
          const isOptional = name.includes('?')
          
          params.push({
            name: name.replace('?', ''),
            type: type,
            optional: isOptional
          })
        }
      }
    })
    
    return params
  }

  /**
   * 提取类注释
   */
  extractClassComment(content, classIndex) {
    const beforeClass = content.substring(0, classIndex)
    const lines = beforeClass.split('\n')
    
    // 从后往前查找注释
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim()
      if (line.startsWith('/**')) {
        // 找到注释开始，提取完整注释
        const commentLines = []
        for (let j = i; j < lines.length; j++) {
          const commentLine = lines[j].trim()
          commentLines.push(commentLine)
          if (commentLine.endsWith('*/')) {
            break
          }
        }
        return this.parseComment(commentLines.join('\n'))
      }
    }
    
    return { description: '', parameters: [], returns: '' }
  }

  /**
   * 提取函数注释
   */
  extractFunctionComment(content, functionIndex) {
    return this.extractClassComment(content, functionIndex)
  }

  /**
   * 解析注释内容
   */
  parseComment(commentText) {
    if (!commentText) {
      return { description: '', parameters: [], returns: '' }
    }
    
    const lines = commentText.split('\n')
    let description = ''
    const parameters = []
    let returns = ''
    
    for (const line of lines) {
      const cleanLine = line.replace(/^[\*\/\s]+/, '').trim()
      
      if (cleanLine.startsWith('@param')) {
        const paramMatch = cleanLine.match(/@param\s+(\w+)\s+(.+)/)
        if (paramMatch) {
          parameters.push({
            name: paramMatch[1],
            description: paramMatch[2]
          })
        }
      } else if (cleanLine.startsWith('@returns')) {
        returns = cleanLine.replace('@returns', '').trim()
      } else if (cleanLine && !cleanLine.startsWith('@')) {
        if (!description) {
          description = cleanLine
        }
      }
    }
    
    return { description, parameters, returns }
  }

  /**
   * 提取导出信息
   */
  extractExports(content) {
    // 匹配导出语句
    const exportRegex = /export\s+(const|class|function)\s+(\w+)/g
    let match

    while ((match = exportRegex.exec(content)) !== null) {
      this.exports.push({
        type: match[1],
        name: match[2]
      })
    }

    // 匹配底部的导出语句
    const bottomExportRegex = /export\s+const\s+(\w+)\s*=/g
    while ((match = bottomExportRegex.exec(content)) !== null) {
      this.exports.push({
        type: 'const',
        name: match[1]
      })
    }
  }

  /**
   * 提取导入信息
   */
  extractImports(content) {
    // 这个文件没有导入，但保留方法以备将来使用
    this.imports = []
  }

  /**
   * 生成API文档
   */
  generateDocumentation() {
    let doc = '# 性能监控工具 API 文档\n\n'
    
    // 概览
    doc += '## 概览\n\n'
    doc += '性能监控工具提供了完整的性能监控和优化功能，包括性能指标收集、内存使用监控、防抖节流工具等。\n\n'
    doc += `- **文件路径**: src/utils/performance.ts\n`
    doc += `- **主要类**: ${this.classes.map(c => c.name).join(', ')}\n`
    doc += `- **工具函数**: ${this.functions.map(f => f.name).join(', ')}\n`
    doc += `- **导出实例**: performanceMonitor, memoryMonitor\n\n`
    
    // 生成类文档
    this.classes.forEach(classInfo => {
      doc += `## ${classInfo.name} 类\n\n`
      
      if (classInfo.comment.description) {
        doc += `${classInfo.comment.description}\n\n`
      }
      
      // 静态方法
      const staticMethods = classInfo.methods.filter(m => m.isStatic && !m.isPrivate)
      if (staticMethods.length > 0) {
        doc += '### 静态方法\n\n'
        staticMethods.forEach(method => {
          doc += this.generateMethodDoc(method)
        })
      }
      
      // 实例方法
      const instanceMethods = classInfo.methods.filter(m => !m.isStatic && !m.isPrivate)
      if (instanceMethods.length > 0) {
        doc += '### 实例方法\n\n'
        instanceMethods.forEach(method => {
          doc += this.generateMethodDoc(method)
        })
      }
    })
    
    // 生成函数文档
    if (this.functions.length > 0) {
      doc += '## 工具函数\n\n'
      this.functions.forEach(func => {
        doc += this.generateFunctionDoc(func)
      })
    }
    
    // 生成导出信息
    doc += '## 导出内容\n\n'
    this.exports.forEach(exp => {
      doc += `- **${exp.type}**: \`${exp.name}\`\n`
    })
    doc += '\n'
    
    return doc
  }

  /**
   * 生成方法文档
   */
  generateMethodDoc(method) {
    let doc = `#### ${method.name}\n\n`
    
    if (method.comment.description) {
      doc += `**功能**: ${method.comment.description}\n\n`
    }
    
    // 构建签名
    const params = method.parameters.map(p => 
      `${p.name}${p.optional ? '?' : ''}: ${p.type}`
    ).join(', ')
    
    const asyncPrefix = method.isAsync ? 'async ' : ''
    const staticPrefix = method.isStatic ? 'static ' : ''
    
    doc += `**签名**: \`${staticPrefix}${asyncPrefix}${method.name}(${params}): ${method.returnType}\`\n\n`
    
    // 参数文档
    if (method.parameters.length > 0) {
      doc += '**参数**:\n'
      method.parameters.forEach(param => {
        const optionalText = param.optional ? ' (可选)' : ''
        doc += `- \`${param.name}\` (\`${param.type}\`)${optionalText}`
        
        const paramDoc = method.comment.parameters.find(p => p.name === param.name)
        if (paramDoc) {
          doc += ` - ${paramDoc.description}`
        }
        doc += '\n'
      })
      doc += '\n'
    }
    
    // 返回值文档
    if (method.comment.returns) {
      doc += `**返回值**: ${method.comment.returns}\n\n`
    }
    
    doc += '---\n\n'
    return doc
  }

  /**
   * 生成函数文档
   */
  generateFunctionDoc(func) {
    let doc = `### ${func.name}\n\n`
    
    if (func.comment.description) {
      doc += `**功能**: ${func.comment.description}\n\n`
    }
    
    // 构建签名
    const params = func.parameters.map(p => 
      `${p.name}${p.optional ? '?' : ''}: ${p.type}`
    ).join(', ')
    
    const generics = func.generics ? `<${func.generics}>` : ''
    
    doc += `**签名**: \`${func.name}${generics}(${params}): ${func.returnType}\`\n\n`
    
    // 参数文档
    if (func.parameters.length > 0) {
      doc += '**参数**:\n'
      func.parameters.forEach(param => {
        const optionalText = param.optional ? ' (可选)' : ''
        doc += `- \`${param.name}\` (\`${param.type}\`)${optionalText}`
        
        const paramDoc = func.comment.parameters.find(p => p.name === param.name)
        if (paramDoc) {
          doc += ` - ${paramDoc.description}`
        }
        doc += '\n'
      })
      doc += '\n'
    }
    
    // 返回值文档
    if (func.comment.returns) {
      doc += `**返回值**: ${func.comment.returns}\n\n`
    }
    
    doc += '---\n\n'
    return doc
  }

  /**
   * 生成统计信息
   */
  generateStats() {
    return {
      classes: this.classes.length,
      functions: this.functions.length,
      exports: this.exports.length,
      totalMethods: this.classes.reduce((sum, cls) => sum + cls.methods.length, 0),
      publicMethods: this.classes.reduce((sum, cls) => 
        sum + cls.methods.filter(m => !m.isPrivate).length, 0
      )
    }
  }
}

/**
 * 主函数
 */
function main() {
  const extractor = new PerformanceAPIExtractor()
  
  // 要分析的文件
  const performanceFile = path.resolve(__dirname, '../src/utils/performance.ts')
  
  if (fs.existsSync(performanceFile)) {
    extractor.extractFromFile(performanceFile)
  } else {
    console.log(`⚠️  性能监控文件不存在: ${performanceFile}`)
    return
  }
  
  // 生成文档
  const documentation = extractor.generateDocumentation()
  const stats = extractor.generateStats()
  
  // 保存文档
  const outputPath = path.resolve(__dirname, '../docs/performance-utils-api.md')
  
  try {
    fs.writeFileSync(outputPath, documentation, 'utf8')
    console.log(`📝 文档已生成: ${outputPath}`)
  } catch (error) {
    console.error('❌ 保存文档失败:', error.message)
  }
  
  // 输出统计信息
  console.log('\n📊 提取统计:')
  console.log(`- 类数量: ${stats.classes} 个`)
  console.log(`- 函数数量: ${stats.functions} 个`)
  console.log(`- 导出内容: ${stats.exports} 个`)
  console.log(`- 总方法数: ${stats.totalMethods} 个`)
  console.log(`- 公有方法: ${stats.publicMethods} 个`)
  
  console.log('\n✅ 性能监控工具API提取完成!')
}

// 运行主函数
main()