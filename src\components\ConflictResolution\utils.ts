// 冲突解决相关的工具函数

// 冲突类型映射工具函数
export const getConflictTypeLabel = (conflictType: string): string => {
  switch (conflictType) {
    case 'duplicate': return '重复项'
    case 'name_conflict': return '名称冲突'
    case 'data_mismatch': return '数据不匹配'
    default: return '未知冲突'
  }
}

// 数据类型映射工具函数
export const getDataTypeLabel = (type: string): string => {
  switch (type) {
    case 'bookmark': return '收藏'
    case 'category': return '分类'
    case 'tag': return '标签'
    default: return '未知类型'
  }
}

// 数据类型颜色映射
export const getDataTypeColor = (type: string): string => {
  switch (type) {
    case 'bookmark': return 'bg-green-500'
    case 'category': return 'bg-blue-500'
    case 'tag': return 'bg-purple-500'
    default: return 'bg-gray-500'
  }
}

// 字段名称映射
export const getFieldLabel = (key: string): string => {
  const fieldLabels: Record<string, string> = {
    title: '标题',
    name: '名称',
    description: '描述',
    url: 'URL',
    category: '分类',
    tags: '标签',
    color: '颜色'
  }
  return fieldLabels[key] || key
}