/**
 * 主题管理 Hook
 * 提供深色/浅色主题切换功能，支持系统主题跟随
 * 
 * 功能特性：
 * - 支持浅色、深色、系统三种主题模式
 * - 自动跟随系统主题变化
 * - 主题状态持久化保存
 * - 自动应用主题到 DOM 和 meta 标签
 */

import { useState, useEffect, useCallback } from 'react'

export type Theme = 'light' | 'dark' | 'system'

interface UseThemeReturn {
  theme: Theme
  actualTheme: 'light' | 'dark'
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
  isSystemTheme: boolean
}

/**
 * 获取系统主题偏好
 */
const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light'
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

/**
 * 从本地存储获取保存的主题
 */
const getSavedTheme = (): Theme => {
  try {
    const saved = localStorage.getItem('options-theme')
    if (saved && ['light', 'dark', 'system'].includes(saved)) {
      return saved as Theme
    }
  } catch (error) {
    console.warn('无法从本地存储读取主题设置:', error)
  }
  return 'system'
}

/**
 * 保存主题到本地存储
 */
const saveTheme = (theme: Theme) => {
  try {
    localStorage.setItem('options-theme', theme)
  } catch (error) {
    console.warn('无法保存主题设置到本地存储:', error)
  }
}

/**
 * 应用主题到 DOM
 */
const applyTheme = (actualTheme: 'light' | 'dark') => {
  const root = document.documentElement
  
  if (actualTheme === 'dark') {
    root.classList.add('dark')
  } else {
    root.classList.remove('dark')
  }
  
  // 更新 meta theme-color
  const metaThemeColor = document.querySelector('meta[name="theme-color"]')
  if (metaThemeColor) {
    metaThemeColor.setAttribute('content', actualTheme === 'dark' ? '#1f2937' : '#ffffff')
  }
}

/**
 * 主题管理 Hook
 */
export const useTheme = (): UseThemeReturn => {
  const [theme, setThemeState] = useState<Theme>(() => getSavedTheme())
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>(() => getSystemTheme())

  // 计算实际应用的主题
  const actualTheme = theme === 'system' ? systemTheme : theme
  const isSystemTheme = theme === 'system'

  // 监听系统主题变化
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light')
    }
    
    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // 应用主题到 DOM
  useEffect(() => {
    applyTheme(actualTheme)
  }, [actualTheme])

  // 设置主题
  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme)
    saveTheme(newTheme)
  }, [])

  // 切换主题（在 light 和 dark 之间切换）
  const toggleTheme = useCallback(() => {
    if (theme === 'system') {
      // 如果当前是系统主题，切换到与系统相反的主题
      setTheme(systemTheme === 'dark' ? 'light' : 'dark')
    } else {
      // 在 light 和 dark 之间切换
      setTheme(theme === 'dark' ? 'light' : 'dark')
    }
  }, [theme, systemTheme, setTheme])

  return {
    theme,
    actualTheme,
    setTheme,
    toggleTheme,
    isSystemTheme
  }
}