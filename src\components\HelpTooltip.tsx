import React, { useState, useMemo } from 'react'
import { getHelpContent, type HelpContext } from './HelpTooltip/helpContent'
import { useTooltip } from './HelpTooltip/useTooltip'

// 位置类型定义
export type TooltipPosition = 'top' | 'bottom' | 'left' | 'right'

// 帮助提示组件属性接口
interface HelpTooltipProps {
  content: string
  title?: string
  position?: TooltipPosition
  className?: string
  children: React.ReactNode
  maxWidth?: string
  delay?: number
}

// 样式策略类 - 使用策略模式处理不同位置的样式
class TooltipStyleStrategy {
  private static readonly POSITION_STYLES = {
    top: {
      tooltip: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
      arrow: 'top-full left-1/2 transform -translate-x-1/2 border-t-gray-800 border-t-8 border-x-transparent border-x-8 border-b-0'
    },
    bottom: {
      tooltip: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
      arrow: 'bottom-full left-1/2 transform -translate-x-1/2 border-b-gray-800 border-b-8 border-x-transparent border-x-8 border-t-0'
    },
    left: {
      tooltip: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
      arrow: 'left-full top-1/2 transform -translate-y-1/2 border-l-gray-800 border-l-8 border-y-transparent border-y-8 border-r-0'
    },
    right: {
      tooltip: 'left-full top-1/2 transform -translate-y-1/2 ml-2',
      arrow: 'right-full top-1/2 transform -translate-y-1/2 border-r-gray-800 border-r-8 border-y-transparent border-y-8 border-l-0'
    }
  } as const

  static getStyles(position: TooltipPosition) {
    return this.POSITION_STYLES[position]
  }
}

/**
 * 帮助提示组件
 * 提供悬停显示的帮助信息，支持多种位置和自定义样式
 */
export const HelpTooltip: React.FC<HelpTooltipProps> = ({
  content,
  title,
  position = 'top',
  className = '',
  maxWidth = 'max-w-xs',
  delay = 0,
  children
}) => {
  // 使用自定义Hook管理tooltip状态
  const { isVisible, triggerProps } = useTooltip({ delay })
  
  // 使用useMemo优化样式计算
  const styles = useMemo(() => TooltipStyleStrategy.getStyles(position), [position])

  return (
    <div 
      className={`relative inline-block ${className}`}
      {...triggerProps}
    >
      {children}
      
      {isVisible && (
        <div className={`absolute z-50 ${styles.tooltip}`}>
          <div 
            id="tooltip-content"
            className={`bg-popover text-popover-foreground text-sm rounded-lg px-3 py-2 border ${maxWidth} shadow-lg`}
            role="tooltip"
            aria-live="polite"
          >
            {title && (
              <div className="font-semibold mb-1 text-yellow-300">{title}</div>
            )}
            <div className="whitespace-pre-wrap">{content}</div>
          </div>
          <div className={`absolute w-0 h-0 ${styles.arrow}`} aria-hidden="true"></div>
        </div>
      )}
    </div>
  )
}

// 问号图标组件 - 提取为独立组件避免重复
const QuestionMarkIcon: React.FC<{ className?: string }> = ({ className = "w-4 h-4" }) => (
  <svg 
    className={className}
    fill="currentColor"
    viewBox="0 0 20 20"
    aria-hidden="true"
  >
    <path 
      fillRule="evenodd" 
      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" 
      clipRule="evenodd" 
    />
  </svg>
)

/**
 * 帮助图标组件
 * 显示问号图标的帮助提示
 */
export const HelpIcon: React.FC<Omit<HelpTooltipProps, 'children'> & { iconClassName?: string }> = ({ 
  iconClassName,
  ...props 
}) => {
  return (
    <HelpTooltip {...props}>
      <QuestionMarkIcon className={`text-gray-500 hover:text-gray-700 cursor-help ${iconClassName || 'w-4 h-4'}`} />
    </HelpTooltip>
  )
}

/**
 * 功能介绍组件
 * 用于显示功能的详细介绍
 */
interface FeatureIntroProps {
  title: string
  description: string
  steps?: string[]
  tips?: string[]
  onClose: () => void
}

export const FeatureIntro: React.FC<FeatureIntroProps> = ({
  title,
  description,
  steps,
  tips,
  onClose
}) => {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="text-gray-700 mb-4">
          {description}
        </div>
        
        {steps && steps.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium text-gray-900 mb-2">操作步骤：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
              {steps.map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ol>
          </div>
        )}
        
        {tips && tips.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium text-gray-900 mb-2">💡 小贴士：</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              {tips.map((tip, index) => (
                <li key={index}>{tip}</li>
              ))}
            </ul>
          </div>
        )}
        
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            我知道了
          </button>
        </div>
      </div>
    </div>
  )
}

/**
 * 上下文帮助系统
 * 根据当前页面状态提供相关帮助
 */
interface ContextHelpProps {
  context: HelpContext
  isVisible: boolean
  onClose: () => void
}

export const ContextHelp: React.FC<ContextHelpProps> = ({
  context,
  isVisible,
  onClose
}) => {
  if (!isVisible) return null

  const content = getHelpContent(context)

  return (
    <FeatureIntro
      title={content.title}
      description={content.description}
      steps={content.steps}
      tips={content.tips}
      onClose={onClose}
    />
  )
}

/**
 * 帮助按钮组件
 * 触发上下文帮助的按钮
 */
interface HelpButtonProps {
  context: HelpContext
  className?: string
  variant?: 'text' | 'icon' | 'button'
}

export const HelpButton: React.FC<HelpButtonProps> = ({ 
  context, 
  className = '',
  variant = 'text'
}) => {
  const [showHelp, setShowHelp] = useState(false)

  const handleClick = () => setShowHelp(true)
  const handleClose = () => setShowHelp(false)

  // 根据变体渲染不同样式的按钮
  const renderButton = () => {
    const baseClasses = "inline-flex items-center text-blue-600 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
    
    switch (variant) {
      case 'icon':
        return (
          <button
            onClick={handleClick}
            className={`${baseClasses} p-1 rounded ${className}`}
            title="获取帮助"
            aria-label="获取帮助"
          >
            <QuestionMarkIcon className="w-4 h-4" />
          </button>
        )
      
      case 'button':
        return (
          <button
            onClick={handleClick}
            className={`${baseClasses} px-3 py-2 border border-blue-300 rounded-md hover:bg-blue-50 ${className}`}
            title="获取帮助"
          >
            <QuestionMarkIcon className="w-4 h-4 mr-1" />
            帮助
          </button>
        )
      
      default: // text
        return (
          <button
            onClick={handleClick}
            className={`${baseClasses} px-3 py-1 text-sm ${className}`}
            title="获取帮助"
          >
            <QuestionMarkIcon className="w-4 h-4 mr-1" />
            帮助
          </button>
        )
    }
  }

  return (
    <>
      {renderButton()}
      
      <ContextHelp
        context={context}
        isVisible={showHelp}
        onClose={handleClose}
      />
    </>
  )
}