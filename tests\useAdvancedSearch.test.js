/**
 * 高级搜索Hook测试
 */

import { renderHook, act } from '@testing-library/react'
import { useAdvancedSearch, useSimpleSearch } from '../src/hooks/useAdvancedSearch'
import { vi } from 'vitest'

// Mock timers
beforeEach(() => {
  vi.useFakeTimers()
})

afterEach(() => {
  vi.runOnlyPendingTimers()
  vi.useRealTimers()
})

// 测试数据
const mockBookmarks = [
  {
    id: '1',
    title: 'React 官方文档',
    description: 'React 是一个用于构建用户界面的 JavaScript 库',
    content: 'React 让你可以通过组件的方式构建用户界面',
    url: 'https://react.dev',
    category: '前端开发',
    tags: ['React', 'JavaScript', '前端'],
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    title: 'Vue.js 指南',
    description: 'Vue.js 是一个渐进式 JavaScript 框架',
    content: 'Vue.js 专注于视图层，易于学习和集成',
    url: 'https://vuejs.org',
    category: '前端开发',
    tags: ['Vue', 'JavaScript', '前端'],
    createdAt: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    title: 'Node.js 教程',
    description: 'Node.js 是一个基于 Chrome V8 引擎的 JavaScript 运行时',
    content: 'Node.js 让 JavaScript 可以在服务器端运行',
    url: 'https://nodejs.org',
    category: '后端开发',
    tags: ['Node.js', 'JavaScript', '后端'],
    createdAt: '2024-01-03T00:00:00Z'
  }
]

describe('useAdvancedSearch Hook', () => {
  test('应该初始化搜索状态', () => {
    const { result } = renderHook(() => useAdvancedSearch(mockBookmarks))
    
    expect(result.current.query).toBe('')
    expect(result.current.results).toHaveLength(3)
    expect(result.current.hasResults).toBe(true)
    expect(result.current.totalResults).toBe(3)
    expect(result.current.isSearching).toBe(false)
    expect(result.current.suggestions).toEqual([])
  })

  test('应该执行搜索', () => {
    const { result } = renderHook(() => useAdvancedSearch(mockBookmarks))
    
    act(() => {
      result.current.setQuery('React')
    })

    // 等待防抖
    act(() => {
      vi.advanceTimersByTime(300)
    })

    expect(result.current.query).toBe('React')
    expect(result.current.results).toHaveLength(1)
    expect(result.current.results[0].item.title).toBe('React 官方文档')
    expect(result.current.hasResults).toBe(true)
  })

  test('应该生成搜索建议', () => {
    const { result } = renderHook(() => 
      useAdvancedSearch(mockBookmarks, { enableSuggestions: true })
    )
    
    act(() => {
      result.current.setQuery('Re')
    })

    expect(result.current.suggestions.length).toBeGreaterThan(0)
    expect(result.current.suggestions).toContain('React')
  })

  test('应该添加筛选条件', () => {
    const { result } = renderHook(() => useAdvancedSearch(mockBookmarks))
    
    act(() => {
      result.current.addFilter({
        field: 'category',
        operator: 'equals',
        value: '前端开发'
      })
    })

    expect(result.current.filterConditions).toHaveLength(1)
    expect(result.current.results).toHaveLength(2)
  })

  test('应该添加排序配置', () => {
    const { result } = renderHook(() => useAdvancedSearch(mockBookmarks))
    
    act(() => {
      result.current.addSort({
        field: 'title',
        direction: 'asc',
        type: 'string'
      })
    })

    expect(result.current.sortConfigs).toHaveLength(1)
    expect(result.current.results[0].item.title).toBe('Node.js 教程')
  })

  test('应该清除筛选条件', () => {
    const { result } = renderHook(() => useAdvancedSearch(mockBookmarks))
    
    act(() => {
      result.current.addFilter({
        field: 'category',
        operator: 'equals',
        value: '前端开发'
      })
    })

    expect(result.current.filterConditions).toHaveLength(1)

    act(() => {
      result.current.clearFilters()
    })

    expect(result.current.filterConditions).toHaveLength(0)
    expect(result.current.results).toHaveLength(3)
  })

  test('应该重置搜索状态', () => {
    const { result } = renderHook(() => useAdvancedSearch(mockBookmarks))
    
    act(() => {
      result.current.setQuery('React')
      result.current.addFilter({
        field: 'category',
        operator: 'equals',
        value: '前端开发'
      })
    })

    act(() => {
      result.current.reset()
    })

    expect(result.current.query).toBe('')
    expect(result.current.filterConditions).toHaveLength(0)
    expect(result.current.results).toHaveLength(3)
  })

  test('应该处理空搜索查询', () => {
    const { result } = renderHook(() => useAdvancedSearch(mockBookmarks))
    
    act(() => {
      result.current.setQuery('')
    })

    // 等待防抖
    act(() => {
      vi.advanceTimersByTime(300)
    })

    expect(result.current.results).toHaveLength(3)
    expect(result.current.hasResults).toBe(true)
  })

  test('应该支持自定义防抖延迟', () => {
    const { result } = renderHook(() => 
      useAdvancedSearch(mockBookmarks, { debounceDelay: 500 })
    )
    
    act(() => {
      result.current.setQuery('React')
    })

    // 300ms后还没有搜索结果
    act(() => {
      vi.advanceTimersByTime(300)
    })
    expect(result.current.results).toHaveLength(3) // 还是所有结果

    // 500ms后有搜索结果
    act(() => {
      vi.advanceTimersByTime(200)
    })
    expect(result.current.results).toHaveLength(1)
  })
})

describe('useSimpleSearch Hook', () => {
  test('应该初始化简单搜索状态', () => {
    const { result } = renderHook(() => useSimpleSearch(mockBookmarks))
    
    expect(result.current.query).toBe('')
    expect(result.current.results).toHaveLength(3)
    expect(result.current.hasResults).toBe(true)
    expect(result.current.totalResults).toBe(3)
  })

  test('应该执行简单搜索', () => {
    const { result } = renderHook(() => useSimpleSearch(mockBookmarks))
    
    act(() => {
      result.current.setQuery('React')
    })

    // 等待防抖
    act(() => {
      vi.advanceTimersByTime(300)
    })

    expect(result.current.query).toBe('React')
    expect(result.current.results).toHaveLength(1)
    expect(result.current.results[0].title).toBe('React 官方文档')
  })

  test('应该搜索多个字段', () => {
    const { result } = renderHook(() => useSimpleSearch(mockBookmarks))
    
    act(() => {
      result.current.setQuery('JavaScript')
    })

    // 等待防抖
    act(() => {
      vi.advanceTimersByTime(300)
    })

    expect(result.current.results).toHaveLength(3) // 所有项目都包含JavaScript
  })

  test('应该不区分大小写搜索', () => {
    const { result } = renderHook(() => useSimpleSearch(mockBookmarks))
    
    act(() => {
      result.current.setQuery('react')
    })

    // 等待防抖
    act(() => {
      vi.advanceTimersByTime(300)
    })

    expect(result.current.results).toHaveLength(1)
    expect(result.current.results[0].title).toBe('React 官方文档')
  })
})

describe('搜索性能测试', () => {
  test('应该在合理时间内处理大量数据', () => {
    // 生成大量测试数据
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      id: `${i}`,
      title: `测试项目 ${i}`,
      description: `这是第 ${i} 个测试项目的描述`,
      content: `内容 ${i} 包含一些测试文本`,
      url: `https://example.com/${i}`,
      category: i % 2 === 0 ? '分类A' : '分类B',
      tags: [`标签${i}`, `标签${i + 1}`],
      createdAt: new Date(2024, 0, i + 1).toISOString()
    }))

    const { result } = renderHook(() => useAdvancedSearch(largeDataset))
    
    const startTime = performance.now()
    
    act(() => {
      result.current.setQuery('测试')
    })

    // 等待防抖
    act(() => {
      vi.advanceTimersByTime(300)
    })

    const endTime = performance.now()
    
    expect(result.current.results.length).toBeGreaterThan(0)
    expect(endTime - startTime).toBeLessThan(500) // 应该在500ms内完成
  })
})