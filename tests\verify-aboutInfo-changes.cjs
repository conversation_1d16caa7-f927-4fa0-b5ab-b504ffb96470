/**
 * 验证 aboutInfo 相关变更的脚本
 * 确保测试与实际数据保持一致
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证 aboutInfo 相关变更...\n');

// 检查源文件中的许可证信息
const aboutInfoPath = path.join(__dirname, '../src/options/data/aboutInfo.ts');
const aboutInfoContent = fs.readFileSync(aboutInfoPath, 'utf8');

console.log('📄 检查源文件中的许可证信息...');
if (aboutInfoContent.includes("type: ''") && 
    aboutInfoContent.includes("text: ''") && 
    aboutInfoContent.includes("url: ''")) {
  console.log('✅ 源文件中的许可证信息为空字符串');
} else {
  console.log('❌ 源文件中的许可证信息不为空');
  process.exit(1);
}

// 检查测试文件中的断言
const testPath = path.join(__dirname, 'options/data/aboutInfo.test.ts');
const testContent = fs.readFileSync(testPath, 'utf8');

console.log('🧪 检查测试文件中的断言...');
if (testContent.includes("expect(defaultAboutData.licenseInfo.type).toBe('')") &&
    testContent.includes("expect(defaultAboutData.licenseInfo.text).toBe('')") &&
    testContent.includes("expect(defaultAboutData.licenseInfo.url).toBe('')")) {
  console.log('✅ 测试文件中的断言已更新为空字符串');
} else {
  console.log('❌ 测试文件中的断言未正确更新');
  process.exit(1);
}

// 检查是否还有旧的MIT许可证断言
if (testContent.includes("'MIT License'") && 
    testContent.includes("expect(defaultAboutData.licenseInfo.type).toBe('MIT License')")) {
  console.log('❌ 测试文件中仍包含旧的MIT许可证断言');
  process.exit(1);
} else {
  console.log('✅ 已移除旧的MIT许可证断言');
}

console.log('\n📊 验证结果:');
console.log('✅ 源文件与测试文件保持一致');
console.log('✅ 许可证信息正确设置为空字符串');
console.log('✅ 测试断言已正确更新');

console.log('\n🎉 所有验证通过！变更正确无误。');