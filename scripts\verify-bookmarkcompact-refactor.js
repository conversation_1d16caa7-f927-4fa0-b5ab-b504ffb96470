// 验证BookmarkCompact组件shadcn重构效果的脚本

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 验证BookmarkCompact组件shadcn重构...\n');

// 检查文件是否存在
const checkFileExists = (filePath, description) => {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${description}: ${filePath} (文件不存在)`);
    return false;
  }
};

// 检查文件内容
const checkFileContent = (filePath, patterns, description) => {
  const fullPath = path.join(__dirname, '..', filePath);
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ ${description}: ${filePath} (文件不存在)`);
    return false;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  let allFound = true;
  
  patterns.forEach(pattern => {
    if (content.includes(pattern.text)) {
      console.log(`  ✅ ${pattern.description}`);
    } else {
      console.log(`  ❌ ${pattern.description}`);
      allFound = false;
    }
  });
  
  return allFound;
};

// 1. 检查主要文件是否存在
console.log('📁 检查文件结构...');
const files = [
  { path: 'src/components/BookmarkCompact.tsx', desc: 'BookmarkCompact组件' },
  { path: 'src/components/examples/BookmarkCompactDemo.tsx', desc: 'BookmarkCompact演示组件' },
  { path: 'src/components/test/BookmarkCompactTest.tsx', desc: 'BookmarkCompact测试页面' },
  { path: 'tests/BookmarkCompact.shadcn.test.tsx', desc: 'shadcn重构测试' },
  { path: 'tests/BookmarkCompact.integration.test.tsx', desc: '集成测试' },
  { path: 'docs/task-7-bookmarkcompact-shadcn-refactor.md', desc: '重构文档' }
];

let allFilesExist = true;
files.forEach(file => {
  if (!checkFileExists(file.path, file.desc)) {
    allFilesExist = false;
  }
});

console.log('');

// 2. 检查BookmarkCompact组件的shadcn集成
console.log('🔧 检查BookmarkCompact组件shadcn集成...');
const componentPatterns = [
  { text: "import { Card, CardContent } from './ui/card'", description: 'Card组件导入' },
  { text: "import { Button } from './ui/button'", description: 'Button组件导入' },
  { text: "import { Badge } from './ui/badge'", description: 'Badge组件导入' },
  { text: "import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from './ui/tooltip'", description: 'Tooltip组件导入' },
  { text: '<TooltipProvider>', description: 'TooltipProvider使用' },
  { text: '<Card', description: 'Card组件使用' },
  { text: '<CardContent', description: 'CardContent组件使用' },
  { text: 'variant="ghost"', description: 'Button ghost变体使用' },
  { text: 'variant="secondary"', description: 'Badge secondary变体使用' },
  { text: 'variant="outline"', description: 'Badge outline变体使用' },
  { text: 'text-foreground', description: 'shadcn颜色变量使用' },
  { text: 'text-muted-foreground', description: 'shadcn muted颜色使用' },
  { text: 'bg-muted', description: 'shadcn背景色使用' }
];

const componentIntegration = checkFileContent('src/components/BookmarkCompact.tsx', componentPatterns, 'BookmarkCompact组件');

console.log('');

// 3. 检查是否移除了旧的自定义样式
console.log('🧹 检查自定义样式移除...');
const removedPatterns = [
  'bg-white',
  'border-gray-200',
  'text-gray-900',
  'text-gray-500',
  'text-gray-400',
  'bg-gray-100',
  'bg-gray-50',
  'hover:text-primary-600',
  'hover:bg-primary-50'
];

const componentContent = fs.readFileSync(path.join(__dirname, '..', 'src/components/BookmarkCompact.tsx'), 'utf8');
let oldStylesRemoved = true;

removedPatterns.forEach(pattern => {
  if (componentContent.includes(pattern)) {
    console.log(`  ❌ 仍包含旧样式: ${pattern}`);
    oldStylesRemoved = false;
  } else {
    console.log(`  ✅ 已移除旧样式: ${pattern}`);
  }
});

console.log('');

// 4. 检查演示组件
console.log('🎨 检查演示组件...');
const demoPatterns = [
  { text: 'BookmarkCompact', description: 'BookmarkCompact组件使用' },
  { text: 'isHighlighted', description: '高亮状态测试' },
  { text: 'onClick={handleClick}', description: '点击事件处理' },
  { text: 'onEdit={handleEdit}', description: '编辑事件处理' },
  { text: 'onDelete={handleDelete}', description: '删除事件处理' },
  { text: 'type: \'text\'', description: '文本类型测试' },
  { text: 'type: \'url\'', description: 'URL类型测试' },
  { text: 'type: \'image\'', description: '图片类型测试' }
];

const demoIntegration = checkFileContent('src/components/examples/BookmarkCompactDemo.tsx', demoPatterns, 'BookmarkCompact演示组件');

console.log('');

// 5. 生成总结报告
console.log('📊 验证总结...');
console.log('='.repeat(50));

const results = {
  '文件结构': allFilesExist ? '✅ 通过' : '❌ 失败',
  'shadcn组件集成': componentIntegration ? '✅ 通过' : '❌ 失败',
  '旧样式移除': oldStylesRemoved ? '✅ 通过' : '❌ 失败',
  '演示组件': demoIntegration ? '✅ 通过' : '❌ 失败'
};

Object.entries(results).forEach(([key, value]) => {
  console.log(`${key}: ${value}`);
});

const allPassed = Object.values(results).every(result => result.includes('✅'));

console.log('='.repeat(50));
console.log(`\n🎯 总体结果: ${allPassed ? '✅ 重构验证通过' : '❌ 重构验证失败'}`);

if (allPassed) {
  console.log('\n🎉 BookmarkCompact组件shadcn重构已成功完成！');
  console.log('📝 可以通过以下方式测试组件效果：');
  console.log('   1. 运行单元测试: npm test BookmarkCompact');
  console.log('   2. 查看演示页面: 在应用中导入BookmarkCompactTest组件');
  console.log('   3. 查看文档: docs/task-7-bookmarkcompact-shadcn-refactor.md');
} else {
  console.log('\n⚠️  请检查上述失败项目并进行修复。');
}

console.log('');