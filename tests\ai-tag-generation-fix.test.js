// AI标签生成修复测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { IndexedDBService } from '../src/utils/indexedDB'
import { AIService } from '../src/services/aiService'

// 模拟依赖
vi.mock('../src/utils/indexedDB', () => ({
  IndexedDBService: {
    initialize: vi.fn(),
    get: vi.fn(),
    save: vi.fn(),
    delete: vi.fn(),
    getAll: vi.fn(),
    clear: vi.fn()
  },
  indexedDBService: {
    init: vi.fn(),
    getTags: vi.fn()
  }
}))

vi.mock('../src/services/tagService', () => ({
  tagService: {
    getTags: vi.fn()
  }
}))

vi.mock('../src/services/aiChatService', () => ({
  aiChatService: {
    generateText: vi.fn()
  }
}))

vi.mock('../src/services/aiConfigService', () => ({
  aiConfigService: {
    getConfig: vi.fn().mockResolvedValue({
      provider: 'test-provider',
      model: 'test-model'
    })
  }
}))

vi.mock('../src/services/aiCacheService', () => ({
  aiCacheService: {
    getTagsCache: vi.fn(),
    saveTagsCache: vi.fn()
  }
}))

describe('AI标签生成修复测试', () => {
  let aiService

  beforeEach(() => {
    aiService = new AIService()
    vi.clearAllMocks()
  })

  describe('IndexedDBService静态方法', () => {
    it('应该有getAll静态方法', () => {
      expect(typeof IndexedDBService.getAll).toBe('function')
    })

    it('应该有save静态方法', () => {
      expect(typeof IndexedDBService.save).toBe('function')
    })

    it('应该有get静态方法', () => {
      expect(typeof IndexedDBService.get).toBe('function')
    })

    it('应该有delete静态方法', () => {
      expect(typeof IndexedDBService.delete).toBe('function')
    })

    it('应该有clear静态方法', () => {
      expect(typeof IndexedDBService.clear).toBe('function')
    })

    it('应该有initialize静态方法', () => {
      expect(typeof IndexedDBService.initialize).toBe('function')
    })
  })

  describe('AI标签生成集成测试', () => {
    it('应该成功生成标签（模拟正常响应）', async () => {
      // 模拟已有标签
      const { tagService } = await import('../src/services/tagService')
      tagService.getTags.mockResolvedValue([
        { name: '游戏开发' },
        { name: 'Unity' },
        { name: '软件工具' }
      ])

      // 模拟AI响应
      const { aiChatService } = await import('../src/services/aiChatService')
      aiChatService.generateText.mockResolvedValue({
        content: '游戏开发,Unity,软件工具'
      })

      // 模拟缓存
      const { aiCacheService } = await import('../src/services/aiCacheService')
      aiCacheService.getTagsCache.mockResolvedValue(null)
      aiCacheService.saveTagsCache.mockResolvedValue()

      const request = {
        content: '下载Unity安装包',
        title: '开始您的创意项目并下载 Unity Hub | Unity',
        url: 'https://unity.com/cn/download',
        maxTags: 5
      }

      const result = await aiService.generateTags(request)

      expect(result.tags).toEqual(['游戏开发', 'Unity', '软件工具'])
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.processingTime).toBeGreaterThan(0)
    })

    it('应该过滤包含思考过程的响应', async () => {
      // 模拟已有标签
      const { tagService } = await import('../src/services/tagService')
      tagService.getTags.mockResolvedValue([
        { name: '游戏开发' },
        { name: 'Unity' }
      ])

      // 模拟包含思考过程的AI响应
      const { aiChatService } = await import('../src/services/aiChatService')
      aiChatService.generateText.mockResolvedValue({
        content: '<think>好的，网址是Unity的下载页面，内容主要是下载Unity安装包。</think>游戏开发,Unity,软件工具'
      })

      // 模拟缓存
      const { aiCacheService } = await import('../src/services/aiCacheService')
      aiCacheService.getTagsCache.mockResolvedValue(null)
      aiCacheService.saveTagsCache.mockResolvedValue()

      const request = {
        content: '下载Unity安装包',
        title: '开始您的创意项目并下载 Unity Hub | Unity',
        maxTags: 5
      }

      const result = await aiService.generateTags(request)

      // 应该过滤掉思考过程，只保留标签
      expect(result.tags).toEqual(['游戏开发', 'Unity', '软件工具'])
      expect(result.tags).not.toContain('好的')
      expect(result.tags).not.toContain('网址是Unity的下载页面')
    })
  })

  describe('错误处理', () => {
    it('应该在AI服务失败时使用降级策略', async () => {
      // 模拟已有标签
      const { tagService } = await import('../src/services/tagService')
      tagService.getTags.mockResolvedValue([])

      // 模拟AI服务失败
      const { aiChatService } = await import('../src/services/aiChatService')
      aiChatService.generateText.mockRejectedValue(new Error('AI服务不可用'))

      // 模拟缓存
      const { aiCacheService } = await import('../src/services/aiCacheService')
      aiCacheService.getTagsCache.mockResolvedValue(null)

      const request = {
        content: '下载Unity安装包',
        title: '开始您的创意项目并下载 Unity Hub | Unity',
        maxTags: 5
      }

      const result = await aiService.generateTags(request)

      // 应该返回降级策略的结果
      expect(result.tags).toBeDefined()
      expect(result.tags.length).toBeGreaterThan(0)
      expect(result.confidence).toBeLessThan(1) // 降级策略的置信度较低
    })
  })
})
