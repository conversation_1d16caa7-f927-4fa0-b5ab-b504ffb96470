/**
 * 颜色主题测试脚本
 * 验证新的颜色主题是否正确应用
 */

// 测试shadcn颜色变量是否正确设置
function testColorVariables() {
  console.log('🎨 测试shadcn颜色变量...')
  
  // 获取根元素的CSS变量
  const root = document.documentElement
  const computedStyle = getComputedStyle(root)
  
  // 测试主要颜色变量
  const colorTests = [
    { name: '--primary', expected: '262.1 83.3% 57.8%', description: '主色调（紫蓝色）' },
    { name: '--secondary', expected: '220 14.3% 95.9%', description: '次要色（蓝灰色）' },
    { name: '--background', expected: '0 0% 100%', description: '背景色（白色）' },
    { name: '--border', expected: '220 13% 91%', description: '边框色（蓝灰色）' }
  ]
  
  colorTests.forEach(test => {
    const actualValue = computedStyle.getPropertyValue(test.name).trim()
    const isCorrect = actualValue === test.expected
    
    console.log(`${isCorrect ? '✅' : '❌'} ${test.description}:`)
    console.log(`   期望: ${test.expected}`)
    console.log(`   实际: ${actualValue}`)
  })
}

// 测试是否移除了黑色背景
function testBlackBackgroundRemoval() {
  console.log('\n🚫 测试黑色背景移除...')
  
  // 查找可能的黑色背景元素
  const blackBackgroundSelectors = [
    '.bg-black',
    '.bg-gray-900',
    '.bg-slate-900',
    '[style*="background-color: black"]',
    '[style*="background: black"]'
  ]
  
  let foundBlackBackgrounds = 0
  
  blackBackgroundSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    if (elements.length > 0) {
      foundBlackBackgrounds += elements.length
      console.log(`❌ 发现 ${elements.length} 个元素使用选择器: ${selector}`)
      elements.forEach((el, index) => {
        console.log(`   元素 ${index + 1}:`, el.className || el.tagName)
      })
    }
  })
  
  if (foundBlackBackgrounds === 0) {
    console.log('✅ 未发现黑色背景元素')
  } else {
    console.log(`⚠️  发现 ${foundBlackBackgrounds} 个可能的黑色背景元素`)
  }
}

// 测试shadcn组件是否正确使用颜色变量
function testShadcnComponents() {
  console.log('\n🧩 测试shadcn组件颜色...')
  
  // 测试常见的shadcn组件
  const componentTests = [
    { selector: '.bg-primary', description: 'Primary背景组件' },
    { selector: '.bg-secondary', description: 'Secondary背景组件' },
    { selector: '.bg-background', description: 'Background背景组件' },
    { selector: '.text-foreground', description: 'Foreground文本组件' },
    { selector: '.text-muted-foreground', description: 'Muted文本组件' },
    { selector: '.border-border', description: 'Border边框组件' }
  ]
  
  componentTests.forEach(test => {
    const elements = document.querySelectorAll(test.selector)
    const count = elements.length
    
    if (count > 0) {
      console.log(`✅ ${test.description}: 找到 ${count} 个元素`)
    } else {
      console.log(`⚠️  ${test.description}: 未找到元素`)
    }
  })
}

// 测试主题切换功能
function testThemeToggle() {
  console.log('\n🌓 测试主题切换...')
  
  const html = document.documentElement
  const currentTheme = html.classList.contains('dark') ? 'dark' : 'light'
  
  console.log(`当前主题: ${currentTheme}`)
  
  // 查找主题切换按钮
  const themeToggleButton = document.querySelector('[aria-label*="切换主题"]')
  if (themeToggleButton) {
    console.log('✅ 找到主题切换按钮')
    
    // 检查按钮是否使用了新的颜色样式
    const hasNewStyles = themeToggleButton.classList.contains('bg-secondary')
    console.log(`${hasNewStyles ? '✅' : '❌'} 主题切换按钮使用新颜色样式`)
  } else {
    console.log('❌ 未找到主题切换按钮')
  }
}

// 主测试函数
function runColorThemeTests() {
  console.log('🚀 开始颜色主题测试...\n')
  
  try {
    testColorVariables()
    testBlackBackgroundRemoval()
    testShadcnComponents()
    testThemeToggle()
    
    console.log('\n🎉 颜色主题测试完成！')
    console.log('请检查上述结果，确保所有测试都通过。')
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }
}

// 如果在浏览器环境中运行，自动执行测试
if (typeof window !== 'undefined') {
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runColorThemeTests)
  } else {
    runColorThemeTests()
  }
}

// 导出测试函数供其他地方使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runColorThemeTests,
    testColorVariables,
    testBlackBackgroundRemoval,
    testShadcnComponents,
    testThemeToggle
  }
}