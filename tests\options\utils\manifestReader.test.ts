/**
 * manifestReader.ts 单元测试
 */

import { 
  getManifestFromChrome, 
  convertManifestToExtensionInfo, 
  getExtensionInfo,
  isExtensionEnvironment,
  getBuildInfo
} from '../../../src/options/utils/manifestReader'

// 模拟 Chrome 扩展 API
const mockChrome = {
  runtime: {
    getManifest: vi.fn()
  }
}

// 模拟 manifest 数据
const mockManifest = {
  name: 'Test Extension',
  version: '2.0.0',
  description: 'Test extension description',
  manifest_version: 3,
  permissions: ['storage', 'activeTab'],
  author: 'Test Author'
}

describe('manifestReader', () => {
  beforeEach(() => {
    // 重置模拟函数
    vi.clearAllMocks()
    
    // 设置全局 chrome 对象
    ;(global as any).chrome = mockChrome
  })

  afterEach(() => {
    // 清理全局 chrome 对象
    delete (global as any).chrome
  })

  describe('getManifestFromChrome', () => {
    it('应该成功获取 manifest 数据', () => {
      mockChrome.runtime.getManifest.mockReturnValue(mockManifest)
      
      const result = getManifestFromChrome()
      
      expect(result).toEqual(mockManifest)
      expect(mockChrome.runtime.getManifest).toHaveBeenCalledTimes(1)
    })

    it('当 Chrome API 不可用时应该返回 null', () => {
      delete (global as any).chrome
      
      const result = getManifestFromChrome()
      
      expect(result).toBeNull()
    })

    it('当 getManifest 抛出异常时应该返回 null', () => {
      mockChrome.runtime.getManifest.mockImplementation(() => {
        throw new Error('API error')
      })
      
      const result = getManifestFromChrome()
      
      expect(result).toBeNull()
    })
  })

  describe('convertManifestToExtensionInfo', () => {
    it('应该正确转换 manifest 数据', () => {
      const result = convertManifestToExtensionInfo(mockManifest)
      
      expect(result).toEqual({
        name: 'Test Extension',
        version: '2.0.0',
        description: 'Test extension description',
        developer: 'Test Author'
      })
    })

    it('应该处理缺失的字段并使用默认值', () => {
      const incompleteManifest = {
        name: '',
        version: '',
        description: '',
        manifest_version: 3
      }
      
      const result = convertManifestToExtensionInfo(incompleteManifest)
      
      expect(result).toEqual({
        name: 'Universe Bag（乾坤袋）',
        version: '1.0.0',
        description: '智能收藏管理工具，支持AI自动分类和云端同步',
        developer: 'coffeebean'
      })
    })
  })

  describe('getExtensionInfo', () => {
    it('应该从 Chrome API 获取扩展信息', () => {
      mockChrome.runtime.getManifest.mockReturnValue(mockManifest)
      
      const result = getExtensionInfo()
      
      expect(result).toEqual({
        name: 'Test Extension',
        version: '2.0.0',
        description: 'Test extension description',
        developer: 'Test Author'
      })
    })

    it('当 Chrome API 不可用时应该返回默认信息', () => {
      delete (global as any).chrome
      
      const result = getExtensionInfo()
      
      expect(result).toEqual({
        name: 'Universe Bag（乾坤袋）',
        version: '1.0.0',
        description: '智能收藏管理工具，支持AI自动分类和云端同步',
        developer: 'coffeebean'
      })
    })

    it('当获取 manifest 失败时应该返回默认信息', () => {
      mockChrome.runtime.getManifest.mockImplementation(() => {
        throw new Error('API error')
      })
      
      const result = getExtensionInfo()
      
      expect(result).toEqual({
        name: 'Universe Bag（乾坤袋）',
        version: '1.0.0',
        description: '智能收藏管理工具，支持AI自动分类和云端同步',
        developer: 'coffeebean'
      })
    })
  })

  describe('isExtensionEnvironment', () => {
    it('当 Chrome API 可用时应该返回 true', () => {
      const result = isExtensionEnvironment()
      expect(result).toBe(true)
    })

    it('当 Chrome API 不可用时应该返回 false', () => {
      delete (global as any).chrome
      
      const result = isExtensionEnvironment()
      expect(result).toBe(false)
    })

    it('当 runtime 不可用时应该返回 false', () => {
      ;(global as any).chrome = {}
      
      const result = isExtensionEnvironment()
      expect(result).toBe(false)
    })

    it('当 getManifest 不可用时应该返回 false', () => {
      ;(global as any).chrome = { runtime: {} }
      
      const result = isExtensionEnvironment()
      expect(result).toBe(false)
    })
  })

  describe('getBuildInfo', () => {
    it('应该返回构建信息', () => {
      mockChrome.runtime.getManifest.mockReturnValue(mockManifest)
      
      const result = getBuildInfo()
      
      expect(result).toEqual({
        buildDate: expect.stringMatching(/^\d{4}-\d{2}-\d{2}$/),
        buildVersion: '2.0.0',
        manifestVersion: 3
      })
    })

    it('当 manifest 不可用时应该使用默认值', () => {
      delete (global as any).chrome
      
      const result = getBuildInfo()
      
      expect(result).toEqual({
        buildDate: expect.stringMatching(/^\d{4}-\d{2}-\d{2}$/),
        buildVersion: '1.0.0',
        manifestVersion: 3
      })
    })
  })
})