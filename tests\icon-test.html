<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标效果测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-test {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .icon-row {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .icon-row img {
            margin-right: 15px;
            border: 1px solid #ddd;
        }
        .icon-info {
            flex: 1;
        }
        .original-logo {
            max-width: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }
        .extension-preview {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .toolbar-simulation {
            background: #f0f0f0;
            padding: 8px;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
        .toolbar-simulation img {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>Universe Bag 图标效果测试</h1>
    
    <div class="icon-test">
        <h2>原始Logo</h2>
        <img src="../logo.png" alt="原始Logo" class="original-logo">
        <p>这是根目录下的原始logo.png文件</p>
    </div>

    <div class="icon-test">
        <h2>生成的图标文件</h2>
        <div class="icon-row">
            <img src="../public/icons/icon-16.png" alt="16x16图标" width="16" height="16">
            <div class="icon-info">
                <strong>icon-16.png</strong> - 16x16像素<br>
                <small>用于浏览器工具栏和标签页</small>
            </div>
        </div>
        <div class="icon-row">
            <img src="../public/icons/icon-32.png" alt="32x32图标" width="32" height="32">
            <div class="icon-info">
                <strong>icon-32.png</strong> - 32x32像素<br>
                <small>用于扩展管理页面和高分辨率显示</small>
            </div>
        </div>
        <div class="icon-row">
            <img src="../public/icons/icon-48.png" alt="48x48图标" width="48" height="48">
            <div class="icon-info">
                <strong>icon-48.png</strong> - 48x48像素<br>
                <small>用于扩展详情页面</small>
            </div>
        </div>
        <div class="icon-row">
            <img src="../public/icons/icon-128.png" alt="128x128图标" width="128" height="128">
            <div class="icon-info">
                <strong>icon-128.png</strong> - 128x128像素<br>
                <small>用于Chrome网上应用店和安装界面</small>
            </div>
        </div>
    </div>

    <div class="icon-test">
        <h2>浏览器工具栏效果预览</h2>
        <div class="extension-preview">
            <div class="toolbar-simulation">
                <img src="../public/icons/icon-16.png" alt="工具栏图标" width="16" height="16">
                <span>Universe Bag（乾坤袋）</span>
            </div>
            <p><small>这是扩展在浏览器工具栏中的显示效果</small></p>
        </div>
    </div>

    <div class="icon-test">
        <h2>使用说明</h2>
        <ol>
            <li>图标文件已经替换完成，位于 <code>public/icons/</code> 目录下</li>
            <li>如果需要重新构建扩展，请运行构建命令</li>
            <li>在Chrome中重新加载扩展以查看新图标效果</li>
            <li>如果图标显示不理想，可以使用 <code>scripts/generate-icons.html</code> 工具重新生成适合的尺寸</li>
        </ol>
    </div>

    <script>
        // 检查图标文件是否加载成功
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img[src*="icon-"]');
            images.forEach(img => {
                img.onerror = function() {
                    this.style.background = '#ffebee';
                    this.style.border = '2px solid #f44336';
                    this.alt = '图标加载失败';
                };
                img.onload = function() {
                    console.log(`图标 ${this.src} 加载成功`);
                };
            });
        });
    </script>
</body>
</html>