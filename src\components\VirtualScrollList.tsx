// 虚拟滚动列表组件 - 优化大量数据的显示性能

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'

/**
 * 虚拟滚动项目接口
 */
export interface VirtualScrollItem {
  id: string
  height?: number
  data: any
}

/**
 * 虚拟滚动配置
 */
export interface VirtualScrollConfig {
  itemHeight: number // 默认项目高度
  overscan: number // 预渲染项目数
  threshold: number // 滚动阈值
  enableDynamicHeight: boolean // 是否启用动态高度
}

/**
 * 虚拟滚动组件属性
 */
export interface VirtualScrollListProps {
  items: VirtualScrollItem[]
  renderItem: (item: VirtualScrollItem, index: number) => React.ReactNode
  config?: Partial<VirtualScrollConfig>
  className?: string
  onScroll?: (scrollTop: number, scrollDirection: 'up' | 'down') => void
  onVisibleRangeChange?: (startIndex: number, endIndex: number) => void
  loading?: boolean
  loadingComponent?: React.ReactNode
  emptyComponent?: React.ReactNode
}

/**
 * 虚拟滚动列表组件
 * 只渲染可见区域的项目，大幅提升大列表性能
 */
export const VirtualScrollList: React.FC<VirtualScrollListProps> = ({
  items,
  renderItem,
  config = {},
  className = '',
  onScroll,
  onVisibleRangeChange,
  loading = false,
  loadingComponent,
  emptyComponent
}) => {
  const finalConfig: VirtualScrollConfig = {
    itemHeight: 60,
    overscan: 5,
    threshold: 100,
    enableDynamicHeight: false,
    ...config
  }

  const containerRef = useRef<HTMLDivElement>(null)
  const [scrollTop, setScrollTop] = useState(0)
  const [containerHeight, setContainerHeight] = useState(0)
  const [itemHeights, setItemHeights] = useState<Map<string, number>>(new Map())
  const lastScrollTop = useRef(0)
  const scrollDirection = useRef<'up' | 'down'>('down')

  // 计算可见范围
  const visibleRange = useMemo(() => {
    if (!containerHeight || items.length === 0) {
      return { startIndex: 0, endIndex: 0, offsetY: 0 }
    }

    let startIndex = 0
    let endIndex = 0
    let offsetY = 0

    if (finalConfig.enableDynamicHeight) {
      // 动态高度计算
      let accumulatedHeight = 0
      let found = false

      for (let i = 0; i < items.length; i++) {
        const itemHeight = itemHeights.get(items[i].id) || finalConfig.itemHeight
        
        if (!found && accumulatedHeight + itemHeight > scrollTop) {
          startIndex = Math.max(0, i - finalConfig.overscan)
          offsetY = accumulatedHeight - (i - startIndex) * finalConfig.itemHeight
          found = true
        }

        accumulatedHeight += itemHeight

        if (found && accumulatedHeight > scrollTop + containerHeight) {
          endIndex = Math.min(items.length - 1, i + finalConfig.overscan)
          break
        }
      }

      if (!found) {
        startIndex = Math.max(0, items.length - 1)
        endIndex = items.length - 1
      }
    } else {
      // 固定高度计算
      startIndex = Math.max(0, Math.floor(scrollTop / finalConfig.itemHeight) - finalConfig.overscan)
      endIndex = Math.min(
        items.length - 1,
        Math.ceil((scrollTop + containerHeight) / finalConfig.itemHeight) + finalConfig.overscan
      )
      offsetY = startIndex * finalConfig.itemHeight
    }

    return { startIndex, endIndex, offsetY }
  }, [scrollTop, containerHeight, items.length, itemHeights, finalConfig])

  // 计算总高度
  const totalHeight = useMemo(() => {
    if (finalConfig.enableDynamicHeight) {
      return items.reduce((total, item) => {
        return total + (itemHeights.get(item.id) || finalConfig.itemHeight)
      }, 0)
    }
    return items.length * finalConfig.itemHeight
  }, [items, itemHeights, finalConfig])

  // 可见项目
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1)
  }, [items, visibleRange.startIndex, visibleRange.endIndex])

  // 处理滚动事件
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = event.currentTarget.scrollTop
    
    // 防抖处理
    if (Math.abs(newScrollTop - scrollTop) < finalConfig.threshold) {
      return
    }

    // 确定滚动方向
    scrollDirection.current = newScrollTop > lastScrollTop.current ? 'down' : 'up'
    lastScrollTop.current = newScrollTop

    setScrollTop(newScrollTop)
    onScroll?.(newScrollTop, scrollDirection.current)
  }, [scrollTop, finalConfig.threshold, onScroll])

  // 监听容器大小变化
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0]
      if (entry) {
        setContainerHeight(entry.contentRect.height)
      }
    })

    resizeObserver.observe(container)
    
    // 初始化高度
    setContainerHeight(container.clientHeight)

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  // 通知可见范围变化
  useEffect(() => {
    onVisibleRangeChange?.(visibleRange.startIndex, visibleRange.endIndex)
  }, [visibleRange.startIndex, visibleRange.endIndex, onVisibleRangeChange])

  // 更新项目高度（动态高度模式）
  const updateItemHeight = useCallback((itemId: string, height: number) => {
    if (finalConfig.enableDynamicHeight) {
      setItemHeights(prev => {
        const newMap = new Map(prev)
        newMap.set(itemId, height)
        return newMap
      })
    }
  }, [finalConfig.enableDynamicHeight])

  // 渲染项目包装器
  const renderItemWrapper = useCallback((item: VirtualScrollItem, index: number) => {
    const actualIndex = visibleRange.startIndex + index
    
    return (
      <VirtualScrollItemWrapper
        key={item.id}
        item={item}
        index={actualIndex}
        height={finalConfig.enableDynamicHeight ? undefined : finalConfig.itemHeight}
        onHeightChange={updateItemHeight}
      >
        {renderItem(item, actualIndex)}
      </VirtualScrollItemWrapper>
    )
  }, [visibleRange.startIndex, finalConfig, updateItemHeight, renderItem])

  // 加载状态
  if (loading) {
    return (
      <div className={`virtual-scroll-container ${className}`} ref={containerRef}>
        {loadingComponent || (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        )}
      </div>
    )
  }

  // 空状态
  if (items.length === 0) {
    return (
      <div className={`virtual-scroll-container ${className}`} ref={containerRef}>
        {emptyComponent || (
          <div className="flex items-center justify-center h-32 text-gray-500">
            暂无数据
          </div>
        )}
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={`virtual-scroll-container overflow-auto ${className}`}
      onScroll={handleScroll}
      style={{ height: '100%' }}
    >
      <div
        className="virtual-scroll-content relative"
        style={{ height: totalHeight }}
      >
        <div
          className="virtual-scroll-items"
          style={{
            transform: `translateY(${visibleRange.offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map((item, index) => renderItemWrapper(item, index))}
        </div>
      </div>
    </div>
  )
}

/**
 * 虚拟滚动项目包装器属性
 */
interface VirtualScrollItemWrapperProps {
  item: VirtualScrollItem
  index: number
  height?: number
  onHeightChange: (itemId: string, height: number) => void
  children: React.ReactNode
}

/**
 * 虚拟滚动项目包装器
 * 负责测量项目高度并通知父组件
 */
const VirtualScrollItemWrapper: React.FC<VirtualScrollItemWrapperProps> = ({
  item,
  index,
  height,
  onHeightChange,
  children
}) => {
  const itemRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!height && itemRef.current) {
      const itemHeight = itemRef.current.offsetHeight
      onHeightChange(item.id, itemHeight)
    }
  }, [item.id, height, onHeightChange])

  return (
    <div
      ref={itemRef}
      className="virtual-scroll-item"
      style={{ height: height || 'auto' }}
      data-index={index}
      data-item-id={item.id}
    >
      {children}
    </div>
  )
}

export default VirtualScrollList