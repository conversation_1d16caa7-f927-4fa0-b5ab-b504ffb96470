import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'

/**
 * PopupApp组件shadcn重构演示
 * 
 * 此演示展示了PopupApp组件如何使用shadcn组件进行重构：
 * 1. 使用Card组件作为主容器
 * 2. 使用Button组件替换自定义按钮
 * 3. 使用shadcn颜色系统
 * 4. 使用Separator组件分隔内容
 */
const PopupAppDemo: React.FC = () => {
  return (
    <div className="p-6 max-w-md mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">PopupApp shadcn重构演示</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-foreground">重构要点</h3>
            <div className="space-y-1">
              <Badge variant="secondary">使用Card组件作为容器</Badge>
              <Badge variant="secondary">Button组件替换自定义按钮</Badge>
              <Badge variant="secondary">Switch组件替换Toggle</Badge>
              <Badge variant="secondary">shadcn颜色系统</Badge>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-foreground">按钮样式演示</h3>
            <div className="space-y-2">
              <Button className="w-full">主要操作按钮</Button>
              <Button variant="outline" className="w-full">次要操作按钮</Button>
              <Button variant="ghost" className="w-full">幽灵按钮</Button>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-foreground">颜色系统演示</h3>
            <div className="space-y-1">
              <p className="text-sm text-foreground">主要文本颜色</p>
              <p className="text-sm text-muted-foreground">次要文本颜色</p>
              <div className="p-2 bg-muted rounded text-sm">背景色示例</div>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-foreground">状态卡片演示</h3>
            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-3 text-center">
                <p className="text-green-800 text-sm font-medium">成功状态</p>
                <p className="text-green-600 text-xs">使用shadcn颜色系统</p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PopupAppDemo