# shadcn基础UI组件安装文档

## 概述

本文档记录了shadcn/ui基础UI组件的安装情况和使用说明。这些组件是shadcn-ui-migration项目的核心组件，用于替换现有的自定义UI组件。

## 已安装的组件

### 1. Button组件
- **文件路径**: `src/components/ui/button.tsx`
- **功能**: 提供各种样式的按钮组件
- **变体**: 
  - `default`: 默认主要按钮
  - `destructive`: 危险操作按钮
  - `outline`: 轮廓按钮
  - `secondary`: 次要按钮
  - `ghost`: 幽灵按钮
  - `link`: 链接样式按钮
- **尺寸**: `sm`, `default`, `lg`, `icon`

### 2. Input组件
- **文件路径**: `src/components/ui/input.tsx`
- **功能**: 标准输入框组件
- **特性**: 支持所有HTML input属性，包含focus状态样式

### 3. Textarea组件
- **文件路径**: `src/components/ui/textarea.tsx`
- **功能**: 多行文本输入组件
- **特性**: 最小高度80px，支持自动调整大小

### 4. Dialog组件
- **文件路径**: `src/components/ui/dialog.tsx`
- **功能**: 模态对话框组件
- **子组件**:
  - `Dialog`: 根组件
  - `DialogTrigger`: 触发器
  - `DialogContent`: 内容容器
  - `DialogHeader`: 头部区域
  - `DialogTitle`: 标题
  - `DialogDescription`: 描述
  - `DialogFooter`: 底部区域
  - `DialogClose`: 关闭按钮

### 5. AlertDialog组件
- **文件路径**: `src/components/ui/alert-dialog.tsx`
- **功能**: 警告确认对话框组件
- **子组件**:
  - `AlertDialog`: 根组件
  - `AlertDialogTrigger`: 触发器
  - `AlertDialogContent`: 内容容器
  - `AlertDialogHeader`: 头部区域
  - `AlertDialogTitle`: 标题
  - `AlertDialogDescription`: 描述
  - `AlertDialogFooter`: 底部区域
  - `AlertDialogAction`: 确认按钮
  - `AlertDialogCancel`: 取消按钮

### 6. Form组件
- **文件路径**: `src/components/ui/form.tsx`
- **功能**: 表单组件，集成react-hook-form
- **子组件**:
  - `Form`: 表单提供者
  - `FormField`: 表单字段
  - `FormItem`: 表单项容器
  - `FormLabel`: 表单标签
  - `FormControl`: 表单控件
  - `FormDescription`: 表单描述
  - `FormMessage`: 表单错误信息

### 7. Card组件
- **文件路径**: `src/components/ui/card.tsx`
- **功能**: 卡片容器组件
- **子组件**:
  - `Card`: 卡片容器
  - `CardHeader`: 卡片头部
  - `CardTitle`: 卡片标题
  - `CardDescription`: 卡片描述
  - `CardContent`: 卡片内容
  - `CardFooter`: 卡片底部

### 8. Label组件
- **文件路径**: `src/components/ui/label.tsx`
- **功能**: 标签组件，用于表单字段标签

## 依赖项

以下依赖项已自动安装：

```json
{
  "@radix-ui/react-alert-dialog": "^1.1.14",
  "@radix-ui/react-dialog": "^1.1.14",
  "@radix-ui/react-label": "^2.1.7",
  "@radix-ui/react-slot": "^1.2.3",
  "class-variance-authority": "^0.7.1",
  "clsx": "^2.1.1",
  "lucide-react": "^0.263.1",
  "react-hook-form": "^7.62.0",
  "tailwind-merge": "^3.3.1",
  "tailwindcss-animate": "^1.0.7"
}
```

## 使用示例

### 基本按钮使用
```tsx
import { Button } from '@/components/ui/button';

<Button variant="default">默认按钮</Button>
<Button variant="destructive">危险按钮</Button>
<Button variant="outline">轮廓按钮</Button>
```

### 表单组件使用
```tsx
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

<div className="space-y-2">
  <Label htmlFor="email">邮箱</Label>
  <Input id="email" type="email" placeholder="请输入邮箱" />
</div>

<div className="space-y-2">
  <Label htmlFor="message">消息</Label>
  <Textarea id="message" placeholder="请输入消息内容" />
</div>
```

### 对话框使用
```tsx
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

<Dialog>
  <DialogTrigger asChild>
    <Button>打开对话框</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>对话框标题</DialogTitle>
    </DialogHeader>
    <p>对话框内容</p>
  </DialogContent>
</Dialog>
```

### 卡片组件使用
```tsx
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent 
} from '@/components/ui/card';

<Card>
  <CardHeader>
    <CardTitle>卡片标题</CardTitle>
    <CardDescription>卡片描述</CardDescription>
  </CardHeader>
  <CardContent>
    <p>卡片内容</p>
  </CardContent>
</Card>
```

## 测试覆盖

已创建测试文件 `tests/shadcn-basic-components.test.tsx` 来验证所有组件的正确安装和基本功能。

测试包括：
- 组件正确渲染
- Button组件所有变体
- Input和Textarea组件功能
- Card组件结构
- 所有Dialog和Form组件的导入验证

## 演示组件

创建了演示组件 `src/components/examples/BasicComponentsDemo.tsx` 来展示所有基础组件的使用方法。

## 下一步

这些基础组件将在后续任务中用于重构收藏夹页面的各个组件：
- AddBookmarkModal将使用Dialog和Form组件
- BookmarkRow将使用Card和Button组件
- BookmarkCompact将使用Card和Badge组件
- DeleteConfirmModal将使用AlertDialog组件

## 注意事项

1. 所有组件都严格遵循shadcn/ui的设计规范
2. 组件支持通过className进行样式扩展
3. 使用Tailwind CSS类进行样式定制
4. 所有组件都支持TypeScript类型检查
5. 组件已集成无障碍访问性支持