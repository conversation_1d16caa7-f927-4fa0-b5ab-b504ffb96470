// 测试智能推荐优化功能的脚本
// 在浏览器控制台中运行此脚本来测试新的智能推荐功能

console.log('🧪 测试智能推荐优化功能...')

// 测试步骤
async function testSmartRecommendationOptimization() {
  console.log('📋 测试步骤:')
  console.log('1. 测试完善的智能推荐功能（标签、文件夹、描述）')
  console.log('2. 测试一键接受功能')
  console.log('3. 测试智能识别开关设置')
  console.log('4. 测试快速收藏表单界面')
  
  try {
    // 步骤1: 测试完善的智能推荐功能
    console.log('\n🔖 步骤1: 测试完善的智能推荐功能...')
    const recommendResponse = await chrome.runtime.sendMessage({
      type: 'AI_RECOMMEND_ALL',
      data: {
        title: '如何使用React Hooks进行状态管理',
        url: 'https://example.com/react-hooks-guide',
        content: '这是一篇关于React Hooks的详细教程，介绍了useState、useEffect等常用钩子的使用方法。',
        maxRecommendations: 6
      }
    })
    
    if (recommendResponse?.success) {
      console.log('✅ 智能推荐成功:', recommendResponse.data)
      console.log('   - 标签推荐:', recommendResponse.data.tags)
      console.log('   - 文件夹推荐:', recommendResponse.data.folders)
      console.log('   - 描述生成:', recommendResponse.data.description)
    } else {
      console.log('❌ 智能推荐失败:', recommendResponse?.error)
    }

    // 步骤2: 测试设置功能
    console.log('\n⚙️ 步骤2: 测试设置功能...')
    
    // 获取当前设置
    const currentSettings = await chrome.storage.sync.get(['appSettings'])
    console.log('当前设置:', currentSettings.appSettings)
    
    // 测试智能识别开关
    const testSettings = {
      ...currentSettings.appSettings,
      smartRecognition: true
    }
    
    await chrome.storage.sync.set({ appSettings: testSettings })
    console.log('✅ 智能识别开关已开启')

    // 步骤3: 测试快速收藏功能
    console.log('\n📝 步骤3: 测试快速收藏功能...')
    const quickBookmarkResponse = await chrome.runtime.sendMessage({
      type: 'SAVE_DETAILED_BOOKMARK',
      data: {
        type: 'url',
        title: '测试页面标题',
        url: 'https://test.example.com',
        tags: ['测试', 'React', '前端'],
        category: '学习',
        metadata: {
          pageTitle: '测试页面标题',
          siteName: 'test.example.com',
          publishDate: new Date(),
          aiGenerated: false
        }
      }
    })
    
    if (quickBookmarkResponse?.success) {
      console.log('✅ 快速收藏成功:', quickBookmarkResponse.data)
    } else {
      console.log('❌ 快速收藏失败:', quickBookmarkResponse?.error)
    }

    // 步骤4: 测试消息处理器
    console.log('\n📡 步骤4: 测试消息处理器...')
    
    // 测试AI_RECOMMEND_ALL消息类型
    const messageTest = await chrome.runtime.sendMessage({
      type: 'AI_RECOMMEND_ALL',
      data: {
        title: 'JavaScript异步编程指南',
        url: 'https://example.com/js-async',
        content: '详细介绍Promise、async/await等异步编程概念'
      }
    })
    
    if (messageTest?.success) {
      console.log('✅ AI_RECOMMEND_ALL消息处理成功')
    } else {
      console.log('❌ AI_RECOMMEND_ALL消息处理失败:', messageTest?.error)
    }

    console.log('\n🎉 所有测试完成！')
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }
}

// 测试UI组件功能
function testUIComponents() {
  console.log('\n🎨 测试UI组件功能...')
  
  console.log('📋 检查项目:')
  console.log('1. 打开浏览器插件弹窗')
  console.log('2. 验证智能识别开关是否显示在设置区域')
  console.log('3. 点击"收藏当前页面"按钮，验证是否显示快速收藏表单')
  console.log('4. 在快速收藏表单中验证标题和标签字段是否可编辑')
  console.log('5. 点击"智能识别"按钮，验证是否显示智能识别组件')
  console.log('6. 在收藏管理页面编辑收藏时，点击"智能推荐"按钮')
  console.log('7. 验证是否同时生成标签、文件夹和描述')
  console.log('8. 验证"一键接受"按钮是否出现并可用')
  
  console.log('\n✨ 新功能特性:')
  console.log('• 智能推荐现在包含描述生成')
  console.log('• 一键接受按钮可同时应用所有推荐')
  console.log('• 智能识别开关控制自动触发行为')
  console.log('• 快速收藏表单支持即时编辑')
  console.log('• 模块化的智能识别组件可复用')
}

// 运行测试
console.log('🚀 开始运行测试...')
testSmartRecommendationOptimization()
testUIComponents()

console.log('\n📖 使用说明:')
console.log('1. 确保已加载最新的扩展程序')
console.log('2. 在任意网页上打开扩展弹窗进行测试')
console.log('3. 访问收藏管理页面测试编辑功能')
console.log('4. 检查设置中的智能识别开关')
