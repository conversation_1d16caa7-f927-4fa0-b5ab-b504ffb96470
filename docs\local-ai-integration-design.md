# 本地AI集成设计方案

## 架构概览

```
书签数据 → AI适配器接口 → 本地AI服务 (Ollama/OpenAI API/自定义)
    ↓           ↓              ↓
标签建议 ← 内容分析 ← 智能分类
```

## AI服务适配器设计

### 1. AI服务基础接口
```typescript
interface AIServiceProvider {
  name: string;
  type: 'local' | 'cloud' | 'custom';
  isAvailable(): Promise<boolean>;
  analyzeBookmark(bookmark: Bookmark): Promise<BookmarkAnalysis>;
  suggestTags(content: string): Promise<string[]>;
  categorizeBookmarks(bookmarks: Bookmark[]): Promise<CategorySuggestion[]>;
  generateDescription(url: string, title: string): Promise<string>;
}

interface BookmarkAnalysis {
  tags: string[];
  category: string;
  description: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  topics: string[];
  language: string;
}

interface CategorySuggestion {
  bookmarkId: string;
  suggestedCategory: string;
  confidence: number;
  reasoning: string;
}
```

### 2. Ollama 本地AI适配器
```typescript
class OllamaAdapter implements AIServiceProvider {
  name = 'Olla<PERSON>';
  type = 'local' as const;
  private baseUrl: string;
  private model: string;
  
  constructor(config: { baseUrl?: string; model?: string } = {}) {
    this.baseUrl = config.baseUrl || 'http://localhost:11434';
    this.model = config.model || 'llama3.1:8b';
  }
  
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      return response.ok;
    } catch {
      return false;
    }
  }
  
  async analyzeBookmark(bookmark: Bookmark): Promise<BookmarkAnalysis> {
    const prompt = `
分析以下书签信息，返回JSON格式的分析结果：

标题: ${bookmark.title}
URL: ${bookmark.url}
现有标签: ${bookmark.tags?.join(', ') || '无'}

请分析并返回：
1. 推荐的标签（最多5个）
2. 适合的分类
3. 简短描述
4. 主要话题
5. 内容语言

返回格式：
{
  "tags": ["标签1", "标签2"],
  "category": "分类名称",
  "description": "简短描述",
  "topics": ["话题1", "话题2"],
  "language": "zh-CN"
}
`;

    const response = await this.callOllama(prompt);
    return this.parseAnalysisResponse(response);
  }
  
  async suggestTags(content: string): Promise<string[]> {
    const prompt = `
基于以下内容，推荐5个最相关的标签：

内容: ${content}

只返回标签列表，用逗号分隔，不要其他文字。
`;

    const response = await this.callOllama(prompt);
    return response.split(',').map(tag => tag.trim()).filter(Boolean);
  }
  
  async categorizeBookmarks(bookmarks: Bookmark[]): Promise<CategorySuggestion[]> {
    const bookmarkList = bookmarks.map(b => `${b.id}: ${b.title} - ${b.url}`).join('\n');
    
    const prompt = `
为以下书签推荐分类，返回JSON数组：

${bookmarkList}

常见分类包括：开发工具、学习资源、新闻资讯、娱乐、购物、社交、工作、设计、技术文档等

返回格式：
[
  {
    "bookmarkId": "书签ID",
    "suggestedCategory": "推荐分类",
    "confidence": 0.9,
    "reasoning": "推荐理由"
  }
]
`;

    const response = await this.callOllama(prompt);
    return JSON.parse(response);
  }
  
  private async callOllama(prompt: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.3,
          top_p: 0.9
        }
      })
    });
    
    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.response;
  }
  
  private parseAnalysisResponse(response: string): BookmarkAnalysis {
    try {
      const parsed = JSON.parse(response);
      return {
        tags: parsed.tags || [],
        category: parsed.category || '未分类',
        description: parsed.description || '',
        sentiment: 'neutral',
        topics: parsed.topics || [],
        language: parsed.language || 'zh-CN'
      };
    } catch {
      // 如果解析失败，返回默认值
      return {
        tags: [],
        category: '未分类',
        description: '',
        sentiment: 'neutral',
        topics: [],
        language: 'zh-CN'
      };
    }
  }
}
```

### 3. OpenAI API适配器
```typescript
class OpenAIAdapter implements AIServiceProvider {
  name = 'OpenAI';
  type = 'cloud' as const;
  private apiKey: string;
  private model: string;
  
  constructor(config: { apiKey: string; model?: string }) {
    this.apiKey = config.apiKey;
    this.model = config.model || 'gpt-3.5-turbo';
  }
  
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });
      return response.ok;
    } catch {
      return false;
    }
  }
  
  async analyzeBookmark(bookmark: Bookmark): Promise<BookmarkAnalysis> {
    const messages = [
      {
        role: 'system',
        content: '你是一个书签分析助手，帮助用户分析和整理书签。请用中文回复，返回JSON格式的结果。'
      },
      {
        role: 'user',
        content: `
分析这个书签：
标题: ${bookmark.title}
URL: ${bookmark.url}

请返回JSON格式：
{
  "tags": ["推荐标签"],
  "category": "分类",
  "description": "描述",
  "topics": ["主题"],
  "language": "语言代码"
}
`
      }
    ];
    
    const response = await this.callOpenAI(messages);
    return this.parseAnalysisResponse(response);
  }
  
  private async callOpenAI(messages: any[]): Promise<string> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: this.model,
        messages: messages,
        temperature: 0.3,
        max_tokens: 1000
      })
    });
    
    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
  }
}
```

### 4. AI管理器
```typescript
class AIManager {
  private providers: Map<string, AIServiceProvider> = new Map();
  private activeProvider: string | null = null;
  
  constructor() {
    this.initializeProviders();
  }
  
  private async initializeProviders() {
    // 检查本地Ollama是否可用
    const ollama = new OllamaAdapter();
    if (await ollama.isAvailable()) {
      this.providers.set('ollama', ollama);
    }
    
    // 从设置中加载API配置
    const settings = await this.loadAISettings();
    
    if (settings.openai?.apiKey) {
      this.providers.set('openai', new OpenAIAdapter(settings.openai));
    }
    
    if (settings.claude?.apiKey) {
      this.providers.set('claude', new ClaudeAdapter(settings.claude));
    }
    
    if (settings.custom?.endpoint) {
      this.providers.set('custom', new CustomAPIAdapter(settings.custom));
    }
    
    // 设置默认提供商
    this.activeProvider = settings.defaultProvider || this.getFirstAvailableProvider();
  }
  
  async analyzeBookmark(bookmark: Bookmark): Promise<BookmarkAnalysis | null> {
    const provider = this.getActiveProvider();
    if (!provider) return null;
    
    try {
      return await provider.analyzeBookmark(bookmark);
    } catch (error) {
      console.error('AI analysis failed:', error);
      return null;
    }
  }
  
  async batchAnalyzeBookmarks(bookmarks: Bookmark[]): Promise<Map<string, BookmarkAnalysis>> {
    const results = new Map<string, BookmarkAnalysis>();
    const provider = this.getActiveProvider();
    
    if (!provider) return results;
    
    // 批量处理，避免API限制
    const batchSize = 5;
    for (let i = 0; i < bookmarks.length; i += batchSize) {
      const batch = bookmarks.slice(i, i + batchSize);
      
      const promises = batch.map(async (bookmark) => {
        try {
          const analysis = await provider.analyzeBookmark(bookmark);
          return { id: bookmark.id, analysis };
        } catch (error) {
          console.error(`Analysis failed for bookmark ${bookmark.id}:`, error);
          return null;
        }
      });
      
      const batchResults = await Promise.all(promises);
      batchResults.forEach(result => {
        if (result) {
          results.set(result.id, result.analysis);
        }
      });
      
      // 添加延迟避免API限制
      if (i + batchSize < bookmarks.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return results;
  }
  
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }
  
  setActiveProvider(providerName: string): boolean {
    if (this.providers.has(providerName)) {
      this.activeProvider = providerName;
      return true;
    }
    return false;
  }
  
  private getActiveProvider(): AIServiceProvider | null {
    return this.activeProvider ? this.providers.get(this.activeProvider) || null : null;
  }
  
  private getFirstAvailableProvider(): string | null {
    return this.providers.size > 0 ? Array.from(this.providers.keys())[0] : null;
  }
}
```

## 用户界面设计

### AI设置页面
```typescript
const AISettingsComponent = () => {
  const [aiSettings, setAISettings] = useState({
    defaultProvider: 'ollama',
    ollama: { baseUrl: 'http://localhost:11434', model: 'llama3.1:8b' },
    openai: { apiKey: '', model: 'gpt-3.5-turbo' },
    claude: { apiKey: '', model: 'claude-3-sonnet' },
    custom: { endpoint: '', apiKey: '', model: '' }
  });
  
  const [availableProviders, setAvailableProviders] = useState<string[]>([]);
  
  return (
    <div className="ai-settings">
      <h3>AI 助手配置</h3>
      
      <div className="provider-selection">
        <label>默认AI服务：</label>
        <select 
          value={aiSettings.defaultProvider}
          onChange={(e) => setAISettings({...aiSettings, defaultProvider: e.target.value})}
        >
          {availableProviders.map(provider => (
            <option key={provider} value={provider}>
              {getProviderDisplayName(provider)}
            </option>
          ))}
        </select>
      </div>
      
      <div className="provider-configs">
        <AIProviderConfig 
          title="本地AI (Ollama)"
          description="在本地运行AI模型，完全私密，无API费用"
          config={aiSettings.ollama}
          onConfigChange={(config) => setAISettings({...aiSettings, ollama: config})}
        />
        
        <AIProviderConfig 
          title="OpenAI"
          description="使用GPT模型，需要API密钥"
          config={aiSettings.openai}
          onConfigChange={(config) => setAISettings({...aiSettings, openai: config})}
        />
        
        <AIProviderConfig 
          title="自定义API"
          description="支持兼容OpenAI格式的其他API服务"
          config={aiSettings.custom}
          onConfigChange={(config) => setAISettings({...aiSettings, custom: config})}
        />
      </div>
      
      <AITestPanel />
    </div>
  );
};
```

## 功能特性

### 1. 智能标签推荐
- 基于书签标题和URL分析
- 学习用户的标签使用习惯
- 支持多语言标签

### 2. 自动分类
- 根据内容自动推荐分类
- 支持自定义分类规则
- 批量分类处理

### 3. 内容摘要
- 自动生成书签描述
- 提取关键信息
- 支持长文本摘要

### 4. 重复检测
- 识别相似或重复的书签
- 智能合并建议
- 清理无效链接

## 隐私和安全

### 1. 本地优先
- 推荐使用本地AI模型
- 数据不离开用户设备
- 支持完全离线使用

### 2. API密钥管理
- 本地加密存储API密钥
- 支持密钥轮换
- 安全的密钥输入界面

### 3. 数据最小化
- 只发送必要的书签信息
- 不发送敏感个人数据
- 支持数据脱敏处理

这个设计方案让用户可以选择最适合自己的AI服务，既支持完全本地化的隐私保护，也支持云端AI的强大功能。