<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TagList 组件演示</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useMemo } = React;

        // 图标组件
        const Tag = ({ className = "w-4 h-4" }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
        );

        const Plus = ({ className = "w-4 h-4" }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
        );

        const Search = ({ className = "w-4 h-4" }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        );

        const Grid = ({ className = "w-4 h-4" }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
        );

        const List = ({ className = "w-4 h-4" }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
            </svg>
        );

        const Hash = ({ className = "w-4 h-4" }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
            </svg>
        );

        const Edit = ({ className = "w-4 h-4" }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
        );

        const Trash2 = ({ className = "w-4 h-4" }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
        );

        // 简化的 TagCard 组件
        const TagCard = ({ tag, onEdit, onDelete, onClick, className = '' }) => {
            const handleCardClick = (e) => {
                if (e.target.closest('.tag-card-actions')) {
                    return;
                }
                onClick?.();
            };

            const formatTime = (date) => {
                const dateObj = new Date(date);
                const now = new Date();
                const diffMs = now.getTime() - dateObj.getTime();
                const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                
                if (diffDays === 0) return '今天';
                if (diffDays === 1) return '昨天';
                if (diffDays < 7) return `${diffDays}天前`;
                return dateObj.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
            };

            const getUsageLevel = () => {
                const usageCount = tag.usageCount || 0;
                if (usageCount >= 20) return { level: 'high', label: '高频', color: 'bg-red-100 text-red-700' };
                if (usageCount >= 5) return { level: 'medium', label: '中频', color: 'bg-yellow-100 text-yellow-700' };
                if (usageCount >= 1) return { level: 'low', label: '低频', color: 'bg-green-100 text-green-700' };
                return { level: 'none', label: '未使用', color: 'bg-gray-100 text-gray-600' };
            };

            const usageLevel = getUsageLevel();

            return (
                <div 
                    className={`group relative bg-white border-2 rounded-lg p-4 hover:shadow-lg transition-all duration-200 cursor-pointer hover:border-gray-300 ${className}`}
                    style={{
                        backgroundColor: tag.color ? `${tag.color}15` : '#F3F4F6',
                        borderColor: tag.color ? `${tag.color}40` : '#D1D5DB'
                    }}
                    onClick={handleCardClick}
                >
                    {/* 头部 */}
                    <div className="flex items-start justify-between mb-3">
                        <div className="flex items-start space-x-3 flex-1 min-w-0">
                            <div className="flex-shrink-0 relative">
                                <div 
                                    className="w-10 h-10 rounded-lg flex items-center justify-center"
                                    style={{ backgroundColor: tag.color || '#F3F4F6' }}
                                >
                                    <Tag className="w-5 h-5 text-white" />
                                </div>
                                <div 
                                    className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                                        usageLevel.level === 'high' ? 'bg-red-500' :
                                        usageLevel.level === 'medium' ? 'bg-yellow-500' :
                                        usageLevel.level === 'low' ? 'bg-green-500' : 'bg-gray-400'
                                    }`}
                                    title={`使用频率: ${usageLevel.label}`}
                                />
                            </div>
                            <div className="flex-1 min-w-0">
                                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors truncate">
                                    {tag.name}
                                </h3>
                                <p className="text-sm text-gray-600 mt-1">
                                    标签颜色: {tag.color || '默认'}
                                </p>
                            </div>
                        </div>
                        <div className="tag-card-actions flex-shrink-0 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                            <button
                                onClick={(e) => { e.stopPropagation(); onEdit(); }}
                                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                title="编辑标签"
                            >
                                <Edit className="w-4 h-4" />
                            </button>
                            <button
                                onClick={(e) => { e.stopPropagation(); onDelete(); }}
                                className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                title="删除标签"
                            >
                                <Trash2 className="w-4 h-4" />
                            </button>
                        </div>
                    </div>

                    {/* 统计信息 */}
                    <div className="space-y-2 mb-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 text-sm text-gray-600">
                                <Hash className="w-4 h-4" />
                                <span>使用次数</span>
                            </div>
                            <span className="text-lg font-semibold text-gray-900">
                                {tag.usageCount || 0}
                            </span>
                        </div>
                        <div className="border-t border-gray-200" />
                        <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>创建时间</span>
                            <span>{formatTime(tag.createdAt)}</span>
                        </div>
                    </div>

                    {/* 底部 */}
                    <div className="flex items-center justify-between">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${usageLevel.color}`}>
                            {usageLevel.label}
                        </span>
                        <div className="flex items-center space-x-2">
                            <div 
                                className="w-4 h-4 rounded-full border border-gray-300"
                                style={{ backgroundColor: tag.color || '#6B7280' }}
                                title={`标签颜色: ${tag.color || '默认'}`}
                            />
                            <span className="text-xs text-gray-500 font-mono">
                                {tag.color || '#6B7280'}
                            </span>
                        </div>
                    </div>
                </div>
            );
        };

        // TagList 组件
        const TagList = ({ 
            tags, 
            onTagEdit, 
            onTagDelete, 
            onTagClick, 
            onCreateTag, 
            loading = false, 
            searchQuery = '', 
            onSearchChange, 
            sortBy = 'name-asc', 
            onSortChange, 
            className = '' 
        }) => {
            const [viewMode, setViewMode] = useState('grid');
            const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

            const handleSearchChange = (e) => {
                const query = e.target.value;
                setLocalSearchQuery(query);
                onSearchChange?.(query);
            };

            const handleSortChange = (e) => {
                const newSortBy = e.target.value;
                onSortChange?.(newSortBy);
            };

            const filteredAndSortedTags = useMemo(() => {
                let result = [...tags];

                // 搜索过滤
                const query = onSearchChange ? searchQuery : localSearchQuery;
                if (query.trim()) {
                    const lowerQuery = query.toLowerCase();
                    result = result.filter(tag => 
                        tag.name.toLowerCase().includes(lowerQuery)
                    );
                }

                // 排序
                result.sort((a, b) => {
                    switch (sortBy) {
                        case 'name-asc':
                            return a.name.localeCompare(b.name, 'zh-CN');
                        case 'name-desc':
                            return b.name.localeCompare(a.name, 'zh-CN');
                        case 'usage-asc':
                            return a.usageCount - b.usageCount;
                        case 'usage-desc':
                            return b.usageCount - a.usageCount;
                        case 'created-asc':
                            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
                        case 'created-desc':
                            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                        default:
                            return 0;
                    }
                });

                return result;
            }, [tags, searchQuery, localSearchQuery, sortBy, onSearchChange]);

            // 渲染加载状态
            const renderLoadingState = () => (
                <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
                    <p className="text-gray-500">加载标签数据中...</p>
                </div>
            );

            // 渲染空状态
            const renderEmptyState = () => {
                const hasSearchQuery = (onSearchChange ? searchQuery : localSearchQuery).trim();
                
                if (hasSearchQuery) {
                    return (
                        <div className="text-center py-12">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <Search className="w-8 h-8 text-gray-400" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的标签</h3>
                            <p className="text-gray-500 mb-6">尝试使用不同的关键词搜索，或创建新的标签</p>
                            {onCreateTag && (
                                <button
                                    onClick={onCreateTag}
                                    className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    创建标签
                                </button>
                            )}
                        </div>
                    );
                }

                return (
                    <div className="text-center py-12">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Tag className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无标签</h3>
                        <p className="text-gray-500 mb-6">创建您的第一个标签来更好地组织书签</p>
                        {onCreateTag && (
                            <button
                                onClick={onCreateTag}
                                className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                            >
                                <Plus className="w-4 h-4 mr-2" />
                                创建标签
                            </button>
                        )}
                    </div>
                );
            };

            // 渲染标签统计信息
            const renderTagStats = () => {
                if (tags.length === 0) return null;

                const totalUsage = tags.reduce((sum, tag) => sum + tag.usageCount, 0);
                const activeTags = tags.filter(tag => tag.usageCount > 0).length;
                const unusedTags = tags.length - activeTags;
                const averageUsage = totalUsage > 0 ? Math.round(totalUsage / activeTags) : 0;

                return (
                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 text-center">
                            <div>
                                <div className="text-2xl font-bold text-gray-900">{tags.length}</div>
                                <div className="text-sm text-gray-600">总标签数</div>
                            </div>
                            <div>
                                <div className="text-2xl font-bold text-green-600">{activeTags}</div>
                                <div className="text-sm text-gray-600">活跃标签</div>
                            </div>
                            <div>
                                <div className="text-2xl font-bold text-gray-400">{unusedTags}</div>
                                <div className="text-sm text-gray-600">未使用标签</div>
                            </div>
                            <div>
                                <div className="text-2xl font-bold text-primary-600">{averageUsage}</div>
                                <div className="text-sm text-gray-600">平均使用次数</div>
                            </div>
                        </div>
                    </div>
                );
            };

            // 渲染工具栏
            const renderToolbar = () => (
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                    <div className="flex-1 relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input
                            type="text"
                            placeholder="搜索标签..."
                            value={onSearchChange ? searchQuery : localSearchQuery}
                            onChange={handleSearchChange}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        />
                    </div>
                    <div className="flex items-center space-x-2">
                        <label className="text-sm font-medium text-gray-700 whitespace-nowrap">排序方式:</label>
                        <select
                            value={sortBy}
                            onChange={handleSortChange}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        >
                            <option value="name-asc">名称 A-Z</option>
                            <option value="name-desc">名称 Z-A</option>
                            <option value="usage-desc">使用次数 ↓</option>
                            <option value="usage-asc">使用次数 ↑</option>
                            <option value="created-desc">创建时间 ↓</option>
                            <option value="created-asc">创建时间 ↑</option>
                        </select>
                    </div>
                    <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                        <button
                            onClick={() => setViewMode('grid')}
                            className={`p-2 rounded-md transition-colors ${
                                viewMode === 'grid' 
                                    ? 'bg-white text-primary-600 shadow-sm' 
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                            title="网格视图"
                        >
                            <Grid className="w-4 h-4" />
                        </button>
                        <button
                            onClick={() => setViewMode('list')}
                            className={`p-2 rounded-md transition-colors ${
                                viewMode === 'list' 
                                    ? 'bg-white text-primary-600 shadow-sm' 
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                            title="列表视图"
                        >
                            <List className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            );

            // 渲染结果计数
            const renderResultCount = () => {
                if (loading || tags.length === 0) return null;

                const query = onSearchChange ? searchQuery : localSearchQuery;
                const isFiltered = query.trim();
                const count = filteredAndSortedTags.length;

                return (
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Hash className="w-4 h-4" />
                            <span>
                                {isFiltered ? `显示 ${count} 个标签，共 ${tags.length} 个` : `共 ${count} 个标签`}
                            </span>
                        </div>
                        {isFiltered && (
                            <button
                                onClick={() => {
                                    setLocalSearchQuery('');
                                    onSearchChange?.('');
                                }}
                                className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                            >
                                清除筛选
                            </button>
                        )}
                    </div>
                );
            };

            return (
                <div className={`space-y-6 ${className}`}>
                    {!loading && renderTagStats()}
                    {!loading && tags.length > 0 && renderToolbar()}
                    {renderResultCount()}
                    <div>
                        {loading ? (
                            renderLoadingState()
                        ) : filteredAndSortedTags.length === 0 ? (
                            renderEmptyState()
                        ) : (
                            <div className={`
                                ${viewMode === 'grid' 
                                    ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
                                    : 'space-y-4'
                                }
                            `}>
                                {filteredAndSortedTags.map((tag) => (
                                    <TagCard
                                        key={tag.id}
                                        tag={tag}
                                        onEdit={() => onTagEdit(tag)}
                                        onDelete={() => onTagDelete(tag)}
                                        onClick={onTagClick ? () => onTagClick(tag) : undefined}
                                        className={viewMode === 'list' ? 'max-w-none' : 'h-full'}
                                    />
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            );
        };

        // 演示应用
        const TagListDemo = () => {
            const createDemoTag = (id, name, usageCount, color) => ({
                id,
                name,
                color: color || `#${Math.floor(Math.random()*16777215).toString(16)}`,
                usageCount,
                createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
            });

            const demoTags = [
                createDemoTag('1', '技术', 25, '#3B82F6'),
                createDemoTag('2', '学习', 18, '#10B981'),
                createDemoTag('3', '工具', 12, '#F59E0B'),
                createDemoTag('4', '新闻', 8, '#EF4444'),
                createDemoTag('5', '娱乐', 15, '#8B5CF6'),
                createDemoTag('6', '其他', 0, '#6B7280'),
                createDemoTag('7', '前端开发', 22, '#06B6D4'),
                createDemoTag('8', '后端开发', 16, '#84CC16'),
                createDemoTag('9', '设计', 9, '#F97316'),
                createDemoTag('10', '产品', 5, '#EC4899')
            ];

            const [tags, setTags] = useState(demoTags);
            const [loading, setLoading] = useState(false);
            const [searchQuery, setSearchQuery] = useState('');
            const [sortBy, setSortBy] = useState('name-asc');

            const handleTagEdit = (tag) => {
                alert(`编辑标签: ${tag.name}`);
            };

            const handleTagDelete = (tag) => {
                if (confirm(`确定要删除标签 "${tag.name}" 吗？`)) {
                    setTags(prevTags => prevTags.filter(t => t.id !== tag.id));
                }
            };

            const handleTagClick = (tag) => {
                alert(`点击了标签: ${tag.name}`);
            };

            const handleCreateTag = () => {
                const name = prompt('请输入新标签名称:');
                if (name && name.trim()) {
                    const newTag = createDemoTag(
                        Date.now().toString(),
                        name.trim(),
                        0
                    );
                    setTags(prevTags => [...prevTags, newTag]);
                }
            };

            const toggleLoading = () => {
                setLoading(true);
                setTimeout(() => setLoading(false), 2000);
            };

            const resetData = () => {
                setTags(demoTags);
                setSearchQuery('');
                setSortBy('name-asc');
            };

            const clearTags = () => {
                setTags([]);
            };

            return (
                <div className="max-w-7xl mx-auto p-6 space-y-8">
                    <div className="text-center">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">TagList 组件演示</h1>
                        <p className="text-gray-600">展示标签列表组件的各种功能和交互效果</p>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border p-6">
                        <h2 className="text-lg font-semibold text-gray-900 mb-4">控制面板</h2>
                        <div className="flex flex-wrap gap-4">
                            <button
                                onClick={toggleLoading}
                                disabled={loading}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                            >
                                {loading ? '加载中...' : '模拟加载'}
                            </button>
                            <button
                                onClick={resetData}
                                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                            >
                                重置数据
                            </button>
                            <button
                                onClick={clearTags}
                                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                                清空标签
                            </button>
                            <button
                                onClick={handleCreateTag}
                                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                            >
                                添加标签
                            </button>
                        </div>
                        <div className="mt-4 text-sm text-gray-600">
                            <p>当前状态: 共 {tags.length} 个标签</p>
                            <p>搜索查询: "{searchQuery}"</p>
                            <p>排序方式: {sortBy}</p>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border p-6">
                        <h2 className="text-lg font-semibold text-gray-900 mb-6">标签列表</h2>
                        <TagList
                            tags={tags}
                            onTagEdit={handleTagEdit}
                            onTagDelete={handleTagDelete}
                            onTagClick={handleTagClick}
                            onCreateTag={handleCreateTag}
                            loading={loading}
                            searchQuery={searchQuery}
                            onSearchChange={setSearchQuery}
                            sortBy={sortBy}
                            onSortChange={setSortBy}
                        />
                    </div>
                </div>
            );
        };

        ReactDOM.render(<TagListDemo />, document.getElementById('root'));
    </script>
</body>
</html>