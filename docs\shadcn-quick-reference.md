# shadcn/ui 快速参考指南

## 🎨 常用颜色映射

### 背景色
```css
bg-gray-100     → bg-secondary
bg-gray-200     → bg-secondary/80
bg-gray-800     → bg-secondary (深色模式自动适配)
bg-white        → bg-background
bg-blue-500     → bg-primary
```

### 文本色
```css
text-gray-700   → text-secondary-foreground
text-gray-300   → text-secondary-foreground (深色模式自动适配)
text-black      → text-foreground
text-white      → text-primary-foreground
```

### 边框色
```css
border-gray-300 → border-border
border-blue-500 → border-primary
focus:ring-*    → focus:ring-ring
```

## 🔧 迁移检查清单

### ✅ 必须做的事情
- [ ] 替换所有 `bg-gray-*` 为 `bg-secondary`
- [ ] 替换所有 `text-gray-*` 为 `text-secondary-foreground`
- [ ] 替换所有 `border-gray-*` 为 `border-border`
- [ ] 替换所有 `focus:ring-*` 为 `focus:ring-ring`
- [ ] 添加性能优化 (`useMemo`, `useCallback`)
- [ ] 编写测试用例
- [ ] 验证无障碍性

### ❌ 避免做的事情
- 不要使用硬编码颜色值
- 不要使用 `dark:` 前缀（shadcn自动处理）
- 不要忘记性能优化
- 不要跳过测试编写

## 📝 代码模板

### 组件模板
```typescript
import React, { useMemo } from 'react'

interface Props {
  className?: string
}

const Component: React.FC<Props> = ({ className = '' }) => {
  // 性能优化
  const memoizedValue = useMemo(() => {
    // 计算逻辑
  }, [dependencies])

  return (
    <div className={`
      bg-secondary hover:bg-secondary/80
      text-secondary-foreground
      border border-border
      focus:ring-2 focus:ring-ring focus:outline-none
      ${className}
    `}>
      {/* 内容 */}
    </div>
  )
}

export default Component
```

### 测试模板
```typescript
describe('Component - shadcn重构测试', () => {
  it('应该使用shadcn颜色系统', () => {
    render(<Component />)
    const element = screen.getByRole('button')
    
    expect(element).toHaveClass('bg-secondary')
    expect(element).not.toHaveClass('bg-gray-100')
  })
})
```

## 🚀 性能优化要点

```typescript
// ✅ 使用useMemo缓存计算
const result = useMemo(() => expensiveCalc(), [deps])

// ✅ 使用useCallback缓存函数
const handler = useCallback(() => {}, [deps])

// ✅ 使用React.memo包装组件
export default React.memo(Component)
```

## 🎯 质量标准

- **shadcn集成：** 100% 使用shadcn颜色变量
- **测试覆盖：** 100% 测试覆盖率
- **性能优化：** 必须使用useMemo/useCallback
- **无障碍性：** 必须支持键盘导航和屏幕阅读器