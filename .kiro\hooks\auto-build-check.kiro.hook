{"enabled": true, "name": "自动构建检查", "description": "当完成一次修改之后，自动构建一次插件，检查报错并修复", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.js", "src/**/*.ts", "src/**/*.jsx", "src/**/*.tsx", "src/**/*.vue", "manifest.json", "package.json", "vite.config.ts", "tsconfig.json"]}, "then": {"type": "askAgent", "prompt": "检测到源代码文件发生变化。请执行以下操作：\n\n1. 运行构建命令 `npm run build` 构建插件\n2. 检查构建过程中的任何错误或警告\n3. 如果有错误，分析错误原因并提供修复方案\n4. 如果有警告，评估是否需要处理\n5. 运行相关测试确保功能正常\n6. 提供构建结果的简要总结\n\n请使用中文回复，并确保修复过程中不影响已有的正常功能。"}}