/**
 * Chrome 扩展 API 模拟
 * 提供完整的Chrome扩展API模拟，用于测试环境
 */

import { vi } from 'vitest'
import { TEST_CONFIG } from './test-config'

// Chrome Runtime API 模拟
const mockRuntime = {
  sendMessage: vi.fn().mockResolvedValue({ success: true, data: [] }),
  getManifest: vi.fn(() => ({
    name: TEST_CONFIG.EXTENSION_NAME,
    version: TEST_CONFIG.EXTENSION_VERSION,
    description: TEST_CONFIG.EXTENSION_DESCRIPTION,
    manifest_version: 3,
    permissions: TEST_CONFIG.EXTENSION_PERMISSIONS,
    action: {
      default_popup: 'src/popup/index.html',
      default_title: 'Universe Bag'
    },
    options_page: 'src/options/index.html'
  })),
  getURL: vi.fn((path: string) => `chrome-extension://${TEST_CONFIG.EXTENSION_ID}/${path}`),
  connect: vi.fn(() => ({
    postMessage: vi.fn(),
    disconnect: vi.fn(),
    onMessage: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    },
    onDisconnect: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  })),
  onMessage: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn().mockReturnValue(false)
  },
  onConnect: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  onInstalled: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  onStartup: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  id: TEST_CONFIG.EXTENSION_ID,
  lastError: null
}

// Chrome Storage API 模拟
const mockStorage = {
  sync: {
    get: vi.fn().mockImplementation((keys) => {
      const defaultData = TEST_CONFIG.DEFAULT_TEST_DATA
      
      if (typeof keys === 'string') {
        return Promise.resolve({ [keys]: defaultData[keys as keyof typeof defaultData] })
      } else if (Array.isArray(keys)) {
        const result: any = {}
        keys.forEach(key => {
          result[key] = defaultData[key as keyof typeof defaultData]
        })
        return Promise.resolve(result)
      } else if (keys === null || keys === undefined) {
        return Promise.resolve(defaultData)
      } else {
        return Promise.resolve(keys)
      }
    }),
    set: vi.fn().mockResolvedValue(undefined),
    remove: vi.fn().mockResolvedValue(undefined),
    clear: vi.fn().mockResolvedValue(undefined),
    getBytesInUse: vi.fn().mockResolvedValue(0),
    onChanged: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  },
  local: {
    get: vi.fn().mockImplementation((keys) => {
      const defaultData = {
        cache: {},
        tempData: {},
        userPreferences: {}
      }
      
      if (typeof keys === 'string') {
        return Promise.resolve({ [keys]: defaultData[keys as keyof typeof defaultData] })
      } else if (Array.isArray(keys)) {
        const result: any = {}
        keys.forEach(key => {
          result[key] = defaultData[key as keyof typeof defaultData]
        })
        return Promise.resolve(result)
      } else if (keys === null || keys === undefined) {
        return Promise.resolve(defaultData)
      } else {
        return Promise.resolve(keys)
      }
    }),
    set: vi.fn().mockResolvedValue(undefined),
    remove: vi.fn().mockResolvedValue(undefined),
    clear: vi.fn().mockResolvedValue(undefined),
    getBytesInUse: vi.fn().mockResolvedValue(0),
    onChanged: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  },
  session: {
    get: vi.fn().mockResolvedValue({}),
    set: vi.fn().mockResolvedValue(undefined),
    remove: vi.fn().mockResolvedValue(undefined),
    clear: vi.fn().mockResolvedValue(undefined)
  },
  onChanged: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  }
}

// Chrome Tabs API 模拟
const mockTabs = {
  query: vi.fn().mockResolvedValue([
    {
      id: 1,
      title: '测试页面',
      url: 'https://example.com',
      active: true,
      windowId: 1,
      index: 0,
      highlighted: true,
      incognito: false,
      pinned: false,
      audible: false,
      discarded: false,
      autoDiscardable: true,
      mutedInfo: { muted: false },
      status: 'complete'
    }
  ]),
  get: vi.fn().mockResolvedValue({
    id: 1,
    title: '测试页面',
    url: 'https://example.com',
    active: true,
    windowId: 1,
    index: 0
  }),
  create: vi.fn().mockResolvedValue({
    id: 2,
    title: '新标签页',
    url: 'chrome://newtab/',
    active: true,
    windowId: 1,
    index: 1
  }),
  update: vi.fn().mockResolvedValue({
    id: 1,
    title: '更新的页面',
    url: 'https://updated.com',
    active: true,
    windowId: 1,
    index: 0
  }),
  remove: vi.fn().mockResolvedValue(undefined),
  reload: vi.fn().mockResolvedValue(undefined),
  duplicate: vi.fn().mockResolvedValue({
    id: 3,
    title: '复制的页面',
    url: 'https://example.com',
    active: false,
    windowId: 1,
    index: 1
  }),
  onCreated: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  onUpdated: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  onRemoved: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  onActivated: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  }
}

// Chrome Action API 模拟
const mockAction = {
  setBadgeText: vi.fn().mockResolvedValue(undefined),
  getBadgeText: vi.fn().mockResolvedValue(''),
  setBadgeBackgroundColor: vi.fn().mockResolvedValue(undefined),
  getBadgeBackgroundColor: vi.fn().mockResolvedValue([0, 0, 0, 0]),
  setTitle: vi.fn().mockResolvedValue(undefined),
  getTitle: vi.fn().mockResolvedValue('Universe Bag'),
  setIcon: vi.fn().mockResolvedValue(undefined),
  setPopup: vi.fn().mockResolvedValue(undefined),
  getPopup: vi.fn().mockResolvedValue('src/popup/index.html'),
  openPopup: vi.fn().mockResolvedValue(undefined),
  enable: vi.fn().mockResolvedValue(undefined),
  disable: vi.fn().mockResolvedValue(undefined),
  isEnabled: vi.fn().mockResolvedValue(true),
  onClicked: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  }
}

// Chrome ContextMenus API 模拟
const mockContextMenus = {
  create: vi.fn().mockReturnValue('menu-id'),
  update: vi.fn().mockResolvedValue(undefined),
  remove: vi.fn().mockResolvedValue(undefined),
  removeAll: vi.fn().mockResolvedValue(undefined),
  onClicked: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  }
}

// Chrome Scripting API 模拟
const mockScripting = {
  executeScript: vi.fn().mockResolvedValue([{ result: null }]),
  insertCSS: vi.fn().mockResolvedValue(undefined),
  removeCSS: vi.fn().mockResolvedValue(undefined),
  registerContentScripts: vi.fn().mockResolvedValue(undefined),
  unregisterContentScripts: vi.fn().mockResolvedValue(undefined),
  getRegisteredContentScripts: vi.fn().mockResolvedValue([])
}

// Chrome Windows API 模拟
const mockWindows = {
  get: vi.fn().mockResolvedValue({
    id: 1,
    focused: true,
    top: 0,
    left: 0,
    width: 1920,
    height: 1080,
    incognito: false,
    type: 'normal',
    state: 'normal',
    alwaysOnTop: false,
    sessionId: 'session-1'
  }),
  getCurrent: vi.fn().mockResolvedValue({
    id: 1,
    focused: true,
    top: 0,
    left: 0,
    width: 1920,
    height: 1080,
    incognito: false,
    type: 'normal',
    state: 'normal',
    alwaysOnTop: false,
    sessionId: 'session-1'
  }),
  getAll: vi.fn().mockResolvedValue([{
    id: 1,
    focused: true,
    top: 0,
    left: 0,
    width: 1920,
    height: 1080,
    incognito: false,
    type: 'normal',
    state: 'normal',
    alwaysOnTop: false,
    sessionId: 'session-1'
  }]),
  create: vi.fn().mockResolvedValue({
    id: 2,
    focused: true,
    top: 100,
    left: 100,
    width: 800,
    height: 600,
    incognito: false,
    type: 'normal',
    state: 'normal',
    alwaysOnTop: false,
    sessionId: 'session-2'
  }),
  update: vi.fn().mockResolvedValue({
    id: 1,
    focused: true,
    top: 0,
    left: 0,
    width: 1920,
    height: 1080,
    incognito: false,
    type: 'normal',
    state: 'maximized',
    alwaysOnTop: false,
    sessionId: 'session-1'
  }),
  remove: vi.fn().mockResolvedValue(undefined),
  onCreated: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  onRemoved: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  onFocusChanged: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  }
}

// 完整的Chrome API模拟对象
export const mockChrome = {
  runtime: mockRuntime,
  storage: mockStorage,
  tabs: mockTabs,
  action: mockAction,
  contextMenus: mockContextMenus,
  scripting: mockScripting,
  windows: mockWindows,
  
  // 其他常用API
  permissions: {
    contains: vi.fn().mockResolvedValue(true),
    request: vi.fn().mockResolvedValue(true),
    remove: vi.fn().mockResolvedValue(true),
    getAll: vi.fn().mockResolvedValue({
      permissions: ['storage', 'activeTab', 'contextMenus', 'tabs', 'scripting'],
      origins: []
    }),
    onAdded: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    },
    onRemoved: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  },
  
  alarms: {
    create: vi.fn(),
    get: vi.fn().mockResolvedValue(null),
    getAll: vi.fn().mockResolvedValue([]),
    clear: vi.fn().mockResolvedValue(true),
    clearAll: vi.fn().mockResolvedValue(true),
    onAlarm: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  },
  
  notifications: {
    create: vi.fn().mockResolvedValue('notification-id'),
    update: vi.fn().mockResolvedValue(true),
    clear: vi.fn().mockResolvedValue(true),
    getAll: vi.fn().mockResolvedValue({}),
    getPermissionLevel: vi.fn().mockResolvedValue('granted'),
    onClicked: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    },
    onClosed: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  }
}

// 设置Chrome API模拟的辅助函数
export const setupChromeApiMock = () => {
  ;(global as any).chrome = mockChrome
  
  // 确保所有模拟函数都被重置
  Object.values(mockChrome).forEach(api => {
    if (typeof api === 'object' && api !== null) {
      Object.values(api).forEach(method => {
        if (typeof method === 'function' && 'mockClear' in method) {
          method.mockClear()
        }
      })
    }
  })
}

// 重置Chrome API模拟的辅助函数
export const resetChromeApiMock = () => {
  vi.clearAllMocks()
  
  // 重置所有模拟函数的返回值
  mockRuntime.sendMessage.mockResolvedValue({ success: true, data: [] })
  mockStorage.sync.get.mockImplementation((keys) => {
    const defaultData = {
      appSettings: {
        autoTagging: true,
        syncEnabled: false,
        theme: 'system',
        language: 'zh-CN'
      },
      bookmarks: [],
      categories: [],
      tags: []
    }
    
    if (typeof keys === 'string') {
      return Promise.resolve({ [keys]: defaultData[keys as keyof typeof defaultData] })
    } else if (Array.isArray(keys)) {
      const result: any = {}
      keys.forEach(key => {
        result[key] = defaultData[key as keyof typeof defaultData]
      })
      return Promise.resolve(result)
    } else if (keys === null || keys === undefined) {
      return Promise.resolve(defaultData)
    } else {
      return Promise.resolve(keys)
    }
  })
  
  mockTabs.query.mockResolvedValue([{
    id: 1,
    title: '测试页面',
    url: 'https://example.com',
    active: true,
    windowId: 1,
    index: 0,
    highlighted: true,
    incognito: false,
    pinned: false,
    audible: false,
    discarded: false,
    autoDiscardable: true,
    mutedInfo: { muted: false },
    status: 'complete'
  }])
}

// 导出各个API模拟对象
export {
  mockRuntime,
  mockStorage,
  mockTabs,
  mockAction,
  mockContextMenus,
  mockScripting,
  mockWindows
}