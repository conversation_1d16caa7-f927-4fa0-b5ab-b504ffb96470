// 模型选择器组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import ModelSelector from '../src/components/ModelSelector'
import { AIModel } from '../src/types/ai'

// 模拟数据
const mockModels: AIModel[] = [
  {
    id: 'gpt-4',
    name: 'gpt-4',
    displayName: 'GPT-4',
    description: 'OpenAI最先进的大型语言模型',
    capabilities: ['chat', 'completion', 'reasoning'],
    tags: ['OpenAI', 'GPT-4', '推理', '对话'],
    providerId: 'openai',
    isRecommended: true,
    isPopular: true
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'gpt-3.5-turbo',
    displayName: 'GPT-3.5 Turbo',
    description: 'OpenAI高效的对话模型',
    capabilities: ['chat', 'completion'],
    tags: ['OpenAI', 'GPT-3.5', '对话', '高效'],
    providerId: 'openai',
    isRecommended: true,
    isPopular: true
  }
]

const mockManyModels: AIModel[] = Array.from({ length: 15 }, (_, i) => ({
  id: `model-${i}`,
  name: `model-${i}`,
  displayName: `Model ${i}`,
  description: `测试模型 ${i}`,
  capabilities: ['chat'],
  tags: ['test'],
  providerId: 'test',
  isRecommended: i < 3,
  isPopular: i < 5
}))

describe('ModelSelector', () => {
  const mockProps = {
    providerId: 'test-provider',
    providerName: '测试提供商',
    models: mockModels,
    selectedModelId: undefined,
    isLoading: false,
    onModelSelect: vi.fn(),
    onRefreshModels: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('空状态', () => {
    it('应该显示空状态当没有模型时', () => {
      render(
        <ModelSelector
          {...mockProps}
          models={[]}
        />
      )

      expect(screen.getByText('暂无可用模型')).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('应该显示加载状态', () => {
      render(
        <ModelSelector
          {...mockProps}
          models={[]}
          isLoading={true}
        />
      )

      expect(screen.getByText('正在加载模型列表...')).toBeInTheDocument()
    })
  })

  describe('紧凑模式 (≤10个模型)', () => {
    it('应该显示下拉选择器', () => {
      render(<ModelSelector {...mockProps} />)

      expect(screen.getByText('选择模型')).toBeInTheDocument()
      expect(screen.getByRole('combobox')).toBeInTheDocument()
    })

    it('应该显示选中的模型', () => {
      render(
        <ModelSelector
          {...mockProps}
          selectedModelId="gpt-4"
        />
      )

      expect(screen.getByText('gpt-4')).toBeInTheDocument()
      expect(screen.getByText('推荐')).toBeInTheDocument()
    })

    it('应该调用onModelSelect当选择模型时', async () => {
      render(<ModelSelector {...mockProps} />)

      // 点击下拉框
      fireEvent.click(screen.getByRole('combobox'))

      // 等待选项出现并点击
      await waitFor(() => {
        const option = screen.getByText('gpt-4')
        fireEvent.click(option)
      })

      expect(mockProps.onModelSelect).toHaveBeenCalledWith('gpt-4')
    })

    it('应该能够清除选择', () => {
      render(
        <ModelSelector
          {...mockProps}
          selectedModelId="gpt-4"
        />
      )

      // 查找并点击清除按钮
      const clearButton = screen.getByLabelText('清除选择')
      fireEvent.click(clearButton)

      expect(mockProps.onModelSelect).toHaveBeenCalledWith('')
    })
  })

  describe('弹窗模式 (>10个模型)', () => {
    const manyModelsProps = {
      ...mockProps,
      models: mockManyModels
    }

    it('应该显示弹窗按钮', () => {
      render(<ModelSelector {...manyModelsProps} />)

      expect(screen.getByText('选择模型 (15)')).toBeInTheDocument()
    })

    it('应该显示未选择状态', () => {
      render(<ModelSelector {...manyModelsProps} />)

      expect(screen.getByText('未选择模型')).toBeInTheDocument()
      expect(screen.getByText('点击选择')).toBeInTheDocument()
    })

    it('应该显示选中的模型信息', () => {
      render(
        <ModelSelector
          {...manyModelsProps}
          selectedModelId="model-0"
        />
      )

      expect(screen.getByText('model-0')).toBeInTheDocument()
    })

    it('应该打开模型选择弹窗', async () => {
      render(<ModelSelector {...manyModelsProps} />)

      // 点击选择模型按钮
      fireEvent.click(screen.getByText('选择模型 (15)'))

      // 等待弹窗出现
      await waitFor(() => {
        expect(screen.getByText('选择模型 - 测试提供商')).toBeInTheDocument()
      })

      expect(screen.getByText('从 15 个可用模型中选择一个')).toBeInTheDocument()
    })

    it('应该支持搜索功能', async () => {
      render(<ModelSelector {...manyModelsProps} />)

      // 打开弹窗
      fireEvent.click(screen.getByText('选择模型 (15)'))

      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText('搜索模型名称或描述...')
        fireEvent.change(searchInput, { target: { value: 'model-1' } })
      })

      // 应该只显示匹配的模型
      await waitFor(() => {
        expect(screen.getByText('model-1')).toBeInTheDocument()
        expect(screen.queryByText('model-2')).not.toBeInTheDocument()
      })
    })

    it('应该在弹窗中选择模型', async () => {
      render(<ModelSelector {...manyModelsProps} />)

      // 打开弹窗
      fireEvent.click(screen.getByText('选择模型 (15)'))

      await waitFor(() => {
        const modelOption = screen.getByText('model-0')
        fireEvent.click(modelOption)
      })

      expect(mockProps.onModelSelect).toHaveBeenCalledWith('model-0')
    })
  })

  describe('刷新功能', () => {
    it('应该调用onRefreshModels', () => {
      render(<ModelSelector {...mockProps} />)

      const refreshButton = screen.getByLabelText('刷新模型列表')
      fireEvent.click(refreshButton)

      expect(mockProps.onRefreshModels).toHaveBeenCalled()
    })

    it('应该在加载时禁用刷新按钮', () => {
      render(
        <ModelSelector
          {...mockProps}
          isLoading={true}
        />
      )

      const refreshButton = screen.getByLabelText('刷新模型列表')
      expect(refreshButton).toBeDisabled()
    })
  })

  describe('模型信息显示', () => {
    it('应该显示推荐和热门标签', () => {
      render(
        <ModelSelector
          {...mockProps}
          selectedModelId="gpt-4"
        />
      )

      expect(screen.getByText('推荐')).toBeInTheDocument()
    })

    it('应该显示模型描述', () => {
      render(
        <ModelSelector
          {...mockProps}
          selectedModelId="gpt-4"
        />
      )

      expect(screen.getByText('OpenAI最先进的大型语言模型')).toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该处理空的搜索结果', async () => {
      render(<ModelSelector {...{ ...mockProps, models: mockManyModels }} />)

      // 打开弹窗
      fireEvent.click(screen.getByText('选择模型 (15)'))

      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText('搜索模型名称或描述...')
        fireEvent.change(searchInput, { target: { value: 'nonexistent' } })
      })

      await waitFor(() => {
        expect(screen.getByText('没有找到匹配的模型')).toBeInTheDocument()
      })
    })
  })
})