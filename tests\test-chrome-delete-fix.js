/**
 * 测试Chrome对象删除修复
 * 验证在不同环境下删除chrome对象不会导致错误
 */

console.log('🧪 开始测试Chrome对象删除修复...')

// 模拟不同的chrome对象情况
const testCases = [
  {
    name: '可删除的chrome对象',
    setup: () => {
      window.chrome = { test: true }
    },
    shouldSucceed: true
  },
  {
    name: '不可删除的chrome对象',
    setup: () => {
      Object.defineProperty(window, 'chrome', {
        value: { test: true },
        configurable: false,
        writable: false
      })
    },
    shouldSucceed: false
  },
  {
    name: '已存在的chrome对象',
    setup: () => {
      // 不设置，使用浏览器原生的chrome对象（如果存在）
    },
    shouldSucceed: false // 通常浏览器的chrome对象是不可删除的
  }
]

// 安全删除函数（修复后的版本）
function safeDeleteChrome() {
  try {
    delete window.chrome
    return { success: true, method: 'delete' }
  } catch (error) {
    // 如果删除失败，则设置为undefined
    window.chrome = undefined
    return { success: true, method: 'undefined', error: error.message }
  }
}

// 运行测试
testCases.forEach((testCase, index) => {
  console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`)
  
  try {
    // 设置测试环境
    testCase.setup()
    
    // 记录初始状态
    const initialChrome = window.chrome
    console.log(`   初始chrome对象:`, !!initialChrome)
    
    // 尝试删除
    const result = safeDeleteChrome()
    console.log(`   删除结果:`, result)
    
    // 验证最终状态
    const finalChrome = window.chrome
    console.log(`   最终chrome对象:`, finalChrome)
    
    // 验证是否成功清理
    if (finalChrome === undefined || finalChrome === null) {
      console.log(`   ✅ 测试通过: chrome对象已成功清理`)
    } else {
      console.log(`   ❌ 测试失败: chrome对象未能清理`)
    }
    
  } catch (error) {
    console.log(`   ❌ 测试异常:`, error.message)
  }
})

console.log('\n🎉 Chrome对象删除修复测试完成')

// 清理测试环境
try {
  delete window.chrome
} catch (e) {
  window.chrome = undefined
}