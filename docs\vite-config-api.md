# Vite 配置 API 文档

## 概述

本文档描述了优化后的 `vite.config.ts` 文件的 API 结构和使用方法。

## 配置常量

### BUILD_CONFIG

主要的构建配置对象，包含所有构建相关的常量和配置。

```typescript
const BUILD_CONFIG = {
  OUTPUT_DIR: string,           // 输出目录
  PATHS: PathsConfig,           // 路径配置
  OUTPUT_PATTERNS: OutputConfig, // 输出模式配置
  SPECIAL_FILES: SpecialFiles,  // 特殊文件标识
  ICON_SIZES: number[]          // 图标尺寸数组
} as const
```

#### PathsConfig

```typescript
interface PathsConfig {
  SRC: string                   // 源代码目录
  POPUP: string                 // 弹窗页面路径
  OPTIONS: string               // 选项页面路径
  BACKGROUND: string            // 后台脚本路径
  CONTENT: string               // 内容脚本路径
  CONTENT_STYLE: string         // 内容样式路径
  PUBLIC_ICONS: string          // 公共图标目录
  DIST_ICONS: string            // 输出图标目录
  MANIFEST: string              // manifest文件路径
  DIST_MANIFEST: string         // 输出manifest路径
}
```

#### OutputConfig

```typescript
interface OutputConfig {
  BACKGROUND_JS: string         // 后台脚本输出路径
  CONTENT_JS: string            // 内容脚本输出路径
  CONTENT_CSS: string           // 内容样式输出路径
  DEFAULT_JS: string            // 默认JS文件模式
  DEFAULT_CHUNK: string         // 默认chunk文件模式
  DEFAULT_ASSET: string         // 默认资源文件模式
}
```

#### SpecialFiles

```typescript
interface SpecialFiles {
  BACKGROUND: string            // 后台脚本标识
  CONTENT: string               // 内容脚本标识
  STYLE_CSS: string             // 样式文件标识
  CONTENT_STYLE: string         // 内容样式标识
}
```

## 工具函数

### resolvePath(relativePath: string): string

解析项目相对路径为绝对路径。

**参数:**
- `relativePath`: 相对路径字符串

**返回值:**
- 解析后的绝对路径

**示例:**
```typescript
const absolutePath = resolvePath('./src')
// 返回: /project/root/src
```

### ensureDirectoryExists(dirPath: string): void

确保指定目录存在，如果不存在则创建。

**参数:**
- `dirPath`: 目录路径

**示例:**
```typescript
ensureDirectoryExists('dist/icons')
// 如果目录不存在，会递归创建
```

### safeCopyFile(src: string, dest: string, description: string): void

安全地复制文件，包含错误处理和日志输出。

**参数:**
- `src`: 源文件路径
- `dest`: 目标文件路径
- `description`: 文件描述（用于日志）

**示例:**
```typescript
safeCopyFile('src/icon.png', 'dist/icon.png', '应用图标')
// 输出: 已复制应用图标: src/icon.png -> dist/icon.png
```

### createInputConfig(): InputConfig

创建 Rollup 输入配置对象。

**返回值:**
```typescript
interface InputConfig {
  popup: string
  options: string
  background: string
  content: string
  contentStyle: string
}
```

**示例:**
```typescript
const inputConfig = createInputConfig()
// 返回所有入口文件的绝对路径配置
```

### generateEntryFileName(chunk: { name: string }): string

根据 chunk 名称生成入口文件名。

**参数:**
- `chunk`: 包含 name 属性的对象

**返回值:**
- 生成的文件名

**逻辑:**
- `background` → `src/background/index.js`
- `content` → `src/content/index.js`
- 其他 → `assets/[name]-[hash].js`

**示例:**
```typescript
generateEntryFileName({ name: 'background' })
// 返回: 'src/background/index.js'

generateEntryFileName({ name: 'popup' })
// 返回: 'assets/[name]-[hash].js'
```

### generateAssetFileName(assetInfo: { name?: string }): string

根据资源信息生成资源文件名。

**参数:**
- `assetInfo`: 包含可选 name 属性的对象

**返回值:**
- 生成的文件名

**逻辑:**
- `style.css` 或包含 `contentStyle` → `src/content/style.css`
- 其他 → `assets/[name]-[hash].[ext]`

**示例:**
```typescript
generateAssetFileName({ name: 'style.css' })
// 返回: 'src/content/style.css'

generateAssetFileName({ name: 'image.png' })
// 返回: 'assets/[name]-[hash].[ext]'
```

## 插件

### copyIconsPlugin(): VitePlugin

复制图标文件和 manifest 的 Vite 插件。

**功能:**
1. 创建输出图标目录
2. 复制所有尺寸的图标文件 (16x16, 32x32, 48x48, 128x128)
3. 复制 manifest.json 文件

**使用:**
```typescript
export default defineConfig({
  plugins: [react(), copyIconsPlugin()],
  // ...其他配置
})
```

**执行时机:**
- 在 Rollup 的 `writeBundle` 阶段执行
- 构建完成后自动复制必要的静态资源

## 配置示例

### 基本配置

```typescript
export default defineConfig({
  plugins: [react(), copyIconsPlugin()],
  
  resolve: {
    alias: {
      '@': resolvePath(BUILD_CONFIG.PATHS.SRC),
    },
  },
  
  build: {
    outDir: BUILD_CONFIG.OUTPUT_DIR,
    rollupOptions: {
      input: createInputConfig(),
      output: {
        entryFileNames: generateEntryFileName,
        chunkFileNames: BUILD_CONFIG.OUTPUT_PATTERNS.DEFAULT_CHUNK,
        assetFileNames: generateAssetFileName
      }
    }
  }
})
```

### 自定义配置

如果需要修改配置，可以通过修改 `BUILD_CONFIG` 对象：

```typescript
// 添加新的特殊文件类型
const BUILD_CONFIG = {
  // ...现有配置
  SPECIAL_FILES: {
    BACKGROUND: 'background',
    CONTENT: 'content',
    WORKER: 'worker',  // 新增
    // ...
  },
  OUTPUT_PATTERNS: {
    BACKGROUND_JS: 'src/background/index.js',
    CONTENT_JS: 'src/content/index.js',
    WORKER_JS: 'src/workers/index.js',  // 新增
    // ...
  }
}
```

## 错误处理

### 文件复制错误

当源文件不存在时，`safeCopyFile` 函数会：
1. 输出警告信息到控制台
2. 跳过复制操作
3. 不会中断构建过程

### 目录创建错误

`ensureDirectoryExists` 函数使用 `recursive: true` 选项，确保：
1. 递归创建所有必要的父目录
2. 如果目录已存在，不会报错
3. 权限不足时会抛出异常

## 性能优化

### 配置缓存

- 使用 `as const` 确保配置对象的不可变性
- 避免运行时的重复计算

### 文件名生成优化

- 使用映射表替代多重条件判断
- 减少字符串操作和正则表达式使用

### 内存优化

- 配置对象在编译时确定，运行时不可修改
- 避免闭包中的大对象引用

## 扩展指南

### 添加新的入口文件

1. 在 `BUILD_CONFIG.PATHS` 中添加新路径
2. 在 `createInputConfig` 函数中添加新入口
3. 如需特殊处理，在 `generateEntryFileName` 中添加规则

### 添加新的资源类型

1. 在 `BUILD_CONFIG.SPECIAL_FILES` 中添加标识
2. 在 `BUILD_CONFIG.OUTPUT_PATTERNS` 中添加输出模式
3. 在 `generateAssetFileName` 中添加处理逻辑

### 添加新的插件

```typescript
const customPlugin = () => {
  return {
    name: 'custom-plugin',
    // 插件逻辑
  }
}

export default defineConfig({
  plugins: [react(), copyIconsPlugin(), customPlugin()],
  // ...
})
```

## 最佳实践

1. **配置集中化**: 所有配置都在 `BUILD_CONFIG` 中管理
2. **函数单一职责**: 每个函数只负责一个特定功能
3. **错误处理**: 所有文件操作都包含适当的错误处理
4. **类型安全**: 使用 TypeScript 确保类型安全
5. **可扩展性**: 通过修改配置对象轻松扩展功能

## 故障排除

### 常见问题

1. **图标文件未复制**
   - 检查 `public/icons/` 目录是否存在
   - 确认图标文件命名格式: `icon-{size}.png`

2. **路径解析错误**
   - 检查 `__dirname` 是否正确
   - 确认相对路径格式

3. **构建输出路径错误**
   - 检查 `BUILD_CONFIG.OUTPUT_PATTERNS` 配置
   - 确认特殊文件标识是否正确

### 调试技巧

1. 启用详细日志输出
2. 检查构建产物的文件结构
3. 使用 `console.log` 调试配置值
4. 运行单元测试验证逻辑

## 版本历史

- **v2.0**: 重构配置结构，添加类型安全和错误处理
- **v1.0**: 初始版本，基本的构建配置