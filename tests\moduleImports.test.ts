// 模块导入验证测试

import { describe, it, expect, vi } from 'vitest'
import fs from 'fs'
import path from 'path'

describe('模块导入验证', () => {
  const srcPath = path.join(process.cwd(), 'src')
  
  it('应该验证所有TypeScript文件都使用静态导入', async () => {
    const tsFiles = await getAllTsFiles(srcPath)
    const dynamicImports: string[] = []
    
    for (const file of tsFiles) {
      const content = fs.readFileSync(file, 'utf8')
      
      // 检查动态导入模式
      const dynamicImportMatches = content.match(/import\s*\(/g)
      if (dynamicImportMatches) {
        dynamicImports.push(`${file}: 发现 ${dynamicImportMatches.length} 个动态导入`)
      }
    }
    
    if (dynamicImports.length > 0) {
      console.log('发现的动态导入:')
      dynamicImports.forEach(item => console.log(`  - ${item}`))
    }
    
    expect(dynamicImports).toHaveLength(0)
  })

  it('应该验证没有循环依赖', async () => {
    const tsFiles = await getAllTsFiles(srcPath)
    const dependencyGraph = new Map<string, Set<string>>()
    
    // 构建依赖图
    for (const file of tsFiles) {
      const content = fs.readFileSync(file, 'utf8')
      const relativePath = path.relative(srcPath, file)
      
      // 提取导入语句
      const importMatches = content.match(/import\s+.*?\s+from\s+['"]([^'"]+)['"]/g)
      if (importMatches) {
        const dependencies = new Set<string>()
        
        for (const importMatch of importMatches) {
          const match = importMatch.match(/from\s+['"]([^'"]+)['"]/)
          if (match) {
            const importPath = match[1]
            // 只检查相对导入
            if (importPath.startsWith('.')) {
              const resolvedPath = path.resolve(path.dirname(file), importPath)
              const normalizedPath = path.relative(srcPath, resolvedPath)
              dependencies.add(normalizedPath)
            }
          }
        }
        
        dependencyGraph.set(relativePath, dependencies)
      }
    }
    
    // 检查循环依赖
    const circularDependencies = findCircularDependencies(dependencyGraph)
    
    if (circularDependencies.length > 0) {
      console.log('发现的循环依赖:')
      circularDependencies.forEach(cycle => {
        console.log(`  - ${cycle.join(' -> ')}`)
      })
    }
    
    expect(circularDependencies).toHaveLength(0)
  })

  it('应该验证关键模块的导入顺序', () => {
    const optionsAppPath = path.join(srcPath, 'options', 'OptionsApp.tsx')
    const content = fs.readFileSync(optionsAppPath, 'utf8')
    
    // 检查filteredBookmarks的定义和使用
    const lines = content.split('\n')
    let filteredBookmarksDefinitionLine = -1
    let filteredBookmarksUsageLine = -1
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      
      // 查找filteredBookmarks的定义
      if (line.includes('const filteredBookmarks = useMemo')) {
        filteredBookmarksDefinitionLine = i
      }
      
      // 查找filteredBookmarks的使用（在useEffect中）
      if (line.includes('useEffect') && i < lines.length - 5) {
        for (let j = i; j < Math.min(i + 10, lines.length); j++) {
          if (lines[j].includes('filteredBookmarks.length')) {
            filteredBookmarksUsageLine = j
            break
          }
        }
      }
    }
    
    // 验证定义在使用之前
    if (filteredBookmarksDefinitionLine !== -1 && filteredBookmarksUsageLine !== -1) {
      expect(filteredBookmarksDefinitionLine).toBeLessThan(filteredBookmarksUsageLine)
    }
  })

  it('应该验证错误边界组件被正确导入', () => {
    const optionsIndexPath = path.join(srcPath, 'options', 'index.tsx')
    const content = fs.readFileSync(optionsIndexPath, 'utf8')
    
    // 检查错误边界组件的导入
    expect(content).toMatch(/import.*OptionsPageErrorBoundary.*from/)
    
    // 检查错误边界组件的使用
    expect(content).toMatch(/<OptionsPageErrorBoundary>/)
    expect(content).toMatch(/<\/OptionsPageErrorBoundary>/)
  })

  it('应该验证所有组件都有正确的导出', async () => {
    const componentFiles = await getAllTsFiles(path.join(srcPath, 'components'))
    const missingExports: string[] = []
    
    for (const file of componentFiles) {
      const content = fs.readFileSync(file, 'utf8')
      const fileName = path.basename(file, path.extname(file))
      
      // 检查是否有默认导出或命名导出
      const hasDefaultExport = content.includes('export default')
      const hasNamedExport = content.includes(`export { ${fileName} }`) || 
                            content.includes(`export const ${fileName}`) ||
                            content.includes(`export function ${fileName}`)
      
      if (!hasDefaultExport && !hasNamedExport) {
        missingExports.push(file)
      }
    }
    
    if (missingExports.length > 0) {
      console.log('缺少导出的组件文件:')
      missingExports.forEach(file => console.log(`  - ${file}`))
    }
    
    expect(missingExports).toHaveLength(0)
  })

  it('应该验证构建后的文件没有初始化错误', () => {
    const distPath = path.join(process.cwd(), 'dist', 'assets')
    
    if (!fs.existsSync(distPath)) {
      console.log('dist目录不存在，跳过构建文件检查')
      return
    }
    
    const files = fs.readdirSync(distPath)
    const optionsFile = files.find(file => file.startsWith('options-') && file.endsWith('.js'))
    
    if (!optionsFile) {
      console.log('没有找到options构建文件，跳过检查')
      return
    }
    
    const filePath = path.join(distPath, optionsFile)
    const content = fs.readFileSync(filePath, 'utf8')
    
    // 检查是否包含初始化错误相关的代码
    const hasInitError = content.includes('Cannot access') && content.includes('before initialization')
    expect(hasInitError).toBe(false)
    
    // 检查变量K的定义和使用
    const kMatches = content.match(/\bK\b/g)
    const kDefinitions = content.match(/\bK\s*=/g)
    
    if (kMatches && kDefinitions) {
      // 如果有K的使用，应该有对应的定义
      expect(kDefinitions.length).toBeGreaterThan(0)
      console.log(`变量K使用次数: ${kMatches.length}, 定义次数: ${kDefinitions.length}`)
    }
  })
})

// 辅助函数：获取所有TypeScript文件
async function getAllTsFiles(dir: string): Promise<string[]> {
  const files: string[] = []
  
  function walkDir(currentDir: string) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        // 跳过node_modules和dist目录
        if (!item.includes('node_modules') && !item.includes('dist')) {
          walkDir(fullPath)
        }
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        files.push(fullPath)
      }
    }
  }
  
  walkDir(dir)
  return files
}

// 辅助函数：查找循环依赖
function findCircularDependencies(graph: Map<string, Set<string>>): string[][] {
  const visited = new Set<string>()
  const recursionStack = new Set<string>()
  const cycles: string[][] = []
  
  function dfs(node: string, path: string[]): void {
    if (recursionStack.has(node)) {
      // 找到循环依赖
      const cycleStart = path.indexOf(node)
      if (cycleStart !== -1) {
        cycles.push([...path.slice(cycleStart), node])
      }
      return
    }
    
    if (visited.has(node)) {
      return
    }
    
    visited.add(node)
    recursionStack.add(node)
    
    const dependencies = graph.get(node) || new Set()
    for (const dep of dependencies) {
      // 尝试解析依赖路径
      const resolvedDep = resolveDependencyPath(dep)
      if (resolvedDep && graph.has(resolvedDep)) {
        dfs(resolvedDep, [...path, node])
      }
    }
    
    recursionStack.delete(node)
  }
  
  for (const node of graph.keys()) {
    if (!visited.has(node)) {
      dfs(node, [])
    }
  }
  
  return cycles
}

// 辅助函数：解析依赖路径
function resolveDependencyPath(importPath: string): string | null {
  // 简单的路径解析，实际项目中可能需要更复杂的逻辑
  let resolved = importPath
  
  // 移除文件扩展名
  resolved = resolved.replace(/\.(ts|tsx|js|jsx)$/, '')
  
  // 处理index文件
  if (resolved.endsWith('/index')) {
    resolved = resolved.replace('/index', '')
  }
  
  // 标准化路径分隔符
  resolved = resolved.replace(/\\/g, '/')
  
  return resolved
}