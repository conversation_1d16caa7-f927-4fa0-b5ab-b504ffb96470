/**
 * AI描述生成修复测试
 * 验证修复后的描述生成功能能够正常工作，不再降级到备用方案
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { aiService } from '../src/services/aiService'
import type { AIDescriptionRequest } from '../src/types/ai'

// Mock dependencies
vi.mock('../src/services/aiChatService', () => ({
  aiChatService: {
    generateText: vi.fn()
  }
}))

vi.mock('../src/services/aiConfigService', () => ({
  aiConfigService: {
    getConfig: vi.fn().mockResolvedValue({
      provider: 'lm-studio',
      model: 'test-model'
    })
  }
}))

vi.mock('../src/services/aiCacheService', () => ({
  aiCacheService: {
    getDescriptionCache: vi.fn().mockResolvedValue(null),
    saveDescriptionCache: vi.fn().mockResolvedValue(undefined)
  }
}))

vi.mock('../src/services/aiIntegrationService', () => ({
  aiIntegrationService: {
    getConfiguredProviders: vi.fn().mockResolvedValue([{
      id: 'lm-studio_test',
      name: '本地LM studio',
      type: 'lm-studio'
    }])
  }
}))

describe('AI描述生成修复测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('content字段处理修复', () => {
    it('应该能处理缺少content字段的请求', async () => {
      const request: AIDescriptionRequest = {
        title: '观影 GYING',
        url: 'https://www.gying.si/',
        maxLength: 200
        // 注意：没有content字段
      }

      // 模拟AI服务返回
      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockResolvedValue({
        content: '这是一个优秀的在线观影平台，提供丰富的影视资源。',
        usage: { totalTokens: 50 }
      })

      const result = await aiService.generateDescription(request)

      expect(result).toBeDefined()
      expect(result.description).toBeTruthy()
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.wordCount).toBeGreaterThan(0)
    })

    it('应该能处理content为空字符串的请求', async () => {
      const request: AIDescriptionRequest = {
        title: '观影 GYING',
        url: 'https://www.gying.si/',
        content: '', // 空字符串
        maxLength: 200
      }

      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockResolvedValue({
        content: '基于标题和网址生成的描述：这是一个在线观影网站。',
        usage: { totalTokens: 40 }
      })

      const result = await aiService.generateDescription(request)

      expect(result).toBeDefined()
      expect(result.description).toBeTruthy()
      expect(result.confidence).toBeGreaterThan(0)
    })

    it('应该能处理content为undefined的请求', async () => {
      const request: AIDescriptionRequest = {
        title: '观影 GYING',
        url: 'https://www.gying.si/',
        content: undefined as any, // 明确设置为undefined
        maxLength: 200
      }

      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockResolvedValue({
        content: '基于网站信息生成的描述。',
        usage: { totalTokens: 30 }
      })

      const result = await aiService.generateDescription(request)

      expect(result).toBeDefined()
      expect(result.description).toBeTruthy()
    })
  })

  describe('降级策略优化', () => {
    it('应该基于URL智能生成描述', async () => {
      const request: AIDescriptionRequest = {
        title: 'GitHub Repository',
        url: 'https://github.com/user/project',
        maxLength: 200
      }

      // 模拟AI调用失败，触发降级策略
      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockRejectedValue(new Error('AI服务不可用'))

      const result = await aiService.generateDescription(request)

      expect(result).toBeDefined()
      expect(result.description).toContain('开源代码仓库')
      expect(result.confidence).toBeGreaterThan(0)
    })

    it('应该为技术文档网站生成合适的描述', async () => {
      const request: AIDescriptionRequest = {
        title: 'API Documentation',
        url: 'https://docs.example.com/api/',
        maxLength: 200
      }

      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockRejectedValue(new Error('超时'))

      const result = await aiService.generateDescription(request)

      expect(result.description).toContain('技术文档')
      expect(result.confidence).toBeGreaterThan(0.3)
    })

    it('应该处理只有URL没有标题的情况', async () => {
      const request: AIDescriptionRequest = {
        url: 'https://stackoverflow.com/questions/123456',
        maxLength: 200
      }

      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockRejectedValue(new Error('网络错误'))

      const result = await aiService.generateDescription(request)

      expect(result.description).toContain('技术问答')
      // 降级策略会基于URL类型生成描述，不一定包含域名
      expect(result.description.length).toBeGreaterThan(0)
    })
  })

  describe('错误处理和超时机制', () => {
    it('应该在AI调用失败时使用降级策略', async () => {
      const request: AIDescriptionRequest = {
        title: '测试标题',
        url: 'https://example.com',
        content: '测试内容',
        maxLength: 200
      }

      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockRejectedValue(new Error('AI服务错误'))

      const result = await aiService.generateDescription(request)

      expect(result).toBeDefined()
      expect(result.description).toBeTruthy()
      expect(result.description).toContain('测试标题')
    })

    it('应该正确计算字数', async () => {
      const request: AIDescriptionRequest = {
        title: '中文标题测试',
        content: '这是一段中文内容，用于测试字数统计功能。',
        maxLength: 200
      }

      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockRejectedValue(new Error('测试降级'))

      const result = await aiService.generateDescription(request)

      expect(result.wordCount).toBeGreaterThan(0)
      expect(typeof result.wordCount).toBe('number')
    })
  })

  describe('长度限制和格式优化', () => {
    it('应该正确截断过长的描述', async () => {
      const longTitle = '这是一个非常长的标题'.repeat(20)
      const request: AIDescriptionRequest = {
        title: longTitle,
        maxLength: 50
      }

      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockRejectedValue(new Error('测试'))

      const result = await aiService.generateDescription(request)

      expect(result.description.length).toBeLessThanOrEqual(50)
    })

    it('应该清理格式并移除多余的标点', async () => {
      const request: AIDescriptionRequest = {
        title: '测试标题。。。',
        content: '测试内容。。。多余的句号。。。',
        maxLength: 200
      }

      const { aiChatService } = await import('../src/services/aiChatService')
      vi.mocked(aiChatService.generateText).mockRejectedValue(new Error('测试'))

      const result = await aiService.generateDescription(request)

      expect(result.description).not.toMatch(/。{2,}/)
      expect(result.description.trim()).toBeTruthy()
    })
  })
})
