import React, { useState, useEffect } from 'react'
import { 
  <PERSON>Text, 
  FolderO<PERSON>, 
  Settings, 
  Download, 
  Upload, 
  RefreshCw, 
  Check, 
  AlertCircle,
  Database,
  Eye,
  Plus,
  Trash2
} from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Switch } from './ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Textarea } from './ui/textarea'
import { Separator } from './ui/separator'
import { Badge } from './ui/badge'
import { Alert, AlertDescription } from './ui/alert'

interface ObsidianVault {
  name: string
  path: string
  configPath: string
}

interface SyncSettings {
  folderPath: string
  includeDataview: boolean
  filenamePattern: string
  autoSync: boolean
  exportType: 'markdown' | 'bases'
  databaseName: string
  createViews: boolean
  enableRating: boolean
  enableStatus: boolean
  template: string
}

interface SyncResult {
  success: number
  failed: number
  errors: string[]
}

const ObsidianIntegrationTab: React.FC = () => {
  const [vaults, setVaults] = useState<ObsidianVault[]>([])
  const [selectedVault, setSelectedVault] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle')
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null)
  const [syncSettings, setSyncSettings] = useState<SyncSettings>({
    folderPath: 'Bookmarks',
    includeDataview: true,
    filenamePattern: '{{title}}',
    autoSync: false,
    exportType: 'markdown',
    databaseName: '书签数据库',
    createViews: true,
    enableRating: true,
    enableStatus: true,
    template: ''
  })

  // 组件挂载时加载设置和检测Obsidian库
  useEffect(() => {
    loadSettings()
    detectObsidianVaults()
  }, [])

  // 加载设置
  const loadSettings = async () => {
    try {
      const result = await chrome.storage.sync.get(['obsidianSyncSettings'])
      if (result.obsidianSyncSettings) {
        setSyncSettings({ ...syncSettings, ...result.obsidianSyncSettings })
      }
    } catch (error) {
      console.error('加载Obsidian同步设置失败:', error)
    }
  }

  // 保存设置
  const saveSettings = async (newSettings: Partial<SyncSettings>) => {
    try {
      const updatedSettings = { ...syncSettings, ...newSettings }
      setSyncSettings(updatedSettings)
      await chrome.storage.sync.set({ obsidianSyncSettings: updatedSettings })
    } catch (error) {
      console.error('保存Obsidian同步设置失败:', error)
    }
  }

  // 检测Obsidian库
  const detectObsidianVaults = async () => {
    try {
      setLoading(true)
      
      // 模拟检测Obsidian库的过程
      // 实际实现中需要调用background script来检测文件系统
      const response = await chrome.runtime.sendMessage({
        type: 'DETECT_OBSIDIAN_VAULTS'
      })
      
      if (response?.success && response.data) {
        setVaults(response.data)
        if (response.data.length > 0 && !selectedVault) {
          setSelectedVault(response.data[0].path)
        }
      } else {
        // 如果检测失败，显示示例数据
        const mockVaults: ObsidianVault[] = [
          {
            name: '我的知识库',
            path: 'C:\\Users\\<USER>\\Documents\\我的知识库',
            configPath: 'C:\\Users\\<USER>\\Documents\\我的知识库\\.obsidian'
          },
          {
            name: '工作笔记',
            path: 'D:\\Obsidian\\工作笔记',
            configPath: 'D:\\Obsidian\\工作笔记\\.obsidian'
          }
        ]
        setVaults(mockVaults)
        if (!selectedVault) {
          setSelectedVault(mockVaults[0].path)
        }
      }
    } catch (error) {
      console.error('检测Obsidian库失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 手动添加Obsidian库路径
  const handleAddVaultPath = () => {
    const path = prompt('请输入Obsidian库的完整路径:')
    if (path && path.trim()) {
      const name = path.split('\\').pop() || path.split('/').pop() || '未知库'
      const newVault: ObsidianVault = {
        name,
        path: path.trim(),
        configPath: `${path.trim()}\\.obsidian`
      }
      setVaults([...vaults, newVault])
      if (!selectedVault) {
        setSelectedVault(newVault.path)
      }
    }
  }

  // 导出单个书签
  const handleExportBookmark = async () => {
    if (!selectedVault) {
      alert('请先选择Obsidian库')
      return
    }

    try {
      setLoading(true)
      setSyncStatus('syncing')
      
      const response = await chrome.runtime.sendMessage({
        type: 'EXPORT_BOOKMARK_TO_OBSIDIAN',
        data: {
          vaultPath: selectedVault,
          settings: syncSettings
        }
      })
      
      if (response?.success) {
        setSyncStatus('success')
        setTimeout(() => setSyncStatus('idle'), 3000)
      } else {
        setSyncStatus('error')
        setTimeout(() => setSyncStatus('idle'), 3000)
      }
    } catch (error) {
      console.error('导出书签失败:', error)
      setSyncStatus('error')
      setTimeout(() => setSyncStatus('idle'), 3000)
    } finally {
      setLoading(false)
    }
  }

  // 同步所有书签
  const handleSyncAllBookmarks = async () => {
    if (!selectedVault) {
      alert('请先选择Obsidian库')
      return
    }

    try {
      setLoading(true)
      setSyncStatus('syncing')
      
      const response = await chrome.runtime.sendMessage({
        type: 'SYNC_ALL_BOOKMARKS_TO_OBSIDIAN',
        data: {
          vaultPath: selectedVault,
          settings: syncSettings
        }
      })
      
      if (response?.success) {
        setSyncStatus('success')
        setLastSyncResult(response.data)
      } else {
        setSyncStatus('error')
      }
      
      setTimeout(() => setSyncStatus('idle'), 5000)
    } catch (error) {
      console.error('同步所有书签失败:', error)
      setSyncStatus('error')
      setTimeout(() => setSyncStatus('idle'), 3000)
    } finally {
      setLoading(false)
    }
  }

  // 创建Bases数据库
  const handleCreateDatabase = async () => {
    if (!selectedVault) {
      alert('请先选择Obsidian库')
      return
    }

    try {
      setLoading(true)
      setSyncStatus('syncing')
      
      const response = await chrome.runtime.sendMessage({
        type: 'CREATE_OBSIDIAN_BASES_DATABASE',
        data: {
          vaultPath: selectedVault,
          settings: syncSettings
        }
      })
      
      if (response?.success) {
        setSyncStatus('success')
        setLastSyncResult(response.data)
      } else {
        setSyncStatus('error')
      }
      
      setTimeout(() => setSyncStatus('idle'), 5000)
    } catch (error) {
      console.error('创建Bases数据库失败:', error)
      setSyncStatus('error')
      setTimeout(() => setSyncStatus('idle'), 3000)
    } finally {
      setLoading(false)
    }
  }

  // 获取默认模板
  const getDefaultTemplate = () => {
    return `# {{title}}

🔗 **链接**: [{{title}}]({{url}})

📝 **描述**: {{description}}

🏷️ **标签**: {{tags}}

📁 **分类**: [[{{category}}]]

## 笔记

<!-- 在这里添加你的笔记 -->

## 相关链接

<!-- 添加相关的双向链接 -->
`
  }

  return (
    <div className="p-6 space-y-6">
      {/* 头部区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <FileText className="w-6 h-6 mr-3 text-primary" />
            Obsidian集成
          </CardTitle>
          <CardDescription>
            将您的书签数据同步到Obsidian笔记库，支持Dataview查询和Bases数据库格式
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 状态提示 */}
      {syncStatus !== 'idle' && (
        <Alert className={
          syncStatus === 'success' ? 'border-green-200 bg-green-50' :
          syncStatus === 'error' ? 'border-red-200 bg-red-50' :
          'border-blue-200 bg-blue-50'
        }>
          <div className="flex items-center">
            {syncStatus === 'syncing' && <RefreshCw className="w-4 h-4 animate-spin mr-2" />}
            {syncStatus === 'success' && <Check className="w-4 h-4 text-green-600 mr-2" />}
            {syncStatus === 'error' && <AlertCircle className="w-4 h-4 text-red-600 mr-2" />}
            <AlertDescription className={
              syncStatus === 'success' ? 'text-green-800' :
              syncStatus === 'error' ? 'text-red-800' :
              'text-blue-800'
            }>
              {syncStatus === 'syncing' && '正在同步数据到Obsidian...'}
              {syncStatus === 'success' && '同步完成！'}
              {syncStatus === 'error' && '同步失败，请检查设置后重试'}
            </AlertDescription>
          </div>
        </Alert>
      )}

      {/* 同步结果 */}
      {lastSyncResult && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">最近同步结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{lastSyncResult.success}</div>
                <div className="text-sm text-muted-foreground">成功</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{lastSyncResult.failed}</div>
                <div className="text-sm text-muted-foreground">失败</div>
              </div>
            </div>
            {lastSyncResult.errors.length > 0 && (
              <div>
                <Label className="text-sm font-medium">错误详情:</Label>
                <div className="mt-2 space-y-1">
                  {lastSyncResult.errors.slice(0, 5).map((error, index) => (
                    <div key={index} className="text-xs text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </div>
                  ))}
                  {lastSyncResult.errors.length > 5 && (
                    <div className="text-xs text-muted-foreground">
                      还有 {lastSyncResult.errors.length - 5} 个错误...
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Obsidian库选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <FolderOpen className="w-5 h-5 mr-2" />
            Obsidian库选择
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Label htmlFor="vault-select">选择Obsidian库</Label>
              <Select value={selectedVault} onValueChange={setSelectedVault}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择Obsidian库..." />
                </SelectTrigger>
                <SelectContent>
                  {vaults.map(vault => (
                    <SelectItem key={vault.path} value={vault.path}>
                      <div className="flex flex-col">
                        <span className="font-medium">{vault.name}</span>
                        <span className="text-xs text-muted-foreground">{vault.path}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={detectObsidianVaults}
                disabled={loading}
                variant="outline"
                size="sm"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              <Button
                onClick={handleAddVaultPath}
                variant="outline"
                size="sm"
              >
                <Plus className="w-4 h-4 mr-2" />
                添加路径
              </Button>
            </div>
          </div>
          
          {vaults.length === 0 && !loading && (
            <Alert>
              <AlertCircle className="w-4 h-4" />
              <AlertDescription>
                未检测到Obsidian库。请确保Obsidian已安装并创建了至少一个库，或手动添加库路径。
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 同步设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            同步设置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 基础设置 */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="folder-path">目标文件夹</Label>
                <Input
                  id="folder-path"
                  value={syncSettings.folderPath}
                  onChange={(e) => saveSettings({ folderPath: e.target.value })}
                  placeholder="Bookmarks"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  书签文件将保存到此文件夹中
                </p>
              </div>
              <div>
                <Label htmlFor="filename-pattern">文件名模式</Label>
                <Input
                  id="filename-pattern"
                  value={syncSettings.filenamePattern}
                  onChange={(e) => saveSettings({ filenamePattern: e.target.value })}
                  placeholder="{{title}}"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  可用变量: {'{{title}}'}, {'{{date}}'}, {'{{id}}'}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">包含Dataview查询</Label>
                <p className="text-xs text-muted-foreground">
                  在生成的Markdown文件中包含Dataview查询代码
                </p>
              </div>
              <Switch
                checked={syncSettings.includeDataview}
                onCheckedChange={(checked) => saveSettings({ includeDataview: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">自动同步</Label>
                <p className="text-xs text-muted-foreground">
                  新增书签时自动同步到Obsidian
                </p>
              </div>
              <Switch
                checked={syncSettings.autoSync}
                onCheckedChange={(checked) => saveSettings({ autoSync: checked })}
              />
            </div>
          </div>

          <Separator />

          {/* 导出格式选择 */}
          <div className="space-y-4">
            <Label className="text-base font-medium">导出格式</Label>
            <div className="grid grid-cols-2 gap-4">
              <Card className={`cursor-pointer transition-colors ${
                syncSettings.exportType === 'markdown' 
                  ? 'border-primary bg-primary/5' 
                  : 'hover:bg-muted/50'
              }`} onClick={() => saveSettings({ exportType: 'markdown' })}>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <FileText className="w-5 h-5" />
                    <div>
                      <div className="font-medium">标准Markdown</div>
                      <div className="text-xs text-muted-foreground">
                        每个书签生成独立的Markdown文件
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className={`cursor-pointer transition-colors ${
                syncSettings.exportType === 'bases' 
                  ? 'border-primary bg-primary/5' 
                  : 'hover:bg-muted/50'
              }`} onClick={() => saveSettings({ exportType: 'bases' })}>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <Database className="w-5 h-5" />
                    <div>
                      <div className="font-medium">Bases数据库</div>
                      <div className="text-xs text-muted-foreground">
                        创建结构化的数据库表格
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Bases数据库特定设置 */}
          {syncSettings.exportType === 'bases' && (
            <>
              <Separator />
              <div className="space-y-4">
                <Label className="text-base font-medium">Bases数据库设置</Label>
                
                <div>
                  <Label htmlFor="database-name">数据库名称</Label>
                  <Input
                    id="database-name"
                    value={syncSettings.databaseName}
                    onChange={(e) => saveSettings({ databaseName: e.target.value })}
                    placeholder="书签数据库"
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">创建预设视图</Label>
                      <p className="text-xs text-muted-foreground">
                        自动创建按分类、收藏、待读等分组的视图
                      </p>
                    </div>
                    <Switch
                      checked={syncSettings.createViews}
                      onCheckedChange={(checked) => saveSettings({ createViews: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">启用评分字段</Label>
                      <p className="text-xs text-muted-foreground">
                        在数据库中添加1-5星评分字段
                      </p>
                    </div>
                    <Switch
                      checked={syncSettings.enableRating}
                      onCheckedChange={(checked) => saveSettings({ enableRating: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">启用状态字段</Label>
                      <p className="text-xs text-muted-foreground">
                        添加未读/已读/收藏/归档状态管理
                      </p>
                    </div>
                    <Switch
                      checked={syncSettings.enableStatus}
                      onCheckedChange={(checked) => saveSettings({ enableStatus: checked })}
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 自定义模板 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">自定义模板</CardTitle>
          <CardDescription>
            自定义Markdown模板，支持变量替换
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="template">模板内容</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => saveSettings({ template: getDefaultTemplate() })}
              >
                恢复默认
              </Button>
            </div>
            <Textarea
              id="template"
              value={syncSettings.template || getDefaultTemplate()}
              onChange={(e) => saveSettings({ template: e.target.value })}
              placeholder="输入自定义模板..."
              rows={10}
              className="font-mono text-sm"
            />
            <div className="mt-2">
              <Label className="text-xs font-medium">可用变量:</Label>
              <div className="flex flex-wrap gap-2 mt-1">
                {['{{title}}', '{{url}}', '{{description}}', '{{tags}}', '{{category}}', '{{date}}'].map(variable => (
                  <Badge key={variable} variant="secondary" className="text-xs">
                    {variable}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">同步操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={handleExportBookmark}
              disabled={loading || !selectedVault}
              className="flex items-center justify-center"
            >
              <Download className="w-4 h-4 mr-2" />
              导出当前书签
            </Button>
            
            <Button
              onClick={handleSyncAllBookmarks}
              disabled={loading || !selectedVault}
              variant="outline"
              className="flex items-center justify-center"
            >
              <Upload className="w-4 h-4 mr-2" />
              同步所有书签
            </Button>
            
            {syncSettings.exportType === 'bases' && (
              <Button
                onClick={handleCreateDatabase}
                disabled={loading || !selectedVault}
                variant="outline"
                className="flex items-center justify-center"
              >
                <Database className="w-4 h-4 mr-2" />
                创建Bases数据库
              </Button>
            )}
          </div>
          
          {!selectedVault && (
            <Alert className="mt-4">
              <AlertCircle className="w-4 h-4" />
              <AlertDescription>
                请先选择一个Obsidian库才能进行同步操作
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 功能说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Eye className="w-5 h-5 mr-2" />
            功能说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">标准Markdown格式</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 每个书签生成独立的.md文件</li>
                <li>• 包含完整的元数据和内容</li>
                <li>• 支持Dataview查询代码</li>
                <li>• 自动生成双向链接</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Bases数据库格式</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 创建结构化的数据库表格</li>
                <li>• 支持筛选、排序、分组</li>
                <li>• 自动生成多种视图</li>
                <li>• 支持评分和状态管理</li>
              </ul>
            </div>
          </div>
          
          <Separator />
          
          <div>
            <h4 className="font-medium mb-2">高级功能</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
              <div>
                <strong>智能模板:</strong> 支持自定义Markdown模板和变量替换
              </div>
              <div>
                <strong>自动同步:</strong> 新增书签时自动同步到Obsidian
              </div>
              <div>
                <strong>批量操作:</strong> 支持按分类或标签批量导出
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ObsidianIntegrationTab