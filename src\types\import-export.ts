// 导入导出相关类型定义

import { BookmarkInput, CategoryInput, TagInput, DateRange, DataType, ConflictType, ConflictAction } from './index'

// 导出格式类型
export type ExportFormat = 'json' | 'html' | 'csv'

// 导出类型枚举
export type ExportType = 'all' | 'bookmarks' | 'categories' | 'tags'

// 基础导出选项接口
export interface BaseExportOptions {
  format: ExportFormat
}

// 导出选项类型
export interface ExportOptions extends BaseExportOptions {
  includeNotes: boolean
  categories?: string[]
  tags?: string[]
}

// 全部数据导出选项
export interface ExportAllOptions extends BaseExportOptions {
  format: 'json'
  includeBookmarks: boolean
  includeCategories: boolean
  includeTags: boolean
  includeMetadata: boolean
  dateRange?: DateRange
}

// 收藏夹导出选项
export interface ExportBookmarksOptions extends ExportOptions {
  includeRelatedCategories: boolean
  includeRelatedTags: boolean
}

// 分类导出选项
export interface ExportCategoriesOptions extends BaseExportOptions {
  format: 'json'
  includeHierarchy: boolean
  includeStatistics: boolean
  categoryIds?: string[]
}

// 标签导出选项
export interface ExportTagsOptions extends BaseExportOptions {
  format: 'json'
  includeUsageStats: boolean
  includeRelatedBookmarks: boolean
  tagIds?: string[]
}

// 导出结果接口
export interface ExportResult {
  success: boolean
  fileName: string
  fileSize: number
  exportedCount: number
  error?: string
}

// 导入元数据
export interface ImportMetadata {
  source: string
  totalBookmarks: number
  totalCategories: number
  totalTags: number
  exportOptions: Record<string, any>
}

// 完整导入数据结构
export interface ImportData {
  version: string
  exportDate: string
  exportType: ExportType
  metadata: ImportMetadata
  bookmarks?: BookmarkInput[]
  categories?: CategoryInput[]
  tags?: TagInput[]
}

// 导入选项
export interface ImportOptions {
  conflictResolution: 'ask' | 'keep_existing' | 'use_imported' | 'merge'
  validateData: boolean
  createBackup: boolean
  batchSize?: number
}

// 导入结果
export interface ImportResult {
  success: boolean
  importedCount: {
    bookmarks: number
    categories: number
    tags: number
  }
  skippedCount: {
    bookmarks: number
    categories: number
    tags: number
  }
  errorCount: {
    bookmarks: number
    categories: number
    tags: number
  }
  conflicts?: ConflictItem[]
  errors?: ImportError[]
}

// 导入错误
export interface ImportError {
  type: DataType
  item: any
  error: string
  field?: string
}

// 冲突项
export interface ConflictItem {
  id: string
  type: DataType
  conflictType: ConflictType
  existingData: any
  importData: any
  conflictFields: string[]
  similarity: number
}

// 冲突解决方案
export interface ConflictResolution {
  conflictId: string
  action: ConflictAction
  mergedData?: any
  manualData?: any
}

// 冲突检测结果
export interface ConflictDetectionResult {
  hasConflicts: boolean
  conflicts: ConflictItem[]
  summary: ConflictSummary
}

// 冲突摘要
export interface ConflictSummary {
  bookmarkConflicts: number
  categoryConflicts: number
  tagConflicts: number
  totalConflicts: number
}

// 解决后的数据
export interface ResolvedData {
  bookmarks: BookmarkInput[]
  categories: CategoryInput[]
  tags: TagInput[]
}

// 进度回调函数类型
export type ProgressCallback = (progress: ImportExportProgress) => void

// 导入导出进度
export interface ImportExportProgress {
  phase: 'parsing' | 'validating' | 'detecting_conflicts' | 'resolving_conflicts' | 'importing' | 'exporting'
  current: number
  total: number
  percentage: number
  message: string
}