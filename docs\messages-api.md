# 消息通信 API 文档

## 概述

`src/types/messages.ts` 文件定义了 Universe Bag Chrome 扩展中所有组件间的消息通信接口。该文件提供了类型安全的消息传递机制，确保 Background Service Worker、Content Script、Popup 和 Options 页面之间的可靠通信。

## 核心接口

### 基础消息接口

#### BaseMessage
```typescript
interface BaseMessage {
  type: string
  requestId?: string
  timestamp?: number
}
```

**功能**: 所有消息的基础接口  
**字段说明**:
- `type`: 消息类型标识符，用于路由消息到正确的处理器
- `requestId`: 可选的请求ID，用于追踪异步请求
- `timestamp`: 可选的时间戳，用于消息排序和调试

#### MessageResponse<T>
```typescript
interface MessageResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  requestId?: string
}
```

**功能**: 标准化的消息响应格式  
**泛型参数**: `T` - 响应数据的类型  
**字段说明**:
- `success`: 操作是否成功
- `data`: 响应数据，类型由泛型参数决定
- `error`: 错误信息（操作失败时）
- `requestId`: 对应请求的ID

## 页面信息相关消息

### GetPageInfoMessage
```typescript
interface GetPageInfoMessage extends BaseMessage {
  type: 'GET_PAGE_INFO'
  data?: {
    selectedText?: string
    includeMetadata?: boolean
  }
}
```

**功能**: 请求获取当前页面信息  
**使用场景**: Content Script 向 Background 提供页面数据  
**可选参数**:
- `selectedText`: 页面选中的文字内容
- `includeMetadata`: 是否包含页面元数据

**使用示例**:
```typescript
// 在 Popup 中请求页面信息
const message: GetPageInfoMessage = {
  type: 'GET_PAGE_INFO',
  data: {
    includeMetadata: true
  }
}

const response = await chrome.runtime.sendMessage(message)
if (response.success) {
  console.log('页面信息:', response.data)
}
```

### GetPageInfoResponse
```typescript
interface GetPageInfoResponse extends MessageResponse<PageInfo> {}
```

**功能**: 页面信息获取的响应  
**数据类型**: `PageInfo` - 包含页面标题、URL、图标等信息

### GetLinkInfoMessage
```typescript
interface GetLinkInfoMessage extends BaseMessage {
  type: 'GET_LINK_INFO'
  data: {
    url: string
  }
}
```

**功能**: 请求获取特定链接的信息  
**必需参数**:
- `url`: 要获取信息的链接地址

**使用示例**:
```typescript
// 获取链接信息
const message: GetLinkInfoMessage = {
  type: 'GET_LINK_INFO',
  data: {
    url: 'https://example.com'
  }
}
```

### GetLinkInfoResponse
```typescript
interface GetLinkInfoResponse extends MessageResponse<{
  title?: string
  description?: string
  favicon?: string
}> {}
```

**功能**: 链接信息获取的响应  
**响应数据**:
- `title`: 链接标题
- `description`: 链接描述
- `favicon`: 网站图标URL

## 收藏功能相关消息

### QuickBookmarkMessage
```typescript
interface QuickBookmarkMessage extends BaseMessage {
  type: 'QUICK_BOOKMARK'
  data: {
    title: string
    url: string
    favIconUrl?: string
    selectedText?: string
  }
}
```

**功能**: 快速收藏页面或链接  
**必需参数**:
- `title`: 收藏标题
- `url`: 收藏链接

**可选参数**:
- `favIconUrl`: 网站图标URL
- `selectedText`: 选中的文字内容

**使用示例**:
```typescript
// 快速收藏当前页面
const message: QuickBookmarkMessage = {
  type: 'QUICK_BOOKMARK',
  data: {
    title: document.title,
    url: window.location.href,
    favIconUrl: '/favicon.ico'
  }
}

const response = await chrome.runtime.sendMessage(message)
if (response.success) {
  console.log('收藏成功，ID:', response.data.bookmarkId)
}
```

### QuickBookmarkResponse
```typescript
interface QuickBookmarkResponse extends MessageResponse<{
  bookmarkId: string
}> {}
```

**功能**: 快速收藏操作的响应  
**响应数据**:
- `bookmarkId`: 新创建的收藏项ID

### BookmarkSelectedTextMessage
```typescript
interface BookmarkSelectedTextMessage extends BaseMessage {
  type: 'BOOKMARK_SELECTED_TEXT'
  data: {
    selectedText: string
    url: string
    title: string
    context?: string
  }
}
```

**功能**: 收藏页面选中的文字  
**必需参数**:
- `selectedText`: 选中的文字内容
- `url`: 页面URL
- `title`: 页面标题

**可选参数**:
- `context`: 选中文字的上下文

**使用示例**:
```typescript
// 收藏选中文字
const selectedText = window.getSelection()?.toString()
if (selectedText) {
  const message: BookmarkSelectedTextMessage = {
    type: 'BOOKMARK_SELECTED_TEXT',
    data: {
      selectedText,
      url: window.location.href,
      title: document.title,
      context: '上下文信息'
    }
  }
  
  await chrome.runtime.sendMessage(message)
}
```

### SaveDetailedBookmarkMessage
```typescript
interface SaveDetailedBookmarkMessage extends BaseMessage {
  type: 'SAVE_DETAILED_BOOKMARK'
  data: BookmarkInput
}
```

**功能**: 保存详细的收藏信息  
**数据类型**: `BookmarkInput` - 完整的收藏输入数据

**使用示例**:
```typescript
// 保存详细收藏
const message: SaveDetailedBookmarkMessage = {
  type: 'SAVE_DETAILED_BOOKMARK',
  data: {
    type: 'url',
    title: '示例页面',
    url: 'https://example.com',
    description: '这是一个示例页面',
    tags: ['示例', '测试'],
    category: '默认分类'
  }
}
```

### UpdateBookmarkMessage
```typescript
interface UpdateBookmarkMessage extends BaseMessage {
  type: 'UPDATE_BOOKMARK'
  data: {
    id: string
    updates: BookmarkUpdate
  }
}
```

**功能**: 更新现有收藏  
**必需参数**:
- `id`: 收藏项ID
- `updates`: 要更新的字段

**使用示例**:
```typescript
// 更新收藏标签
const message: UpdateBookmarkMessage = {
  type: 'UPDATE_BOOKMARK',
  data: {
    id: 'bookmark-123',
    updates: {
      tags: ['新标签', '更新'],
      description: '更新后的描述'
    }
  }
}
```

### DeleteBookmarkMessage
```typescript
interface DeleteBookmarkMessage extends BaseMessage {
  type: 'DELETE_BOOKMARK'
  data: {
    id: string
  }
}
```

**功能**: 删除收藏项  
**必需参数**:
- `id`: 要删除的收藏项ID

## 状态管理相关消息

### CheckBookmarkStatusMessage
```typescript
interface CheckBookmarkStatusMessage extends BaseMessage {
  type: 'CHECK_BOOKMARK_STATUS'
  data: {
    url: string
  }
}
```

**功能**: 检查URL是否已被收藏  
**必需参数**:
- `url`: 要检查的URL

**使用示例**:
```typescript
// 检查当前页面是否已收藏
const message: CheckBookmarkStatusMessage = {
  type: 'CHECK_BOOKMARK_STATUS',
  data: {
    url: window.location.href
  }
}

const response = await chrome.runtime.sendMessage(message)
if (response.success && response.data.isBookmarked) {
  console.log('页面已收藏，ID:', response.data.bookmarkId)
}
```

### CheckBookmarkStatusResponse
```typescript
interface CheckBookmarkStatusResponse extends MessageResponse<{
  isBookmarked: boolean
  bookmarkId?: string
}> {}
```

**功能**: 收藏状态检查的响应  
**响应数据**:
- `isBookmarked`: 是否已收藏
- `bookmarkId`: 收藏项ID（如果已收藏）

### UpdateIconStatusMessage
```typescript
interface UpdateIconStatusMessage extends BaseMessage {
  type: 'UPDATE_ICON_STATUS'
  data: {
    tabId: number
    isBookmarked: boolean
  }
}
```

**功能**: 更新浏览器扩展图标状态  
**必需参数**:
- `tabId`: 标签页ID
- `isBookmarked`: 是否已收藏

## AI功能相关消息

### AIGenerateSuggestionsMessage
```typescript
interface AIGenerateSuggestionsMessage extends BaseMessage {
  type: 'AI_GENERATE_SUGGESTIONS'
  data: {
    content: string
    url?: string
    title?: string
  }
}
```

**功能**: 请求AI生成标签和分类建议  
**必需参数**:
- `content`: 要分析的内容

**可选参数**:
- `url`: 页面URL
- `title`: 页面标题

**使用示例**:
```typescript
// 请求AI生成建议
const message: AIGenerateSuggestionsMessage = {
  type: 'AI_GENERATE_SUGGESTIONS',
  data: {
    content: '这是一篇关于机器学习的技术文章',
    url: 'https://example.com/ml-article',
    title: '机器学习入门指南'
  }
}

const response = await chrome.runtime.sendMessage(message)
if (response.success) {
  console.log('AI建议:', response.data)
  console.log('建议标签:', response.data.tags)
  console.log('建议分类:', response.data.category)
}
```

### AIGenerateSuggestionsResponse
```typescript
interface AIGenerateSuggestionsResponse extends MessageResponse<AISuggestions> {}
```

**功能**: AI建议生成的响应  
**数据类型**: `AISuggestions` - 包含标签、分类和置信度

## 设置管理相关消息

### GetSettingsMessage
```typescript
interface GetSettingsMessage extends BaseMessage {
  type: 'GET_SETTINGS'
}
```

**功能**: 获取用户设置  
**无需参数**

**使用示例**:
```typescript
// 获取用户设置
const message: GetSettingsMessage = {
  type: 'GET_SETTINGS'
}

const response = await chrome.runtime.sendMessage(message)
if (response.success) {
  console.log('用户设置:', response.data)
}
```

### UpdateSettingsMessage
```typescript
interface UpdateSettingsMessage extends BaseMessage {
  type: 'UPDATE_SETTINGS'
  data: Record<string, any>
}
```

**功能**: 更新用户设置  
**参数**: 键值对形式的设置数据

**使用示例**:
```typescript
// 更新设置
const message: UpdateSettingsMessage = {
  type: 'UPDATE_SETTINGS',
  data: {
    theme: 'dark',
    autoTagging: true,
    language: 'zh-CN'
  }
}
```

## 同步功能相关消息

### ManualSyncMessage
```typescript
interface ManualSyncMessage extends BaseMessage {
  type: 'MANUAL_SYNC'
}
```

**功能**: 触发手动同步  
**无需参数**

### ManualSyncResponse
```typescript
interface ManualSyncResponse extends MessageResponse<{
  syncedCount: number
  lastSync: Date
}> {}
```

**功能**: 手动同步的响应  
**响应数据**:
- `syncedCount`: 同步的项目数量
- `lastSync`: 最后同步时间

## 工具消息

### PingMessage
```typescript
interface PingMessage extends BaseMessage {
  type: 'PING'
}
```

**功能**: 测试连接状态  
**用途**: 检查组件间通信是否正常

**使用示例**:
```typescript
// 测试连接
const message: PingMessage = {
  type: 'PING'
}

const response = await chrome.runtime.sendMessage(message)
if (response.success && response.data.status === 'pong') {
  console.log('连接正常')
}
```

### PingResponse
```typescript
interface PingResponse extends MessageResponse<{
  status: 'pong'
  timestamp: number
}> {}
```

**功能**: Ping消息的响应  
**响应数据**:
- `status`: 固定返回 'pong'
- `timestamp`: 响应时间戳

## 联合类型

### ExtensionMessage
```typescript
type ExtensionMessage = 
  | GetPageInfoMessage
  | GetLinkInfoMessage
  | QuickBookmarkMessage
  | BookmarkSelectedTextMessage
  | SaveDetailedBookmarkMessage
  | UpdateBookmarkMessage
  | DeleteBookmarkMessage
  | CheckBookmarkStatusMessage
  | UpdateIconStatusMessage
  | AIGenerateSuggestionsMessage
  | GetSettingsMessage
  | UpdateSettingsMessage
  | ManualSyncMessage
  | PingMessage
```

**功能**: 所有消息类型的联合类型  
**用途**: 类型安全的消息处理

### ExtensionMessageResponse
```typescript
type ExtensionMessageResponse = 
  | GetPageInfoResponse
  | GetLinkInfoResponse
  | QuickBookmarkResponse
  | BookmarkSelectedTextResponse
  | SaveDetailedBookmarkResponse
  | UpdateBookmarkResponse
  | DeleteBookmarkResponse
  | CheckBookmarkStatusResponse
  | UpdateIconStatusResponse
  | AIGenerateSuggestionsResponse
  | GetSettingsResponse
  | UpdateSettingsResponse
  | ManualSyncResponse
  | PingResponse
```

**功能**: 所有响应类型的联合类型  
**用途**: 类型安全的响应处理

## 辅助类型

### MessageSender
```typescript
type MessageSender = 'background' | 'popup' | 'content' | 'options'
```

**功能**: 消息发送者类型标识  
**用途**: 识别消息来源

### MessageHandler<T, R>
```typescript
type MessageHandler<T extends ExtensionMessage, R extends MessageResponse> = (
  message: T,
  sender: chrome.runtime.MessageSender
) => Promise<R>
```

**功能**: 消息处理器函数类型  
**泛型参数**:
- `T`: 消息类型
- `R`: 响应类型

**使用示例**:
```typescript
// 定义消息处理器
const handleQuickBookmark: MessageHandler<QuickBookmarkMessage, QuickBookmarkResponse> = 
  async (message, sender) => {
    try {
      const bookmarkId = await createBookmark(message.data)
      return {
        success: true,
        data: { bookmarkId }
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }
```

### MessageError
```typescript
interface MessageError {
  code: string
  message: string
  details?: any
}
```

**功能**: 标准化的错误信息格式  
**字段说明**:
- `code`: 错误代码
- `message`: 错误描述
- `details`: 错误详细信息

### MessageValidationResult
```typescript
interface MessageValidationResult {
  isValid: boolean
  errors: MessageError[]
}
```

**功能**: 消息验证结果  
**字段说明**:
- `isValid`: 消息是否有效
- `errors`: 验证错误列表

## 最佳实践

### 1. 消息发送
```typescript
// 推荐的消息发送方式
async function sendMessage<T extends ExtensionMessage>(message: T): Promise<MessageResponse> {
  try {
    // 添加时间戳和请求ID
    const messageWithMeta = {
      ...message,
      timestamp: Date.now(),
      requestId: generateRequestId()
    }
    
    const response = await chrome.runtime.sendMessage(messageWithMeta)
    return response
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}
```

### 2. 消息处理
```typescript
// 推荐的消息处理方式
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  handleMessage(message, sender)
    .then(response => sendResponse(response))
    .catch(error => sendResponse({
      success: false,
      error: error.message,
      requestId: message.requestId
    }))
  
  return true // 异步响应
})
```

### 3. 错误处理
```typescript
// 统一的错误处理
function createErrorResponse(error: Error, requestId?: string): MessageResponse {
  return {
    success: false,
    error: error.message,
    requestId
  }
}
```

### 4. 类型安全
```typescript
// 使用类型守卫确保类型安全
function isQuickBookmarkMessage(message: ExtensionMessage): message is QuickBookmarkMessage {
  return message.type === 'QUICK_BOOKMARK'
}

// 在处理器中使用
if (isQuickBookmarkMessage(message)) {
  // TypeScript 现在知道 message 是 QuickBookmarkMessage 类型
  console.log(message.data.title)
}
```

## 扩展指南

### 添加新消息类型

1. **定义消息接口**:
```typescript
interface NewFeatureMessage extends BaseMessage {
  type: 'NEW_FEATURE'
  data: {
    // 消息数据
  }
}

interface NewFeatureResponse extends MessageResponse<{
  // 响应数据
}> {}
```

2. **更新联合类型**:
```typescript
export type ExtensionMessage = 
  | GetPageInfoMessage
  | GetLinkInfoMessage
  // ... 其他类型
  | NewFeatureMessage // 添加新类型
```

3. **实现消息处理器**:
```typescript
const handleNewFeature: MessageHandler<NewFeatureMessage, NewFeatureResponse> = 
  async (message, sender) => {
    // 处理逻辑
  }
```

### 消息验证

```typescript
// 消息验证函数
function validateMessage(message: any): MessageValidationResult {
  const errors: MessageError[] = []
  
  if (!message.type) {
    errors.push({
      code: 'MISSING_TYPE',
      message: '消息类型不能为空'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}
```

## 调试技巧

### 1. 消息日志
```typescript
// 添加消息日志
function logMessage(message: ExtensionMessage, direction: 'send' | 'receive') {
  console.log(`[${direction.toUpperCase()}] ${message.type}:`, message)
}
```

### 2. 性能监控
```typescript
// 监控消息处理时间
async function handleMessageWithTiming<T extends ExtensionMessage>(
  message: T,
  handler: MessageHandler<T, any>
): Promise<MessageResponse> {
  const startTime = performance.now()
  
  try {
    const response = await handler(message, sender)
    const endTime = performance.now()
    
    console.log(`消息 ${message.type} 处理耗时: ${endTime - startTime}ms`)
    return response
  } catch (error) {
    console.error(`消息 ${message.type} 处理失败:`, error)
    throw error
  }
}
```

### 3. 消息队列
```typescript
// 实现消息队列以处理并发
class MessageQueue {
  private queue: Array<{
    message: ExtensionMessage
    resolve: (response: MessageResponse) => void
    reject: (error: Error) => void
  }> = []
  
  private processing = false
  
  async enqueue(message: ExtensionMessage): Promise<MessageResponse> {
    return new Promise((resolve, reject) => {
      this.queue.push({ message, resolve, reject })
      this.processQueue()
    })
  }
  
  private async processQueue() {
    if (this.processing || this.queue.length === 0) return
    
    this.processing = true
    
    while (this.queue.length > 0) {
      const { message, resolve, reject } = this.queue.shift()!
      
      try {
        const response = await this.handleMessage(message)
        resolve(response)
      } catch (error) {
        reject(error)
      }
    }
    
    this.processing = false
  }
  
  private async handleMessage(message: ExtensionMessage): Promise<MessageResponse> {
    // 实际的消息处理逻辑
    return { success: true }
  }
}
```

## 总结

`src/types/messages.ts` 文件提供了完整的消息通信类型系统，确保了 Universe Bag 扩展各组件间的类型安全通信。通过使用这些类型定义，开发者可以：

1. **类型安全**: 编译时检查消息格式
2. **代码提示**: IDE 提供完整的代码补全
3. **文档化**: 类型即文档，清晰的接口定义
4. **可维护性**: 统一的消息格式便于维护
5. **扩展性**: 易于添加新的消息类型

这个类型系统为扩展的可靠性和开发效率提供了坚实的基础。