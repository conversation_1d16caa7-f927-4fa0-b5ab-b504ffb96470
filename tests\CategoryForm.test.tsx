// CategoryForm组件单元测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import CategoryForm from '../src/components/CategoryForm'
import { categoryService } from '../src/services/categoryService'
import type { Category } from '../src/types'

// 模拟categoryService
vi.mock('../src/services/categoryService', () => ({
  categoryService: {
    validateCategoryName: vi.fn()
  }
}))

const mockCategoryService = categoryService as any

// 模拟分类数据
const mockCategory: Category = {
  id: 'category-1',
  name: '技术',
  description: '技术相关的书签分类',
  color: '#3B82F6',
  createdAt: new Date('2024-01-01T10:00:00Z'),
  updatedAt: new Date('2024-01-02T10:00:00Z'),
  bookmarkCount: 0
}

describe('CategoryForm组件', () => {
  const mockOnSubmit = vi.fn()
  const mockOnCancel = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    // 默认情况下名称验证通过
    mockCategoryService.validateCategoryName.mockResolvedValue(true)
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('创建模式', () => {
    it('应该正确渲染创建表单', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      // 检查表单字段
      expect(screen.getByLabelText(/分类名称/)).toBeInTheDocument()
      expect(screen.getByLabelText(/分类描述/)).toBeInTheDocument()
      expect(screen.getByLabelText(/分类颜色/)).toBeInTheDocument()
      
      // 检查按钮
      expect(screen.getByText('创建分类')).toBeInTheDocument()
      expect(screen.getByText('取消')).toBeInTheDocument()
    })

    it('应该显示默认的表单值', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/) as HTMLInputElement
      const descriptionInput = screen.getByLabelText(/分类描述/) as HTMLTextAreaElement
      const colorInput = screen.getByDisplayValue('#3B82F6') as HTMLInputElement

      expect(nameInput.value).toBe('')
      expect(descriptionInput.value).toBe('')
      expect(colorInput.value).toBe('#3B82F6')
    })
  })

  describe('编辑模式', () => {
    it('应该正确渲染编辑表单', () => {
      render(
        <CategoryForm
          mode="edit"
          category={mockCategory}
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      // 检查按钮文本
      expect(screen.getByText('保存修改')).toBeInTheDocument()
    })

    it('应该使用分类数据填充表单', () => {
      render(
        <CategoryForm
          mode="edit"
          category={mockCategory}
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByDisplayValue('技术') as HTMLInputElement
      const descriptionInput = screen.getByDisplayValue('技术相关的书签分类') as HTMLTextAreaElement
      const colorInput = screen.getByDisplayValue('#3B82F6') as HTMLInputElement

      expect(nameInput.value).toBe('技术')
      expect(descriptionInput.value).toBe('技术相关的书签分类')
      expect(colorInput.value).toBe('#3B82F6')
    })
  })

  describe('表单验证', () => {
    it('应该验证分类名称不能为空', async () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('分类名称不能为空')).toBeInTheDocument()
      })

      expect(mockOnSubmit).not.toHaveBeenCalled()
    })

    it('应该验证分类名称长度限制', async () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/)
      
      // 测试最小长度
      fireEvent.change(nameInput, { target: { value: 'a' } })
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('分类名称至少需要2个字符')).toBeInTheDocument()
      })

      // 测试最大长度
      const longName = 'a'.repeat(51)
      fireEvent.change(nameInput, { target: { value: longName } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('分类名称不能超过50个字符')).toBeInTheDocument()
      })

      expect(mockOnSubmit).not.toHaveBeenCalled()
    })

    it('应该验证分类名称唯一性', async () => {
      // 模拟名称已存在
      mockCategoryService.validateCategoryName.mockResolvedValue(false)

      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/)
      fireEvent.change(nameInput, { target: { value: '已存在的分类' } })
      
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('分类名称已存在，请使用其他名称')).toBeInTheDocument()
      })

      expect(mockCategoryService.validateCategoryName).toHaveBeenCalledWith('已存在的分类', undefined)
      expect(mockOnSubmit).not.toHaveBeenCalled()
    })

    it('应该验证描述长度限制', async () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/)
      const descriptionInput = screen.getByLabelText(/分类描述/)
      
      fireEvent.change(nameInput, { target: { value: '有效名称' } })
      fireEvent.change(descriptionInput, { target: { value: 'a'.repeat(201) } })
      
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('分类描述不能超过200个字符')).toBeInTheDocument()
      })

      expect(mockOnSubmit).not.toHaveBeenCalled()
    })

    it('应该验证颜色格式', async () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/)
      const colorInput = screen.getByLabelText(/分类颜色/)
      
      fireEvent.change(nameInput, { target: { value: '有效名称' } })
      fireEvent.change(colorInput, { target: { value: '' } }) // 空颜色值
      
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('请选择分类颜色')).toBeInTheDocument()
      })

      expect(mockOnSubmit).not.toHaveBeenCalled()
    })
  })

  describe('颜色选择功能', () => {
    it('应该显示预定义的颜色选项', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      // 检查是否有颜色选择按钮
      const colorButtons = screen.getAllByRole('button').filter(button => 
        button.getAttribute('title')?.includes('色')
      )
      
      expect(colorButtons.length).toBeGreaterThan(0)
    })

    it('应该能够选择预定义颜色', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      // 点击红色选项
      const redColorButton = screen.getByTitle('红色')
      fireEvent.click(redColorButton)

      // 检查颜色输入框的值是否更新
      const colorInput = screen.getByDisplayValue('#EF4444') as HTMLInputElement
      expect(colorInput.value).toBe('#EF4444')
    })

    it('应该支持自定义颜色输入', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const colorInput = screen.getByLabelText(/分类颜色/)
      fireEvent.change(colorInput, { target: { value: '#FF5733' } })

      expect((colorInput as HTMLInputElement).value).toBe('#FF5733')
    })
  })

  describe('表单提交', () => {
    it('应该在验证通过后提交表单', async () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/)
      const descriptionInput = screen.getByLabelText(/分类描述/)
      
      fireEvent.change(nameInput, { target: { value: '新分类' } })
      fireEvent.change(descriptionInput, { target: { value: '新分类的描述' } })
      
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: '新分类',
          description: '新分类的描述',
          color: '#3B82F6',
          parentId: undefined
        })
      })
    })

    it('应该在编辑模式下正确提交', async () => {
      render(
        <CategoryForm
          mode="edit"
          category={mockCategory}
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByDisplayValue('技术')
      fireEvent.change(nameInput, { target: { value: '更新的技术' } })
      
      const submitButton = screen.getByText('保存修改')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: '更新的技术',
          description: '技术相关的书签分类',
          color: '#3B82F6',
          parentId: undefined
        })
      })
    })

    it('应该处理空描述', async () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/)
      fireEvent.change(nameInput, { target: { value: '无描述分类' } })
      
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: '无描述分类',
          description: undefined,
          color: '#3B82F6',
          parentId: undefined
        })
      })
    })
  })

  describe('交互功能', () => {
    it('应该在点击取消按钮时调用onCancel', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const cancelButton = screen.getByText('取消')
      fireEvent.click(cancelButton)

      expect(mockOnCancel).toHaveBeenCalledTimes(1)
    })

    it('应该清除字段错误当用户输入时', async () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      // 先触发验证错误
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('分类名称不能为空')).toBeInTheDocument()
      })

      // 然后输入内容，错误应该消失
      const nameInput = screen.getByLabelText(/分类名称/)
      fireEvent.change(nameInput, { target: { value: '新名称' } })

      await waitFor(() => {
        expect(screen.queryByText('分类名称不能为空')).not.toBeInTheDocument()
      })
    })
  })

  describe('加载状态', () => {
    it('应该在加载时禁用表单元素', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
          loading={true}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/)
      const descriptionInput = screen.getByLabelText(/分类描述/)
      const colorInput = screen.getByLabelText(/分类颜色/)
      const submitButton = screen.getByRole('button', { name: /保存中/ })
      const cancelButton = screen.getByText('取消')

      expect(nameInput).toBeDisabled()
      expect(descriptionInput).toBeDisabled()
      expect(colorInput).toBeDisabled()
      expect(submitButton).toBeDisabled()
      expect(cancelButton).toBeDisabled()
    })

    it('应该显示加载状态文本', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
          loading={true}
        />
      )

      expect(screen.getByText('保存中...')).toBeInTheDocument()
    })
  })

  describe('字符计数', () => {
    it('应该显示名称字符计数', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      expect(screen.getByText('0/50 字符')).toBeInTheDocument()

      const nameInput = screen.getByLabelText(/分类名称/)
      fireEvent.change(nameInput, { target: { value: '测试' } })

      expect(screen.getByText('2/50 字符')).toBeInTheDocument()
    })

    it('应该显示描述字符计数', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      expect(screen.getByText('0/200 字符')).toBeInTheDocument()

      const descriptionInput = screen.getByLabelText(/分类描述/)
      fireEvent.change(descriptionInput, { target: { value: '测试描述' } })

      expect(screen.getByText('4/200 字符')).toBeInTheDocument()
    })
  })

  describe('颜色名称显示', () => {
    it('应该显示预定义颜色的名称', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      // 默认应该显示蓝色
      expect(screen.getByText('蓝色')).toBeInTheDocument()

      // 选择红色
      const redColorButton = screen.getByTitle('红色')
      fireEvent.click(redColorButton)

      expect(screen.getByText('红色')).toBeInTheDocument()
    })

    it('应该为自定义颜色显示"自定义颜色"', () => {
      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const colorInput = screen.getByLabelText(/分类颜色/)
      fireEvent.change(colorInput, { target: { value: '#123456' } })

      expect(screen.getByText('自定义颜色')).toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该处理名称验证服务错误', async () => {
      // 模拟服务错误
      mockCategoryService.validateCategoryName.mockRejectedValue(new Error('服务错误'))

      render(
        <CategoryForm
          mode="create"
          onSubmit={mockOnSubmit}
          onCancel={mockOnCancel}
        />
      )

      const nameInput = screen.getByLabelText(/分类名称/)
      fireEvent.change(nameInput, { target: { value: '测试名称' } })
      
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      // 即使验证服务出错，也应该允许提交（由服务端处理）
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalled()
      })
    })
  })
})