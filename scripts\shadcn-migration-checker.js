#!/usr/bin/env node

/**
 * shadcn/ui 迁移检查工具
 * 自动检查代码中的shadcn迁移质量
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 颜色映射规则
const COLOR_MIGRATION_RULES = {
  // 背景色规则
  'bg-gray-100': 'bg-secondary',
  'bg-gray-200': 'bg-secondary/80',
  'bg-gray-800': 'bg-secondary',
  'bg-gray-900': 'bg-background',
  'bg-white': 'bg-background',
  'bg-blue-500': 'bg-primary',
  'bg-blue-600': 'bg-primary/90',
  
  // 文本色规则
  'text-gray-700': 'text-secondary-foreground',
  'text-gray-300': 'text-secondary-foreground',
  'text-gray-600': 'text-muted-foreground',
  'text-black': 'text-foreground',
  'text-white': 'text-primary-foreground',
  
  // 边框色规则
  'border-gray-300': 'border-border',
  'border-gray-200': 'border-border',
  'border-blue-500': 'border-primary',
  
  // 焦点环规则
  'focus:ring-blue-500': 'focus:ring-ring',
  'focus:ring-primary-500': 'focus:ring-ring',
  'focus:ring-gray-500': 'focus:ring-ring'
}

// 需要避免的深色模式类
const DARK_MODE_CLASSES = [
  'dark:bg-gray-800',
  'dark:bg-gray-900',
  'dark:text-gray-300',
  'dark:text-white',
  'dark:border-gray-600',
  'dark:border-gray-700'
]

// 性能优化检查规则
const PERFORMANCE_RULES = {
  // 需要useMemo的模式
  needsUseMemo: [
    /\.find\(/,
    /\.filter\(/,
    /\.map\(/,
    /\.reduce\(/,
    /expensiveCalculation/,
    /heavyComputation/
  ],
  
  // 需要useCallback的模式
  needsUseCallback: [
    /const handle\w+ = \(/,
    /const on\w+ = \(/,
    /onClick.*=>/,
    /onChange.*=>/
  ]
}

class ShadcnMigrationChecker {
  constructor() {
    this.issues = []
    this.stats = {
      filesChecked: 0,
      issuesFound: 0,
      migratedFiles: 0,
      totalFiles: 0
    }
  }

  /**
   * 检查单个文件
   */
  checkFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    const fileIssues = []

    this.stats.filesChecked++

    // 检查颜色迁移
    this.checkColorMigration(filePath, content, lines, fileIssues)
    
    // 检查深色模式类
    this.checkDarkModeClasses(filePath, content, lines, fileIssues)
    
    // 检查性能优化
    this.checkPerformanceOptimization(filePath, content, lines, fileIssues)
    
    // 检查测试文件
    this.checkTestCoverage(filePath, fileIssues)

    if (fileIssues.length > 0) {
      this.issues.push({
        file: filePath,
        issues: fileIssues
      })
      this.stats.issuesFound += fileIssues.length
    } else {
      this.stats.migratedFiles++
    }

    return fileIssues
  }

  /**
   * 检查颜色迁移
   */
  checkColorMigration(filePath, content, lines, issues) {
    Object.entries(COLOR_MIGRATION_RULES).forEach(([oldClass, newClass]) => {
      const regex = new RegExp(`\\b${oldClass.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g')
      let match
      
      while ((match = regex.exec(content)) !== null) {
        const lineNumber = content.substring(0, match.index).split('\n').length
        issues.push({
          type: 'color-migration',
          severity: 'high',
          line: lineNumber,
          message: `使用了旧的颜色类 "${oldClass}"，应该替换为 "${newClass}"`,
          suggestion: `将 "${oldClass}" 替换为 "${newClass}"`
        })
      }
    })
  }

  /**
   * 检查深色模式类
   */
  checkDarkModeClasses(filePath, content, lines, issues) {
    DARK_MODE_CLASSES.forEach(darkClass => {
      if (content.includes(darkClass)) {
        const lineNumber = content.split(darkClass)[0].split('\n').length
        issues.push({
          type: 'dark-mode',
          severity: 'medium',
          line: lineNumber,
          message: `使用了不必要的深色模式类 "${darkClass}"，shadcn会自动处理主题切换`,
          suggestion: `移除 "${darkClass}"，使用shadcn颜色变量`
        })
      }
    })
  }

  /**
   * 检查性能优化
   */
  checkPerformanceOptimization(filePath, content, lines, issues) {
    // 检查是否需要useMemo
    PERFORMANCE_RULES.needsUseMemo.forEach(pattern => {
      if (pattern.test(content) && !content.includes('useMemo')) {
        issues.push({
          type: 'performance',
          severity: 'medium',
          line: 1,
          message: '检测到可能需要性能优化的计算，建议使用useMemo',
          suggestion: '考虑使用useMemo缓存复杂计算结果'
        })
      }
    })

    // 检查是否需要useCallback
    PERFORMANCE_RULES.needsUseCallback.forEach(pattern => {
      if (pattern.test(content) && !content.includes('useCallback')) {
        issues.push({
          type: 'performance',
          severity: 'low',
          line: 1,
          message: '检测到事件处理函数，建议使用useCallback优化',
          suggestion: '考虑使用useCallback缓存事件处理函数'
        })
      }
    })
  }

  /**
   * 检查测试覆盖
   */
  checkTestCoverage(filePath, issues) {
    if (filePath.includes('/components/') && !filePath.includes('.test.')) {
      const testFilePath = filePath.replace('.tsx', '.shadcn.test.tsx').replace('.ts', '.shadcn.test.ts')
      if (!fs.existsSync(testFilePath)) {
        issues.push({
          type: 'test-coverage',
          severity: 'medium',
          line: 1,
          message: '缺少对应的shadcn测试文件',
          suggestion: `创建测试文件: ${path.basename(testFilePath)}`
        })
      }
    }
  }

  /**
   * 检查项目中的所有相关文件
   */
  checkProject() {
    console.log('🔍 开始检查shadcn迁移质量...\n')

    // 查找所有需要检查的文件
    const files = this.findTSXFiles('src')

    this.stats.totalFiles = files.length

    files.forEach(file => {
      this.checkFile(file)
    })

    this.generateReport()
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    console.log('📊 shadcn迁移质量检查报告')
    console.log('=' .repeat(50))
    
    // 统计信息
    console.log(`📁 检查文件数: ${this.stats.filesChecked}`)
    console.log(`✅ 已迁移文件: ${this.stats.migratedFiles}`)
    console.log(`⚠️  发现问题数: ${this.stats.issuesFound}`)
    console.log(`📈 迁移进度: ${Math.round((this.stats.migratedFiles / this.stats.filesChecked) * 100)}%`)
    console.log('')

    // 问题详情
    if (this.issues.length > 0) {
      console.log('🚨 发现的问题:')
      console.log('-'.repeat(30))
      
      this.issues.forEach(fileIssue => {
        console.log(`\n📄 ${fileIssue.file}`)
        
        fileIssue.issues.forEach(issue => {
          const severityIcon = {
            high: '🔴',
            medium: '🟡',
            low: '🟢'
          }[issue.severity] || '⚪'
          
          console.log(`  ${severityIcon} 第${issue.line}行: ${issue.message}`)
          console.log(`     💡 建议: ${issue.suggestion}`)
        })
      })
    } else {
      console.log('🎉 恭喜！所有文件都已正确迁移到shadcn!')
    }

    // 生成JSON报告
    this.generateJSONReport()
    
    console.log('\n📋 详细报告已保存到: docs/shadcn-migration-report.json')
  }

  /**
   * 生成JSON格式的详细报告
   */
  generateJSONReport() {
    const report = {
      timestamp: new Date().toISOString(),
      stats: this.stats,
      issues: this.issues,
      summary: {
        migrationProgress: Math.round((this.stats.migratedFiles / this.stats.filesChecked) * 100),
        qualityScore: Math.max(0, 100 - (this.stats.issuesFound * 5)),
        recommendations: this.generateRecommendations()
      }
    }

    fs.writeFileSync(
      'docs/shadcn-migration-report.json',
      JSON.stringify(report, null, 2)
    )
  }

  /**
   * 生成改进建议
   */
  generateRecommendations() {
    const recommendations = []
    
    const issueTypes = {}
    this.issues.forEach(fileIssue => {
      fileIssue.issues.forEach(issue => {
        issueTypes[issue.type] = (issueTypes[issue.type] || 0) + 1
      })
    })

    if (issueTypes['color-migration'] > 0) {
      recommendations.push({
        priority: 'high',
        action: '完成颜色系统迁移',
        description: `还有 ${issueTypes['color-migration']} 个颜色类需要迁移到shadcn系统`
      })
    }

    if (issueTypes['performance'] > 0) {
      recommendations.push({
        priority: 'medium',
        action: '添加性能优化',
        description: `有 ${issueTypes['performance']} 个组件可以通过useMemo/useCallback优化性能`
      })
    }

    if (issueTypes['test-coverage'] > 0) {
      recommendations.push({
        priority: 'medium',
        action: '完善测试覆盖',
        description: `有 ${issueTypes['test-coverage']} 个组件缺少shadcn测试文件`
      })
    }

    return recommendations
  }
}

// 运行检查器
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new ShadcnMigrationChecker()
  checker.checkProject()
}

export default ShadcnMigrationChecker