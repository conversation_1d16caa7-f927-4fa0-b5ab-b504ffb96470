// 测试标签创建功能的调试脚本

console.log('开始测试标签创建功能...')

// 模拟点击新建标签按钮
function testTagCreation() {
  console.log('1. 模拟点击新建标签按钮')
  
  // 查找新建标签按钮
  const createButton = document.querySelector('button:contains("新建标签")')
  if (!createButton) {
    console.error('❌ 未找到新建标签按钮')
    return
  }
  
  console.log('✅ 找到新建标签按钮')
  
  // 模拟点击
  createButton.click()
  
  // 等待模态窗口出现
  setTimeout(() => {
    console.log('2. 检查模态窗口是否出现')
    
    const modal = document.querySelector('[data-testid="tag-modal"], .tag-modal, [role="dialog"]')
    if (!modal) {
      console.error('❌ 模态窗口未出现')
      return
    }
    
    console.log('✅ 模态窗口已出现')
    
    // 查找表单元素
    setTimeout(() => {
      console.log('3. 检查表单元素')
      
      const nameInput = document.querySelector('input[id="tag-name"], input[placeholder*="标签名称"]')
      const submitButton = document.querySelector('button[type="submit"], button:contains("创建标签")')
      
      if (!nameInput) {
        console.error('❌ 未找到标签名称输入框')
        return
      }
      
      if (!submitButton) {
        console.error('❌ 未找到创建标签按钮')
        return
      }
      
      console.log('✅ 找到表单元素')
      console.log('输入框状态:', {
        disabled: nameInput.disabled,
        value: nameInput.value,
        placeholder: nameInput.placeholder
      })
      console.log('提交按钮状态:', {
        disabled: submitButton.disabled,
        textContent: submitButton.textContent,
        type: submitButton.type
      })
      
      // 测试输入
      console.log('4. 测试输入标签名称')
      nameInput.value = '测试标签'
      nameInput.dispatchEvent(new Event('input', { bubbles: true }))
      nameInput.dispatchEvent(new Event('change', { bubbles: true }))
      
      setTimeout(() => {
        console.log('5. 检查按钮状态更新')
        console.log('提交按钮状态:', {
          disabled: submitButton.disabled,
          textContent: submitButton.textContent
        })
        
        if (submitButton.disabled) {
          console.error('❌ 创建标签按钮仍然被禁用')
          
          // 检查可能的验证错误
          const errorElements = document.querySelectorAll('.text-red-600, .text-red-500, .error')
          if (errorElements.length > 0) {
            console.log('发现错误信息:')
            errorElements.forEach((el, index) => {
              console.log(`错误 ${index + 1}:`, el.textContent)
            })
          }
        } else {
          console.log('✅ 创建标签按钮已启用')
          
          // 测试点击提交
          console.log('6. 测试点击创建标签按钮')
          submitButton.click()
          
          setTimeout(() => {
            console.log('7. 检查提交结果')
            const loadingIndicator = document.querySelector('.animate-spin, [data-testid="loading"]')
            if (loadingIndicator) {
              console.log('✅ 显示加载状态')
            }
          }, 100)
        }
      }, 500)
    }, 100)
  }, 100)
}

// 等待页面加载完成后执行测试
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', testTagCreation)
} else {
  testTagCreation()
}

// 导出测试函数供手动调用
window.testTagCreation = testTagCreation

console.log('测试脚本已加载，可以手动调用 testTagCreation() 函数')