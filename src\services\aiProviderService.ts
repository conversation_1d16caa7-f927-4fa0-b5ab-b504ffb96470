// AI提供商服务类 - 处理不同AI提供商的API交互

import { AIProviderConfig, AIConnectionResult, AIModel } from "../types/ai";
import {
  localAIServiceAdapter,
  LocalServiceConfig,
} from "./localAIServiceAdapter";
import { ChromeStorageService } from '../utils/chromeStorage';

/**
 * Ollama模型接口
 */
interface OllamaModel {
  name: string;
  size: number;
  digest: string;
  details?: {
    format: string;
    family: string;
    families?: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

/**
 * LM Studio模型接口
 */
interface LMStudioModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

/**
 * OpenRouter模型接口
 */
interface OpenRouterModel {
  id: string;
  name: string;
  description?: string;
  context_length: number;
  architecture: {
    modality: string;
    tokenizer: string;
    instruct_type?: string;
  };
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
    request?: string;
  };
  top_provider: {
    context_length: number;
    max_completion_tokens?: number;
  };
}

/**
 * Xinference模型接口
 */
interface XinferenceModel {
  model_name: string;
  model_uid: string;
  model_type: string;
  model_size_in_billions?: number;
  quantization?: string;
  model_description?: string;
  context_length?: number;
  model_lang?: string[];
  model_ability?: string[];
  model_specs?: {
    model_format?: string;
    model_size_in_billions?: number;
    quantizations?: string[];
    model_id?: string;
    model_revision?: string;
  };
}

/**
 * Together AI模型接口
 */
interface TogetherModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  display_name?: string;
  description?: string;
  context_length?: number;
  pricing?: {
    input: number;
    output: number;
  };
  type?: string;
  license?: string;
}

/**
 * 自定义API配置接口
 */
interface CustomAPIConfig {
  baseUrl: string;
  apiKey?: string;
  headers?: Record<string, string>;
  timeout?: number;
}

/**
 * AI提供商服务类
 * 专门处理不同AI提供商的API交互
 */
export class AIProviderService {
  /**
   * 测试提供商连接
   * @param config 提供商配置
   * @returns Promise<AIConnectionResult>
   */
  async testConnection(config: AIProviderConfig): Promise<AIConnectionResult> {
    const startTime = Date.now();

    try {
      let result: { success: boolean; modelCount?: number; error?: string };

      switch (config.type) {
        case "ollama":
          result = await this.testOllamaConnection(config.baseUrl);
          break;
        case "lm-studio":
          result = await this.testLMStudioConnection(config.baseUrl);
          break;
        case "openrouter":
          result = await this.testOpenRouterConnection(config.apiKey || "");
          break;
        case "openai":
          result = await this.testOpenAIConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "azure-openai":
          result = await this.testAzureOpenAIConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "claude":
          result = await this.testClaudeConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "gemini":
          result = await this.testGeminiConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "deepseek":
          result = await this.testDeepSeekConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "zhipu":
          result = await this.testZhipuConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "qwen":
          result = await this.testQwenConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "together":
          result = await this.testTogetherConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "grok":
          result = await this.testGrokConnection(
            config.baseUrl,
            config.apiKey || ""
          );
          break;
        case "xinference":
          result = await this.testXinferenceConnection(config.baseUrl);
          break;
        case "custom":
          result = await this.testCustomAPI({
            baseUrl: config.baseUrl,
            apiKey: config.apiKey,
            headers: config.headers,
            timeout: config.timeout,
          });
          break;
        default:
          throw new Error(`不支持的提供商类型: ${config.type}`);
      }

      const responseTime = Date.now() - startTime;

      return {
        providerId: config.id,
        success: result.success,
        responseTime,
        modelCount: result.modelCount,
        error: result.error,
        testedAt: new Date(),
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      return {
        providerId: config.id,
        success: false,
        responseTime,
        error: error.message,
        testedAt: new Date(),
      };
    }
  }

  /**
   * 获取提供商模型列表
   * @param config 提供商配置
   * @returns Promise<AIModel[]>
   */
  async getModels(config: AIProviderConfig): Promise<AIModel[]> {
    try {
      switch (config.type) {
        case "ollama":
          return await this.getOllamaModels(config.baseUrl);
        case "lm-studio":
          return await this.getLMStudioModels(config.baseUrl);
        case "openrouter":
          return await this.getOpenRouterModels(config.apiKey || "");
        case "openai":
          return await this.getOpenAIModels(
            config.baseUrl,
            config.apiKey || ""
          );
        case "azure-openai":
          return await this.getAzureOpenAIModels(
            config.baseUrl,
            config.apiKey || ""
          );
        case "claude":
          return await this.getClaudeModels();
        case "gemini":
          return await this.getGeminiModels(
            config.baseUrl,
            config.apiKey || ""
          );
        case "deepseek":
          return await this.getDeepSeekModels();
        case "zhipu":
          return await this.getZhipuModels();
        case "qwen":
          return await this.getQwenModels();
        case "together":
          return await this.getTogetherModels(
            config.baseUrl,
            config.apiKey || ""
          );
        case "grok":
          return await this.getGrokModels();
        case "xinference":
          return await this.getXinferenceModels(config.baseUrl);
        case "custom":
          return await this.getCustomModels(config);
        default:
          throw new Error(`不支持的提供商类型: ${config.type}`);
      }
    } catch (error) {
      console.error(`获取${config.type}模型列表失败:`, error);
      return [];
    }
  }

  // ==================== Ollama相关方法 ====================

  /**
   * 验证Ollama服务状态
   * @param baseUrl Ollama服务地址
   * @returns Promise<{isRunning: boolean, version?: string, error?: string}>
   */
  async validateOllamaService(
    baseUrl: string
  ): Promise<{ isRunning: boolean; version?: string; error?: string }> {
    try {
      // 测试版本端点
      const versionResponse = await fetch(`${baseUrl}/api/version`, {
        method: "GET",
        signal: AbortSignal.timeout(5000),
      });

      if (versionResponse.ok) {
        const versionData = await versionResponse.json();
        return {
          isRunning: true,
          version: versionData.version || "未知版本",
        };
      }

      // 如果版本端点失败，测试标签端点
      const tagsResponse = await fetch(`${baseUrl}/api/tags`, {
        method: "GET",
        signal: AbortSignal.timeout(5000),
      });

      if (tagsResponse.ok) {
        return {
          isRunning: true,
          version: "未知版本",
        };
      }

      return {
        isRunning: false,
        error: `HTTP ${tagsResponse.status}: ${tagsResponse.statusText}`,
      };
    } catch (error) {
      return {
        isRunning: false,
        error: error.message,
      };
    }
  }

  /**
   * 测试Ollama连接
   * @param baseUrl Ollama服务地址
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testOllamaConnection(
    baseUrl: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 首先测试基础连接
      const healthResponse = await fetch(`${baseUrl}/api/version`, {
        method: "GET",
        signal: AbortSignal.timeout(5000),
      });

      if (!healthResponse.ok) {
        // 如果version端点不可用，尝试tags端点
        const tagsResponse = await fetch(`${baseUrl}/api/tags`, {
          method: "GET",
          signal: AbortSignal.timeout(10000),
        });

        if (!tagsResponse.ok) {
          throw new Error(
            `HTTP ${tagsResponse.status}: ${tagsResponse.statusText}`
          );
        }

        const data = await tagsResponse.json();
        const modelCount = data.models?.length || 0;

        return {
          success: true,
          modelCount,
        };
      }

      // 获取版本信息和模型列表
      const versionData = await healthResponse.json();
      console.log("Ollama版本信息:", versionData);

      // 获取模型列表
      const tagsResponse = await fetch(`${baseUrl}/api/tags`, {
        method: "GET",
        signal: AbortSignal.timeout(10000),
      });

      if (!tagsResponse.ok) {
        // 连接成功但无法获取模型列表
        return {
          success: true,
          modelCount: 0,
          error: "无法获取模型列表，但服务可用",
        };
      }

      const tagsData = await tagsResponse.json();
      const modelCount = tagsData.models?.length || 0;

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查Ollama服务是否运行";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到Ollama服务，请检查地址和端口";
      } else if (error.message.includes("ECONNREFUSED")) {
        errorMessage = "Ollama服务未运行或端口不正确";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取Ollama模型列表
   * @param baseUrl Ollama服务地址
   * @returns Promise<AIModel[]>
   */
  async getOllamaModels(baseUrl: string): Promise<AIModel[]> {
    try {
      const response = await fetch(`${baseUrl}/api/tags`, {
        method: "GET",
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const models: OllamaModel[] = data.models || [];

      return models
        .map((model) => {
          // 解析模型名称和标签
          const [modelName, tag] = model.name.split(":");
          const displayName = this.formatOllamaModelName(modelName, tag);

          // 生成模型描述
          const description = this.generateOllamaModelDescription(model);

          // 确定模型类型和能力
          const capabilities = this.determineOllamaCapabilities(model);

          // 提取模型标签
          const modelTags = this.extractOllamaModelTags(model);

          return {
            id: model.name,
            name: model.name,
            displayName,
            description,
            size: this.formatBytes(model.size),
            parameters: model.details?.parameter_size,
            tags: modelTags,
            capabilities,
            providerId: "ollama",
            isRecommended: this.isOllamaModelRecommended(modelName),
            isPopular: this.isOllamaModelPopular(modelName),
          };
        })
        .sort((a, b) => {
          // 按推荐度和名称排序
          if (a.isRecommended && !b.isRecommended) return -1;
          if (!a.isRecommended && b.isRecommended) return 1;
          return a.name.localeCompare(b.name);
        });
    } catch (error) {
      console.error("获取Ollama模型列表失败:", error);

      // 提供更详细的错误信息
      if (error.name === "AbortError") {
        console.error("获取模型列表超时，Ollama服务可能响应缓慢");
      } else if (error.message.includes("HTTP")) {
        console.error("Ollama API返回错误:", error.message);
      } else {
        console.error("网络连接错误:", error.message);
      }

      return [];
    }
  }

  /**
   * 格式化Ollama模型名称
   * @param modelName 模型名称
   * @param tag 模型标签
   * @returns string
   */
  private formatOllamaModelName(modelName: string, tag?: string): string {
    const nameMap: Record<string, string> = {
      llama2: "Llama 2",
      llama3: "Llama 3",
      qwen: "Qwen",
      mistral: "Mistral",
      codellama: "Code Llama",
      "deepseek-coder": "DeepSeek Coder",
      phi: "Phi",
      gemma: "Gemma",
      vicuna: "Vicuna",
      orca: "Orca",
      wizardcoder: "WizardCoder",
      starcoder: "StarCoder",
    };

    const formattedName = nameMap[modelName.toLowerCase()] || modelName;
    return tag ? `${formattedName} (${tag})` : formattedName;
  }

  /**
   * 生成Ollama模型描述
   * @param model Ollama模型信息
   * @returns string
   */
  private generateOllamaModelDescription(model: OllamaModel): string {
    const [modelName] = model.name.split(":");
    const size = model.details?.parameter_size || "未知大小";
    const format = model.details?.format || "GGUF";

    const descriptions: Record<string, string> = {
      llama2: "Meta开发的大型语言模型，适合通用对话和文本生成",
      llama3: "Meta最新的大型语言模型，性能更强，支持多语言",
      qwen: "阿里巴巴开发的通义千问模型，中文能力出色",
      mistral: "Mistral AI开发的高效语言模型，平衡性能与资源消耗",
      codellama: "基于Llama 2的代码生成模型，专门优化编程任务",
      "deepseek-coder": "DeepSeek开发的代码生成模型，支持多种编程语言",
      phi: "微软开发的小型高效语言模型",
      gemma: "Google开发的开源语言模型",
      vicuna: "基于Llama微调的对话模型",
      orca: "微软开发的推理能力强化模型",
    };

    const baseDescription =
      descriptions[modelName.toLowerCase()] || "Ollama本地部署的语言模型";
    return `${baseDescription} (${size}参数, ${format}格式)`;
  }

  /**
   * 确定Ollama模型能力
   * @param model Ollama模型信息
   * @returns string[]
   */
  private determineOllamaCapabilities(model: OllamaModel): string[] {
    const capabilities = ["chat", "completion"];

    // 根据模型类型添加特定能力
    if (model.name.includes("code") || model.name.includes("coder")) {
      capabilities.push("coding");
    }

    if (model.name.includes("instruct") || model.name.includes("chat")) {
      capabilities.push("instruction-following");
    }

    if (model.details?.families?.includes("embedding")) {
      capabilities.push("embedding");
    }

    return capabilities;
  }

  /**
   * 提取Ollama模型标签
   * @param model Ollama模型信息
   * @returns string[]
   */
  private extractOllamaModelTags(model: OllamaModel): string[] {
    const [modelName, tag] = model.name.split(":");
    const tags = [];

    // 添加模型系列标签
    if (model.details?.family) {
      tags.push(model.details.family);
    }

    // 添加量化级别标签
    if (model.details?.quantization_level) {
      tags.push(model.details.quantization_level);
    }

    // 添加参数大小标签
    if (model.details?.parameter_size) {
      tags.push(model.details.parameter_size);
    }

    // 添加模型类型标签
    if (model.name.includes("code")) {
      tags.push("代码生成");
    }

    if (model.name.includes("chat") || model.name.includes("instruct")) {
      tags.push("对话");
    }

    // 添加标签版本
    if (tag) {
      tags.push(tag);
    }

    return tags.filter(Boolean);
  }

  /**
   * 判断是否为推荐的Ollama模型
   * @param modelName 模型名称
   * @returns boolean
   */
  private isOllamaModelRecommended(modelName: string): boolean {
    const recommendedModels = [
      "llama2",
      "llama3",
      "qwen",
      "mistral",
      "codellama",
      "deepseek-coder",
    ];
    return recommendedModels.some((recommended) =>
      modelName.toLowerCase().includes(recommended)
    );
  }

  /**
   * 判断是否为热门的Ollama模型
   * @param modelName 模型名称
   * @returns boolean
   */
  private isOllamaModelPopular(modelName: string): boolean {
    const popularModels = ["llama2", "mistral", "qwen", "phi", "gemma"];
    return popularModels.some((popular) =>
      modelName.toLowerCase().includes(popular)
    );
  }

  // ==================== LM Studio相关方法 ====================

  /**
   * 验证LM Studio服务状态
   * @param baseUrl LM Studio服务地址
   * @returns Promise<{isRunning: boolean, version?: string, error?: string}>
   */
  async validateLMStudioService(
    baseUrl: string
  ): Promise<{ isRunning: boolean; version?: string; error?: string }> {
    try {
      // 首先尝试获取模型列表来验证服务
      const modelsResponse = await fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(5000),
      });

      if (modelsResponse.ok) {
        // 尝试获取版本信息（如果有的话）
        try {
          const versionResponse = await fetch(`${baseUrl}/v1/models`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            signal: AbortSignal.timeout(3000),
          });

          return {
            isRunning: true,
            version: versionResponse.ok ? "LM Studio Local Server" : "未知版本",
          };
        } catch {
          return {
            isRunning: true,
            version: "LM Studio Local Server",
          };
        }
      }

      return {
        isRunning: false,
        error: `HTTP ${modelsResponse.status}: ${modelsResponse.statusText}`,
      };
    } catch (error) {
      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查LM Studio是否运行并启用了本地服务器";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到LM Studio服务，请检查地址和端口";
      } else if (error.message.includes("ECONNREFUSED")) {
        errorMessage = "LM Studio本地服务器未启动或端口不正确";
      }

      return {
        isRunning: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 测试LM Studio连接
   * @param baseUrl LM Studio服务地址
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testLMStudioConnection(
    baseUrl: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 首先验证服务状态
      const serviceStatus = await this.validateLMStudioService(baseUrl);

      if (!serviceStatus.isRunning) {
        return {
          success: false,
          error: serviceStatus.error || "LM Studio服务未运行",
        };
      }

      console.log("LM Studio服务状态:", serviceStatus);

      // 获取模型列表
      const response = await fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(10000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查LM Studio是否运行并启用了本地服务器";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到LM Studio服务，请检查地址和端口";
      } else if (error.message.includes("ECONNREFUSED")) {
        errorMessage = "LM Studio本地服务器未启动或端口不正确";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取LM Studio模型列表
   * @param baseUrl LM Studio服务地址
   * @returns Promise<AIModel[]>
   */
  async getLMStudioModels(baseUrl: string): Promise<AIModel[]> {
    try {
      const response = await fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const models: LMStudioModel[] = data.data || [];

      return models
        .map((model) => {
          // 解析模型信息
          const displayName = this.formatLMStudioModelName(model.id);
          const description = this.generateLMStudioModelDescription(model);
          const capabilities = this.determineLMStudioCapabilities(model);
          const modelTags = this.extractLMStudioModelTags(model);

          return {
            id: model.id,
            name: model.id,
            displayName,
            description,
            capabilities,
            tags: modelTags,
            providerId: "lm-studio",
            isRecommended: this.isLMStudioModelRecommended(model.id),
            isPopular: this.isLMStudioModelPopular(model.id),
          };
        })
        .sort((a, b) => {
          // 按推荐度和名称排序
          if (a.isRecommended && !b.isRecommended) return -1;
          if (!a.isRecommended && b.isRecommended) return 1;
          return a.name.localeCompare(b.name);
        });
    } catch (error) {
      console.error("获取LM Studio模型列表失败:", error);

      // 提供更详细的错误信息
      if (error.name === "AbortError") {
        console.error("获取模型列表超时，LM Studio服务可能响应缓慢");
      } else if (error.message.includes("HTTP")) {
        console.error("LM Studio API返回错误:", error.message);
      } else {
        console.error("网络连接错误:", error.message);
      }

      return [];
    }
  }

  /**
   * 格式化LM Studio模型名称
   * @param modelId 模型ID
   * @returns string
   */
  private formatLMStudioModelName(modelId: string): string {
    // LM Studio模型ID通常是文件路径格式，需要提取有用信息
    const parts = modelId.split(/[/\\]/);
    const fileName = parts[parts.length - 1] || modelId;

    // 移除文件扩展名
    const nameWithoutExt = fileName.replace(/\.(gguf|bin|safetensors)$/i, "");

    // 格式化常见的模型名称
    const nameMap: Record<string, string> = {
      "llama-2": "Llama 2",
      llama2: "Llama 2",
      "llama-3": "Llama 3",
      llama3: "Llama 3",
      qwen: "Qwen",
      mistral: "Mistral",
      codellama: "Code Llama",
      deepseek: "DeepSeek",
      phi: "Phi",
      gemma: "Gemma",
      vicuna: "Vicuna",
      orca: "Orca",
    };

    // 尝试匹配已知模型名称
    for (const [key, value] of Object.entries(nameMap)) {
      if (nameWithoutExt.toLowerCase().includes(key)) {
        return `${value} (${nameWithoutExt})`;
      }
    }

    return nameWithoutExt;
  }

  /**
   * 生成LM Studio模型描述
   * @param model LM Studio模型信息
   * @returns string
   */
  private generateLMStudioModelDescription(model: LMStudioModel): string {
    const modelName = model.id.toLowerCase();

    const descriptions: Record<string, string> = {
      codellama: "基于Llama的代码生成模型，专门优化编程任务",
      llama: "Meta开发的大型语言模型，适合通用对话和文本生成",
      qwen: "阿里巴巴开发的通义千问模型，中文能力出色",
      mistral: "Mistral AI开发的高效语言模型",
      deepseek: "DeepSeek开发的高性能语言模型",
      phi: "微软开发的小型高效语言模型",
      gemma: "Google开发的开源语言模型",
      vicuna: "基于Llama微调的对话模型",
      orca: "微软开发的推理能力强化模型",
    };

    // 按优先级匹配，codellama优先于llama
    for (const [key, desc] of Object.entries(descriptions)) {
      if (modelName.includes(key)) {
        return `${desc} (LM Studio本地部署)`;
      }
    }

    return "LM Studio本地部署的语言模型";
  }

  /**
   * 确定LM Studio模型能力
   * @param model LM Studio模型信息
   * @returns string[]
   */
  private determineLMStudioCapabilities(model: LMStudioModel): string[] {
    const capabilities = ["chat", "completion"];
    const modelName = model.id.toLowerCase();

    // 根据模型类型添加特定能力
    if (modelName.includes("code") || modelName.includes("coder")) {
      capabilities.push("coding");
    }

    if (modelName.includes("instruct") || modelName.includes("chat")) {
      capabilities.push("instruction-following");
    }

    if (modelName.includes("embed")) {
      capabilities.push("embedding");
    }

    return capabilities;
  }

  /**
   * 提取LM Studio模型标签
   * @param model LM Studio模型信息
   * @returns string[]
   */
  private extractLMStudioModelTags(model: LMStudioModel): string[] {
    const tags = ["LM Studio"];
    const modelName = model.id.toLowerCase();

    // 添加模型类型标签
    if (modelName.includes("code")) {
      tags.push("代码生成");
    }

    if (modelName.includes("chat") || modelName.includes("instruct")) {
      tags.push("对话");
    }

    if (modelName.includes("embed")) {
      tags.push("嵌入");
    }

    // 添加量化信息
    if (modelName.includes("q4")) {
      tags.push("Q4量化");
    } else if (modelName.includes("q8")) {
      tags.push("Q8量化");
    } else if (modelName.includes("f16")) {
      tags.push("FP16");
    }

    // 添加参数大小信息
    const sizeMatch = modelName.match(/(\d+)b/i);
    if (sizeMatch) {
      tags.push(`${sizeMatch[1]}B参数`);
    }

    return tags;
  }

  /**
   * 判断是否为推荐的LM Studio模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isLMStudioModelRecommended(modelId: string): boolean {
    const recommendedModels = [
      "llama",
      "qwen",
      "mistral",
      "codellama",
      "deepseek",
      "phi",
    ];
    const modelName = modelId.toLowerCase();
    return recommendedModels.some((recommended) =>
      modelName.includes(recommended)
    );
  }

  /**
   * 判断是否为热门的LM Studio模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isLMStudioModelPopular(modelId: string): boolean {
    const popularModels = [
      "llama",
      "mistral",
      "qwen",
      "phi",
      "chat",
      "instruct",
    ];
    const modelName = modelId.toLowerCase();
    return popularModels.some((popular) => modelName.includes(popular));
  }

  // ==================== Xinference相关方法 ====================

  /**
   * 验证Xinference服务状态
   * @param baseUrl Xinference服务地址
   * @returns Promise<{isRunning: boolean, version?: string, error?: string}>
   */
  async validateXinferenceService(
    baseUrl: string
  ): Promise<{ isRunning: boolean; version?: string; error?: string }> {
    try {
      // 尝试获取集群状态
      const statusResponse = await fetch(`${baseUrl}/v1/cluster/status`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(5000),
      });

      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        return {
          isRunning: true,
          version: `Xinference ${statusData.version || "Unknown"}`,
        };
      }

      // 如果集群状态端点失败，尝试模型列表端点
      const modelsResponse = await fetch(`${baseUrl}/v1/models`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(5000),
      });

      if (modelsResponse.ok) {
        return {
          isRunning: true,
          version: "Xinference Server",
        };
      }

      return {
        isRunning: false,
        error: `HTTP ${modelsResponse.status}: ${modelsResponse.statusText}`,
      };
    } catch (error) {
      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查Xinference服务是否运行";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到Xinference服务，请检查地址和端口";
      } else if (error.message.includes("ECONNREFUSED")) {
        errorMessage = "Xinference服务未运行或端口不正确";
      }

      return {
        isRunning: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 测试Xinference连接
   * @param baseUrl Xinference服务地址
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testXinferenceConnection(
    baseUrl: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 首先验证服务状态
      const serviceStatus = await this.validateXinferenceService(baseUrl);

      if (!serviceStatus.isRunning) {
        return {
          success: false,
          error: serviceStatus.error || "Xinference服务未运行",
        };
      }

      console.log("Xinference服务状态:", serviceStatus);

      // 获取已部署的模型列表
      const response = await fetch(`${baseUrl}/v1/models`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(10000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查Xinference服务是否运行";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到Xinference服务，请检查地址和端口";
      } else if (error.message.includes("ECONNREFUSED")) {
        errorMessage = "Xinference服务未运行或端口不正确";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取Xinference模型列表
   * @param baseUrl Xinference服务地址
   * @returns Promise<AIModel[]>
   */
  async getXinferenceModels(baseUrl: string): Promise<AIModel[]> {
    try {
      const response = await fetch(`${baseUrl}/v1/models`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const models: XinferenceModel[] = data.data || [];

      return models
        .map((model) => {
          // 解析模型信息
          const displayName = this.formatXinferenceModelName(model);
          const description = this.generateXinferenceModelDescription(model);
          const capabilities = this.determineXinferenceCapabilities(model);
          const modelTags = this.extractXinferenceModelTags(model);

          return {
            id: model.model_uid,
            name: model.model_name,
            displayName,
            description,
            size: model.model_size_in_billions
              ? `${model.model_size_in_billions}B`
              : undefined,
            parameters: model.model_size_in_billions
              ? `${model.model_size_in_billions}B`
              : undefined,
            capabilities,
            tags: modelTags,
            providerId: "xinference",
            isRecommended: this.isXinferenceModelRecommended(model.model_name),
            isPopular: this.isXinferenceModelPopular(model.model_name),
          };
        })
        .sort((a, b) => {
          // 按推荐度和名称排序
          if (a.isRecommended && !b.isRecommended) return -1;
          if (!a.isRecommended && b.isRecommended) return 1;
          return a.name.localeCompare(b.name);
        });
    } catch (error) {
      console.error("获取Xinference模型列表失败:", error);

      // 提供更详细的错误信息
      if (error.name === "AbortError") {
        console.error("获取模型列表超时，Xinference服务可能响应缓慢");
      } else if (error.message.includes("HTTP")) {
        console.error("Xinference API返回错误:", error.message);
      } else {
        console.error("网络连接错误:", error.message);
      }

      return [];
    }
  }

  /**
   * 格式化Xinference模型名称
   * @param model Xinference模型信息
   * @returns string
   */
  private formatXinferenceModelName(model: XinferenceModel): string {
    const modelName = model.model_name;
    const modelType = model.model_type;
    const size = model.model_size_in_billions
      ? `${model.model_size_in_billions}B`
      : "";
    const quantization = model.quantization ? ` (${model.quantization})` : "";

    // 格式化常见的模型名称
    const nameMap: Record<string, string> = {
      chatglm3: "ChatGLM3",
      chatglm2: "ChatGLM2",
      chatglm: "ChatGLM",
      "llama-2": "Llama 2",
      llama2: "Llama 2",
      "llama-3": "Llama 3",
      llama3: "Llama 3",
      codellama: "Code Llama",
      qwen: "Qwen",
      baichuan: "Baichuan",
      internlm: "InternLM",
      vicuna: "Vicuna",
      wizardlm: "WizardLM",
    };

    // 尝试匹配已知模型名称（按优先级匹配）
    const lowerModelName = modelName.toLowerCase();
    for (const [key, value] of Object.entries(nameMap)) {
      if (lowerModelName.includes(key)) {
        return `${value}${size ? ` ${size}` : ""}${quantization}`;
      }
    }

    // 如果没有匹配到已知模型，返回原始名称
    return `${modelName}${size ? ` ${size}` : ""}${quantization}`;
  }

  /**
   * 生成Xinference模型描述
   * @param model Xinference模型信息
   * @returns string
   */
  private generateXinferenceModelDescription(model: XinferenceModel): string {
    const modelName = model.model_name.toLowerCase();
    const modelType = model.model_type;
    const description = model.model_description || "";

    const descriptions: Record<string, string> = {
      llama: "Meta开发的大型语言模型，适合通用对话和文本生成",
      qwen: "阿里巴巴开发的通义千问模型，中文能力出色",
      chatglm: "清华大学开发的对话语言模型，中英文双语能力强",
      baichuan: "百川智能开发的中英文语言模型",
      internlm: "上海AI实验室开发的多语言大模型",
      vicuna: "基于Llama微调的对话模型",
      wizardlm: "微软开发的指令跟随语言模型",
      codellama: "基于Llama的代码生成模型，专门优化编程任务",
    };

    for (const [key, desc] of Object.entries(descriptions)) {
      if (modelName.includes(key)) {
        return `${desc} (Xinference分布式部署)`;
      }
    }

    if (description) {
      return `${description} (Xinference分布式部署)`;
    }

    return `Xinference分布式部署的${modelType}模型`;
  }

  /**
   * 确定Xinference模型能力
   * @param model Xinference模型信息
   * @returns string[]
   */
  private determineXinferenceCapabilities(model: XinferenceModel): string[] {
    const capabilities = [];
    const modelType = model.model_type.toLowerCase();
    const modelName = model.model_name.toLowerCase();
    const abilities = model.model_ability || [];

    // 根据模型类型确定基础能力
    if (modelType.includes("llm") || modelType.includes("language")) {
      capabilities.push("chat", "completion");
    }

    if (modelType.includes("embedding")) {
      capabilities.push("embedding");
    }

    if (modelType.includes("rerank")) {
      capabilities.push("reranking");
    }

    // 根据模型名称添加特定能力
    if (modelName.includes("code") || modelName.includes("coder")) {
      capabilities.push("coding");
    }

    if (modelName.includes("instruct") || modelName.includes("chat")) {
      capabilities.push("instruction-following");
    }

    // 根据模型能力添加
    abilities.forEach((ability) => {
      if (ability.includes("chat")) capabilities.push("chat");
      if (ability.includes("generate")) capabilities.push("completion");
      if (ability.includes("embed")) capabilities.push("embedding");
    });

    return [...new Set(capabilities)]; // 去重
  }

  /**
   * 提取Xinference模型标签
   * @param model Xinference模型信息
   * @returns string[]
   */
  private extractXinferenceModelTags(model: XinferenceModel): string[] {
    const tags = ["Xinference"];
    const modelType = model.model_type;
    const modelName = model.model_name.toLowerCase();
    const languages = model.model_lang || [];

    // 添加模型类型标签
    if (modelType) {
      tags.push(modelType.toUpperCase());
    }

    // 添加语言标签
    languages.forEach((lang) => {
      if (lang === "en") tags.push("英文");
      if (lang === "zh") tags.push("中文");
      if (lang === "code") tags.push("代码");
    });

    // 添加量化信息
    if (model.quantization) {
      tags.push(model.quantization);
    }

    // 添加模型大小
    if (model.model_size_in_billions) {
      tags.push(`${model.model_size_in_billions}B参数`);
    }

    // 添加特殊能力标签
    if (modelName.includes("code")) {
      tags.push("代码生成");
    }

    if (modelName.includes("chat") || modelName.includes("instruct")) {
      tags.push("对话");
    }

    if (modelName.includes("embed")) {
      tags.push("嵌入");
    }

    return tags;
  }

  /**
   * 判断是否为推荐的Xinference模型
   * @param modelName 模型名称
   * @returns boolean
   */
  private isXinferenceModelRecommended(modelName: string): boolean {
    const recommendedModels = [
      "llama",
      "qwen",
      "chatglm",
      "baichuan",
      "internlm",
      "codellama",
    ];
    const name = modelName.toLowerCase();
    return recommendedModels.some((recommended) => name.includes(recommended));
  }

  /**
   * 判断是否为热门的Xinference模型
   * @param modelName 模型名称
   * @returns boolean
   */
  private isXinferenceModelPopular(modelName: string): boolean {
    const popularModels = ["llama", "qwen", "chatglm", "chat", "instruct"];
    const name = modelName.toLowerCase();
    return popularModels.some((popular) => name.includes(popular));
  }

  // ==================== OpenRouter相关方法 ====================

  /**
   * 验证OpenRouter API密钥格式
   * @param apiKey API密钥
   * @returns {isValid: boolean, error?: string}
   */
  private validateOpenRouterApiKey(apiKey: string): {
    isValid: boolean;
    error?: string;
  } {
    if (!apiKey || !apiKey.trim()) {
      return {
        isValid: false,
        error: "API密钥不能为空",
      };
    }

    // OpenRouter API密钥通常以sk-or-开头
    if (!apiKey.startsWith("sk-or-") && !apiKey.startsWith("sk-")) {
      return {
        isValid: false,
        error: '无效的OpenRouter API密钥格式，应以"sk-or-"或"sk-"开头',
      };
    }

    // 检查密钥长度（OpenRouter密钥通常较长）
    if (apiKey.length < 20) {
      return {
        isValid: false,
        error: "API密钥长度不足，请检查密钥是否完整",
      };
    }

    return { isValid: true };
  }

  /**
   * 测试OpenRouter连接
   * @param apiKey API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testOpenRouterConnection(
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 首先验证API密钥格式
      const validation = this.validateOpenRouterApiKey(apiKey);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      console.log("开始测试OpenRouter连接...");

      // 测试连接并获取模型列表
      const response = await fetch("https://openrouter.ai/api/v1/models", {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "HTTP-Referer": "https://localhost:3000", // OpenRouter要求提供Referer
          "X-Title": "Bookmark Manager Extension", // 可选的应用标识
        },
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        // 详细的错误处理
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        if (response.status === 401) {
          errorMessage = "API密钥无效或已过期，请检查密钥是否正确";
        } else if (response.status === 403) {
          errorMessage = "API密钥权限不足，请检查密钥权限设置";
        } else if (response.status === 429) {
          errorMessage = "API请求频率超限，请稍后重试";
        } else if (response.status >= 500) {
          errorMessage = "OpenRouter服务暂时不可用，请稍后重试";
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      console.log(`OpenRouter连接成功，发现${modelCount}个模型`);

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      console.error("OpenRouter连接测试失败:", error);

      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接或稍后重试";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到OpenRouter服务，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取OpenRouter模型列表
   * @param apiKey API密钥
   * @returns Promise<AIModel[]>
   */
  async getOpenRouterModels(apiKey: string): Promise<AIModel[]> {
    try {
      // 验证API密钥
      const validation = this.validateOpenRouterApiKey(apiKey);
      if (!validation.isValid) {
        console.error("OpenRouter API密钥验证失败:", validation.error);
        return [];
      }

      console.log("开始获取OpenRouter模型列表...");

      const response = await fetch("https://openrouter.ai/api/v1/models", {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "HTTP-Referer": "https://localhost:3000",
          "X-Title": "Bookmark Manager Extension",
        },
        signal: AbortSignal.timeout(20000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const models: OpenRouterModel[] = data.data || [];

      console.log(`成功获取${models.length}个OpenRouter模型`);

      return models
        .map((model) => {
          // 解析模型信息
          const displayName = this.formatOpenRouterModelName(model);
          const description = this.generateOpenRouterModelDescription(model);
          const capabilities = this.determineOpenRouterCapabilities(model);
          const modelTags = this.extractOpenRouterModelTags(model);
          const pricing = this.formatOpenRouterPricing(model);

          return {
            id: model.id,
            name: model.id,
            displayName,
            description,
            size: this.extractModelSize(model.id),
            parameters: `${model.context_length} tokens`,
            tags: modelTags,
            capabilities,
            providerId: "openrouter",
            isRecommended: this.isOpenRouterModelRecommended(model),
            isPopular: this.isOpenRouterModelPopular(model),
            maxTokens: model.context_length,
            contextLength: model.context_length,
            supportedFormats: ["chat", "completion"],
            // 添加定价信息到描述中
            ...(pricing && {
              description: `${description}\n\n💰 定价: ${pricing}`,
            }),
          };
        })
        .sort((a, b) => {
          // 按推荐度、热门度和名称排序
          if (a.isRecommended && !b.isRecommended) return -1;
          if (!a.isRecommended && b.isRecommended) return 1;
          if (a.isPopular && !b.isPopular) return -1;
          if (!a.isPopular && b.isPopular) return 1;
          return a.displayName.localeCompare(b.displayName);
        });
    } catch (error) {
      console.error("获取OpenRouter模型列表失败:", error);

      // 提供更详细的错误信息
      if (error.name === "AbortError") {
        console.error("获取模型列表超时，OpenRouter服务可能响应缓慢");
      } else if (error.message.includes("HTTP 401")) {
        console.error("API密钥无效，请检查OpenRouter API密钥");
      } else if (error.message.includes("HTTP 403")) {
        console.error("API密钥权限不足，请检查密钥权限设置");
      } else if (error.message.includes("HTTP")) {
        console.error("OpenRouter API返回错误:", error.message);
      } else {
        console.error("网络连接错误:", error.message);
      }

      return [];
    }
  }

  /**
   * 格式化OpenRouter模型名称
   * @param model OpenRouter模型信息
   * @returns string
   */
  private formatOpenRouterModelName(model: OpenRouterModel): string {
    // 如果有显示名称，优先使用
    if (model.name && model.name !== model.id) {
      return model.name;
    }

    // 解析模型ID，提取有用信息
    const modelId = model.id;

    // 常见模型名称映射
    const nameMap: Record<string, string> = {
      "openai/gpt-4": "GPT-4",
      "openai/gpt-4-turbo": "GPT-4 Turbo",
      "openai/gpt-3.5-turbo": "GPT-3.5 Turbo",
      "anthropic/claude-3": "Claude 3",
      "anthropic/claude-2": "Claude 2",
      "meta-llama/llama-2": "Llama 2",
      "meta-llama/llama-3": "Llama 3",
      "google/gemini": "Gemini",
      "mistralai/mistral": "Mistral",
      "cohere/command": "Command",
    };

    // 尝试匹配已知模型
    for (const [key, value] of Object.entries(nameMap)) {
      if (modelId.includes(key.split("/")[1])) {
        return value;
      }
    }

    // 格式化模型ID为更友好的显示名称
    return (
      modelId
        .split("/")
        .pop()
        ?.replace(/-/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase()) || modelId
    );
  }

  /**
   * 生成OpenRouter模型描述
   * @param model OpenRouter模型信息
   * @returns string
   */
  private generateOpenRouterModelDescription(model: OpenRouterModel): string {
    let description = model.description || "";

    // 如果没有描述，根据模型ID生成
    if (!description) {
      const modelId = model.id.toLowerCase();

      if (modelId.includes("gpt-4")) {
        description = "OpenAI最先进的大型语言模型，具有出色的推理和创作能力";
      } else if (modelId.includes("gpt-3.5")) {
        description = "OpenAI高效的语言模型，平衡性能与成本";
      } else if (modelId.includes("claude")) {
        description = "Anthropic开发的安全、有用的AI助手";
      } else if (modelId.includes("llama")) {
        description = "Meta开发的开源大型语言模型";
      } else if (modelId.includes("gemini")) {
        description = "Google开发的多模态AI模型";
      } else if (modelId.includes("mistral")) {
        description = "Mistral AI开发的高效语言模型";
      } else {
        description = "通过OpenRouter提供的AI模型";
      }
    }

    // 添加上下文长度信息
    const contextInfo = `支持${model.context_length.toLocaleString()}个token的上下文`;

    // 添加模态信息
    const modalityInfo =
      model.architecture.modality === "text"
        ? "文本模型"
        : model.architecture.modality === "multimodal"
        ? "多模态模型"
        : model.architecture.modality;

    return `${description} (${modalityInfo}, ${contextInfo})`;
  }

  /**
   * 确定OpenRouter模型能力
   * @param model OpenRouter模型信息
   * @returns string[]
   */
  private determineOpenRouterCapabilities(model: OpenRouterModel): string[] {
    const capabilities = ["chat", "completion"];

    // 根据模型特性添加能力
    if (model.architecture.modality === "multimodal") {
      capabilities.push("vision", "image-analysis");
    }

    if (model.id.includes("code") || model.id.includes("coder")) {
      capabilities.push("coding");
    }

    if (model.architecture.instruct_type) {
      capabilities.push("instruction-following");
    }

    // 根据上下文长度判断长文本处理能力
    if (model.context_length >= 32000) {
      capabilities.push("long-context");
    }

    return capabilities;
  }

  /**
   * 提取OpenRouter模型标签
   * @param model OpenRouter模型信息
   * @returns string[]
   */
  private extractOpenRouterModelTags(model: OpenRouterModel): string[] {
    const tags = ["OpenRouter"];

    // 添加提供商标签
    const provider = model.id.split("/")[0];
    if (provider) {
      tags.push(provider);
    }

    // 添加模态标签
    if (model.architecture.modality) {
      tags.push(model.architecture.modality);
    }

    // 添加分词器标签
    if (model.architecture.tokenizer) {
      tags.push(model.architecture.tokenizer);
    }

    // 添加指令类型标签
    if (model.architecture.instruct_type) {
      tags.push(model.architecture.instruct_type);
    }

    // 根据上下文长度添加标签
    if (model.context_length >= 100000) {
      tags.push("超长上下文");
    } else if (model.context_length >= 32000) {
      tags.push("长上下文");
    }

    // 根据模型类型添加标签
    if (model.id.includes("turbo")) {
      tags.push("快速");
    }

    if (model.id.includes("instruct")) {
      tags.push("指令优化");
    }

    return tags.filter(Boolean);
  }

  /**
   * 格式化OpenRouter定价信息
   * @param model OpenRouter模型信息
   * @returns string | null
   */
  private formatOpenRouterPricing(model: OpenRouterModel): string | null {
    if (!model.pricing) return null;

    const promptPrice = parseFloat(model.pricing.prompt);
    const completionPrice = parseFloat(model.pricing.completion);

    if (isNaN(promptPrice) || isNaN(completionPrice)) return null;

    // 转换为更友好的格式（每1M token的价格）
    const promptPricePerM = (promptPrice * 1000000).toFixed(2);
    const completionPricePerM = (completionPrice * 1000000).toFixed(2);

    return `输入: $${promptPricePerM}/1M tokens, 输出: $${completionPricePerM}/1M tokens`;
  }

  /**
   * 从模型ID中提取模型大小
   * @param modelId 模型ID
   * @returns string | undefined
   */
  private extractModelSize(modelId: string): string | undefined {
    // 尝试从模型ID中提取参数大小
    const sizeMatch = modelId.match(/(\d+)b/i);
    if (sizeMatch) {
      return `${sizeMatch[1]}B`;
    }

    // 根据已知模型推断大小
    if (modelId.includes("gpt-4")) {
      return "1.76T"; // GPT-4的估计参数量
    } else if (modelId.includes("gpt-3.5")) {
      return "175B";
    } else if (modelId.includes("claude-3")) {
      return "未知";
    }

    return undefined;
  }

  /**
   * 判断是否为推荐的OpenRouter模型
   * @param model OpenRouter模型信息
   * @returns boolean
   */
  private isOpenRouterModelRecommended(model: OpenRouterModel): boolean {
    const recommendedModels = [
      "gpt-4",
      "claude-3",
      "llama-3",
      "gemini-pro",
      "mistral-large",
    ];

    return recommendedModels.some((recommended) =>
      model.id.toLowerCase().includes(recommended)
    );
  }

  /**
   * 判断是否为热门的OpenRouter模型
   * @param model OpenRouter模型信息
   * @returns boolean
   */
  private isOpenRouterModelPopular(model: OpenRouterModel): boolean {
    const popularModels = [
      "gpt-3.5-turbo",
      "gpt-4",
      "claude-2",
      "llama-2",
      "mistral-7b",
    ];

    return popularModels.some((popular) =>
      model.id.toLowerCase().includes(popular)
    );
  }

  // ==================== OpenAI相关方法 ====================

  /**
   * 测试OpenAI连接
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testOpenAIConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 验证API密钥格式
      if (!apiKey || !apiKey.trim()) {
        throw new Error("API密钥不能为空");
      }

      if (!apiKey.startsWith("sk-")) {
        throw new Error('无效的OpenAI API密钥格式，应以"sk-"开头');
      }

      console.log("开始测试OpenAI连接...");

      const response = await fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "User-Agent": "BookmarkExtension/1.0",
        },
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        // 处理常见的错误状态码
        switch (response.status) {
          case 401:
            throw new Error("API密钥无效或已过期");
          case 403:
            throw new Error("API密钥权限不足");
          case 429:
            throw new Error("API请求频率限制，请稍后重试");
          case 500:
            throw new Error("OpenAI服务器内部错误");
          case 503:
            throw new Error("OpenAI服务暂时不可用");
          default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      console.log(`OpenAI连接测试成功，发现 ${modelCount} 个模型`);

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      console.error("OpenAI连接测试失败:", error);

      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接或API服务状态";
      } else if (error.message.includes("fetch")) {
        errorMessage = "网络连接失败，请检查网络设置";
      } else if (error.message.includes("ENOTFOUND")) {
        errorMessage = "无法解析API域名，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取OpenAI模型列表
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @returns Promise<AIModel[]>
   */
  async getOpenAIModels(baseUrl: string, apiKey: string): Promise<AIModel[]> {
    try {
      console.log("开始获取OpenAI模型列表...");

      const response = await fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "User-Agent": "BookmarkExtension/1.0",
        },
        signal: AbortSignal.timeout(20000),
      });

      if (!response.ok) {
        // 处理错误状态码
        switch (response.status) {
          case 401:
            throw new Error("API密钥无效或已过期");
          case 403:
            throw new Error("API密钥权限不足，无法访问模型列表");
          case 429:
            throw new Error("API请求频率限制，请稍后重试");
          default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      const data = await response.json();
      const models = data.data || [];

      console.log(`获取到 ${models.length} 个OpenAI模型`);

      // 过滤和处理模型列表
      const processedModels = models
        .filter((model: any) => this.isValidOpenAIModel(model.id))
        .map((model: any) => {
          const displayName = this.formatOpenAIModelName(model.id);
          const description = this.generateOpenAIModelDescription(model.id);
          const capabilities = this.determineOpenAICapabilities(model.id);
          const modelTags = this.extractOpenAIModelTags(model.id);
          const contextLength = this.getOpenAIModelContextLength(model.id);

          return {
            id: model.id,
            name: model.id,
            displayName,
            description,
            capabilities,
            tags: modelTags,
            providerId: "openai",
            isRecommended: this.isOpenAIModelRecommended(model.id),
            isPopular: this.isOpenAIModelPopular(model.id),
            maxTokens: contextLength,
            contextLength,
            supportedFormats: ["text", "json"],
          };
        })
        .sort((a, b) => {
          // 按推荐度、受欢迎程度和名称排序
          if (a.isRecommended && !b.isRecommended) return -1;
          if (!a.isRecommended && b.isRecommended) return 1;
          if (a.isPopular && !b.isPopular) return -1;
          if (!a.isPopular && b.isPopular) return 1;
          return a.name.localeCompare(b.name);
        });

      console.log(`处理后的OpenAI模型数量: ${processedModels.length}`);
      return processedModels;
    } catch (error) {
      console.error("获取OpenAI模型列表失败:", error);

      // 提供更详细的错误信息
      if (error.name === "AbortError") {
        console.error("获取模型列表超时，OpenAI API可能响应缓慢");
      } else if (error.message.includes("HTTP")) {
        console.error("OpenAI API返回错误:", error.message);
      } else {
        console.error("网络连接错误:", error.message);
      }

      return [];
    }
  }

  /**
   * 验证是否为有效的OpenAI模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isValidOpenAIModel(modelId: string): boolean {
    // 过滤掉不相关的模型
    const validPrefixes = [
      "gpt-4",
      "gpt-3.5",
      "text-davinci",
      "text-curie",
      "text-babbage",
      "text-ada",
      "code-davinci",
      "code-cushman",
      "davinci",
      "curie",
      "babbage",
      "ada",
      "text-embedding",
      "text-similarity",
      "text-search",
      "whisper",
      "tts",
      "dall-e",
    ];

    // 排除的模型类型
    const excludePatterns = ["if:", "org-", "user-", "fine-tune", "ft:", "ft-"];

    // 检查是否匹配有效前缀
    const hasValidPrefix = validPrefixes.some((prefix) =>
      modelId.toLowerCase().startsWith(prefix.toLowerCase())
    );

    // 检查是否包含排除模式
    const hasExcludePattern = excludePatterns.some((pattern) =>
      modelId.toLowerCase().includes(pattern.toLowerCase())
    );

    return hasValidPrefix && !hasExcludePattern;
  }

  /**
   * 格式化OpenAI模型名称
   * @param modelId 模型ID
   * @returns string
   */
  private formatOpenAIModelName(modelId: string): string {
    const nameMap: Record<string, string> = {
      "gpt-4-turbo": "GPT-4 Turbo",
      "gpt-4-turbo-preview": "GPT-4 Turbo Preview",
      "gpt-4-0125-preview": "GPT-4 Turbo (0125)",
      "gpt-4-1106-preview": "GPT-4 Turbo (1106)",
      "gpt-4": "GPT-4",
      "gpt-4-0613": "GPT-4 (0613)",
      "gpt-4-32k": "GPT-4 32K",
      "gpt-4-32k-0613": "GPT-4 32K (0613)",
      "gpt-3.5-turbo": "GPT-3.5 Turbo",
      "gpt-3.5-turbo-16k": "GPT-3.5 Turbo 16K",
      "gpt-3.5-turbo-instruct": "GPT-3.5 Turbo Instruct",
      "text-davinci-003": "Davinci 003",
      "text-davinci-002": "Davinci 002",
      "text-curie-001": "Curie 001",
      "text-babbage-001": "Babbage 001",
      "text-ada-001": "Ada 001",
      "code-davinci-002": "Codex Davinci",
      "whisper-1": "Whisper",
      "tts-1": "Text-to-Speech",
      "tts-1-hd": "Text-to-Speech HD",
      "dall-e-2": "DALL-E 2",
      "dall-e-3": "DALL-E 3",
    };

    return nameMap[modelId] || modelId;
  }

  /**
   * 生成OpenAI模型描述
   * @param modelId 模型ID
   * @returns string
   */
  private generateOpenAIModelDescription(modelId: string): string {
    const descriptions: Record<string, string> = {
      "gpt-4-turbo": "最新的GPT-4模型，性能更强，成本更低",
      "gpt-4": "OpenAI最强大的语言模型，适合复杂任务",
      "gpt-4-32k": "GPT-4的长上下文版本，支持32K tokens",
      "gpt-3.5-turbo": "快速高效的对话模型，性价比高",
      "gpt-3.5-turbo-16k": "GPT-3.5的长上下文版本，支持16K tokens",
      "gpt-3.5-turbo-instruct": "针对指令优化的GPT-3.5模型",
      "text-davinci-003": "强大的文本生成模型，适合创作任务",
      "code-davinci-002": "专门优化的代码生成模型",
      "whisper-1": "语音转文字模型，支持多语言",
      "tts-1": "文字转语音模型",
      "dall-e-2": "图像生成模型",
      "dall-e-3": "最新的图像生成模型，质量更高",
    };

    // 按优先级匹配描述
    for (const [key, desc] of Object.entries(descriptions)) {
      if (modelId.includes(key)) {
        return desc;
      }
    }

    // 根据模型类型生成通用描述
    if (modelId.includes("gpt-4")) {
      return "GPT-4系列语言模型，性能强大";
    } else if (modelId.includes("gpt-3.5")) {
      return "GPT-3.5系列语言模型，快速高效";
    } else if (modelId.includes("text-")) {
      return "OpenAI文本生成模型";
    } else if (modelId.includes("code-")) {
      return "OpenAI代码生成模型";
    } else if (modelId.includes("embedding")) {
      return "OpenAI文本嵌入模型";
    }

    return "OpenAI语言模型";
  }

  /**
   * 确定OpenAI模型能力
   * @param modelId 模型ID
   * @returns string[]
   */
  private determineOpenAICapabilities(modelId: string): string[] {
    const capabilities = [];

    // 基础能力
    if (modelId.includes("gpt-") || modelId.includes("text-")) {
      capabilities.push("chat", "completion");
    }

    // 代码能力
    if (modelId.includes("code-") || modelId.includes("gpt-4")) {
      capabilities.push("coding");
    }

    // 指令跟随
    if (
      modelId.includes("turbo") ||
      modelId.includes("instruct") ||
      modelId.includes("gpt-4")
    ) {
      capabilities.push("instruction-following");
    }

    // 嵌入能力
    if (modelId.includes("embedding")) {
      capabilities.push("embedding");
    }

    // 语音能力
    if (modelId.includes("whisper")) {
      capabilities.push("speech-to-text");
    }

    if (modelId.includes("tts")) {
      capabilities.push("text-to-speech");
    }

    // 图像能力
    if (modelId.includes("dall-e")) {
      capabilities.push("image-generation");
    }

    // 函数调用
    if (modelId.includes("gpt-4") || modelId.includes("gpt-3.5-turbo")) {
      capabilities.push("function-calling");
    }

    return capabilities;
  }

  /**
   * 提取OpenAI模型标签
   * @param modelId 模型ID
   * @returns string[]
   */
  private extractOpenAIModelTags(modelId: string): string[] {
    const tags = ["OpenAI"];

    // 模型系列标签
    if (modelId.includes("gpt-4")) {
      tags.push("GPT-4");
    } else if (modelId.includes("gpt-3.5")) {
      tags.push("GPT-3.5");
    }

    // 特性标签
    if (modelId.includes("turbo")) {
      tags.push("Turbo");
    }

    if (modelId.includes("32k")) {
      tags.push("长上下文", "32K");
    } else if (modelId.includes("16k")) {
      tags.push("长上下文", "16K");
    }

    if (modelId.includes("instruct")) {
      tags.push("指令优化");
    }

    if (modelId.includes("code")) {
      tags.push("代码生成");
    }

    if (modelId.includes("preview")) {
      tags.push("预览版");
    }

    // 功能标签
    if (modelId.includes("whisper")) {
      tags.push("语音识别");
    }

    if (modelId.includes("tts")) {
      tags.push("语音合成");
    }

    if (modelId.includes("dall-e")) {
      tags.push("图像生成");
    }

    if (modelId.includes("embedding")) {
      tags.push("文本嵌入");
    }

    return tags;
  }

  /**
   * 获取OpenAI模型上下文长度
   * @param modelId 模型ID
   * @returns number
   */
  private getOpenAIModelContextLength(modelId: string): number {
    const contextLengths: Record<string, number> = {
      "gpt-4-turbo": 128000,
      "gpt-4-turbo-preview": 128000,
      "gpt-4-0125-preview": 128000,
      "gpt-4-1106-preview": 128000,
      "gpt-4-32k": 32768,
      "gpt-4": 8192,
      "gpt-3.5-turbo-16k": 16384,
      "gpt-3.5-turbo": 4096,
      "gpt-3.5-turbo-instruct": 4096,
      "text-davinci-003": 4097,
      "text-davinci-002": 4097,
      "text-curie-001": 2049,
      "text-babbage-001": 2049,
      "text-ada-001": 2049,
      "code-davinci-002": 8001,
    };

    // 按优先级匹配
    for (const [key, length] of Object.entries(contextLengths)) {
      if (modelId.includes(key)) {
        return length;
      }
    }

    // 默认值
    if (modelId.includes("gpt-4")) {
      return 8192;
    } else if (modelId.includes("gpt-3.5")) {
      return 4096;
    }

    return 2048;
  }

  /**
   * 判断是否为推荐的OpenAI模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isOpenAIModelRecommended(modelId: string): boolean {
    const recommendedModels = [
      "gpt-4-turbo",
      "gpt-4",
      "gpt-3.5-turbo",
      "gpt-3.5-turbo-16k",
    ];
    return recommendedModels.some((recommended) =>
      modelId.includes(recommended)
    );
  }

  /**
   * 判断是否为热门的OpenAI模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isOpenAIModelPopular(modelId: string): boolean {
    const popularModels = [
      "gpt-4-turbo",
      "gpt-4",
      "gpt-3.5-turbo",
      "text-davinci-003",
    ];
    return popularModels.some((popular) => modelId.includes(popular));
  }

  // ==================== Claude相关方法 ====================

  /**
   * 测试Claude连接
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testClaudeConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 验证API密钥格式
      if (!apiKey || !apiKey.trim()) {
        throw new Error("API密钥不能为空");
      }

      if (!apiKey.startsWith("sk-ant-")) {
        throw new Error('无效的Claude API密钥格式，应以"sk-ant-"开头');
      }

      console.log("开始测试Claude连接...");

      // Claude没有公开的模型列表API，通过发送最小的测试消息来验证连接
      const response = await fetch(`${baseUrl}/messages`, {
        method: "POST",
        headers: {
          "x-api-key": apiKey,
          "Content-Type": "application/json",
          "anthropic-version": "2023-06-01",
          "User-Agent": "BookmarkExtension/1.0",
        },
        body: JSON.stringify({
          model: "claude-3-haiku-20240307", // 使用最便宜的模型进行测试
          max_tokens: 1, // 最小token数量
          messages: [
            {
              role: "user",
              content: "Hi",
            },
          ],
        }),
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        // 处理常见的错误状态码
        switch (response.status) {
          case 400:
            const errorData = await response.json().catch(() => ({}));
            if (errorData.error?.type === "invalid_request_error") {
              throw new Error(`请求格式错误: ${errorData.error.message}`);
            }
            throw new Error("请求参数无效");
          case 401:
            throw new Error("API密钥无效或已过期");
          case 403:
            throw new Error("API密钥权限不足或账户被限制");
          case 429:
            throw new Error("API请求频率限制，请稍后重试");
          case 500:
            throw new Error("Claude服务器内部错误");
          case 529:
            throw new Error("Claude服务过载，请稍后重试");
          default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      // 验证响应格式
      const data = await response.json();
      if (!data.content || !Array.isArray(data.content)) {
        throw new Error("Claude API响应格式异常");
      }

      console.log("Claude连接测试成功");

      return {
        success: true,
        modelCount: 4, // Claude 3系列有4个主要模型
      };
    } catch (error) {
      console.error("Claude连接测试失败:", error);

      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接或API服务状态";
      } else if (error.message.includes("fetch")) {
        errorMessage = "网络连接失败，请检查网络设置";
      } else if (error.message.includes("ENOTFOUND")) {
        errorMessage = "无法解析API域名，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取Claude模型列表（预设）
   * Claude API不提供公开的模型列表端点，所以使用预定义的模型列表
   * @returns Promise<AIModel[]>
   */
  async getClaudeModels(): Promise<AIModel[]> {
    try {
      console.log("获取Claude模型列表...");

      // Claude 3系列模型的完整列表
      const claudeModels = [
        {
          id: "claude-3-5-sonnet-20241022",
          name: "claude-3-5-sonnet-20241022",
          displayName: "Claude 3.5 Sonnet",
          description:
            "Anthropic最新的高性能模型，在编程、写作和分析方面表现出色",
          size: "未公开",
          capabilities: ["chat", "coding", "analysis", "writing"],
          tags: ["Claude 3.5", "最新版本", "高性能", "编程优化"],
          providerId: "claude",
          isRecommended: true,
          isPopular: true,
          maxTokens: 8192,
          contextLength: 200000,
          supportedFormats: ["text", "json"],
        },
        {
          id: "claude-3-opus-20240229",
          name: "claude-3-opus-20240229",
          displayName: "Claude 3 Opus",
          description:
            "Anthropic最强大的模型，在复杂推理和创意任务方面表现卓越",
          size: "未公开",
          capabilities: ["chat", "reasoning", "creative-writing", "analysis"],
          tags: ["Claude 3", "最强性能", "复杂推理", "创意写作"],
          providerId: "claude",
          isRecommended: true,
          isPopular: true,
          maxTokens: 4096,
          contextLength: 200000,
          supportedFormats: ["text", "json"],
        },
        {
          id: "claude-3-sonnet-20240229",
          name: "claude-3-sonnet-20240229",
          displayName: "Claude 3 Sonnet",
          description: "平衡性能和成本的模型，适合大多数日常任务",
          size: "未公开",
          capabilities: ["chat", "writing", "analysis"],
          tags: ["Claude 3", "平衡性能", "性价比高", "通用任务"],
          providerId: "claude",
          isRecommended: true,
          isPopular: true,
          maxTokens: 4096,
          contextLength: 200000,
          supportedFormats: ["text", "json"],
        },
        {
          id: "claude-3-haiku-20240307",
          name: "claude-3-haiku-20240307",
          displayName: "Claude 3 Haiku",
          description: "快速响应的轻量级模型，适合简单任务和快速交互",
          size: "未公开",
          capabilities: ["chat", "quick-response"],
          tags: ["Claude 3", "快速响应", "轻量级", "简单任务"],
          providerId: "claude",
          isRecommended: false,
          isPopular: true,
          maxTokens: 4096,
          contextLength: 200000,
          supportedFormats: ["text", "json"],
        },
      ];

      // 按推荐度和性能排序
      const sortedModels = claudeModels.sort((a, b) => {
        // 推荐模型优先
        if (a.isRecommended && !b.isRecommended) return -1;
        if (!a.isRecommended && b.isRecommended) return 1;

        // 按模型版本排序（3.5 > 3.0）
        if (a.id.includes("3-5") && !b.id.includes("3-5")) return -1;
        if (!a.id.includes("3-5") && b.id.includes("3-5")) return 1;

        // 按性能等级排序（opus > sonnet > haiku）
        const performanceOrder = ["opus", "sonnet", "haiku"];
        const aPerf = performanceOrder.findIndex((p) => a.id.includes(p));
        const bPerf = performanceOrder.findIndex((p) => b.id.includes(p));

        if (aPerf !== bPerf) {
          return aPerf - bPerf;
        }

        // 按名称排序
        return a.name.localeCompare(b.name);
      });

      console.log(`Claude模型列表获取成功: ${sortedModels.length} 个模型`);
      return sortedModels;
    } catch (error) {
      console.error("获取Claude模型列表失败:", error);
      return [];
    }
  }

  /**
   * 验证Claude模型是否可用
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @param modelId 模型ID
   * @returns Promise<boolean>
   */
  async validateClaudeModel(
    baseUrl: string,
    apiKey: string,
    modelId: string
  ): Promise<boolean> {
    try {
      const response = await fetch(`${baseUrl}/messages`, {
        method: "POST",
        headers: {
          "x-api-key": apiKey,
          "Content-Type": "application/json",
          "anthropic-version": "2023-06-01",
          "User-Agent": "BookmarkExtension/1.0",
        },
        body: JSON.stringify({
          model: modelId,
          max_tokens: 1,
          messages: [
            {
              role: "user",
              content: "Test",
            },
          ],
        }),
        signal: AbortSignal.timeout(10000),
      });

      return response.ok;
    } catch (error) {
      console.error(`Claude模型 ${modelId} 验证失败:`, error);
      return false;
    }
  }

  /**
   * 获取Claude模型的详细信息
   * @param modelId 模型ID
   * @returns Claude模型详细信息
   */
  private getClaudeModelDetails(modelId: string): {
    displayName: string;
    description: string;
    capabilities: string[];
    tags: string[];
    contextLength: number;
    maxTokens: number;
  } {
    const modelDetails: Record<string, any> = {
      "claude-3-5-sonnet-20241022": {
        displayName: "Claude 3.5 Sonnet",
        description:
          "Anthropic最新的高性能模型，在编程、写作和分析方面表现出色",
        capabilities: ["chat", "coding", "analysis", "writing", "reasoning"],
        tags: ["Claude 3.5", "最新版本", "高性能", "编程优化"],
        contextLength: 200000,
        maxTokens: 8192,
      },
      "claude-3-opus-20240229": {
        displayName: "Claude 3 Opus",
        description: "Anthropic最强大的模型，在复杂推理和创意任务方面表现卓越",
        capabilities: [
          "chat",
          "reasoning",
          "creative-writing",
          "analysis",
          "complex-tasks",
        ],
        tags: ["Claude 3", "最强性能", "复杂推理", "创意写作"],
        contextLength: 200000,
        maxTokens: 4096,
      },
      "claude-3-sonnet-20240229": {
        displayName: "Claude 3 Sonnet",
        description: "平衡性能和成本的模型，适合大多数日常任务",
        capabilities: ["chat", "writing", "analysis", "general-tasks"],
        tags: ["Claude 3", "平衡性能", "性价比高", "通用任务"],
        contextLength: 200000,
        maxTokens: 4096,
      },
      "claude-3-haiku-20240307": {
        displayName: "Claude 3 Haiku",
        description: "快速响应的轻量级模型，适合简单任务和快速交互",
        capabilities: ["chat", "quick-response", "simple-tasks"],
        tags: ["Claude 3", "快速响应", "轻量级", "简单任务"],
        contextLength: 200000,
        maxTokens: 4096,
      },
    };

    return (
      modelDetails[modelId] || {
        displayName: modelId,
        description: "Claude模型",
        capabilities: ["chat"],
        tags: ["Claude"],
        contextLength: 200000,
        maxTokens: 4096,
      }
    );
  }

  /**
   * 判断是否为推荐的Claude模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isClaudeModelRecommended(modelId: string): boolean {
    const recommendedModels = [
      "claude-3-5-sonnet-20241022",
      "claude-3-opus-20240229",
      "claude-3-sonnet-20240229",
    ];
    return recommendedModels.includes(modelId);
  }

  /**
   * 判断是否为热门的Claude模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isClaudeModelPopular(modelId: string): boolean {
    // 所有Claude 3系列模型都比较热门
    return modelId.includes("claude-3");
  }

  // ==================== 其他提供商的占位方法 ====================

  /**
   * 测试Gemini连接
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testGeminiConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 验证API密钥格式
      if (!apiKey || !apiKey.trim()) {
        throw new Error("API密钥不能为空");
      }

      // Gemini API密钥通常以AIza开头
      if (!apiKey.startsWith("AIza")) {
        throw new Error('无效的Gemini API密钥格式，应以"AIza"开头');
      }

      console.log("开始测试Gemini连接...");

      // 使用模型列表API来测试连接
      const response = await fetch(`${baseUrl}/models?key=${apiKey}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "BookmarkExtension/1.0",
        },
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        // 处理常见的错误状态码
        switch (response.status) {
          case 400:
            const errorData = await response.json().catch(() => ({}));
            if (errorData.error?.message) {
              throw new Error(`请求错误: ${errorData.error.message}`);
            }
            throw new Error("请求参数无效");
          case 401:
            throw new Error("API密钥无效或已过期");
          case 403:
            throw new Error("API密钥权限不足或服务被禁用");
          case 429:
            throw new Error("API请求频率限制，请稍后重试");
          case 500:
            throw new Error("Gemini服务器内部错误");
          case 503:
            throw new Error("Gemini服务暂时不可用");
          default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      // 验证响应格式
      const data = await response.json();
      if (!data.models || !Array.isArray(data.models)) {
        throw new Error("Gemini API响应格式异常");
      }

      const modelCount = data.models.length;

      console.log(`Gemini连接测试成功，发现 ${modelCount} 个模型`);

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      console.error("Gemini连接测试失败:", error);

      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接或API服务状态";
      } else if (error.message.includes("fetch")) {
        errorMessage = "网络连接失败，请检查网络设置";
      } else if (error.message.includes("ENOTFOUND")) {
        errorMessage = "无法解析API域名，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取Gemini模型列表
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @returns Promise<AIModel[]>
   */
  async getGeminiModels(baseUrl: string, apiKey: string): Promise<AIModel[]> {
    try {
      console.log("开始获取Gemini模型列表...");

      const response = await fetch(`${baseUrl}/models?key=${apiKey}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "BookmarkExtension/1.0",
        },
        signal: AbortSignal.timeout(20000),
      });

      if (!response.ok) {
        // 处理错误状态码
        switch (response.status) {
          case 401:
            throw new Error("API密钥无效或已过期");
          case 403:
            throw new Error("API密钥权限不足，无法访问模型列表");
          case 429:
            throw new Error("API请求频率限制，请稍后重试");
          default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      const data = await response.json();
      const models = data.models || [];

      console.log(`获取到 ${models.length} 个Gemini模型`);

      // 过滤和处理模型列表
      const processedModels = models
        .filter((model: any) => this.isValidGeminiModel(model.name))
        .map((model: any) => {
          const modelId = this.extractGeminiModelId(model.name);
          const displayName = this.formatGeminiModelName(modelId);
          const description = this.generateGeminiModelDescription(
            modelId,
            model
          );
          const capabilities = this.determineGeminiCapabilities(modelId);
          const modelTags = this.extractGeminiModelTags(modelId);
          const contextLength = this.getGeminiModelContextLength(modelId);

          return {
            id: modelId,
            name: modelId,
            displayName,
            description,
            capabilities,
            tags: modelTags,
            providerId: "gemini",
            isRecommended: this.isGeminiModelRecommended(modelId),
            isPopular: this.isGeminiModelPopular(modelId),
            maxTokens: this.getGeminiModelMaxTokens(modelId),
            contextLength,
            supportedFormats: ["text", "json"],
          };
        })
        .sort((a, b) => {
          // 按推荐度、受欢迎程度和名称排序
          if (a.isRecommended && !b.isRecommended) return -1;
          if (!a.isRecommended && b.isRecommended) return 1;
          if (a.isPopular && !b.isPopular) return -1;
          if (!a.isPopular && b.isPopular) return 1;
          return a.name.localeCompare(b.name);
        });

      console.log(`处理后的Gemini模型数量: ${processedModels.length}`);
      return processedModels;
    } catch (error) {
      console.error("获取Gemini模型列表失败:", error);

      // 提供更详细的错误信息
      if (error.name === "AbortError") {
        console.error("获取模型列表超时，Gemini API可能响应缓慢");
      } else if (error.message.includes("HTTP")) {
        console.error("Gemini API返回错误:", error.message);
      } else {
        console.error("网络连接错误:", error.message);
      }

      return [];
    }
  }

  /**
   * 验证是否为有效的Gemini模型
   * @param modelName 模型名称（完整路径）
   * @returns boolean
   */
  private isValidGeminiModel(modelName: string): boolean {
    // Gemini模型名称格式：models/gemini-pro, models/gemini-1.5-pro等
    if (!modelName.startsWith("models/")) {
      return false;
    }

    const modelId = modelName.replace("models/", "");

    // 有效的Gemini模型前缀
    const validPrefixes = [
      "gemini-pro",
      "gemini-1.5-pro",
      "gemini-1.5-flash",
      "gemini-1.0-pro",
      "text-bison",
      "chat-bison",
      "code-bison",
      "codechat-bison",
    ];

    return validPrefixes.some((prefix) => modelId.startsWith(prefix));
  }

  /**
   * 从完整模型名称中提取模型ID
   * @param modelName 完整模型名称
   * @returns string
   */
  private extractGeminiModelId(modelName: string): string {
    return modelName.replace("models/", "");
  }

  /**
   * 格式化Gemini模型名称
   * @param modelId 模型ID
   * @returns string
   */
  private formatGeminiModelName(modelId: string): string {
    const nameMap: Record<string, string> = {
      "gemini-1.5-pro": "Gemini 1.5 Pro",
      "gemini-1.5-pro-latest": "Gemini 1.5 Pro (Latest)",
      "gemini-1.5-flash": "Gemini 1.5 Flash",
      "gemini-1.5-flash-latest": "Gemini 1.5 Flash (Latest)",
      "gemini-1.0-pro": "Gemini 1.0 Pro",
      "gemini-1.0-pro-latest": "Gemini 1.0 Pro (Latest)",
      "gemini-pro": "Gemini Pro",
      "gemini-pro-vision": "Gemini Pro Vision",
      "text-bison-001": "PaLM 2 Text Bison",
      "chat-bison-001": "PaLM 2 Chat Bison",
      "code-bison-001": "PaLM 2 Code Bison",
      "codechat-bison-001": "PaLM 2 CodeChat Bison",
    };

    // 精确匹配
    if (nameMap[modelId]) {
      return nameMap[modelId];
    }

    // 模糊匹配
    for (const [key, value] of Object.entries(nameMap)) {
      if (modelId.includes(key)) {
        return value;
      }
    }

    return modelId;
  }

  /**
   * 生成Gemini模型描述
   * @param modelId 模型ID
   * @param model 原始模型数据
   * @returns string
   */
  private generateGeminiModelDescription(modelId: string, model: any): string {
    const descriptions: Record<string, string> = {
      "gemini-1.5-pro": "Google最新的高性能多模态模型，支持长上下文",
      "gemini-1.5-flash": "Google快速响应的多模态模型，平衡性能与速度",
      "gemini-1.0-pro": "Google Gemini 1.0专业版，适合复杂任务",
      "gemini-pro": "Google Gemini专业版模型",
      "gemini-pro-vision": "Google Gemini视觉模型，支持图像理解",
      "text-bison": "Google PaLM 2文本生成模型",
      "chat-bison": "Google PaLM 2对话模型",
      "code-bison": "Google PaLM 2代码生成模型",
      "codechat-bison": "Google PaLM 2代码对话模型",
    };

    // 按优先级匹配描述
    for (const [key, desc] of Object.entries(descriptions)) {
      if (modelId.includes(key)) {
        return desc;
      }
    }

    // 使用API返回的描述（如果有）
    if (model.description) {
      return model.description;
    }

    return "Google AI模型";
  }

  /**
   * 确定Gemini模型能力
   * @param modelId 模型ID
   * @returns string[]
   */
  private determineGeminiCapabilities(modelId: string): string[] {
    const capabilities = [];

    // 基础能力
    if (modelId.includes("gemini") || modelId.includes("bison")) {
      capabilities.push("chat", "completion");
    }

    // 代码能力
    if (modelId.includes("code")) {
      capabilities.push("coding");
    }

    // 视觉能力
    if (modelId.includes("vision") || modelId.includes("1.5")) {
      capabilities.push("vision", "multimodal");
    }

    // 长上下文
    if (modelId.includes("1.5")) {
      capabilities.push("long-context");
    }

    // 快速响应
    if (modelId.includes("flash")) {
      capabilities.push("fast-response");
    }

    // 指令跟随
    if (modelId.includes("gemini") || modelId.includes("chat")) {
      capabilities.push("instruction-following");
    }

    return capabilities;
  }

  /**
   * 提取Gemini模型标签
   * @param modelId 模型ID
   * @returns string[]
   */
  private extractGeminiModelTags(modelId: string): string[] {
    const tags = ["Google AI"];

    // 模型系列标签
    if (modelId.includes("gemini-1.5")) {
      tags.push("Gemini 1.5");
    } else if (modelId.includes("gemini-1.0")) {
      tags.push("Gemini 1.0");
    } else if (modelId.includes("gemini")) {
      tags.push("Gemini");
    } else if (modelId.includes("bison")) {
      tags.push("PaLM 2");
    }

    // 特性标签
    if (modelId.includes("pro")) {
      tags.push("专业版");
    }

    if (modelId.includes("flash")) {
      tags.push("快速响应");
    }

    if (modelId.includes("vision")) {
      tags.push("视觉理解");
    }

    if (modelId.includes("code")) {
      tags.push("代码生成");
    }

    if (modelId.includes("chat")) {
      tags.push("对话优化");
    }

    if (modelId.includes("latest")) {
      tags.push("最新版本");
    }

    // 长上下文支持
    if (modelId.includes("1.5")) {
      tags.push("长上下文");
    }

    return tags;
  }

  /**
   * 获取Gemini模型上下文长度
   * @param modelId 模型ID
   * @returns number
   */
  private getGeminiModelContextLength(modelId: string): number {
    const contextLengths: Record<string, number> = {
      "gemini-1.5-pro": 2000000, // 2M tokens
      "gemini-1.5-flash": 1000000, // 1M tokens
      "gemini-1.0-pro": 32768,
      "gemini-pro": 32768,
      "text-bison": 8192,
      "chat-bison": 8192,
      "code-bison": 8192,
      "codechat-bison": 8192,
    };

    // 按优先级匹配
    for (const [key, length] of Object.entries(contextLengths)) {
      if (modelId.includes(key)) {
        return length;
      }
    }

    // 默认值
    if (modelId.includes("gemini-1.5")) {
      return 1000000;
    } else if (modelId.includes("gemini")) {
      return 32768;
    }

    return 8192;
  }

  /**
   * 获取Gemini模型最大输出tokens
   * @param modelId 模型ID
   * @returns number
   */
  private getGeminiModelMaxTokens(modelId: string): number {
    const maxTokens: Record<string, number> = {
      "gemini-1.5-pro": 8192,
      "gemini-1.5-flash": 8192,
      "gemini-1.0-pro": 2048,
      "gemini-pro": 2048,
      "text-bison": 1024,
      "chat-bison": 1024,
      "code-bison": 1024,
      "codechat-bison": 1024,
    };

    // 按优先级匹配
    for (const [key, tokens] of Object.entries(maxTokens)) {
      if (modelId.includes(key)) {
        return tokens;
      }
    }

    // 默认值
    if (modelId.includes("gemini-1.5")) {
      return 8192;
    } else if (modelId.includes("gemini")) {
      return 2048;
    }

    return 1024;
  }

  /**
   * 判断是否为推荐的Gemini模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isGeminiModelRecommended(modelId: string): boolean {
    const recommendedModels = [
      "gemini-1.5-pro",
      "gemini-1.5-flash",
      "gemini-1.0-pro",
    ];
    return recommendedModels.some((recommended) =>
      modelId.includes(recommended)
    );
  }

  /**
   * 判断是否为热门的Gemini模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isGeminiModelPopular(modelId: string): boolean {
    const popularModels = [
      "gemini-1.5",
      "gemini-pro",
      "chat-bison",
      "code-bison",
    ];
    return popularModels.some((popular) => modelId.includes(popular));
  }

  /**
   * 测试DeepSeek连接
   * @param baseUrl DeepSeek API基础URL
   * @param apiKey DeepSeek API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testDeepSeekConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      if (!apiKey) {
        return {
          success: false,
          error: "DeepSeek API密钥不能为空",
        };
      }

      // 确保baseUrl格式正确
      const normalizedBaseUrl = baseUrl.replace(/\/+$/, "");
      const modelsUrl = normalizedBaseUrl.includes("/v1")
        ? `${normalizedBaseUrl}/models`
        : `${normalizedBaseUrl}/models`;

      console.log("测试DeepSeek连接:", modelsUrl);

      // 测试获取模型列表
      const response = await fetch(modelsUrl, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(10000),
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        if (response.status === 401) {
          errorMessage = "API密钥无效或已过期";
        } else if (response.status === 403) {
          errorMessage = "API密钥权限不足";
        } else if (response.status === 429) {
          errorMessage = "API请求频率超限，请稍后重试";
        } else if (response.status >= 500) {
          errorMessage = "DeepSeek服务器错误，请稍后重试";
        }

        return {
          success: false,
          error: errorMessage,
        };
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      console.log("DeepSeek连接测试成功，模型数量:", modelCount);

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      console.error("DeepSeek连接测试失败:", error);

      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到DeepSeek服务，请检查网络和API地址";
      } else if (error.message.includes("ENOTFOUND")) {
        errorMessage = "DNS解析失败，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取DeepSeek模型列表
   * @returns Promise<AIModel[]>
   */
  async getDeepSeekModels(): Promise<AIModel[]> {
    try {
      // DeepSeek目前主要提供两个模型
      const models: AIModel[] = [
        {
          id: "deepseek-chat",
          name: "deepseek-chat",
          displayName: "DeepSeek Chat",
          description: "DeepSeek-V3-0324 对话模型，支持多轮对话和复杂推理任务",
          capabilities: ["chat", "completion", "reasoning"],
          tags: ["DeepSeek", "对话", "推理", "中文", "英文"],
          providerId: "deepseek",
          isRecommended: true,
          isPopular: true,
          maxTokens: 64000,
          contextLength: 64000,
        },
        {
          id: "deepseek-reasoner",
          name: "deepseek-reasoner",
          displayName: "DeepSeek Reasoner",
          description: "DeepSeek-R1-0528 推理模型，专门优化复杂推理和数学问题",
          capabilities: ["reasoning", "math", "logic", "analysis"],
          tags: ["DeepSeek", "推理", "数学", "逻辑", "分析"],
          providerId: "deepseek",
          isRecommended: true,
          isPopular: false,
          maxTokens: 64000,
          contextLength: 64000,
        },
      ];

      console.log("获取DeepSeek模型列表成功，模型数量:", models.length);
      return models;
    } catch (error) {
      console.error("获取DeepSeek模型列表失败:", error);
      return [];
    }
  }

  /**
   * 测试智谱AI连接
   * @param baseUrl 智谱AI API基础URL
   * @param apiKey 智谱AI API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testZhipuConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      if (!apiKey) {
        return {
          success: false,
          error: "智谱AI API密钥不能为空",
        };
      }

      // 确保baseUrl格式正确
      const normalizedBaseUrl = baseUrl.replace(/\/+$/, "");
      const modelsUrl = `${normalizedBaseUrl}/models`;

      console.log("测试智谱AI连接:", modelsUrl);

      // 测试获取模型列表
      const response = await fetch(modelsUrl, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(10000),
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        if (response.status === 401) {
          errorMessage = "API密钥无效或已过期";
        } else if (response.status === 403) {
          errorMessage = "API密钥权限不足";
        } else if (response.status === 429) {
          errorMessage = "API请求频率超限，请稍后重试";
        } else if (response.status >= 500) {
          errorMessage = "智谱AI服务器错误，请稍后重试";
        }

        return {
          success: false,
          error: errorMessage,
        };
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      console.log("智谱AI连接测试成功，模型数量:", modelCount);

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      console.error("智谱AI连接测试失败:", error);

      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到智谱AI服务，请检查网络和API地址";
      } else if (error.message.includes("ENOTFOUND")) {
        errorMessage = "DNS解析失败，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取智谱AI模型列表
   * @returns Promise<AIModel[]>
   */
  async getZhipuModels(): Promise<AIModel[]> {
    try {
      // 智谱AI主要提供GLM系列模型
      const models: AIModel[] = [
        {
          id: "glm-4",
          name: "glm-4",
          displayName: "GLM-4",
          description: "智谱AI最新一代超大规模预训练模型，支持更长的上下文",
          capabilities: ["chat", "completion", "reasoning", "code"],
          tags: ["智谱AI", "GLM", "对话", "推理", "中文", "英文"],
          providerId: "zhipu",
          isRecommended: true,
          isPopular: true,
          maxTokens: 128000,
          contextLength: 128000,
        },
        {
          id: "glm-4v",
          name: "glm-4v",
          displayName: "GLM-4V",
          description: "智谱AI多模态模型，支持图像理解和文本生成",
          capabilities: ["chat", "vision", "multimodal"],
          tags: ["智谱AI", "GLM", "多模态", "图像理解", "视觉"],
          providerId: "zhipu",
          isRecommended: true,
          isPopular: false,
          maxTokens: 8000,
          contextLength: 8000,
        },
        {
          id: "glm-3-turbo",
          name: "glm-3-turbo",
          displayName: "GLM-3 Turbo",
          description: "智谱AI高性能对话模型，平衡效果与速度",
          capabilities: ["chat", "completion"],
          tags: ["智谱AI", "GLM", "对话", "高速", "中文"],
          providerId: "zhipu",
          isRecommended: false,
          isPopular: true,
          maxTokens: 128000,
          contextLength: 128000,
        },
        {
          id: "chatglm3-6b",
          name: "chatglm3-6b",
          displayName: "ChatGLM3-6B",
          description: "智谱AI开源对话模型，支持多轮对话和工具调用",
          capabilities: ["chat", "completion", "function-calling"],
          tags: ["智谱AI", "ChatGLM", "开源", "工具调用", "中文"],
          providerId: "zhipu",
          isRecommended: false,
          isPopular: false,
          maxTokens: 8192,
          contextLength: 8192,
        },
        {
          id: "codegeex2-6b",
          name: "codegeex2-6b",
          displayName: "CodeGeeX2-6B",
          description: "智谱AI代码生成模型，支持多种编程语言",
          capabilities: ["code", "completion", "generation"],
          tags: ["智谱AI", "CodeGeeX", "代码生成", "编程", "开源"],
          providerId: "zhipu",
          isRecommended: false,
          isPopular: false,
          maxTokens: 8192,
          contextLength: 8192,
        },
      ];

      console.log("获取智谱AI模型列表成功，模型数量:", models.length);
      return models;
    } catch (error) {
      console.error("获取智谱AI模型列表失败:", error);
      return [];
    }
  }

  /**
   * 测试通义千问连接
   * @param baseUrl 通义千问API基础URL
   * @param apiKey 通义千问API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testQwenConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      if (!apiKey) {
        return {
          success: false,
          error: "通义千问API密钥不能为空",
        };
      }

      // 确保baseUrl格式正确，通义千问使用OpenAI兼容模式
      const normalizedBaseUrl = baseUrl.replace(/\/+$/, "");
      const modelsUrl = normalizedBaseUrl.includes("compatible-mode")
        ? `${normalizedBaseUrl}/models`
        : `${normalizedBaseUrl}/compatible-mode/v1/models`;

      console.log("测试通义千问连接:", modelsUrl);

      // 测试获取模型列表
      const response = await fetch(modelsUrl, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(10000),
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        if (response.status === 401) {
          errorMessage = "API密钥无效或已过期";
        } else if (response.status === 403) {
          errorMessage = "API密钥权限不足";
        } else if (response.status === 429) {
          errorMessage = "API请求频率超限，请稍后重试";
        } else if (response.status >= 500) {
          errorMessage = "通义千问服务器错误，请稍后重试";
        }

        return {
          success: false,
          error: errorMessage,
        };
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      console.log("通义千问连接测试成功，模型数量:", modelCount);

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      console.error("通义千问连接测试失败:", error);

      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到通义千问服务，请检查网络和API地址";
      } else if (error.message.includes("ENOTFOUND")) {
        errorMessage = "DNS解析失败，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取通义千问模型列表
   * @returns Promise<AIModel[]>
   */
  async getQwenModels(): Promise<AIModel[]> {
    try {
      // 通义千问提供多个模型系列
      const models: AIModel[] = [
        {
          id: "qwen-plus",
          name: "qwen-plus",
          displayName: "通义千问 Plus",
          description: "通义千问超大规模语言模型，具有强大的中文理解和生成能力",
          capabilities: ["chat", "completion", "reasoning", "code"],
          tags: ["阿里云", "通义千问", "对话", "推理", "中文", "英文"],
          providerId: "qwen",
          isRecommended: true,
          isPopular: true,
          maxTokens: 32000,
          contextLength: 32000,
        },
        {
          id: "qwen-turbo",
          name: "qwen-turbo",
          displayName: "通义千问 Turbo",
          description: "通义千问高效模型，平衡性能与速度，适合大规模应用",
          capabilities: ["chat", "completion"],
          tags: ["阿里云", "通义千问", "对话", "高速", "中文"],
          providerId: "qwen",
          isRecommended: true,
          isPopular: true,
          maxTokens: 8000,
          contextLength: 8000,
        },
        {
          id: "qwen-max",
          name: "qwen-max",
          displayName: "通义千问 Max",
          description: "通义千问最强模型，具有最佳的理解和生成能力",
          capabilities: ["chat", "completion", "reasoning", "analysis"],
          tags: ["阿里云", "通义千问", "最强", "推理", "分析"],
          providerId: "qwen",
          isRecommended: true,
          isPopular: false,
          maxTokens: 8000,
          contextLength: 8000,
        },
        {
          id: "qwen-long",
          name: "qwen-long",
          displayName: "通义千问 Long",
          description: "通义千问长文本模型，支持超长上下文处理",
          capabilities: ["chat", "completion", "long-context"],
          tags: ["阿里云", "通义千问", "长文本", "上下文", "文档"],
          providerId: "qwen",
          isRecommended: false,
          isPopular: false,
          maxTokens: 1000000,
          contextLength: 1000000,
        },
        {
          id: "qwen-coder-plus",
          name: "qwen-coder-plus",
          displayName: "通义千问 Coder Plus",
          description: "通义千问代码专用模型，专门优化代码生成和理解",
          capabilities: ["code", "completion", "generation", "debugging"],
          tags: ["阿里云", "通义千问", "代码生成", "编程", "调试"],
          providerId: "qwen",
          isRecommended: false,
          isPopular: false,
          maxTokens: 128000,
          contextLength: 128000,
        },
        {
          id: "qwen-math-plus",
          name: "qwen-math-plus",
          displayName: "通义千问 Math Plus",
          description: "通义千问数学专用模型，专门优化数学推理和计算",
          capabilities: ["math", "reasoning", "calculation", "analysis"],
          tags: ["阿里云", "通义千问", "数学", "推理", "计算"],
          providerId: "qwen",
          isRecommended: false,
          isPopular: false,
          maxTokens: 4000,
          contextLength: 4000,
        },
      ];

      console.log("获取通义千问模型列表成功，模型数量:", models.length);
      return models;
    } catch (error) {
      console.error("获取通义千问模型列表失败:", error);
      return [];
    }
  }

  // ==================== Together AI相关方法 ====================

  /**
   * 验证Together AI API密钥格式
   * @param apiKey API密钥
   * @returns {isValid: boolean, error?: string}
   */
  private validateTogetherApiKey(apiKey: string): {
    isValid: boolean;
    error?: string;
  } {
    if (!apiKey || !apiKey.trim()) {
      return {
        isValid: false,
        error: "API密钥不能为空",
      };
    }

    // Together AI密钥通常以特定格式开头
    if (apiKey.length < 20) {
      return {
        isValid: false,
        error: "API密钥长度不足，请检查密钥是否完整",
      };
    }

    return { isValid: true };
  }

  /**
   * 测试Together AI连接
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testTogetherConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 验证API密钥格式
      const validation = this.validateTogetherApiKey(apiKey);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      console.log("开始测试Together AI连接...");

      // 测试连接并获取模型列表
      const response = await fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        // 详细的错误处理
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        if (response.status === 401) {
          errorMessage = "API密钥无效或已过期，请检查密钥是否正确";
        } else if (response.status === 403) {
          errorMessage = "API密钥权限不足，请检查密钥权限设置";
        } else if (response.status === 429) {
          errorMessage = "API请求频率超限，请稍后重试";
        } else if (response.status >= 500) {
          errorMessage = "Together AI服务暂时不可用，请稍后重试";
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      console.log(`Together AI连接成功，发现${modelCount}个模型`);

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      console.error("Together AI连接测试失败:", error);

      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接或稍后重试";
      } else if (error.message.includes("fetch")) {
        errorMessage = "无法连接到Together AI服务，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取Together AI模型列表
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @returns Promise<AIModel[]>
   */
  async getTogetherModels(baseUrl: string, apiKey: string): Promise<AIModel[]> {
    try {
      // 验证API密钥
      const validation = this.validateTogetherApiKey(apiKey);
      if (!validation.isValid) {
        console.error("Together AI API密钥验证失败:", validation.error);
        return [];
      }

      console.log("开始获取Together AI模型列表...");

      const response = await fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(20000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const models: TogetherModel[] = data.data || [];

      console.log(`成功获取${models.length}个Together AI模型`);

      return models
        .map((model) => {
          // 解析模型信息
          const displayName = this.formatTogetherModelName(model);
          const description = this.generateTogetherModelDescription(model);
          const capabilities = this.determineTogetherCapabilities(model);
          const modelTags = this.extractTogetherModelTags(model);
          const pricing = this.formatTogetherPricing(model);

          return {
            id: model.id,
            name: model.id,
            displayName,
            description,
            size: this.extractModelSizeFromId(model.id),
            parameters: model.context_length
              ? `${model.context_length} tokens`
              : undefined,
            tags: modelTags,
            capabilities,
            providerId: "together",
            isRecommended: this.isTogetherModelRecommended(model),
            isPopular: this.isTogetherModelPopular(model),
            maxTokens: model.context_length,
            contextLength: model.context_length,
            supportedFormats: ["chat", "completion"],
            // 添加定价信息到描述中
            ...(pricing && {
              description: `${description}\n\n💰 定价: ${pricing}`,
            }),
          };
        })
        .sort((a, b) => {
          // 按推荐度、热门度和名称排序
          if (a.isRecommended && !b.isRecommended) return -1;
          if (!a.isRecommended && b.isRecommended) return 1;
          if (a.isPopular && !b.isPopular) return -1;
          if (!a.isPopular && b.isPopular) return 1;
          return a.displayName.localeCompare(b.displayName);
        });
    } catch (error) {
      console.error("获取Together AI模型列表失败:", error);

      // 提供更详细的错误信息
      if (error.name === "AbortError") {
        console.error("获取模型列表超时，Together AI服务可能响应缓慢");
      } else if (error.message.includes("HTTP 401")) {
        console.error("API密钥无效，请检查Together AI API密钥");
      } else if (error.message.includes("HTTP 403")) {
        console.error("API密钥权限不足，请检查密钥权限设置");
      } else if (error.message.includes("HTTP")) {
        console.error("Together AI API返回错误:", error.message);
      } else {
        console.error("网络连接错误:", error.message);
      }

      return [];
    }
  }

  /**
   * 格式化Together AI模型名称
   * @param model Together AI模型信息
   * @returns string
   */
  private formatTogetherModelName(model: TogetherModel): string {
    // 如果有显示名称，优先使用
    if (model.display_name) {
      return model.display_name;
    }

    // 解析模型ID，提取有用信息
    const modelId = model.id;

    // 常见模型名称映射
    const nameMap: Record<string, string> = {
      "meta-llama/Llama-2": "Llama 2",
      "meta-llama/Llama-3": "Llama 3",
      "mistralai/Mistral": "Mistral",
      "mistralai/Mixtral": "Mixtral",
      "togethercomputer/RedPajama": "RedPajama",
      "NousResearch/Nous-Hermes": "Nous Hermes",
      "WizardLM/WizardCoder": "WizardCoder",
      "codellama/CodeLlama": "Code Llama",
      "Qwen/Qwen": "Qwen",
    };

    // 尝试匹配已知模型
    for (const [key, value] of Object.entries(nameMap)) {
      if (modelId.includes(key)) {
        // 提取版本信息
        const versionMatch = modelId.match(/(\d+(?:\.\d+)?[bB]?)/g);
        const version = versionMatch
          ? versionMatch[versionMatch.length - 1]
          : "";
        return version ? `${value} ${version}` : value;
      }
    }

    // 格式化模型ID为更友好的显示名称
    return (
      modelId
        .split("/")
        .pop()
        ?.replace(/-/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase()) || modelId
    );
  }

  /**
   * 生成Together AI模型描述
   * @param model Together AI模型信息
   * @returns string
   */
  private generateTogetherModelDescription(model: TogetherModel): string {
    let description = model.description || "";

    // 如果没有描述，根据模型ID生成
    if (!description) {
      const modelId = model.id.toLowerCase();

      if (modelId.includes("llama-2")) {
        description = "Meta开发的Llama 2大型语言模型，开源且性能优秀";
      } else if (modelId.includes("llama-3")) {
        description = "Meta最新的Llama 3模型，性能更强，支持更长上下文";
      } else if (modelId.includes("mistral")) {
        description = "Mistral AI开发的高效语言模型";
      } else if (modelId.includes("mixtral")) {
        description = "Mistral AI的混合专家模型，性能卓越";
      } else if (modelId.includes("codellama") || modelId.includes("code")) {
        description = "专门优化代码生成和编程任务的语言模型";
      } else if (modelId.includes("qwen")) {
        description = "阿里巴巴开发的通义千问模型";
      } else if (modelId.includes("wizard")) {
        description = "经过指令微调的高性能语言模型";
      } else {
        description = "通过Together AI提供的开源语言模型";
      }
    }

    // 添加上下文长度信息
    const contextInfo = model.context_length
      ? `支持${model.context_length.toLocaleString()}个token的上下文`
      : "上下文长度未知";

    // 添加许可证信息
    const licenseInfo = model.license
      ? `开源许可: ${model.license}`
      : "开源模型";

    return `${description} (${licenseInfo}, ${contextInfo})`;
  }

  /**
   * 确定Together AI模型能力
   * @param model Together AI模型信息
   * @returns string[]
   */
  private determineTogetherCapabilities(model: TogetherModel): string[] {
    const capabilities = ["chat", "completion"];
    const modelId = model.id.toLowerCase();

    // 根据模型特性添加能力
    if (modelId.includes("code") || modelId.includes("coder")) {
      capabilities.push("coding");
    }

    if (modelId.includes("instruct") || modelId.includes("chat")) {
      capabilities.push("instruction-following");
    }

    // 根据上下文长度判断长文本处理能力
    if (model.context_length && model.context_length >= 32000) {
      capabilities.push("long-context");
    }

    // 根据模型类型添加特定能力
    if (modelId.includes("wizard")) {
      capabilities.push("reasoning");
    }

    return capabilities;
  }

  /**
   * 提取Together AI模型标签
   * @param model Together AI模型信息
   * @returns string[]
   */
  private extractTogetherModelTags(model: TogetherModel): string[] {
    const tags = ["Together AI", "开源"];

    // 添加提供商标签
    const provider = model.id.split("/")[0];
    if (provider) {
      tags.push(provider);
    }

    // 添加模型类型标签
    if (model.type) {
      tags.push(model.type);
    }

    // 添加许可证标签
    if (model.license) {
      tags.push(model.license);
    }

    // 根据上下文长度添加标签
    if (model.context_length) {
      if (model.context_length >= 32000) {
        tags.push("长上下文");
      }
    }

    // 根据模型名称添加特定标签
    const modelId = model.id.toLowerCase();
    if (modelId.includes("instruct")) {
      tags.push("指令优化");
    }

    if (modelId.includes("chat")) {
      tags.push("对话优化");
    }

    if (modelId.includes("code")) {
      tags.push("代码生成");
    }

    return tags.filter(Boolean);
  }

  /**
   * 格式化Together AI定价信息
   * @param model Together AI模型信息
   * @returns string | null
   */
  private formatTogetherPricing(model: TogetherModel): string | null {
    if (!model.pricing) return null;

    const inputPrice = model.pricing.input;
    const outputPrice = model.pricing.output;

    if (typeof inputPrice !== "number" || typeof outputPrice !== "number")
      return null;

    // 转换为更友好的格式（每1M token的价格）
    const inputPricePerM = (inputPrice * 1000000).toFixed(3);
    const outputPricePerM = (outputPrice * 1000000).toFixed(3);

    return `输入: $${inputPricePerM}/1M tokens, 输出: $${outputPricePerM}/1M tokens`;
  }

  /**
   * 从模型ID中提取模型大小
   * @param modelId 模型ID
   * @returns string | undefined
   */
  private extractModelSizeFromId(modelId: string): string | undefined {
    // 尝试从模型ID中提取参数大小
    const sizeMatch = modelId.match(/(\d+(?:\.\d+)?)[bB]/i);
    if (sizeMatch) {
      return `${sizeMatch[1]}B`;
    }

    // 尝试匹配其他格式
    const altSizeMatch = modelId.match(/(\d+)b/i);
    if (altSizeMatch) {
      return `${altSizeMatch[1]}B`;
    }

    return undefined;
  }

  /**
   * 判断是否为推荐的Together AI模型
   * @param model Together AI模型信息
   * @returns boolean
   */
  private isTogetherModelRecommended(model: TogetherModel): boolean {
    const recommendedModels = [
      "llama-2-70b",
      "llama-3",
      "mixtral-8x7b",
      "codellama",
      "qwen",
    ];

    return recommendedModels.some((recommended) =>
      model.id.toLowerCase().includes(recommended)
    );
  }

  /**
   * 判断是否为热门的Together AI模型
   * @param model Together AI模型信息
   * @returns boolean
   */
  private isTogetherModelPopular(model: TogetherModel): boolean {
    const popularModels = [
      "llama-2-7b",
      "llama-2-13b",
      "mistral-7b",
      "codellama-7b",
    ];

    return popularModels.some((popular) =>
      model.id.toLowerCase().includes(popular)
    );
  }

  /**
   * 测试Grok连接
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testGrokConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 验证API密钥格式
      if (!apiKey || !apiKey.trim()) {
        throw new Error("API密钥不能为空");
      }

      // xAI Grok API密钥通常以xai-开头
      if (!apiKey.startsWith("xai-")) {
        throw new Error('无效的Grok API密钥格式，应以"xai-"开头');
      }

      console.log("开始测试Grok连接...");

      // 使用模型列表API来测试连接
      const response = await fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "User-Agent": "BookmarkExtension/1.0",
        },
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        // 处理常见的错误状态码
        switch (response.status) {
          case 400:
            throw new Error("请求参数无效");
          case 401:
            throw new Error("API密钥无效或已过期");
          case 403:
            throw new Error("API密钥权限不足或服务被禁用");
          case 429:
            throw new Error("API请求频率限制，请稍后重试");
          case 500:
            throw new Error("Grok服务器内部错误");
          case 503:
            throw new Error("Grok服务暂时不可用");
          default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      // 验证响应格式
      const data = await response.json();
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error("Grok API响应格式异常");
      }

      const modelCount = data.data.length;

      console.log(`Grok连接测试成功，发现 ${modelCount} 个模型`);

      return {
        success: true,
        modelCount,
      };
    } catch (error) {
      console.error("Grok连接测试失败:", error);

      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接或API服务状态";
      } else if (error.message.includes("fetch")) {
        errorMessage = "网络连接失败，请检查网络设置";
      } else if (error.message.includes("ENOTFOUND")) {
        errorMessage = "无法解析API域名，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取Grok模型列表
   * @returns Promise<AIModel[]>
   */
  async getGrokModels(): Promise<AIModel[]> {
    try {
      console.log("获取Grok模型列表...");

      // xAI Grok目前主要有几个预定义的模型
      // 由于Grok API可能不提供公开的模型列表端点，我们使用预定义列表
      const grokModels = [
        {
          id: "grok-beta",
          name: "grok-beta",
          displayName: "Grok Beta",
          description: "xAI开发的Grok模型测试版本，具有强大的推理和对话能力",
          capabilities: ["chat", "reasoning", "real-time-info"],
          tags: ["xAI", "Grok", "Beta版本", "实时信息"],
          providerId: "grok",
          isRecommended: true,
          isPopular: true,
          maxTokens: 4096,
          contextLength: 8192,
          supportedFormats: ["text", "json"],
        },
        {
          id: "grok-1",
          name: "grok-1",
          displayName: "Grok-1",
          description: "xAI的第一代Grok模型，专注于真实性和有用性",
          capabilities: ["chat", "reasoning", "real-time-info"],
          tags: ["xAI", "Grok", "第一代", "真实性"],
          providerId: "grok",
          isRecommended: true,
          isPopular: true,
          maxTokens: 4096,
          contextLength: 8192,
          supportedFormats: ["text", "json"],
        },
        {
          id: "grok-1.5",
          name: "grok-1.5",
          displayName: "Grok-1.5",
          description: "xAI改进的Grok模型，提升了推理能力和响应质量",
          capabilities: [
            "chat",
            "reasoning",
            "real-time-info",
            "improved-logic",
          ],
          tags: ["xAI", "Grok", "改进版本", "增强推理"],
          providerId: "grok",
          isRecommended: true,
          isPopular: true,
          maxTokens: 4096,
          contextLength: 16384,
          supportedFormats: ["text", "json"],
        },
      ];

      // 按推荐度和版本排序
      const sortedModels = grokModels.sort((a, b) => {
        // 推荐模型优先
        if (a.isRecommended && !b.isRecommended) return -1;
        if (!a.isRecommended && b.isRecommended) return 1;

        // 按版本号排序（1.5 > 1 > beta）
        const versionOrder = ["grok-1.5", "grok-1", "grok-beta"];
        const aIndex = versionOrder.findIndex((v) => a.id.includes(v));
        const bIndex = versionOrder.findIndex((v) => b.id.includes(v));

        if (aIndex !== -1 && bIndex !== -1) {
          return aIndex - bIndex;
        }

        // 按名称排序
        return a.name.localeCompare(b.name);
      });

      console.log(`Grok模型列表获取成功: ${sortedModels.length} 个模型`);
      return sortedModels;
    } catch (error) {
      console.error("获取Grok模型列表失败:", error);
      return [];
    }
  }

  /**
   * 验证Grok模型是否可用
   * @param baseUrl API基础URL
   * @param apiKey API密钥
   * @param modelId 模型ID
   * @returns Promise<boolean>
   */
  async validateGrokModel(
    baseUrl: string,
    apiKey: string,
    modelId: string
  ): Promise<boolean> {
    try {
      // 通过发送测试消息来验证模型可用性
      const response = await fetch(`${baseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "User-Agent": "BookmarkExtension/1.0",
        },
        body: JSON.stringify({
          model: modelId,
          messages: [
            {
              role: "user",
              content: "Hello",
            },
          ],
          max_tokens: 1,
        }),
        signal: AbortSignal.timeout(10000),
      });

      return response.ok;
    } catch (error) {
      console.error(`Grok模型 ${modelId} 验证失败:`, error);
      return false;
    }
  }

  /**
   * 获取Grok模型的详细信息
   * @param modelId 模型ID
   * @returns Grok模型详细信息
   */
  private getGrokModelDetails(modelId: string): {
    displayName: string;
    description: string;
    capabilities: string[];
    tags: string[];
    contextLength: number;
    maxTokens: number;
  } {
    const modelDetails: Record<string, any> = {
      "grok-beta": {
        displayName: "Grok Beta",
        description: "xAI开发的Grok模型测试版本，具有强大的推理和对话能力",
        capabilities: ["chat", "reasoning", "real-time-info"],
        tags: ["xAI", "Grok", "Beta版本", "实时信息"],
        contextLength: 8192,
        maxTokens: 4096,
      },
      "grok-1": {
        displayName: "Grok-1",
        description: "xAI的第一代Grok模型，专注于真实性和有用性",
        capabilities: ["chat", "reasoning", "real-time-info"],
        tags: ["xAI", "Grok", "第一代", "真实性"],
        contextLength: 8192,
        maxTokens: 4096,
      },
      "grok-1.5": {
        displayName: "Grok-1.5",
        description: "xAI改进的Grok模型，提升了推理能力和响应质量",
        capabilities: ["chat", "reasoning", "real-time-info", "improved-logic"],
        tags: ["xAI", "Grok", "改进版本", "增强推理"],
        contextLength: 16384,
        maxTokens: 4096,
      },
    };

    return (
      modelDetails[modelId] || {
        displayName: modelId,
        description: "xAI Grok模型",
        capabilities: ["chat"],
        tags: ["xAI", "Grok"],
        contextLength: 8192,
        maxTokens: 4096,
      }
    );
  }

  /**
   * 判断是否为推荐的Grok模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isGrokModelRecommended(modelId: string): boolean {
    // 所有Grok模型都是推荐的，因为数量较少
    return true;
  }

  /**
   * 判断是否为热门的Grok模型
   * @param modelId 模型ID
   * @returns boolean
   */
  private isGrokModelPopular(modelId: string): boolean {
    const popularModels = ["grok-1.5", "grok-1"];
    return popularModels.some((popular) => modelId.includes(popular));
  }

  // ==================== 自定义API相关方法 ====================

  /**
   * 测试自定义API连接
   * @param config 自定义API配置
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testCustomAPI(
    config: CustomAPIConfig
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
        ...config.headers,
      };

      if (config.apiKey) {
        headers["Authorization"] = `Bearer ${config.apiKey}`;
      }

      // 尝试发送一个简单的请求
      const response = await fetch(`${config.baseUrl}/chat/completions`, {
        method: "POST",
        headers,
        body: JSON.stringify({
          model: "test",
          messages: [
            {
              role: "user",
              content: "Hello",
            },
          ],
          max_tokens: 10,
        }),
        signal: AbortSignal.timeout(config.timeout || 10000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return {
        success: true,
        modelCount: 1,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 获取自定义API模型列表
   * @param config 提供商配置
   * @returns Promise<AIModel[]>
   */
  async getCustomModels(config: AIProviderConfig): Promise<AIModel[]> {
    // 自定义API通常只返回配置中指定的模型
    return [
      {
        id: "custom-model",
        name: "custom-model",
        displayName: "自定义模型",
        description: "自定义API模型",
        capabilities: ["chat", "completion"],
        providerId: config.id,
        isRecommended: false,
        isPopular: false,
      },
    ];
  }

  // ==================== 本地服务发现和适配器方法 ====================

  /**
   * 发现本地AI服务
   * @param customPorts 自定义端口列表
   * @returns Promise<LocalServiceConfig[]>
   */
  async discoverLocalServices(
    customPorts: number[] = []
  ): Promise<LocalServiceConfig[]> {
    try {
      const result = await localAIServiceAdapter.discoverLocalServices(
        customPorts
      );
      console.log(`发现 ${result.services.length} 个本地AI服务`);

      if (result.errors.length > 0) {
        console.warn("服务发现过程中的错误:", result.errors);
      }

      return result.services;
    } catch (error) {
      console.error("本地服务发现失败:", error);
      return [];
    }
  }

  /**
   * 测试本地服务连接
   * @param config 本地服务配置
   * @returns Promise<AIConnectionResult>
   */
  async testLocalServiceConnection(
    config: LocalServiceConfig
  ): Promise<AIConnectionResult> {
    return await localAIServiceAdapter.testLocalServiceConnection(config);
  }

  /**
   * 获取本地服务模型列表
   * @param config 本地服务配置
   * @returns Promise<AIModel[]>
   */
  async getLocalServiceModels(config: LocalServiceConfig): Promise<AIModel[]> {
    return await localAIServiceAdapter.getLocalServiceModels(config);
  }

  /**
   * 创建自定义本地服务配置
   * @param name 服务名称
   * @param baseUrl 基础URL
   * @param options 可选配置
   * @returns LocalServiceConfig
   */
  createCustomLocalService(
    name: string,
    baseUrl: string,
    options: Partial<LocalServiceConfig> = {}
  ): LocalServiceConfig {
    return localAIServiceAdapter.createCustomServiceConfig(
      name,
      baseUrl,
      options
    );
  }

  /**
   * 获取默认本地服务配置
   * @returns LocalServiceConfig[]
   */
  getDefaultLocalServices(): LocalServiceConfig[] {
    return localAIServiceAdapter.getDefaultServices();
  }

  // ==================== Azure OpenAI相关方法 ====================

  /**
   * 测试Azure OpenAI连接
   * @param baseUrl Azure OpenAI端点URL
   * @param apiKey Azure OpenAI API密钥
   * @returns Promise<{success: boolean, modelCount?: number, error?: string}>
   */
  async testAzureOpenAIConnection(
    baseUrl: string,
    apiKey: string
  ): Promise<{ success: boolean; modelCount?: number; error?: string }> {
    try {
      // 验证API密钥格式
      if (!apiKey || !apiKey.trim()) {
        throw new Error("API密钥不能为空");
      }

      // Azure OpenAI API密钥通常是32位十六进制字符串
      if (apiKey.length !== 32 || !/^[a-f0-9]{32}$/i.test(apiKey)) {
        throw new Error(
          "无效的Azure OpenAI API密钥格式，应为32位十六进制字符串"
        );
      }

      // 验证Azure OpenAI端点URL格式
      if (!baseUrl.includes(".openai.azure.com")) {
        throw new Error('无效的Azure OpenAI端点URL，应包含".openai.azure.com"');
      }

      console.log("开始测试Azure OpenAI连接...");

      // Azure OpenAI使用不同的API路径和版本
      const apiVersion = "2024-02-01";
      const deploymentsUrl = `${baseUrl}/openai/deployments?api-version=${apiVersion}`;

      const response = await fetch(deploymentsUrl, {
        method: "GET",
        headers: {
          "api-key": apiKey,
          "Content-Type": "application/json",
          "User-Agent": "BookmarkExtension/1.0",
        },
        signal: AbortSignal.timeout(15000),
      });

      if (!response.ok) {
        // 处理常见的错误状态码
        switch (response.status) {
          case 401:
            throw new Error("API密钥无效或已过期");
          case 403:
            throw new Error("API密钥权限不足或资源访问被拒绝");
          case 404:
            throw new Error("Azure OpenAI端点不存在或配置错误");
          case 429:
            throw new Error("API请求频率限制，请稍后重试");
          case 500:
            throw new Error("Azure OpenAI服务器内部错误");
          case 503:
            throw new Error("Azure OpenAI服务暂时不可用");
          default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      // 验证响应格式
      const data = await response.json();
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error("Azure OpenAI API响应格式异常");
      }

      const deploymentCount = data.data.length;

      console.log(`Azure OpenAI连接测试成功，发现 ${deploymentCount} 个部署`);

      return {
        success: true,
        modelCount: deploymentCount,
      };
    } catch (error) {
      console.error("Azure OpenAI连接测试失败:", error);

      // 详细的错误处理
      let errorMessage = error.message;

      if (error.name === "AbortError") {
        errorMessage = "连接超时，请检查网络连接或API服务状态";
      } else if (error.message.includes("fetch")) {
        errorMessage = "网络连接失败，请检查网络设置";
      } else if (error.message.includes("ENOTFOUND")) {
        errorMessage = "无法解析API域名，请检查网络连接";
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取Azure OpenAI部署列表
   * @param baseUrl Azure OpenAI端点URL
   * @param apiKey Azure OpenAI API密钥
   * @returns Promise<AIModel[]>
   */
  async getAzureOpenAIModels(
    baseUrl: string,
    apiKey: string
  ): Promise<AIModel[]> {
    try {
      console.log("开始获取Azure OpenAI部署列表...");

      const apiVersion = "2024-02-01";
      const deploymentsUrl = `${baseUrl}/openai/deployments?api-version=${apiVersion}`;

      const response = await fetch(deploymentsUrl, {
        method: "GET",
        headers: {
          "api-key": apiKey,
          "Content-Type": "application/json",
          "User-Agent": "BookmarkExtension/1.0",
        },
        signal: AbortSignal.timeout(20000),
      });

      if (!response.ok) {
        // 处理错误状态码
        switch (response.status) {
          case 401:
            throw new Error("API密钥无效或已过期");
          case 403:
            throw new Error("API密钥权限不足，无法访问部署列表");
          case 404:
            throw new Error("Azure OpenAI端点不存在或配置错误");
          case 429:
            throw new Error("API请求频率限制，请稍后重试");
          default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      const data = await response.json();
      const deployments = data.data || [];

      console.log(`获取到 ${deployments.length} 个Azure OpenAI部署`);

      // 处理部署列表
      const processedModels = deployments
        .filter((deployment: any) => deployment.status === "succeeded")
        .map((deployment: any) => {
          const modelName = deployment.model;
          const deploymentName = deployment.id;
          const displayName = this.formatAzureOpenAIModelName(
            modelName,
            deploymentName
          );
          const description = this.generateAzureOpenAIModelDescription(
            modelName,
            deployment
          );
          const capabilities = this.determineAzureOpenAICapabilities(modelName);
          const modelTags = this.extractAzureOpenAIModelTags(
            modelName,
            deployment
          );
          const contextLength =
            this.getAzureOpenAIModelContextLength(modelName);

          return {
            id: deploymentName, // 使用部署名称作为ID
            name: modelName, // 原始模型名称
            displayName,
            description,
            capabilities,
            tags: modelTags,
            providerId: "azure-openai",
            isRecommended: this.isAzureOpenAIModelRecommended(modelName),
            isPopular: this.isAzureOpenAIModelPopular(modelName),
            maxTokens: this.getAzureOpenAIModelMaxTokens(modelName),
            contextLength,
            supportedFormats: ["text", "json"],
            // Azure特有的属性
            deploymentName,
            azureModel: modelName,
            status: deployment.status,
            createdAt: deployment.created_at
              ? new Date(deployment.created_at * 1000)
              : undefined,
          };
        })
        .sort((a, b) => {
          // 按推荐度、受欢迎程度和名称排序
          if (a.isRecommended && !b.isRecommended) return -1;
          if (!a.isRecommended && b.isRecommended) return 1;
          if (a.isPopular && !b.isPopular) return -1;
          if (!a.isPopular && b.isPopular) return 1;
          return a.name.localeCompare(b.name);
        });

      console.log(`处理后的Azure OpenAI模型数量: ${processedModels.length}`);
      return processedModels;
    } catch (error) {
      console.error("获取Azure OpenAI部署列表失败:", error);

      // 提供更详细的错误信息
      if (error.name === "AbortError") {
        console.error("获取部署列表超时，Azure OpenAI API可能响应缓慢");
      } else if (error.message.includes("HTTP")) {
        console.error("Azure OpenAI API返回错误:", error.message);
      } else {
        console.error("网络连接错误:", error.message);
      }

      return [];
    }
  }

  /**
   * 格式化Azure OpenAI模型名称
   * @param modelName 原始模型名称
   * @param deploymentName 部署名称
   * @returns string
   */
  private formatAzureOpenAIModelName(
    modelName: string,
    deploymentName: string
  ): string {
    // 基础模型名称映射
    const nameMap: Record<string, string> = {
      "gpt-4": "GPT-4",
      "gpt-4-32k": "GPT-4 32K",
      "gpt-4-turbo": "GPT-4 Turbo",
      "gpt-4-turbo-preview": "GPT-4 Turbo Preview",
      "gpt-35-turbo": "GPT-3.5 Turbo",
      "gpt-35-turbo-16k": "GPT-3.5 Turbo 16K",
      "gpt-35-turbo-instruct": "GPT-3.5 Turbo Instruct",
      "text-davinci-003": "Davinci 003",
      "text-embedding-ada-002": "Text Embedding Ada 002",
      "text-embedding-3-small": "Text Embedding 3 Small",
      "text-embedding-3-large": "Text Embedding 3 Large",
      "dall-e-2": "DALL-E 2",
      "dall-e-3": "DALL-E 3",
    };

    const baseName = nameMap[modelName] || modelName;

    // 如果部署名称与模型名称不同，显示部署名称
    if (deploymentName !== modelName) {
      return `${baseName} (${deploymentName})`;
    }

    return baseName;
  }

  /**
   * 生成Azure OpenAI模型描述
   * @param modelName 模型名称
   * @param deployment 部署信息
   * @returns string
   */
  private generateAzureOpenAIModelDescription(
    modelName: string,
    deployment: any
  ): string {
    const descriptions: Record<string, string> = {
      "gpt-4-turbo": "Azure部署的GPT-4 Turbo模型，性能强大，成本优化",
      "gpt-4": "Azure部署的GPT-4模型，适合复杂任务",
      "gpt-4-32k": "Azure部署的GPT-4 32K模型，支持长上下文",
      "gpt-35-turbo": "Azure部署的GPT-3.5 Turbo模型，快速高效",
      "gpt-35-turbo-16k": "Azure部署的GPT-3.5 Turbo 16K模型，支持长上下文",
      "gpt-35-turbo-instruct": "Azure部署的GPT-3.5 Turbo指令模型",
      "text-davinci-003": "Azure部署的Davinci 003文本生成模型",
      "text-embedding-ada-002": "Azure部署的Ada 002文本嵌入模型",
      "text-embedding-3-small": "Azure部署的Text Embedding 3 Small模型",
      "text-embedding-3-large": "Azure部署的Text Embedding 3 Large模型",
      "dall-e-2": "Azure部署的DALL-E 2图像生成模型",
      "dall-e-3": "Azure部署的DALL-E 3图像生成模型",
    };

    // 按优先级匹配描述
    for (const [key, desc] of Object.entries(descriptions)) {
      if (modelName.includes(key)) {
        return desc;
      }
    }

    // 根据模型类型生成通用描述
    if (modelName.includes("gpt-4")) {
      return "Azure部署的GPT-4系列模型，性能强大";
    } else if (modelName.includes("gpt-35") || modelName.includes("gpt-3.5")) {
      return "Azure部署的GPT-3.5系列模型，快速高效";
    } else if (modelName.includes("embedding")) {
      return "Azure部署的文本嵌入模型";
    } else if (modelName.includes("dall-e")) {
      return "Azure部署的图像生成模型";
    }

    return `Azure部署的${modelName}模型`;
  }

  /**
   * 确定Azure OpenAI模型能力
   * @param modelName 模型名称
   * @returns string[]
   */
  private determineAzureOpenAICapabilities(modelName: string): string[] {
    const capabilities = [];

    // 基础能力
    if (modelName.includes("gpt-") || modelName.includes("davinci")) {
      capabilities.push("chat", "completion");
    }

    // 代码能力
    if (modelName.includes("gpt-4")) {
      capabilities.push("coding");
    }

    // 指令跟随
    if (
      modelName.includes("turbo") ||
      modelName.includes("instruct") ||
      modelName.includes("gpt-4")
    ) {
      capabilities.push("instruction-following");
    }

    // 嵌入能力
    if (modelName.includes("embedding")) {
      capabilities.push("embedding");
    }

    // 图像能力
    if (modelName.includes("dall-e")) {
      capabilities.push("image-generation");
    }

    // 函数调用
    if (modelName.includes("gpt-4") || modelName.includes("gpt-35-turbo")) {
      capabilities.push("function-calling");
    }

    return capabilities;
  }

  /**
   * 提取Azure OpenAI模型标签
   * @param modelName 模型名称
   * @param deployment 部署信息
   * @returns string[]
   */
  private extractAzureOpenAIModelTags(
    modelName: string,
    deployment: any
  ): string[] {
    const tags = ["Azure OpenAI"];

    // 模型系列标签
    if (modelName.includes("gpt-4")) {
      tags.push("GPT-4");
    } else if (modelName.includes("gpt-35") || modelName.includes("gpt-3.5")) {
      tags.push("GPT-3.5");
    }

    // 特性标签
    if (modelName.includes("turbo")) {
      tags.push("Turbo");
    }

    if (modelName.includes("32k") || modelName.includes("16k")) {
      tags.push("长上下文");
      if (modelName.includes("32k")) {
        tags.push("32K");
      } else if (modelName.includes("16k")) {
        tags.push("16K");
      }
    }

    if (modelName.includes("instruct")) {
      tags.push("指令优化");
    }

    if (modelName.includes("preview")) {
      tags.push("预览版");
    }

    // 功能标签
    if (modelName.includes("embedding")) {
      tags.push("文本嵌入");
    }

    if (modelName.includes("dall-e")) {
      tags.push("图像生成");
    }

    // 部署状态
    if (deployment.status) {
      tags.push(`状态: ${deployment.status}`);
    }

    return tags;
  }

  /**
   * 获取Azure OpenAI模型上下文长度
   * @param modelName 模型名称
   * @returns number
   */
  private getAzureOpenAIModelContextLength(modelName: string): number {
    const contextLengths: Record<string, number> = {
      "gpt-4-turbo": 128000,
      "gpt-4-32k": 32768,
      "gpt-4": 8192,
      "gpt-35-turbo-16k": 16384,
      "gpt-35-turbo": 4096,
      "gpt-35-turbo-instruct": 4096,
      "text-davinci-003": 4097,
    };

    // 按优先级匹配
    for (const [key, length] of Object.entries(contextLengths)) {
      if (modelName.includes(key)) {
        return length;
      }
    }

    // 默认值
    if (modelName.includes("gpt-4")) {
      return 8192;
    } else if (modelName.includes("gpt-35") || modelName.includes("gpt-3.5")) {
      return 4096;
    }

    return 2048;
  }

  /**
   * 获取Azure OpenAI模型最大输出tokens
   * @param modelName 模型名称
   * @returns number
   */
  private getAzureOpenAIModelMaxTokens(modelName: string): number {
    const maxTokens: Record<string, number> = {
      "gpt-4-turbo": 4096,
      "gpt-4": 4096,
      "gpt-35-turbo": 4096,
      "text-davinci-003": 4097,
    };

    // 按优先级匹配
    for (const [key, tokens] of Object.entries(maxTokens)) {
      if (modelName.includes(key)) {
        return tokens;
      }
    }

    // 默认值
    if (modelName.includes("gpt-4") || modelName.includes("gpt-35")) {
      return 4096;
    }

    return 2048;
  }

  /**
   * 判断是否为推荐的Azure OpenAI模型
   * @param modelName 模型名称
   * @returns boolean
   */
  private isAzureOpenAIModelRecommended(modelName: string): boolean {
    const recommendedModels = ["gpt-4-turbo", "gpt-4", "gpt-35-turbo"];
    return recommendedModels.some((recommended) =>
      modelName.includes(recommended)
    );
  }

  /**
   * 判断是否为热门的Azure OpenAI模型
   * @param modelName 模型名称
   * @returns boolean
   */
  private isAzureOpenAIModelPopular(modelName: string): boolean {
    const popularModels = [
      "gpt-4-turbo",
      "gpt-4",
      "gpt-35-turbo",
      "text-davinci-003",
    ];
    return popularModels.some((popular) => modelName.includes(popular));
  }

  // ==================== AI文本生成方法 ====================

  /**
   * 生成AI文本
   * @param request 生成请求
   * @returns Promise<{success: boolean, content?: string, error?: string}>
   */
  async generateText(request: {
    providerId: string;
    modelId: string;
    messages: Array<{ role: string; content: string }>;
    maxTokens?: number;
    temperature?: number;
  }): Promise<{ success: boolean; content?: string; error?: string }> {
    try {
      console.log("开始AI文本生成:", request.providerId, request.modelId);

      // 获取提供商配置
      const providers = await this.getConfiguredProviders();
      console.log("aiProviderService获取到的提供商列表:", providers.map(p => ({ id: p.id, name: p.name, enabled: p.enabled })));

      const provider = providers.find((p) => p.id === request.providerId);
      console.log("查找提供商结果:", provider ? `找到: ${provider.name}` : `未找到: ${request.providerId}`);

      if (!provider) {
        console.error("可用的提供商ID列表:", providers.map(p => p.id));
        throw new Error(`提供商不存在: ${request.providerId}`);
      }

      if (!provider.enabled) {
        throw new Error(`提供商已禁用: ${request.providerId}`);
      }

      // 根据提供商类型调用相应的API
      const response = await this.callProviderAPI(provider, request);

      console.log("AI文本生成成功");
      return {
        success: true,
        content: response,
      };
    } catch (error) {
      console.error("AI文本生成失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "未知错误",
      };
    }
  }

  /**
   * 获取已配置的提供商列表
   * @returns Promise<AIProviderConfig[]>
   */
  private async getConfiguredProviders(): Promise<AIProviderConfig[]> {
    try {
      // 使用与aiIntegrationService相同的存储访问方法
      const providers = await ChromeStorageService.getSyncSetting('ai_providers', [])
      return providers.map((provider: any) => ({
        ...provider,
        createdAt: new Date(provider.createdAt),
        updatedAt: new Date(provider.updatedAt)
      }))
    } catch (error) {
      console.error("获取提供商配置失败:", error);
      return [];
    }
  }

  /**
   * 调用提供商API
   * @param provider 提供商配置
   * @param request 请求参数
   * @returns Promise<string>
   */
  private async callProviderAPI(
    provider: AIProviderConfig,
    request: {
      modelId: string;
      messages: Array<{ role: string; content: string }>;
      maxTokens?: number;
      temperature?: number;
    }
  ): Promise<string> {
    const { modelId, messages, maxTokens = 1000, temperature = 0.7 } = request;

    // 构建请求头
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // 构建请求体和URL
    let url: string;
    let requestBody: any;

    switch (provider.type) {
      case "openai":
      case "custom":
        url = `${provider.baseUrl}/chat/completions`;
        headers["Authorization"] = `Bearer ${provider.apiKey}`;
        requestBody = {
          model: modelId,
          messages: messages,
          max_tokens: maxTokens,
          temperature: temperature,
        };
        break;

      case "claude":
        url = `${provider.baseUrl}/messages`;
        headers["x-api-key"] = provider.apiKey || "";
        headers["anthropic-version"] = "2023-06-01";
        requestBody = {
          model: modelId,
          messages: messages,
          max_tokens: maxTokens,
          temperature: temperature,
        };
        break;

      case "gemini":
        url = `${provider.baseUrl}/models/${modelId}:generateContent?key=${provider.apiKey}`;
        requestBody = {
          contents: messages.map((msg) => ({
            parts: [{ text: msg.content }],
            role: msg.role === "assistant" ? "model" : "user",
          })),
          generationConfig: {
            temperature: temperature,
            maxOutputTokens: maxTokens,
          },
        };
        break;

      case "ollama":
      case "lm-studio":
      case "xinference":
        url = `${provider.baseUrl}/chat/completions`;
        requestBody = {
          model: modelId,
          messages: messages,
          max_tokens: maxTokens,
          temperature: temperature,
          stream: false,
        };
        break;

      case "openrouter":
        url = `${provider.baseUrl}/chat/completions`;
        headers["Authorization"] = `Bearer ${provider.apiKey}`;
        headers["HTTP-Referer"] = "https://github.com/your-repo";
        headers["X-Title"] = "Bookmark Extension";
        requestBody = {
          model: modelId,
          messages: messages,
          max_tokens: maxTokens,
          temperature: temperature,
        };
        break;

      case "deepseek":
      case "zhipu":
      case "qwen":
      case "together":
      case "grok":
        url = `${provider.baseUrl}/chat/completions`;
        headers["Authorization"] = `Bearer ${provider.apiKey}`;
        requestBody = {
          model: modelId,
          messages: messages,
          max_tokens: maxTokens,
          temperature: temperature,
        };
        break;

      case "azure-openai":
        url = `${provider.baseUrl}/openai/deployments/${modelId}/chat/completions?api-version=2024-02-15-preview`;
        headers["api-key"] = provider.apiKey || "";
        requestBody = {
          messages: messages,
          max_tokens: maxTokens,
          temperature: temperature,
        };
        break;

      default:
        throw new Error(`不支持的提供商类型: ${provider.type}`);
    }

    // 根据提供商类型设置不同的超时时间
    let timeoutMs = 30000; // 默认30秒

    // 本地模型需要更长的超时时间，特别是首token生成
    if (provider.name.includes('LM Studio') ||
        provider.name.includes('本地') ||
        provider.name.includes('Local') ||
        url.includes('localhost') ||
        url.includes('127.0.0.1')) {
      timeoutMs = 120000; // 本地模型使用2分钟超时
      console.log('检测到本地AI服务，使用延长超时时间:', timeoutMs / 1000, '秒')
    }

    // 发送请求
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(timeoutMs),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();

    // 解析响应
    return this.extractResponseContent(data, provider.type);
  }

  /**
   * 从响应中提取内容
   * @param data 响应数据
   * @param providerType 提供商类型
   * @returns string
   */
  private extractResponseContent(data: any, providerType: string): string {
    switch (providerType) {
      case "openai":
      case "custom":
      case "ollama":
      case "lm-studio":
      case "xinference":
      case "openrouter":
      case "deepseek":
      case "zhipu":
      case "qwen":
      case "together":
      case "grok":
      case "azure-openai":
        return data.choices?.[0]?.message?.content || "";

      case "claude":
        return data.content?.[0]?.text || "";

      case "gemini":
        return data.candidates?.[0]?.content?.parts?.[0]?.text || "";

      default:
        throw new Error(`不支持的提供商类型: ${providerType}`);
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 格式化字节大小
   * @param bytes 字节数
   * @returns string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
}

// 导出单例实例
export const aiProviderService = new AIProviderService();
