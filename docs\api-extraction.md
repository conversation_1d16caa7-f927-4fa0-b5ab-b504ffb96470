# API 提取报告

## 函数列表

### createContextMenus

**文件**: index.ts
**类型**: function
**签名**: `function createContextMenus()`

**说明**: 创建右键菜单

---

### handleBookmarkLink

**文件**: index.ts
**类型**: function
**签名**: `async function handleBookmarkLink(info: chrome.contextMenus.OnClickData, tab?: chrome.tabs.Tab)`

**说明**: 处理收藏链接

---

### handleBookmarkSelection

**文件**: index.ts
**类型**: function
**签名**: `async function handleBookmarkSelection(info: chrome.contextMenus.OnClickData, tab?: chrome.tabs.Tab)`

**说明**: 处理收藏选中文字

---

### handleBookmarkPage

**文件**: index.ts
**类型**: function
**签名**: `async function handleBookmarkPage(_info: chrome.contextMenus.OnClickData, tab?: chrome.tabs.Tab)`

**说明**: 处理收藏当前页面

---

### initializeStorage

**文件**: index.ts
**类型**: function
**签名**: `async function initializeStorage()`

**说明**: 初始化存储

---

### handleBackgroundMessage

**文件**: index.ts
**类型**: function
**签名**: `async function handleBackgroundMessage(message: any, sender: chrome.runtime.MessageSender)`

**说明**: 处理消息的主函数

---

### getSettings

**文件**: index.ts
**类型**: function
**签名**: `async function getSettings()`

**说明**: 获取设置

---

### updateSettings

**文件**: index.ts
**类型**: function
**签名**: `async function updateSettings(newSettings: any)`

**说明**: 更新设置

---

### handleQuickBookmark

**文件**: index.ts
**类型**: function
**签名**: `async function handleQuickBookmark(data: any, tab?: chrome.tabs.Tab)`

**说明**: 快速收藏处理

---

### handleBookmarkSelectedText

**文件**: index.ts
**类型**: function
**签名**: `async function handleBookmarkSelectedText(data: any, tab?: chrome.tabs.Tab)`

**说明**: 收藏选中文字处理

---

### handleSaveDetailedBookmark

**文件**: index.ts
**类型**: function
**签名**: `async function handleSaveDetailedBookmark(data: any, tab?: chrome.tabs.Tab)`

**说明**: 保存详细收藏处理

---

### saveBookmark

**文件**: index.ts
**类型**: function
**签名**: `async function saveBookmark(bookmark: any)`

**说明**: 保存收藏到存储

---

### updateIconStatus

**文件**: index.ts
**类型**: function
**签名**: `async function updateIconStatus(tabId: number, isBookmarked: boolean)`

**说明**: 更新插件图标状态

---

### checkBookmarkStatus

**文件**: index.ts
**类型**: function
**签名**: `async function checkBookmarkStatus(url: string)`

**说明**: 检查收藏状态

---

### handleSettingsUpdated

**文件**: index.ts
**类型**: function
**签名**: `async function handleSettingsUpdated(settings: any)`

**说明**: 处理设置更新

---

### handleManualSync

**文件**: index.ts
**类型**: function
**签名**: `async function handleManualSync()`

**说明**: 手动同步处理

---

### handleAIGenerateSuggestions

**文件**: index.ts
**类型**: function
**签名**: `async function handleAIGenerateSuggestions(data: any)`

**说明**: AI生成建议处理

---

### generateId

**文件**: index.ts
**类型**: function
**签名**: `function generateId(): string `

**说明**: 生成唯一ID

---

### handleContentMessage

**文件**: index.ts
**类型**: function
**签名**: `async function handleContentMessage(message: any)`

**说明**: 处理消息的主函数

---

### handleGetPageInfo

**文件**: index.ts
**类型**: function
**签名**: `async function handleGetPageInfo(data: any)`

**说明**: 处理获取页面信息请求

---

### handleGetLinkInfo

**文件**: index.ts
**类型**: function
**签名**: `async function handleGetLinkInfo(data: any)`

**说明**: 处理获取链接信息请求

---

### initialize

**文件**: index.ts
**类型**: function
**签名**: `function initialize()`

---

## 接口列表

### Bookmark

**文件**: index.ts

```typescript
interface Bookmark {
  id: string
  type: 'url' | 'text' | 'image'
  title: string
  url?: string
  content?: string
  description?: string
  tags: string[]
  category: string
  favicon?: string
  thumbnail?: string
  createdAt: Date
  updatedAt: Date
  metadata: BookmarkMetadata
}
```

---

### BookmarkMetadata

**文件**: index.ts

```typescript
interface BookmarkMetadata {
  pageTitle?: string
  siteName?: string
  author?: string
  publishDate?: Date
  wordCount?: number
  language?: string
  aiGenerated: boolean
}
```

---

### Category

**文件**: index.ts

```typescript
interface Category {
  id: string
  name: string
  description?: string
  color?: string
  parentId?: string
  createdAt: Date
}
```

---

### Tag

**文件**: index.ts

```typescript
interface Tag {
  id: string
  name: string
  color?: string
  usageCount: number
  createdAt: Date
}
```

---

### AIConfig

**文件**: index.ts

```typescript
interface AIConfig {
  provider: 'openai' | 'claude' | 'local' | 'custom'
  baseUrl?: string
  apiKey?: string
  model: string
  autoTagging: boolean
  autoCategories: boolean
}
```

---

### SyncConfig

**文件**: index.ts

```typescript
interface SyncConfig {
  enabled: boolean
  provider: 'notion' | 'airtable' | 'custom'
  credentials: Record<string, any>
  syncFrequency: number
  lastSync?: Date
}
```

---

### ExtensionMessage

**文件**: index.ts

```typescript
interface ExtensionMessage {
  type: string
  data?: any
}
```

---

### PageInfo

**文件**: index.ts

```typescript
interface PageInfo {
  title: string
  url: string
  favicon?: string
  selectedText?: string
  timestamp: Date
}
```

---

### ExportOptions

**文件**: index.ts

```typescript
interface ExportOptions {
  format: 'json' | 'html' | 'csv'
  includeNotes: boolean
  categories?: string[]
  tags?: string[]
}
```

---

### BatchOperation

**文件**: index.ts

```typescript
interface BatchOperation {
  type: 'delete' | 'updateTags' | 'moveCategory'
  bookmarkIds: string[]
  data?: any
}
```

---

## 类型定义

### ViewMode

**文件**: index.ts

```typescript
type ViewMode = 'list' | 'card' | 'gallery' | 'simple'
```

---

