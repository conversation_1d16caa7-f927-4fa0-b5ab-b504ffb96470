// DeleteConfirmModal组件测试页面

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import DeleteConfirmModal from '../DeleteConfirmModal'

/**
 * DeleteConfirmModal组件测试页面
 * 用于测试shadcn重构后的删除确认模态窗口功能
 */
const DeleteConfirmModalTest: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedBookmark, setSelectedBookmark] = useState<any>(null)
  const [testResults, setTestResults] = useState<string[]>([])

  // 测试用的收藏数据
  const testBookmarks = [
    {
      id: 'test-url-1',
      title: 'React官方文档 - 学习现代Web开发的最佳实践和设计模式',
      url: 'https://react.dev/learn',
      type: 'url' as const,
      category: '技术文档',
      tags: ['React', 'JavaScript', '前端开发', 'Web开发'],
      createdAt: new Date('2024-01-15T10:30:00Z')
    },
    {
      id: 'test-text-1',
      title: '重要会议记录 - 产品开发讨论',
      content: '今天的产品会议讨论了新功能的开发计划，包括用户界面改进、性能优化和新的API集成。团队决定采用shadcn/ui作为新的UI组件库，以提高开发效率和用户体验。会议还讨论了项目时间线和资源分配问题。',
      type: 'text' as const,
      category: '工作笔记',
      tags: ['会议', '产品', '开发计划'],
      createdAt: new Date('2024-01-10T14:20:00Z')
    },
    {
      id: 'test-image-1',
      title: '设计灵感图片集合',
      url: 'https://example.com/design-inspiration.jpg',
      type: 'image' as const,
      category: '设计',
      tags: ['UI设计', '灵感', '配色', '布局'],
      createdAt: new Date('2024-01-08T09:15:00Z')
    },
    {
      id: 'test-no-title',
      title: '',
      url: 'https://example.com/untitled-page',
      type: 'url' as const,
      category: '未分类',
      tags: [],
      createdAt: new Date('2024-01-05T16:45:00Z')
    },
    {
      id: 'test-many-tags',
      title: '多标签测试收藏',
      url: 'https://example.com/many-tags',
      type: 'url' as const,
      category: '测试',
      tags: ['标签1', '标签2', '标签3', '标签4', '标签5', '标签6', '标签7'],
      createdAt: new Date('2024-01-01T12:00:00Z')
    }
  ]

  // 添加测试结果
  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  // 处理删除确认
  const handleConfirm = async (bookmarkId: string) => {
    addTestResult(`开始删除收藏: ${bookmarkId}`)
    setLoading(true)
    
    // 模拟删除操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    addTestResult(`成功删除收藏: ${bookmarkId}`)
    setLoading(false)
    setIsOpen(false)
    setSelectedBookmark(null)
  }

  // 处理取消
  const handleCancel = () => {
    addTestResult('用户取消删除操作')
    setIsOpen(false)
    setSelectedBookmark(null)
  }

  // 打开删除确认对话框
  const openDeleteModal = (bookmark: any, testName: string) => {
    addTestResult(`打开删除确认对话框: ${testName}`)
    setSelectedBookmark(bookmark)
    setIsOpen(true)
  }

  // 清空测试结果
  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">DeleteConfirmModal 测试页面</h1>
        <p className="text-muted-foreground">
          测试shadcn重构后的删除确认模态窗口功能和样式
        </p>
      </div>

      <Separator />

      {/* 功能特性说明 */}
      <Card>
        <CardHeader>
          <CardTitle>shadcn重构特性</CardTitle>
          <CardDescription>
            DeleteConfirmModal组件已使用shadcn AlertDialog重构
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">组件特性</h4>
              <ul className="text-sm space-y-1">
                <li className="flex items-center space-x-2">
                  <Badge variant="secondary" className="w-2 h-2 p-0"></Badge>
                  <span>使用shadcn AlertDialog组件</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Badge variant="secondary" className="w-2 h-2 p-0"></Badge>
                  <span>destructive按钮变体</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Badge variant="secondary" className="w-2 h-2 p-0"></Badge>
                  <span>shadcn主题颜色系统</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Badge variant="secondary" className="w-2 h-2 p-0"></Badge>
                  <span>标准确认对话框模式</span>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">测试功能</h4>
              <ul className="text-sm space-y-1">
                <li className="flex items-center space-x-2">
                  <Badge variant="outline" className="w-2 h-2 p-0"></Badge>
                  <span>不同类型收藏删除</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Badge variant="outline" className="w-2 h-2 p-0"></Badge>
                  <span>加载状态显示</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Badge variant="outline" className="w-2 h-2 p-0"></Badge>
                  <span>取消和确认操作</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Badge variant="outline" className="w-2 h-2 p-0"></Badge>
                  <span>边界情况处理</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试用例 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 测试收藏列表 */}
        <Card>
          <CardHeader>
            <CardTitle>测试收藏列表</CardTitle>
            <CardDescription>
              点击删除按钮测试不同场景下的删除确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {testBookmarks.map((bookmark, index) => (
              <div key={bookmark.id} className="p-4 border rounded-lg space-y-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm truncate">
                      {bookmark.title || '无标题'}
                    </h4>
                    
                    {bookmark.url && (
                      <p className="text-xs text-primary truncate mt-1">
                        {bookmark.url}
                      </p>
                    )}
                    
                    {bookmark.content && (
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                        {bookmark.content}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant="outline" className="text-xs">
                        {bookmark.type === 'url' ? '网页' : 
                         bookmark.type === 'text' ? '文本' : '图片'}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {bookmark.category}
                      </span>
                    </div>
                    
                    {bookmark.tags && bookmark.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {bookmark.tags.slice(0, 3).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {bookmark.tags.length > 3 && (
                          <span className="text-xs text-muted-foreground">
                            +{bookmark.tags.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => openDeleteModal(bookmark, `测试用例${index + 1}`)}
                    className="ml-2"
                  >
                    删除
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* 测试结果 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              测试结果日志
              <Button variant="outline" size="sm" onClick={clearResults}>
                清空日志
              </Button>
            </CardTitle>
            <CardDescription>
              记录组件交互和操作结果
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted/50 rounded-lg p-4 h-64 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-muted-foreground text-sm">
                  暂无测试结果，请点击删除按钮开始测试
                </p>
              ) : (
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-sm font-mono">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 特殊测试用例 */}
      <Card>
        <CardHeader>
          <CardTitle>特殊测试用例</CardTitle>
          <CardDescription>
            测试边界情况和特殊场景
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              onClick={() => openDeleteModal({
                ...testBookmarks[0],
                title: '超长标题测试：这是一个非常非常长的收藏标题，用来测试标题截断和显示效果，看看组件如何处理长文本内容'
              }, '超长标题测试')}
            >
              超长标题测试
            </Button>
            
            <Button
              variant="outline"
              onClick={() => openDeleteModal({
                id: 'empty-test',
                title: '',
                type: 'url' as const,
                tags: [],
                createdAt: new Date()
              }, '空内容测试')}
            >
              空内容测试
            </Button>
            
            <Button
              variant="outline"
              onClick={() => openDeleteModal({
                ...testBookmarks[1],
                content: '这是一个超长的文本内容测试，用来验证文本类型收藏在删除确认对话框中的显示效果。内容包含了很多文字，需要测试截断和换行的处理。这段文本会继续很长很长，直到达到测试的目的。'
              }, '超长文本测试')}
            >
              超长文本测试
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 当前状态显示 */}
      <Card>
        <CardHeader>
          <CardTitle>当前状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">对话框状态：</span>
              <Badge variant={isOpen ? "default" : "secondary"} className="ml-2">
                {isOpen ? '打开' : '关闭'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">加载状态：</span>
              <Badge variant={loading ? "destructive" : "secondary"} className="ml-2">
                {loading ? '加载中' : '空闲'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">选中收藏：</span>
              <span className="ml-2 text-muted-foreground">
                {selectedBookmark ? selectedBookmark.id : '无'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* DeleteConfirmModal组件 */}
      <DeleteConfirmModal
        isOpen={isOpen}
        bookmark={selectedBookmark}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        loading={loading}
        enableUndo={true}
      />
    </div>
  )
}

export default DeleteConfirmModalTest