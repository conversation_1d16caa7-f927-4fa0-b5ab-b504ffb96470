import React from 'react'
import ViewModeSelector, { type ViewMode } from '../components/ViewModeSelector'
import { useViewMode } from '../hooks/useViewMode'

/**
 * ViewModeSelector组件使用示例
 * 展示如何集成视图模式选择器到应用中
 */
const ViewModeSelectorExample: React.FC = () => {
  const { viewMode, setViewMode, isLoading } = useViewMode()

  // 处理视图模式变化
  const handleViewModeChange = (newMode: ViewMode) => {
    console.log('视图模式切换到:', newMode)
    setViewMode(newMode)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        视图模式选择器示例
      </h1>
      
      {/* 视图模式选择器 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">
          选择视图模式
        </h2>
        <ViewModeSelector
          currentMode={viewMode}
          onModeChange={handleViewModeChange}
          className="mb-4"
        />
        <p className="text-sm text-gray-600">
          当前视图模式: <span className="font-medium">{viewMode}</span>
        </p>
      </div>

      {/* 模拟内容区域 */}
      <div className="border rounded-lg p-4 bg-gray-50">
        <h3 className="text-md font-medium text-gray-800 mb-4">
          模拟收藏列表 ({viewMode} 视图)
        </h3>
        
        {viewMode === 'card' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="h-32 bg-gray-200 rounded mb-3"></div>
                <h4 className="font-medium text-gray-900 mb-2">收藏项目 {i}</h4>
                <p className="text-sm text-gray-600 mb-2">
                  这是一个示例收藏项目的描述信息，展示卡片视图的详细布局。
                </p>
                <div className="text-xs text-gray-500">
                  https://example.com/bookmark-{i}
                </div>
              </div>
            ))}
          </div>
        )}

        {viewMode === 'row' && (
          <div className="space-y-2">
            {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
              <div key={i} className="flex items-center p-3 bg-white rounded border hover:bg-gray-50">
                <div className="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <span className="font-medium text-gray-900 mr-2">收藏项目 {i}</span>
                  <span className="text-sm text-gray-500">https://example.com/bookmark-{i}</span>
                </div>
                <div className="text-xs text-gray-400">2024-01-{i.toString().padStart(2, '0')}</div>
              </div>
            ))}
          </div>
        )}

        {viewMode === 'compact' && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(i => (
              <div key={i} className="bg-white p-2 rounded border text-sm hover:bg-gray-50">
                <div className="font-medium text-gray-900 truncate mb-1">
                  收藏项目 {i}
                </div>
                <div className="text-xs text-gray-500 truncate">
                  example.com/bookmark-{i}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-md font-medium text-blue-900 mb-2">
          使用说明
        </h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• <strong>卡片视图</strong>: 显示详细信息，包括缩略图和描述</li>
          <li>• <strong>行视图</strong>: 单行显示，适合快速浏览</li>
          <li>• <strong>紧凑视图</strong>: 密集布局，显示更多项目</li>
          <li>• 视图偏好会自动保存到本地存储</li>
          <li>• 支持键盘导航和屏幕阅读器</li>
        </ul>
      </div>
    </div>
  )
}

export default ViewModeSelectorExample