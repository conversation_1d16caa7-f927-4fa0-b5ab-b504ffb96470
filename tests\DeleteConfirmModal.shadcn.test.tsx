// DeleteConfirmModal组件shadcn重构测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import DeleteConfirmModal from '../src/components/DeleteConfirmModal'

// Mock组件的props
const mockBookmark = {
  id: 'test-id',
  title: '测试收藏标题',
  url: 'https://example.com',
  type: 'url' as const,
  category: '工作',
  tags: ['标签1', '标签2'],
  createdAt: new Date('2024-01-01T10:00:00Z')
}

const mockProps = {
  isOpen: true,
  bookmark: mockBookmark,
  onConfirm: vi.fn(),
  onCancel: vi.fn(),
  loading: false
}

describe('DeleteConfirmModal shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('shadcn AlertDialog基本渲染测试', () => {
    it('应该使用shadcn AlertDialog正确渲染', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 验证shadcn AlertDialog组件的存在（使用alertdialog角色）
      expect(screen.getByRole('alertdialog')).toBeInTheDocument()
      expect(screen.getByRole('heading', { name: '确认删除' })).toBeInTheDocument()
      expect(screen.getByText('此操作无法撤销')).toBeInTheDocument()
    })

    it('应该在isOpen为false时不显示对话框', () => {
      render(<DeleteConfirmModal {...mockProps} isOpen={false} />)
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })

    it('应该在bookmark为null时不显示对话框', () => {
      render(<DeleteConfirmModal {...mockProps} bookmark={null} />)
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })

    it('应该显示shadcn AlertDialog的标准按钮', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /确认删除/ })).toBeInTheDocument()
    })
  })

  describe('shadcn主题样式测试', () => {
    it('应该使用shadcn的destructive主题样式', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 检查确认删除按钮是否使用了destructive样式
      const confirmButton = screen.getByRole('button', { name: /确认删除/ })
      expect(confirmButton).toHaveClass('bg-destructive')
      expect(confirmButton).toHaveClass('text-destructive-foreground')
    })

    it('应该使用shadcn的主题颜色变量', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 检查是否使用了shadcn的颜色类
      const warningIcon = document.querySelector('.text-destructive')
      expect(warningIcon).toBeInTheDocument()
      
      const mutedText = document.querySelector('.text-muted-foreground')
      expect(mutedText).toBeInTheDocument()
    })

    it('应该使用shadcn的间距和布局系统', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 检查对话框内容的shadcn样式类（使用alertdialog角色）
      const dialogContent = document.querySelector('[role="alertdialog"]')
      expect(dialogContent).toHaveClass('max-w-md')
    })
  })

  describe('收藏信息显示测试', () => {
    it('应该在shadcn样式容器中显示收藏信息', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      expect(screen.getByText('测试收藏标题')).toBeInTheDocument()
      expect(screen.getByText('https://example.com')).toBeInTheDocument()
      expect(screen.getByText('分类: 工作')).toBeInTheDocument()
      
      // 检查标签是否使用了shadcn的secondary样式
      const tag1 = screen.getByText('标签1')
      expect(tag1).toHaveClass('bg-secondary')
      expect(tag1).toHaveClass('text-secondary-foreground')
    })

    it('应该为文本类型收藏显示正确的shadcn样式', () => {
      const textBookmark = {
        ...mockBookmark,
        type: 'text' as const,
        content: '这是一段测试文本内容',
        url: undefined
      }
      
      render(<DeleteConfirmModal {...mockProps} bookmark={textBookmark} />)
      
      expect(screen.getByText('您确定要删除以下文本摘录吗？')).toBeInTheDocument()
      expect(screen.getByText('这是一段测试文本内容')).toBeInTheDocument()
    })

    it('应该使用shadcn样式显示警告信息', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      const warningContainer = document.querySelector('.bg-destructive\\/10')
      expect(warningContainer).toBeInTheDocument()
      
      expect(screen.getByText('删除后将无法恢复')).toBeInTheDocument()
    })
  })

  describe('shadcn AlertDialog交互测试', () => {
    it('应该通过shadcn AlertDialogCancel取消删除', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      const cancelButton = screen.getByRole('button', { name: '取消' })
      fireEvent.click(cancelButton)
      
      expect(mockProps.onCancel).toHaveBeenCalled()
    })

    it('应该通过shadcn AlertDialogAction确认删除', async () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      const confirmButton = screen.getByRole('button', { name: /确认删除/ })
      fireEvent.click(confirmButton)
      
      await waitFor(() => {
        expect(mockProps.onConfirm).toHaveBeenCalledWith('test-id')
      })
    })

    it('应该通过onOpenChange处理对话框关闭', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 模拟按ESC键关闭对话框
      fireEvent.keyDown(document, { key: 'Escape' })
      
      // 由于shadcn AlertDialog的内部实现，我们检查onCancel是否被调用
      // 这个测试可能需要根据实际的shadcn行为进行调整
    })
  })

  describe('加载状态shadcn样式测试', () => {
    it('应该在loading时正确显示shadcn按钮的禁用状态', () => {
      render(<DeleteConfirmModal {...mockProps} loading={true} />)
      
      const cancelButton = screen.getByRole('button', { name: '取消' })
      const confirmButton = screen.getByRole('button', { name: /删除中/ })
      
      expect(cancelButton).toBeDisabled()
      expect(confirmButton).toBeDisabled()
      
      // 检查shadcn按钮的禁用样式
      expect(confirmButton).toHaveClass('disabled:opacity-50')
    })

    it('应该在loading时显示shadcn样式的加载指示器', () => {
      render(<DeleteConfirmModal {...mockProps} loading={true} />)
      
      expect(screen.getByText('删除中...')).toBeInTheDocument()
      
      // 检查加载动画
      const spinner = document.querySelector('.animate-spin')
      expect(spinner).toBeInTheDocument()
    })
  })

  describe('内部删除状态测试', () => {
    it('应该在确认删除时显示shadcn样式的内部加载状态', async () => {
      const slowConfirm = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100))
      )
      
      render(<DeleteConfirmModal {...mockProps} onConfirm={slowConfirm} />)
      
      const confirmButton = screen.getByRole('button', { name: /确认删除/ })
      fireEvent.click(confirmButton)
      
      // 应该立即显示加载状态
      expect(screen.getByText('删除中...')).toBeInTheDocument()
      
      // 等待异步操作完成
      await waitFor(() => {
        expect(slowConfirm).toHaveBeenCalledWith('test-id')
      })
    })

    it('应该处理删除操作中的错误并保持shadcn样式', async () => {
      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {})
      const failingConfirm = vi.fn().mockRejectedValue(new Error('删除失败'))
      
      render(<DeleteConfirmModal {...mockProps} onConfirm={failingConfirm} />)
      
      const confirmButton = screen.getByRole('button', { name: /确认删除/ })
      fireEvent.click(confirmButton)
      
      await waitFor(() => {
        expect(failingConfirm).toHaveBeenCalledWith('test-id')
      })
      
      expect(consoleError).toHaveBeenCalledWith('删除收藏失败:', expect.any(Error))
      
      consoleError.mockRestore()
    })
  })

  describe('shadcn主题一致性测试', () => {
    it('应该在不同收藏类型中保持shadcn主题一致性', () => {
      const imageBookmark = {
        ...mockBookmark,
        type: 'image' as const
      }
      
      render(<DeleteConfirmModal {...mockProps} bookmark={imageBookmark} />)
      
      expect(screen.getByText('您确定要删除以下图片收藏吗？')).toBeInTheDocument()
      
      // 检查主题颜色是否一致
      const warningIcon = document.querySelector('.text-destructive')
      expect(warningIcon).toBeInTheDocument()
    })

    it('应该在撤销功能启用时保持shadcn样式', () => {
      render(<DeleteConfirmModal {...mockProps} enableUndo={true} />)
      
      expect(screen.getByText(/您可以在删除后的短时间内撤销此操作/)).toBeInTheDocument()
      
      // 检查警告容器的shadcn样式
      const warningContainer = document.querySelector('.bg-destructive\\/10')
      expect(warningContainer).toBeInTheDocument()
    })
  })

  describe('shadcn可访问性测试', () => {
    it('应该保持shadcn AlertDialog的可访问性特性', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 检查对话框的ARIA属性（使用alertdialog角色）
      const dialog = screen.getByRole('alertdialog')
      expect(dialog).toBeInTheDocument()
      
      // 检查按钮的可访问性
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '确认删除' })).toBeInTheDocument()
    })
  })

  describe('shadcn响应式设计测试', () => {
    it('应该使用shadcn的响应式类', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 检查对话框内容是否使用了shadcn的响应式类（使用alertdialog角色）
      const dialogContent = document.querySelector('[role="alertdialog"]')
      expect(dialogContent).toHaveClass('max-w-md')
    })
  })
})