// AI聊天服务单元测试

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { aiChatService } from '../src/services/aiChatService'
import { ChromeStorageService } from '../src/utils/chromeStorage'

// Mock ChromeStorageService
vi.mock('../src/utils/chromeStorage', () => ({
  ChromeStorageService: {
    getSyncSetting: vi.fn(),
    saveSyncSetting: vi.fn(),
    getLocalSetting: vi.fn(),
    saveLocalSetting: vi.fn()
  }
}))

// Mock aiIntegrationService
vi.mock('../src/services/aiIntegrationService', () => ({
  aiIntegrationService: {
    getConfiguredProviders: vi.fn(),
    getAvailableModels: vi.fn()
  }
}))

describe('AIChatService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('setDefaultModel', () => {
    it('应该正确保存默认模型配置', async () => {
      const providerId = 'test-provider'
      const modelId = 'test-model'

      await aiChatService.setDefaultModel(providerId, modelId)

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_default_model',
        { providerId, modelId }
      )
    })

    it('应该在保存失败时抛出错误', async () => {
      const error = new Error('保存失败')
      vi.mocked(ChromeStorageService.saveSyncSetting).mockRejectedValue(error)

      await expect(
        aiChatService.setDefaultModel('provider', 'model')
      ).rejects.toThrow('保存失败')
    })
  })

  describe('generateText', () => {
    it('应该正确生成描述类型的文本', async () => {
      // Mock 默认模型配置
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue({
        providerId: 'test-provider',
        modelId: 'test-model'
      })

      // Mock 提供商配置
      const mockProvider = {
        id: 'test-provider',
        name: '测试提供商',
        type: 'openai',
        baseUrl: 'https://api.test.com',
        apiKey: 'test-key'
      }

      const { aiIntegrationService } = await import('../src/services/aiIntegrationService')
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue([mockProvider])

      const request = {
        prompt: '请生成描述',
        generationType: 'description' as const,
        context: {
          title: '测试标题',
          url: 'https://example.com'
        }
      }

      const result = await aiChatService.generateText(request)

      expect(result).toHaveProperty('content')
      expect(result).toHaveProperty('metadata')
      expect(result.metadata?.provider).toBe('测试提供商')
      expect(result.metadata?.model).toBe('test-model')
    })

    it('应该在没有默认模型时抛出错误', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(null)

      const { aiIntegrationService } = await import('../src/services/aiIntegrationService')
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue([])

      const request = {
        prompt: '测试提示',
        generationType: 'description' as const
      }

      await expect(
        aiChatService.generateText(request)
      ).rejects.toThrow('未找到可用的AI模型，请先配置AI提供商')
    })

    it('应该在提供商不存在时抛出错误', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue({
        providerId: 'non-existent-provider',
        modelId: 'test-model'
      })

      const { aiIntegrationService } = await import('../src/services/aiIntegrationService')
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue([])

      const request = {
        prompt: '测试提示',
        generationType: 'description' as const
      }

      await expect(
        aiChatService.generateText(request)
      ).rejects.toThrow('AI提供商配置不存在')
    })

    it('应该正确处理不同的生成类型', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue({
        providerId: 'test-provider',
        modelId: 'test-model'
      })

      const mockProvider = {
        id: 'test-provider',
        name: '测试提供商',
        type: 'openai',
        baseUrl: 'https://api.test.com',
        apiKey: 'test-key'
      }

      const { aiIntegrationService } = await import('../src/services/aiIntegrationService')
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue([mockProvider])

      // 只测试一种类型以避免超时
      const request = {
        prompt: '测试description生成',
        generationType: 'description' as const,
        context: { title: '测试' }
      }

      const result = await aiChatService.generateText(request)
      expect(result).toHaveProperty('content')
      expect(typeof result.content).toBe('string')
    }, 15000) // 增加超时时间
  })

  describe('saveChatHistory', () => {
    it('应该正确保存聊天历史', async () => {
      const messages = [
        { role: 'user' as const, content: '用户消息' },
        { role: 'assistant' as const, content: 'AI回复' }
      ]

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([])

      await aiChatService.saveChatHistory(messages)

      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_chat_history',
        messages
      )
    })

    it('应该限制历史记录数量', async () => {
      const existingHistory = Array.from({ length: 99 }, (_, i) => ({
        role: 'user' as const,
        content: `消息${i}`
      }))

      const newMessages = [
        { role: 'user' as const, content: '新消息1' },
        { role: 'assistant' as const, content: '新消息2' }
      ]

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue(existingHistory)

      await aiChatService.saveChatHistory(newMessages)

      const savedHistory = vi.mocked(ChromeStorageService.saveLocalSetting).mock.calls[0][1]
      expect(savedHistory).toHaveLength(100) // 限制为100条
      
      // 检查包含新消息
      expect(savedHistory).toEqual(
        expect.arrayContaining([
          { role: 'user', content: '新消息1' },
          { role: 'assistant', content: '新消息2' }
        ])
      )
    })
  })

  describe('getChatHistory', () => {
    it('应该正确获取聊天历史', async () => {
      const mockHistory = [
        { role: 'user' as const, content: '用户消息' },
        { role: 'assistant' as const, content: 'AI回复' }
      ]

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue(mockHistory)

      const result = await aiChatService.getChatHistory()

      expect(result).toEqual(mockHistory)
      expect(ChromeStorageService.getLocalSetting).toHaveBeenCalledWith(
        'ai_chat_history',
        []
      )
    })

    it('应该在获取失败时返回空数组', async () => {
      vi.mocked(ChromeStorageService.getLocalSetting).mockRejectedValue(new Error('获取失败'))

      const result = await aiChatService.getChatHistory()

      expect(result).toEqual([])
    })
  })

  describe('clearChatHistory', () => {
    it('应该正确清除聊天历史', async () => {
      await aiChatService.clearChatHistory()

      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_chat_history',
        []
      )
    })

    it('应该在清除失败时不抛出错误', async () => {
      vi.mocked(ChromeStorageService.saveLocalSetting).mockRejectedValue(new Error('清除失败'))

      // 不应该抛出错误
      await expect(aiChatService.clearChatHistory()).resolves.toBeUndefined()
    })
  })

  describe('私有方法测试', () => {
    it('应该正确构建不同类型的系统提示词', () => {
      // 由于是私有方法，我们通过生成文本来间接测试
      const service = aiChatService as any

      const descriptionPrompt = service.buildSystemPrompt('description')
      expect(descriptionPrompt).toContain('描述生成助手')
      expect(descriptionPrompt).toContain('简洁明了')

      const summaryPrompt = service.buildSystemPrompt('summary')
      expect(summaryPrompt).toContain('摘要生成助手')
      expect(summaryPrompt).toContain('提取关键信息')

      const tagsPrompt = service.buildSystemPrompt('tags')
      expect(tagsPrompt).toContain('标签推荐助手')
      expect(tagsPrompt).toContain('推荐5-8个')

      const titlePrompt = service.buildSystemPrompt('title')
      expect(titlePrompt).toContain('标题生成助手')
      expect(titlePrompt).toContain('简洁明了')

      const notesPrompt = service.buildSystemPrompt('notes')
      expect(notesPrompt).toContain('笔记生成助手')
      expect(notesPrompt).toContain('有价值的见解')
    })

    it('应该正确解析AI响应', () => {
      const service = aiChatService as any

      // 测试普通内容解析
      const descResult = service.parseAIResponse('这是一个描述', 'description')
      expect(descResult.content).toBe('这是一个描述')
      expect(descResult.suggestions).toBeDefined()

      // 测试标签类型解析
      const tagsResult = service.parseAIResponse('标签1,标签2,标签3', 'tags')
      expect(tagsResult.content).toBe('标签1, 标签2, 标签3')
      expect(tagsResult.suggestions).toEqual(['标签1', '标签2', '标签3'])
    })

    it('应该正确生成相关建议', () => {
      const service = aiChatService as any

      const descSuggestions = service.generateSuggestions('这是一个很长的描述内容，用于测试建议生成功能', 'description')
      expect(descSuggestions).toHaveLength(3)
      expect(descSuggestions[0]).toContain('...')

      const titleSuggestions = service.generateSuggestions('测试标题', 'title')
      expect(titleSuggestions).toHaveLength(3)
      expect(titleSuggestions[0]).toContain('测试标题')
    })
  })
})