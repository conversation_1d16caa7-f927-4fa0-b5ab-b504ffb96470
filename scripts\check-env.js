#!/usr/bin/env node

/**
 * 开发环境检查脚本
 * 检查Node.js版本和必要的依赖是否已安装
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔍 检查开发环境...\n')

// 检查Node.js版本
function checkNodeVersion() {
  const nodeVersion = process.version
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
  
  console.log(`✅ Node.js版本: ${nodeVersion}`)
  
  if (majorVersion < 16) {
    console.log('⚠️  警告: 推荐使用Node.js 16+版本')
  }
  
  return true
}

// 检查npm版本
function checkNpmVersion() {
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim()
    console.log(`✅ npm版本: ${npmVersion}`)
    return true
  } catch (error) {
    console.log('❌ npm未安装或不可用')
    return false
  }
}

// 检查package.json
function checkPackageJson() {
  const packagePath = path.join(process.cwd(), 'package.json')
  
  if (fs.existsSync(packagePath)) {
    console.log('✅ package.json存在')
    
    try {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
      console.log(`✅ 项目名称: ${packageJson.name}`)
      console.log(`✅ 项目版本: ${packageJson.version}`)
      return true
    } catch (error) {
      console.log('❌ package.json格式错误')
      return false
    }
  } else {
    console.log('❌ package.json不存在')
    return false
  }
}

// 检查依赖是否已安装
function checkDependencies() {
  const nodeModulesPath = path.join(process.cwd(), 'node_modules')
  
  if (fs.existsSync(nodeModulesPath)) {
    console.log('✅ node_modules存在')
    
    // 检查关键依赖
    const keyDeps = ['react', 'react-dom', 'vite', '@vitejs/plugin-react']
    const missingDeps = []
    
    keyDeps.forEach(dep => {
      const depPath = path.join(nodeModulesPath, dep)
      if (!fs.existsSync(depPath)) {
        missingDeps.push(dep)
      }
    })
    
    if (missingDeps.length > 0) {
      console.log(`⚠️  缺少依赖: ${missingDeps.join(', ')}`)
      console.log('请运行: npm install')
      return false
    } else {
      console.log('✅ 关键依赖已安装')
      return true
    }
  } else {
    console.log('❌ node_modules不存在，请运行: npm install')
    return false
  }
}

// 检查构建配置
function checkBuildConfig() {
  const configs = [
    'vite.config.ts',
    'tsconfig.json',
    'tailwind.config.js',
    'postcss.config.js'
  ]
  
  let allExist = true
  
  configs.forEach(config => {
    const configPath = path.join(process.cwd(), config)
    if (fs.existsSync(configPath)) {
      console.log(`✅ ${config}存在`)
    } else {
      console.log(`❌ ${config}不存在`)
      allExist = false
    }
  })
  
  return allExist
}

// 主检查函数
function main() {
  const checks = [
    { name: 'Node.js版本', fn: checkNodeVersion },
    { name: 'npm版本', fn: checkNpmVersion },
    { name: 'package.json', fn: checkPackageJson },
    { name: '项目依赖', fn: checkDependencies },
    { name: '构建配置', fn: checkBuildConfig }
  ]
  
  let allPassed = true
  
  checks.forEach(check => {
    try {
      const result = check.fn()
      if (!result) {
        allPassed = false
      }
    } catch (error) {
      console.log(`❌ ${check.name}检查失败: ${error.message}`)
      allPassed = false
    }
    console.log('')
  })
  
  if (allPassed) {
    console.log('🎉 开发环境检查通过！')
    console.log('\n可以运行以下命令开始开发:')
    console.log('  npm run dev    # 开发模式')
    console.log('  npm run build  # 构建生产版本')
  } else {
    console.log('❌ 开发环境检查未通过，请解决上述问题后重试')
    process.exit(1)
  }
}

// 运行检查
main()