// DeleteConfirmModal组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import DeleteConfirmModal from '../src/components/DeleteConfirmModal'

// Mock组件的props
const mockBookmark = {
  id: 'test-id',
  title: '测试收藏标题',
  url: 'https://example.com',
  type: 'url' as const,
  category: '工作',
  tags: ['标签1', '标签2'],
  createdAt: new Date('2024-01-01T10:00:00Z')
}

const mockProps = {
  isOpen: true,
  bookmark: mockBookmark,
  onConfirm: vi.fn(),
  onCancel: vi.fn(),
  loading: false
}

describe('DeleteConfirmModal组件测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基本渲染测试', () => {
    it('应该在isOpen为true时正确渲染', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      expect(screen.getByRole('heading', { name: '确认删除' })).toBeInTheDocument()
      expect(screen.getByText('此操作无法撤销')).toBeInTheDocument()
      expect(screen.getByText('您确定要删除以下网页收藏吗？')).toBeInTheDocument()
      expect(screen.getByText('测试收藏标题')).toBeInTheDocument()
    })

    it('应该在isOpen为false时不渲染', () => {
      render(<DeleteConfirmModal {...mockProps} isOpen={false} />)
      
      expect(screen.queryByText('确认删除')).not.toBeInTheDocument()
    })

    it('应该在bookmark为null时不渲染', () => {
      render(<DeleteConfirmModal {...mockProps} bookmark={null} />)
      
      expect(screen.queryByText('确认删除')).not.toBeInTheDocument()
    })

    it('应该显示正确的按钮', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /确认删除/ })).toBeInTheDocument()
    })
  })

  describe('收藏信息显示测试', () => {
    it('应该显示URL类型收藏的信息', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      expect(screen.getByText('测试收藏标题')).toBeInTheDocument()
      expect(screen.getByText('https://example.com')).toBeInTheDocument()
      expect(screen.getByText('分类: 工作')).toBeInTheDocument()
      expect(screen.getByText('标签1')).toBeInTheDocument()
      expect(screen.getByText('标签2')).toBeInTheDocument()
    })

    it('应该显示文本类型收藏的信息', () => {
      const textBookmark = {
        ...mockBookmark,
        type: 'text' as const,
        content: '这是一段测试文本内容',
        url: undefined
      }
      
      render(<DeleteConfirmModal {...mockProps} bookmark={textBookmark} />)
      
      expect(screen.getByText('您确定要删除以下文本摘录吗？')).toBeInTheDocument()
      expect(screen.getByText('这是一段测试文本内容')).toBeInTheDocument()
    })

    it('应该正确显示创建时间', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 检查是否显示了创建时间（格式可能因本地化而异）
      expect(screen.getByText(/创建:/)).toBeInTheDocument()
    })

    it('应该处理没有标签的情况', () => {
      const bookmarkWithoutTags = {
        ...mockBookmark,
        tags: []
      }
      
      render(<DeleteConfirmModal {...mockProps} bookmark={bookmarkWithoutTags} />)
      
      expect(screen.queryByText('标签1')).not.toBeInTheDocument()
    })

    it('应该限制显示的标签数量', () => {
      const bookmarkWithManyTags = {
        ...mockBookmark,
        tags: ['标签1', '标签2', '标签3', '标签4', '标签5']
      }
      
      render(<DeleteConfirmModal {...mockProps} bookmark={bookmarkWithManyTags} />)
      
      expect(screen.getByText('标签1')).toBeInTheDocument()
      expect(screen.getByText('标签2')).toBeInTheDocument()
      expect(screen.getByText('标签3')).toBeInTheDocument()
      expect(screen.getByText('+2 更多')).toBeInTheDocument()
    })

    it('应该处理无标题的收藏', () => {
      const bookmarkWithoutTitle = {
        ...mockBookmark,
        title: ''
      }
      
      render(<DeleteConfirmModal {...mockProps} bookmark={bookmarkWithoutTitle} />)
      
      expect(screen.getByText('无标题')).toBeInTheDocument()
    })
  })

  describe('交互测试', () => {
    it('应该能够取消删除', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      const cancelButton = screen.getByText('取消')
      fireEvent.click(cancelButton)
      
      expect(mockProps.onCancel).toHaveBeenCalled()
    })

    it('应该能够通过X按钮取消删除', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      const closeButton = screen.getByRole('button', { name: '' }) // X按钮
      fireEvent.click(closeButton)
      
      expect(mockProps.onCancel).toHaveBeenCalled()
    })

    it('应该能够通过遮罩层取消删除', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      const overlay = document.querySelector('.fixed.inset-0.bg-black')
      fireEvent.click(overlay!)
      
      expect(mockProps.onCancel).toHaveBeenCalled()
    })

    it('应该能够确认删除', async () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      const confirmButton = screen.getByRole('button', { name: /确认删除/ })
      fireEvent.click(confirmButton)
      
      await waitFor(() => {
        expect(mockProps.onConfirm).toHaveBeenCalledWith('test-id')
      })
    })
  })

  describe('加载状态测试', () => {
    it('应该在loading为true时禁用按钮', () => {
      render(<DeleteConfirmModal {...mockProps} loading={true} />)
      
      const cancelButton = screen.getByRole('button', { name: '取消' })
      const confirmButton = screen.getByRole('button', { name: /删除中/ })
      
      expect(cancelButton).toBeDisabled()
      expect(confirmButton).toBeDisabled()
    })

    it('应该在loading时显示加载指示器', () => {
      render(<DeleteConfirmModal {...mockProps} loading={true} />)
      
      expect(screen.getByText('删除中...')).toBeInTheDocument()
      expect(document.querySelector('.animate-spin')).toBeInTheDocument()
    })

    it('应该在loading时阻止遮罩层点击', () => {
      render(<DeleteConfirmModal {...mockProps} loading={true} />)
      
      const overlay = document.querySelector('.fixed.inset-0.bg-black')
      fireEvent.click(overlay!)
      
      expect(mockProps.onCancel).not.toHaveBeenCalled()
    })
  })

  describe('内部加载状态测试', () => {
    it('应该在确认删除时显示内部加载状态', async () => {
      // 模拟异步删除操作
      const slowConfirm = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100))
      )
      
      render(<DeleteConfirmModal {...mockProps} onConfirm={slowConfirm} />)
      
      const confirmButton = screen.getByRole('button', { name: /确认删除/ })
      fireEvent.click(confirmButton)
      
      // 应该立即显示加载状态
      expect(screen.getByText('删除中...')).toBeInTheDocument()
      
      // 等待异步操作完成
      await waitFor(() => {
        expect(slowConfirm).toHaveBeenCalledWith('test-id')
      })
    })

    it('应该处理删除操作中的错误', async () => {
      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {})
      const failingConfirm = vi.fn().mockRejectedValue(new Error('删除失败'))
      
      render(<DeleteConfirmModal {...mockProps} onConfirm={failingConfirm} />)
      
      const confirmButton = screen.getByRole('button', { name: /确认删除/ })
      fireEvent.click(confirmButton)
      
      await waitFor(() => {
        expect(failingConfirm).toHaveBeenCalledWith('test-id')
      })
      
      expect(consoleError).toHaveBeenCalledWith('删除收藏失败:', expect.any(Error))
      
      consoleError.mockRestore()
    })
  })

  describe('撤销功能测试', () => {
    it('应该在enableUndo为true时显示撤销提示', () => {
      render(<DeleteConfirmModal {...mockProps} enableUndo={true} />)
      
      expect(screen.getByText(/您可以在删除后的短时间内撤销此操作/)).toBeInTheDocument()
    })

    it('应该在enableUndo为false时不显示撤销提示', () => {
      render(<DeleteConfirmModal {...mockProps} enableUndo={false} />)
      
      expect(screen.queryByText(/您可以在删除后的短时间内撤销此操作/)).not.toBeInTheDocument()
    })
  })

  describe('不同收藏类型测试', () => {
    it('应该正确显示图片类型收藏', () => {
      const imageBookmark = {
        ...mockBookmark,
        type: 'image' as const
      }
      
      render(<DeleteConfirmModal {...mockProps} bookmark={imageBookmark} />)
      
      expect(screen.getByText('您确定要删除以下图片收藏吗？')).toBeInTheDocument()
    })

    it('应该处理未知类型的收藏', () => {
      const unknownBookmark = {
        ...mockBookmark,
        type: 'unknown' as any
      }
      
      render(<DeleteConfirmModal {...mockProps} bookmark={unknownBookmark} />)
      
      expect(screen.getByText('您确定要删除以下收藏吗？')).toBeInTheDocument()
    })
  })

  describe('警告信息测试', () => {
    it('应该显示删除警告信息', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      expect(screen.getByText('删除后将无法恢复')).toBeInTheDocument()
      expect(screen.getByText(/删除操作会永久移除此收藏及其所有相关信息/)).toBeInTheDocument()
    })
  })

  describe('可访问性测试', () => {
    it('应该有正确的ARIA标签', () => {
      render(<DeleteConfirmModal {...mockProps} />)
      
      // 检查模态窗口的可访问性
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '确认删除' })).toBeInTheDocument()
    })
  })
})