# Universe Bag 开发者指南

本文档包含Universe Bag项目的技术细节、开发环境设置、API文档和贡献指南。

## 技术栈

- **前端框架**: React 18 + TypeScript
- **样式框架**: Tailwind CSS
- **构建工具**: Vite + Chrome Extension Plugin
- **测试框架**: Vitest + jsdom
- **图标库**: Lucide React
- **存储**: IndexedDB + Chrome Storage API
- **AI集成**: OpenAI API / Azure OpenAI / Ollama 本地模型

## 开发环境设置

### 1. 安装Node.js

请先安装Node.js（推荐版本18+）：
- 访问 [Node.js官网](https://nodejs.org/) 下载并安装
- 或使用包管理器安装（如Chocolatey、Scoop等）

### 2. 安装依赖

```bash
npm install
```

### 3. 开发模式

```bash
npm run dev
```

### 4. 构建生产版本

```bash
npm run build
```

## Chrome扩展安装

### 开发模式安装

1. 首先验证项目配置：`node scripts/test-build.js`
2. 安装依赖：`npm install`
3. 构建项目：`npm run build`
4. 打开Chrome浏览器，访问 `chrome://extensions/`
5. 开启"开发者模式"
6. 点击"加载已解压的扩展程序"
7. 选择项目的 `dist` 文件夹

### 生产模式安装

1. 从Chrome Web Store安装（待发布）
2. 或下载发布版本的.crx文件手动安装

## 项目结构

```
src/
├── background/          # Background Service Worker
│   └── index.ts        # 后台服务主文件，处理右键菜单、消息通信、存储管理
├── content/            # Content Script
│   ├── index.ts        # 页面内容脚本，提取页面信息
│   └── style.css       # 内容脚本样式
├── popup/              # 弹出窗口界面
│   ├── index.html      # 弹出窗口HTML模板
│   ├── index.tsx       # 弹出窗口入口文件
│   └── PopupApp.tsx    # 弹出窗口主组件
├── options/            # 管理页面
│   ├── index.html      # 管理页面HTML模板
│   ├── index.tsx       # 管理页面入口文件
│   └── OptionsApp.tsx  # 管理页面主组件
├── services/           # 业务服务层
│   ├── bookmarkService.ts         # 收藏服务 - 核心业务逻辑
│   ├── categoryService.ts         # 分类服务 - 分类管理
│   ├── tagService.ts              # 标签服务 - 标签管理
│   ├── aiService.ts               # AI服务 - 智能功能
│   ├── aiChatService.ts           # AI对话服务 - 处理与AI模型的实际对话交互
│   ├── aiIntegrationService.ts    # AI集成服务 - 统一管理AI集成相关的业务逻辑
│   ├── aiProviderService.ts       # AI提供商服务 - 处理不同AI提供商的API交互（支持OpenAI/Azure OpenAI等）
│   ├── aiModelService.ts          # AI模型服务 - 处理模型搜索、筛选和缓存功能
│   └── localAIServiceAdapter.ts   # 本地AI服务适配器 - 通用本地AI服务接口
├── utils/              # 工具函数和服务
│   ├── indexedDB.ts          # IndexedDB存储服务
│   ├── fallbackStorage.ts   # 降级存储服务
│   ├── modelFactory.ts      # 数据模型工厂
│   ├── validation.ts        # 数据验证工具
│   ├── performance.ts       # 性能监控工具
│   └── chromeStorage.ts     # Chrome存储API封装
├── styles/             # 全局样式
│   └── globals.css     # Tailwind CSS全局样式
└── types/              # TypeScript类型定义
    ├── index.ts        # 核心数据类型定义
    └── ai.ts           # AI相关类型定义（支持Azure OpenAI等17种AI服务）
docs/                   # 项目文档
├── documentation-summary.md        # 文档总结和导航
├── background-service-worker-api.md  # Background Service Worker API文档
├── bookmark-service-api.md          # BookmarkService API文档
├── popup-components-api.md          # Popup组件API文档
├── performance-utils-api.md         # 性能监控工具API文档
├── test-build-test-api.md           # 测试框架API文档
├── integration-test-shadcn-api.md   # shadcn集成测试API文档
├── test-ai-integration-script-api.md        # AI集成服务测试脚本API文档（新增）
├── test-ai-integration-usage-examples.md    # AI集成服务测试使用示例（新增）
├── test-ai-integration-function-signatures.md  # AI集成服务测试函数签名（新增）
├── test-local-ai-services-script-api.md     # 本地AI服务测试脚本API文档
├── test-local-ai-services-usage-examples.md # 本地AI服务测试使用示例
├── test-local-ai-services-function-signatures.md # 本地AI服务测试函数签名
├── aiChatService-api.md             # AI对话服务API文档
├── aiChatService-usage-examples.md  # AI对话服务使用示例
├── ai-types-api.md                          # AI类型定义API文档（新增Azure OpenAI支持）
├── aiProviderService-openai-connection-api.md  # OpenAI连接测试增强功能API文档
├── aiProviderService-openrouter-api.md         # OpenRouter API详细文档（新增）
├── aiProviderService-function-signatures.md    # AI提供商服务函数签名文档
├── aiProviderService-usage-examples.md         # AI提供商服务使用示例
├── ai-integration-task1-completion-summary.md  # AI集成任务1完成总结
├── task-1-completion-summary.md     # 任务完成总结
├── task-13-detailedbookmarkform-shadcn-refactor.md  # DetailedBookmarkForm shadcn重构文档
└── task-13-final-verification-report.md  # 任务13最终验证报告
scripts/                # 项目脚本
├── README.md           # 脚本文档
├── API.md              # 脚本API文档
├── check-env.js        # 环境检查脚本
├── test-build.js       # 构建测试脚本
├── test-build.test.js  # 构建测试的单元测试
├── test-ai-integration.js  # AI集成服务综合测试脚本（新增）
├── test-local-ai-services.js  # 本地AI服务集成测试脚本
├── quick-test-local-ai.js     # 本地AI服务快速测试脚本
├── extract-api.js      # API提取脚本
├── extract-bookmark-service-api.js  # BookmarkService API提取脚本
├── extract-test-api.js # 测试API提取脚本
├── post-build.js       # 构建后处理脚本
└── verify-project.ps1  # 项目验证脚本(PowerShell)
```

## 核心功能模块

### Background Service Worker
Background Service Worker 是扩展的核心后台服务，提供以下功能：

- **右键菜单管理**: 创建和处理上下文相关的右键菜单
- **消息通信**: 处理扩展内部各组件间的消息传递
- **存储管理**: 初始化和管理用户数据、设置和收藏内容
- **生命周期管理**: 处理扩展安装、更新等生命周期事件

详细API文档请参考：[Background Service Worker API](background-service-worker-api.md)

### Content Script
Content Script 运行在网页环境中，负责：

- **页面信息提取**: 获取页面标题、URL、元数据等信息
- **用户交互处理**: 处理页面内的用户操作和选择
- **浮窗功能**: 提供页面内的收藏工具浮窗（后续版本）

### Popup界面
Popup 是用户点击扩展图标时显示的弹出窗口：

- **快速收藏**: 一键收藏当前页面或选中内容
- **功能开关**: 控制自动标签、去重检测等功能
- **导航入口**: 提供管理页面和设置页面的快速入口

#### Popup 组件库
项目提供了完整的可复用组件库，位于 `src/popup/components/`：

**可用组件**:
- `Toggle`: 功能开关组件，支持多种尺寸和状态
- `DetailedBookmarkForm`: 详细收藏表单，支持AI辅助生成

**组件导入**:
```typescript
// 统一导入接口
import { Toggle, DetailedBookmarkForm } from '@/popup/components'

// 单个组件导入
import { Toggle } from '@/popup/components'
```

详细的组件API文档请参考：[Popup组件API文档](popup-components-api.md)

### Options管理页面
Options 页面提供完整的收藏管理功能：

- **收藏管理**: 查看、编辑、删除收藏内容
- **分类管理**: 创建和管理收藏分类
- **标签管理**: 管理标签和查看使用统计
- **导入导出**: 数据的导入导出功能
- **设置配置**: AI功能、同步设置等配置选项

## 核心服务模块

### BookmarkService - 收藏服务
BookmarkService 是负责收藏功能核心业务逻辑的服务类，提供完整的收藏数据管理功能。

**主要功能**:
- ✅ **收藏保存**: 支持URL和文字内容的收藏保存
- ✅ **数据验证**: 自动验证输入数据的完整性和正确性
- ✅ **重复检测**: 智能检测重复收藏，避免数据冗余
- ✅ **查询筛选**: 支持多条件筛选、搜索和排序
- ✅ **批量操作**: 支持批量删除和更新操作
- ✅ **统计分析**: 提供详细的收藏统计信息
- ✅ **类型安全**: 完整的TypeScript类型支持

**核心方法**:
```typescript
// 保存收藏
await bookmarkService.saveBookmark(bookmarkInput)

// 快速收藏当前页面
await bookmarkService.quickBookmark(title, url, favicon, selectedText)

// 收藏选中文字
await bookmarkService.bookmarkSelectedText(selectedText, url, title, context)

// 获取收藏列表（支持筛选和排序）
await bookmarkService.getBookmarks(filter, sort)

// 搜索收藏
await bookmarkService.searchBookmarks(query, filter)

// 检测重复收藏
await bookmarkService.detectDuplicates(threshold)

// 获取统计信息
await bookmarkService.getBookmarkStats()
```

**使用示例**:
```typescript
import { bookmarkService } from '@/services/bookmarkService'

// 保存收藏
const bookmarkInput = {
  type: 'url',
  title: '示例网站',
  url: 'https://example.com',
  category: '技术',
  tags: ['编程', '学习']
}

try {
  const bookmarkId = await bookmarkService.saveBookmark(bookmarkInput)
  console.log('收藏保存成功:', bookmarkId)
} catch (error) {
  console.error('保存失败:', error)
}
```

详细API文档请参考：[BookmarkService API文档](bookmark-service-api.md)

## AI提供商服务使用示例

```typescript
import { aiProviderService } from '@/services/aiProviderService'

// OpenAI连接测试（增强版）
async function testOpenAIConnection() {
  const baseUrl = 'https://api.openai.com/v1'
  const apiKey = 'sk-your-api-key-here'

  try {
    const result = await aiProviderService.testOpenAIConnection(baseUrl, apiKey)

    if (result.success) {
      console.log(`✅ OpenAI连接成功`)
      console.log(`📊 可用模型数量: ${result.modelCount}`)
    } else {
      console.error(`❌ OpenAI连接失败: ${result.error}`)
    }
  } catch (error) {
    console.error('连接测试异常:', error.message)
  }
}

// OpenRouter连接测试（新增增强版）
async function testOpenRouterConnection() {
  const apiKey = 'sk-or-your-api-key-here'

  try {
    const result = await aiProviderService.testOpenRouterConnection(apiKey)

    if (result.success) {
      console.log(`✅ OpenRouter连接成功`)
      console.log(`📊 可用模型数量: ${result.modelCount}`)

      // 获取模型列表
      const models = await aiProviderService.getOpenRouterModels(apiKey)
      console.log(`📋 获取到 ${models.length} 个模型`)

      // 显示推荐模型
      const recommendedModels = models.filter(m => m.isRecommended).slice(0, 3)
      console.log('⭐ 推荐模型:')
      recommendedModels.forEach(model => {
        console.log(`  - ${model.displayName} (${model.parameters})`)
        if (model.description?.includes('定价:')) {
          const pricingMatch = model.description.match(/💰 定价: (.+?)(?:\n|$)/)
          if (pricingMatch) {
            console.log(`    💰 ${pricingMatch[1]}`)
          }
        }
      })
    } else {
      console.error(`❌ OpenRouter连接失败: ${result.error}`)
    }
  } catch (error) {
    console.error('连接测试异常:', error.message)
  }
}

// Azure OpenAI连接测试（新增）
async function testAzureOpenAIConnection() {
  const baseUrl = 'https://your-resource.openai.azure.com'
  const apiKey = 'your-azure-api-key'

  try {
    const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, apiKey)

    if (result.success) {
      console.log(`✅ Azure OpenAI连接成功`)
      console.log(`📊 可用部署数量: ${result.modelCount}`)
    } else {
      console.error(`❌ Azure OpenAI连接失败: ${result.error}`)
    }
  } catch (error) {
    console.error('Azure连接测试异常:', error.message)
  }
}

// 本地Ollama服务测试
async function testOllamaService() {
  const baseUrl = 'http://localhost:11434'

  const result = await aiProviderService.testOllamaConnection(baseUrl)
  if (result.success) {
    const models = await aiProviderService.getOllamaModels(baseUrl)
    console.log(`发现 ${models.length} 个Ollama模型`)
  }
}

// 批量测试多个提供商
async function testAllProviders() {
  const providers = [
    { type: 'ollama', baseUrl: 'http://localhost:11434' },
    { type: 'lm-studio', baseUrl: 'http://localhost:1234/v1' },
    { type: 'openai', baseUrl: 'https://api.openai.com/v1', apiKey: 'sk-key' },
    { type: 'azure-openai', baseUrl: 'https://your-resource.openai.azure.com', apiKey: 'azure-key' }
  ]

  for (const provider of providers) {
    const result = await aiProviderService.testConnection(provider)
    console.log(`${provider.type}: ${result.success ? '成功' : '失败'}`)
  }
}
```

## 性能监控工具使用示例

```typescript
import { performanceMonitor, memoryMonitor, debounce } from '@/utils/performance'

// 性能监控示例
async function monitoredOperation() {
  performanceMonitor.startTimer('database-query')

  try {
    const result = await database.query('SELECT * FROM bookmarks')
    return result
  } finally {
    const duration = performanceMonitor.endTimer('database-query')
    console.log(`数据库查询耗时: ${duration}ms`)
  }
}

// 内存监控示例
memoryMonitor.startMonitoring(30000) // 每30秒检查一次

// 防抖搜索示例
const debouncedSearch = debounce(async (query: string) => {
  const results = await searchBookmarks(query)
  displayResults(results)
}, 300)

// 获取性能统计
const stats = performanceMonitor.getMetricStats('database-query')
if (stats) {
  console.log(`平均查询时间: ${stats.average}ms`)
}
```

## 存储服务
项目提供了双重存储保障机制：

**IndexedDBService** - 主要存储服务:
- 使用IndexedDB提供高性能的本地数据存储
- 支持复杂查询、索引和事务操作
- 自动处理数据库初始化和版本升级

**FallbackStorageService** - 降级存储服务:
- 当IndexedDB不可用时自动切换到Chrome Storage API
- 确保在各种环境下的数据存储可靠性
- 提供与IndexedDBService相同的API接口

**存储特性**:
- ✅ **自动降级**: IndexedDB不可用时自动切换到Chrome Storage
- ✅ **数据一致性**: 统一的API接口确保数据操作一致性
- ✅ **性能优化**: 支持索引查询和批量操作
- ✅ **错误恢复**: 完善的错误处理和恢复机制

## 性能监控工具
项目提供了完整的性能监控和优化工具：

**PerformanceMonitor** - 性能指标监控:
- 提供计时器功能监控操作耗时
- 收集和分析性能指标数据
- 生成详细的性能报告
- 支持自定义指标记录

**MemoryMonitor** - 内存使用监控:
- 实时监控应用内存使用情况
- 自动检测内存使用率过高的情况
- 提供内存使用统计和分析
- 支持自定义监控间隔

**工具函数**:
- `debounce()` - 防抖函数，优化频繁操作
- `throttle()` - 节流函数，限制执行频率

**性能特性**:
- ✅ **单例模式**: 全局统一的监控实例
- ✅ **类型安全**: 完整的TypeScript类型支持
- ✅ **内存安全**: 自动清理过期数据和计时器
- ✅ **错误处理**: 完善的错误捕获和恢复机制
- ✅ **浏览器兼容**: 支持现代浏览器性能API

## 开发指南

### 项目验证脚本

项目提供了多个实用脚本来帮助开发和验证：

#### test-build.js - 构建测试脚本
验证项目配置是否正确，无需安装依赖即可检查：
```bash
node scripts/test-build.js
```

**功能特性**：
- ✅ 检查必要文件是否存在
- ✅ 验证package.json配置完整性
- ✅ 验证Chrome扩展manifest.json配置
- ✅ 验证TypeScript配置正确性
- ✅ 检查源代码目录结构完整性

**适用场景**：
- 新开发者环境设置前的预检查
- CI/CD流程中的配置验证
- 项目结构完整性验证

#### test-ai-integration.js - AI集成服务综合测试脚本
专门用于测试聚合AI服务集成功能的综合测试脚本：
```bash
node scripts/test-ai-integration.js
# 或者
npm run test:ai-integration
```

**功能特性**：
- ✅ 测试AI集成服务基本功能（提供商管理、连接测试、模型获取）
- ✅ 测试本地AI服务发现功能（自动扫描和连接测试）
- ✅ 测试AI对话功能（本地和云端服务对话）
- ✅ 测试配置管理功能（导出配置、统计信息、配置验证）
- ✅ 模拟Chrome扩展环境和存储API
- ✅ 生成详细的测试报告和汇总统计
- ✅ 彩色输出和友好的用户界面

**测试覆盖**：
- AI集成服务基本功能
- 本地AI服务发现功能
- AI对话功能
- 配置管理功能

**使用场景**：
- 验证AI集成服务的完整功能
- 测试聚合AI服务的集成效果
- 开发环境AI功能综合检查
- CI/CD中的AI集成验证

#### test-local-ai-services.js - 本地AI服务测试脚本
专门用于测试本地AI服务集成功能的自动化测试脚本：
```bash
node scripts/test-local-ai-services.js
# 或者
npm run test:local-ai
```

**功能特性**：
- ✅ 自动发现本地AI服务（Ollama、LM Studio、Xinference等）
- ✅ 测试服务连接状态和响应时间
- ✅ 获取和验证模型列表信息
- ✅ 测试自定义服务配置功能
- ✅ 生成详细的测试报告和统计信息
- ✅ 彩色输出和友好的错误提示

**支持的AI服务**：
- Ollama (端口 11434)
- LM Studio (端口 1234)
- Xinference (端口 9997)
- Text Generation WebUI (端口 5000)
- LocalAI (端口 8080)
- 自定义端口服务

**使用场景**：
- 验证本地AI服务集成功能
- 测试AI服务连接和模型获取
- 开发环境AI服务状态检查
- CI/CD中的AI功能验证

详细文档请参考：[scripts/README.md](../scripts/README.md)、[AI集成服务测试脚本API文档](test-ai-integration-script-api.md) 和 [本地AI服务测试脚本API文档](test-local-ai-services-script-api.md)

### 添加新功能

1. 在对应的目录下创建新的组件或服务
2. 更新类型定义（如需要）
3. 添加相应的测试
4. 更新文档

### 调试技巧

- 使用Chrome开发者工具调试Content Script
- 在 `chrome://extensions/` 页面查看Background Script日志
- 使用React DevTools调试React组件

### 测试页面配置

为了保持生产环境界面的整洁，项目中的测试页面默认隐藏。这些页面仍保留在代码中，可在需要时启用。

#### 隐藏的测试页面
- **shadcn测试** - 测试shadcn/ui组件集成
- **删除测试** - 测试删除确认模态框
- **VirtualBookmarkList测试** - 测试虚拟书签列表
- **PopupApp测试** - 测试弹出窗口组件
- **BookmarksTab测试** - 测试书签标签页

#### 启用测试页面的方法

**方法1：URL参数（推荐）**
在扩展设置页面URL后添加参数：
```
chrome-extension://[extension-id]/src/options/index.html?dev=true
chrome-extension://[extension-id]/src/options/index.html?showTests=true
```

**方法2：修改代码配置**
编辑 `src/options/OptionsApp.tsx` 中的 `DEV_CONFIG`：
```typescript
const DEV_CONFIG = {
  showTestPages: true,  // 改为 true
  enableTestPagesViaUrl: true
}
```

**方法3：开发者控制台**
在设置页面的控制台中执行：
```javascript
window.location.href = window.location.href + '?dev=true'
```

详细配置说明请参考：[测试页面配置文档](test-pages-configuration.md)

## Git版本控制

### 提交规范

本项目采用[约定式提交](https://www.conventionalcommits.org/zh-hans/v1.0.0/)规范：

```
<类型>[可选 范围]: <描述>

[可选 正文]

[可选 脚注]
```

**提交类型**：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 重构
- `test`: 增加测试
- `chore`: 构建过程或辅助工具的变动

### 克隆和设置

```bash
# 克隆项目
git clone <repository-url>
cd Qiankun-Pouch

# 安装依赖
npm install

# 构建项目
npm run build
```

## 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'feat: add some amazing feature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

**提交前检查**：
- 运行 `npm run type-check` 确保类型检查通过
- 运行 `npm run build` 确保构建成功
- 运行测试确保功能正常

## 测试框架

项目提供了完整的测试覆盖：

### 单元测试
```bash
# 运行BookmarkService单元测试
node tests/bookmarkService.test.js

# 运行构建脚本测试
node scripts/test-build.test.js

# 运行AI服务相关测试
npm run test:ai-services

# 运行所有测试
npm run test
```

### 集成测试
```bash
# 运行shadcn组件集成测试
node tests/integration-test-shadcn.js

# 运行Popup组件集成测试
node tests/popup-components.test.js

# 运行本地AI服务集成测试
node scripts/test-local-ai-services.js
```

### AI功能测试
```bash
# 测试AI集成服务综合功能
npm run test:ai-integration

# 测试本地AI服务集成
npm run test:local-ai

# 测试AI对话服务
npm run test -- tests/aiChatService.test.ts

# 测试AI模型服务
npm run test -- tests/aiModelService.test.ts

# 测试AI提供商服务
npm run test -- tests/aiProviderService.test.ts

# 测试AI集成服务
npm run test -- tests/aiIntegrationService.test.ts
```

### 手动测试
- `tests/manual-test-detailedbookmarkform.md` - DetailedBookmarkForm组件手动测试指南

**测试文件**:
- `tests/bookmarkService.test.js` - BookmarkService功能测试
- `tests/integration-test-shadcn.js` - shadcn组件重构集成测试
- `tests/popup-components.test.js` - Popup组件功能测试
- `tests/DetailedBookmarkForm.shadcn.test.tsx` - DetailedBookmarkForm shadcn单元测试
- `scripts/test-build.test.js` - 构建脚本测试
- `scripts/test-ai-integration.js` - AI集成服务综合测试脚本（新增）
- `scripts/test-local-ai-services.js` - 本地AI服务集成测试脚本
- `scripts/quick-test-local-ai.js` - 本地AI服务快速测试脚本
- `tests/aiChatService.test.ts` - AI对话服务单元测试
- `tests/aiIntegrationService.test.ts` - AI集成服务单元测试
- `tests/aiProviderService.test.ts` - AI提供商服务单元测试
- `tests/aiModelService.test.ts` - AI模型服务单元测试
- `tests/localAIServiceAdapter.test.ts` - 本地AI服务适配器单元测试
- `tests/*.test.js` - 其他组件和服务测试

**测试特性**:
- ✅ **轻量级框架**: 自定义测试框架，零依赖
- ✅ **异步支持**: 完整的异步测试支持
- ✅ **详细报告**: 清晰的测试结果和错误信息
- ✅ **模拟数据**: 完整的Mock数据和服务
- ✅ **边界测试**: 覆盖边界情况和错误处理
- ✅ **shadcn集成测试**: 验证shadcn/ui组件重构完整性
- ✅ **静态分析**: 代码结构和导入语句验证
- ✅ **构建产物验证**: 确保构建输出包含必要的shadcn资源
- ✅ **AI服务测试**: 完整的本地AI服务集成测试覆盖
- ✅ **自动化测试**: 支持自动发现和测试多种AI服务
- ✅ **性能监控**: 测试响应时间和连接状态
- ✅ **错误处理**: 友好的错误提示和恢复机制

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

## 更新日志

### v2.0.0 (2025-01-20)

- ✅ AI模型管理功能全面优化
- ✅ 智能模型过滤和数据同步
- ✅ 供应商名称自动识别
- ✅ 用户体验显著改进

### v1.1.0 (2024-12-15)

- ✅ 关于我们页面功能
- ✅ 帮助中心页面功能
- ✅ 主题切换功能

### v1.0.0 (开发中)

- ✅ 项目初始化和基础架构搭建
- ✅ Chrome扩展Manifest V3配置
- ✅ React + TypeScript + Tailwind CSS集成
- ✅ Vite构建工具配置
- ✅ 基础UI界面（Popup和Options页面）
- 🔄 核心功能开发中...

## 致谢

感谢所有为这个项目做出贡献的开发者和用户！
