import React, { useState, useEffect } from 'react'
import { Star, Info, Globe, Calendar, Code, Shield, Package } from 'lucide-react'
import type { AboutPageData } from '../data/aboutInfo'
import { defaultAboutData } from '../data/aboutInfo'
import { getExtensionInfo, getBuildInfo, getExtensionPermissions, isExtensionEnvironment } from '../utils/manifestReader'
import { useResponsive } from '../hooks/useResponsive'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'

interface AboutTabProps {
  aboutData?: AboutPageData
}

/**
 * 关于我们页面组件
 * 显示扩展的基本信息、版本、开发者信息等
 */
const AboutTab: React.FC<AboutTabProps> = ({ aboutData: propAboutData }) => {
  const [aboutData, setAboutData] = useState<AboutPageData | null>(propAboutData || null)
  const [loading, setLoading] = useState(!propAboutData)
  const [permissions, setPermissions] = useState<string[]>([])
  const [isExtensionEnv, setIsExtensionEnv] = useState(false)

  // 响应式支持
  const { isMobile, getResponsiveValue } = useResponsive()

  // 组件挂载时加载数据
  useEffect(() => {
    const loadAboutData = async () => {
      try {
        setLoading(true)
        
        // 检查是否在扩展环境中
        const extensionEnv = isExtensionEnvironment()
        setIsExtensionEnv(extensionEnv)
        
        // 获取扩展信息
        const extensionInfo = getExtensionInfo()
        const buildInfo = getBuildInfo()
        const extensionPermissions = getExtensionPermissions()
        
        setPermissions(extensionPermissions)
        
        // 构建完整的关于数据
        const completeAboutData: AboutPageData = {
          extensionInfo,
          buildInfo,
          developerInfo: defaultAboutData.developerInfo,
          licenseInfo: defaultAboutData.licenseInfo
        }
        
        setAboutData(completeAboutData)
      } catch (error) {
        console.error('加载关于数据时发生错误:', error)
        
        // 使用默认数据，确保页面能正常显示
        const fallbackData: AboutPageData = {
          ...defaultAboutData,
          extensionInfo: {
            ...defaultAboutData.extensionInfo,
            name: 'Universe Bag（乾坤袋）',
            version: '1.0.0',
            description: '智能收藏管理工具，支持AI自动分类和云端同步',
            developer: 'coffeebean'
          },
          buildInfo: {
            buildDate: new Date().toISOString().split('T')[0],
            buildVersion: '1.0.0'
          }
        }
        
        setAboutData(fallbackData)
        setPermissions([]) // 设置空权限列表
        setIsExtensionEnv(false) // 标记为非扩展环境
      } finally {
        setLoading(false)
      }
    }

    if (!propAboutData) {
      loadAboutData()
    }
  }, [propAboutData])

  // 如果正在加载，显示加载状态
  if (loading || !aboutData) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">加载扩展信息中...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const { extensionInfo, buildInfo, developerInfo, licenseInfo } = aboutData

  return (
    <div className={`${getResponsiveValue({ sm: 'p-4', md: 'p-6' }, 'p-6')}`}>
      <CardTitle className="text-2xl mb-6">关于我们</CardTitle>
      
      <div className={`${getResponsiveValue({ sm: 'space-y-4', md: 'space-y-6' }, 'space-y-6')}`}>
        {/* 扩展基本信息卡片 */}
        <Card>
          <CardContent className="pt-6">
            <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'items-start space-x-4'}`}>
              <div className={`flex-shrink-0 ${isMobile ? 'self-center' : ''}`}>
                <Star className="w-12 h-12 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="flex-1">
                <CardTitle className={`${isMobile ? 'text-center' : ''} text-xl mb-2`}>
                  {extensionInfo.name}
                </CardTitle>
                <CardDescription className={`${isMobile ? 'text-center' : ''} mb-4`}>
                  {extensionInfo.description}
                </CardDescription>
                <div className={`grid ${getResponsiveValue({ sm: 'grid-cols-1', md: 'grid-cols-2' }, 'grid-cols-1 md:grid-cols-2')} gap-4 text-sm`}>
                  <div className="flex items-center space-x-2">
                    <Code className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">版本:</span>
                    <Badge variant="secondary">{extensionInfo.version}</Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">构建日期:</span>
                    <Badge variant="secondary">{buildInfo.buildDate}</Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 开发者信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Info className="w-5 h-5 mr-2 text-primary-600 dark:text-primary-400" />
              开发者信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <span className="text-muted-foreground w-16">开发者:</span>
                <Badge variant="outline">{developerInfo.name}</Badge>
              </div>
              {developerInfo.website && (
                <div className="flex items-center space-x-3">
                  <Globe className="w-4 h-4 text-muted-foreground" />
                  <span className="text-muted-foreground w-16">网站:</span>
                  <a 
                    href={developerInfo.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 underline"
                  >
                    {developerInfo.website}
                  </a>
                </div>
              )}
              {developerInfo.communityUrl && (
                <div className="flex items-center space-x-3">
                  <Globe className="w-4 h-4 text-muted-foreground" />
                  <span className="text-muted-foreground w-16">交流群:</span>
                  <a 
                    href={developerInfo.communityUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 underline"
                  >
                    {developerInfo.communityUrl}
                  </a>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 技术信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="w-5 h-5 mr-2 text-primary-600 dark:text-primary-400" />
              技术信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <span className="text-muted-foreground w-20">运行环境:</span>
                  <Badge variant={isExtensionEnv ? "default" : "secondary"}>
                    {isExtensionEnv ? '扩展环境' : '开发环境'}
                  </Badge>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-muted-foreground w-20">构建版本:</span>
                  <Badge variant="outline">{buildInfo.buildVersion}</Badge>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-muted-foreground w-20">Manifest:</span>
                  <Badge variant="outline">v{buildInfo.manifestVersion}</Badge>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <span className="text-muted-foreground w-20">权限数量:</span>
                  <Badge variant="secondary">{permissions.length} 个</Badge>
                </div>
                {permissions.length > 0 && (
                  <div>
                    <span className="text-muted-foreground text-sm">主要权限:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {permissions.slice(0, 3).map((permission) => (
                        <Badge key={permission} variant="outline" className="text-xs">
                          {permission}
                        </Badge>
                      ))}
                      {permissions.length > 3 && (
                        <span className="text-muted-foreground text-xs">
                          +{permissions.length - 3} 更多
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 权限详情卡片 */}
        {permissions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="w-5 h-5 mr-2 text-primary-600 dark:text-primary-400" />
                权限详情
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                {permissions.map((permission) => (
                  <Card key={permission} className="p-3">
                    <CardContent className="p-0">
                      <Badge variant="outline" className="mb-1">
                        {permission}
                      </Badge>
                      <CardDescription className="text-xs">
                        {getPermissionDescription(permission)}
                      </CardDescription>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}



        {/* 版权信息 */}
        <Card>
          <CardContent className="text-center pt-6">
            <CardDescription>
              <p>© 2024 {developerInfo.name}. 保留所有权利。</p>
              <p className="mt-1">感谢您使用 Universe Bag，让收藏管理变得更简单！</p>
            </CardDescription>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

/**
 * 获取权限的中文描述
 */
const getPermissionDescription = (permission: string): string => {
  const descriptions: Record<string, string> = {
    'storage': '存储收藏数据和用户设置',
    'activeTab': '获取当前标签页信息',
    'contextMenus': '添加右键菜单选项',
    'tabs': '管理浏览器标签页',
    'scripting': '在网页中注入脚本',
    'bookmarks': '访问浏览器书签',
    'history': '访问浏览器历史记录',
    'cookies': '读取网站 Cookie',
    'notifications': '显示系统通知',
    'alarms': '设置定时任务',
    'identity': '用户身份验证',
    'webRequest': '监控网络请求',
    'webNavigation': '监控页面导航',
    'downloads': '管理下载文件'
  }
  
  return descriptions[permission] || '扩展功能所需权限'
}

export default AboutTab