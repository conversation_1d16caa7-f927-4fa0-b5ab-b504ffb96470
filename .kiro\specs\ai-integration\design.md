# AI集成功能设计文档

## 概述

本设计文档基于现有的AI集成基础架构，扩展和完善AI服务集成功能。项目中已经存在基础的AI服务架构（`aiService.ts`、`aiConfigService.ts`、`aiCacheService.ts`）和基础的UI组件（`AIIntegrationTab.tsx`），我们将在此基础上实现真实的连接测试、模型列表获取、搜索功能和配置持久化。

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "UI层"
        A[AIIntegrationTab] --> B[AIProviderCard]
        A --> C[AIModelSelector]
        A --> D[AIConnectionTester]
        A --> E[AIModelSearch]
        A --> F[AIChatTester]
        F --> G[ChatMessageBubble]
        F --> H[ChatConfigPanel]
    end
    
    subgraph "服务层"
        I[AIIntegrationService] --> J[AIConfigService]
        I --> K[AIProviderService]
        I --> L[AIModelService]
        I --> M[AIChatService]
        J --> N[ChromeStorageService]
        M --> O[ChatSessionManager]
    end
    
    subgraph "API层"
        P[OllamaAPI] --> Q[HTTP Client]
        R[LMStudioAPI] --> Q
        S[OpenRouterAPI] --> Q
        T[CustomAPI] --> Q
        U[ChatAPI] --> Q
    end
    
    A --> I
    F --> M
    I --> P
    I --> R
    I --> S
    I --> T
    M --> U
```

### 数据流设计

```mermaid
sequenceDiagram
    participant UI as AIIntegrationTab
    participant Service as AIIntegrationService
    participant Config as AIConfigService
    participant API as Provider API
    participant Storage as ChromeStorage
    
    UI->>Service: 配置AI提供商
    Service->>Config: 验证配置
    Config->>Storage: 保存配置
    
    UI->>Service: 测试连接
    Service->>API: 发送测试请求
    API-->>Service: 返回连接状态
    Service-->>UI: 更新连接状态
    
    UI->>Service: 获取模型列表
    Service->>API: 请求模型列表
    API-->>Service: 返回模型数据
    Service->>Config: 缓存模型列表
    Service-->>UI: 返回模型列表
```

### 对话测试数据流

```mermaid
sequenceDiagram
    participant ChatUI as AIChatTester
    participant ChatService as AIChatService
    participant SessionMgr as ChatSessionManager
    participant API as ChatAPI
    participant Storage as LocalStorage
    
    ChatUI->>ChatService: 创建对话会话
    ChatService->>SessionMgr: 初始化会话
    SessionMgr->>Storage: 保存会话配置
    
    ChatUI->>ChatService: 发送消息
    ChatService->>API: 调用AI模型API
    API-->>ChatService: 返回AI回复
    ChatService->>SessionMgr: 更新对话历史
    SessionMgr->>Storage: 保存消息记录
    ChatService-->>ChatUI: 显示AI回复
    
    ChatUI->>ChatService: 更新配置
    ChatService->>SessionMgr: 更新会话配置
    SessionMgr->>Storage: 保存新配置
    
    ChatUI->>ChatService: 导出对话
    ChatService->>SessionMgr: 获取完整会话
    SessionMgr-->>ChatService: 返回会话数据
    ChatService-->>ChatUI: 返回导出文件
```

## 组件设计

### 1. AIIntegrationService（新增）

负责统一管理AI集成相关的业务逻辑。

```typescript
interface AIIntegrationService {
  // 提供商管理
  getSupportedProviders(): AIProviderInfo[]
  configureProvider(config: AIProviderConfig): Promise<void>
  removeProvider(providerId: string): Promise<void>
  
  // 连接测试
  testConnection(providerId: string): Promise<AIConnectionResult>
  testAllConnections(): Promise<AIConnectionResult[]>
  
  // 模型管理
  getAvailableModels(providerId: string, forceRefresh?: boolean): Promise<AIModel[]>
  searchModels(providerId: string, query: string): Promise<AIModel[]>
  
  // 配置持久化
  saveConfiguration(): Promise<void>
  loadConfiguration(): Promise<void>
  exportConfiguration(): Promise<string>
  importConfiguration(config: string): Promise<void>
}
```

### 2. AIProviderService（新增）

专门处理不同AI提供商的API交互。

```typescript
interface AIProviderService {
  // Ollama相关
  testOllamaConnection(baseUrl: string): Promise<boolean>
  getOllamaModels(baseUrl: string): Promise<OllamaModel[]>
  
  // LM Studio相关
  testLMStudioConnection(baseUrl: string): Promise<boolean>
  getLMStudioModels(baseUrl: string): Promise<LMStudioModel[]>
  
  // OpenRouter相关
  testOpenRouterConnection(apiKey: string): Promise<boolean>
  getOpenRouterModels(apiKey: string): Promise<OpenRouterModel[]>
  
  // 通用API测试
  testCustomAPI(config: CustomAPIConfig): Promise<boolean>
}
```

### 3. AIModelService（新增）

处理模型相关的操作，包括搜索、筛选、缓存等。

```typescript
interface AIModelService {
  // 模型搜索
  searchModels(models: AIModel[], query: string): AIModel[]
  filterModels(models: AIModel[], filters: ModelFilter): AIModel[]
  
  // 模型缓存
  cacheModels(providerId: string, models: AIModel[]): Promise<void>
  getCachedModels(providerId: string): Promise<AIModel[] | null>
  clearModelCache(providerId?: string): Promise<void>
  
  // 模型推荐
  getRecommendedModels(useCase: string): AIModel[]
  getPopularModels(): AIModel[]
}
```

### 4. AIChatService（新增）

专门处理AI对话测试相关的功能。

```typescript
interface AIChatService {
  // 对话管理
  createChatSession(modelId: string, config: ChatConfig): ChatSession
  getChatSession(sessionId: string): ChatSession | null
  updateChatSession(sessionId: string, session: Partial<ChatSession>): void
  deleteChatSession(sessionId: string): void
  
  // 消息发送
  sendMessage(
    sessionId: string, 
    message: string, 
    serviceConfig: LocalServiceConfig
  ): Promise<ChatTestResult>
  
  sendStreamMessage(
    sessionId: string, 
    message: string, 
    serviceConfig: LocalServiceConfig,
    onChunk: (chunk: string) => void
  ): Promise<ChatTestResult>
  
  // 对话历史
  addMessage(sessionId: string, message: ChatMessage): void
  getMessages(sessionId: string): ChatMessage[]
  clearMessages(sessionId: string): void
  
  // 配置管理
  updateChatConfig(sessionId: string, config: Partial<ChatConfig>): void
  getChatConfig(sessionId: string): ChatConfig
  
  // 导出功能
  exportChatSession(sessionId: string): string
  importChatSession(data: string): ChatSession
}
```

### 4. 增强的UI组件

#### AIProviderCard（新增）
```typescript
interface AIProviderCardProps {
  provider: AIProviderInfo
  config?: AIProviderConfig
  onConfigure: (config: AIProviderConfig) => void
  onTest: () => void
  onRemove: () => void
  isConnected: boolean
  isTesting: boolean
}
```

#### AIModelSelector（增强）
```typescript
interface AIModelSelectorProps {
  providerId: string
  models: AIModel[]
  selectedModel?: string
  onModelSelect: (model: AIModel) => void
  onRefresh: () => void
  searchable?: boolean
  filterable?: boolean
  loading?: boolean
}
```

#### AIConnectionTester（新增）
```typescript
interface AIConnectionTesterProps {
  providerId: string
  config: AIProviderConfig
  onTestComplete: (result: AIConnectionResult) => void
  autoTest?: boolean
}
```

#### AIChatTester（新增）
```typescript
interface AIChatTesterProps {
  selectedModel: AIModel
  serviceConfig: LocalServiceConfig
  onClose: () => void
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  tokens?: number
  responseTime?: number
}

interface ChatSession {
  id: string
  modelId: string
  messages: ChatMessage[]
  config: ChatConfig
  createdAt: Date
  updatedAt: Date
}

interface ChatConfig {
  temperature: number
  maxTokens: number
  systemPrompt?: string
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
}
```

#### AIModelChatInterface（新增）
```typescript
interface AIModelChatInterfaceProps {
  model: AIModel
  serviceConfig: LocalServiceConfig
  onSendMessage: (message: string) => Promise<void>
  messages: ChatMessage[]
  isLoading: boolean
  config: ChatConfig
  onConfigChange: (config: ChatConfig) => void
}
```

#### ChatMessageBubble（新增）
```typescript
interface ChatMessageBubbleProps {
  message: ChatMessage
  onCopy: (content: string) => void
  onRegenerate?: () => void
  showActions?: boolean
}
```

## 数据模型

### 扩展的AI提供商配置

```typescript
interface AIProviderConfig {
  id: string
  name: string
  type: 'ollama' | 'lm-studio' | 'openrouter' | 'custom'
  baseUrl: string
  apiKey?: string
  headers?: Record<string, string>
  timeout?: number
  enabled: boolean
  createdAt: Date
  updatedAt: Date
}

interface AIProviderInfo {
  id: string
  name: string
  description: string
  icon: string
  type: 'ollama' | 'lm-studio' | 'openrouter' | 'custom'
  defaultBaseUrl: string
  requiresApiKey: boolean
  supportedFeatures: string[]
  documentationUrl?: string
}

interface AIModel {
  id: string
  name: string
  displayName: string
  description?: string
  size?: string
  parameters?: string
  tags?: string[]
  capabilities?: string[]
  providerId: string
  isRecommended?: boolean
  isPopular?: boolean
  maxTokens?: number
  contextLength?: number
  supportedFormats?: string[]
}

interface AIConnectionResult {
  providerId: string
  success: boolean
  responseTime?: number
  error?: string
  modelCount?: number
  testedAt: Date
}

### 对话测试相关数据模型

```typescript
interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  tokens?: number
  responseTime?: number
  error?: string
}

interface ChatSession {
  id: string
  modelId: string
  providerId: string
  serviceConfig: LocalServiceConfig
  messages: ChatMessage[]
  config: ChatConfig
  createdAt: Date
  updatedAt: Date
  totalTokens: number
  totalCost?: number
}

interface ChatConfig {
  temperature: number
  maxTokens: number
  systemPrompt?: string
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stream?: boolean
}

interface ChatRequest {
  model: string
  messages: Array<{
    role: 'user' | 'assistant' | 'system'
    content: string
  }>
  temperature?: number
  max_tokens?: number
  top_p?: number
  frequency_penalty?: number
  presence_penalty?: number
  stream?: boolean
}

interface ChatResponse {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    message: {
      role: 'assistant'
      content: string
    }
    finish_reason: string
  }>
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

interface StreamChatResponse {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    delta: {
      role?: 'assistant'
      content?: string
    }
    finish_reason?: string
  }>
}

interface ChatTestResult {
  success: boolean
  message?: ChatMessage
  error?: string
  responseTime: number
  tokenUsage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}
```

## 错误处理

### 错误类型定义

```typescript
enum AIIntegrationErrorType {
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  INVALID_API_KEY = 'INVALID_API_KEY',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  INVALID_CONFIG = 'INVALID_CONFIG',
  MODEL_NOT_FOUND = 'MODEL_NOT_FOUND',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}

interface AIIntegrationError {
  type: AIIntegrationErrorType
  message: string
  details?: any
  providerId?: string
  timestamp: Date
}
```

### 错误处理策略

1. **连接错误**：显示用户友好的错误信息，提供重试选项
2. **API密钥错误**：引导用户检查和更新API密钥
3. **网络错误**：提供网络诊断建议
4. **超时错误**：允许用户调整超时设置
5. **配置错误**：提供配置验证和修复建议

## 测试策略

### 单元测试

1. **AIIntegrationService测试**
   - 提供商配置管理
   - 连接测试逻辑
   - 模型获取和缓存

2. **AIProviderService测试**
   - 各提供商API调用
   - 错误处理
   - 响应解析

3. **AIModelService测试**
   - 模型搜索算法
   - 缓存机制
   - 筛选逻辑

### 集成测试

1. **端到端连接测试**
   - 真实API连接
   - 模型列表获取
   - 配置持久化

2. **UI交互测试**
   - 用户操作流程
   - 错误状态显示
   - 响应式布局

### 性能测试

1. **模型列表加载性能**
2. **搜索响应速度**
3. **大量模型的虚拟滚动**
4. **并发连接测试**

## 安全考虑

### API密钥安全

1. **加密存储**：使用Chrome存储API的加密功能
2. **内存保护**：避免在日志中记录敏感信息
3. **传输安全**：确保HTTPS连接
4. **访问控制**：限制API密钥的访问权限

### 数据隐私

1. **本地存储**：配置信息仅存储在本地
2. **数据清理**：提供清除所有配置的选项
3. **同步控制**：允许用户选择是否同步配置

## 性能优化

### 缓存策略

1. **模型列表缓存**：缓存24小时，支持手动刷新
2. **连接状态缓存**：缓存5分钟，避免频繁测试
3. **搜索结果缓存**：缓存搜索结果，提高响应速度

### 虚拟滚动

对于大量模型的显示，使用虚拟滚动技术：

```typescript
interface VirtualScrollConfig {
  itemHeight: number
  containerHeight: number
  overscan: number
}
```

### 懒加载

1. **按需加载模型详情**
2. **分页获取大量模型**
3. **延迟加载非关键功能**

## 用户体验设计

### 加载状态

1. **连接测试**：显示进度条和状态信息
2. **模型加载**：显示骨架屏和加载动画
3. **搜索**：实时搜索结果更新

### 错误反馈

1. **友好的错误信息**：避免技术术语
2. **解决方案建议**：提供具体的修复步骤
3. **快速重试**：一键重试失败的操作

### 响应式设计

1. **移动端适配**：优化小屏幕显示
2. **键盘导航**：支持完整的键盘操作
3. **无障碍支持**：符合WCAG标准

## 国际化支持

### 多语言文本

```typescript
interface AIIntegrationTexts {
  providers: {
    ollama: string
    lmStudio: string
    openRouter: string
    custom: string
  }
  status: {
    connected: string
    disconnected: string
    testing: string
    error: string
  }
  errors: {
    connectionFailed: string
    invalidApiKey: string
    networkError: string
    timeout: string
  }
}
```

### 本地化配置

支持不同地区的默认配置和推荐模型。

## 监控和分析

### 使用统计

1. **连接成功率**：各提供商的连接成功率
2. **模型使用频率**：用户最常用的模型
3. **错误统计**：常见错误类型和频率

### 性能监控

1. **响应时间**：API调用响应时间
2. **加载时间**：UI组件加载时间
3. **内存使用**：组件内存占用情况

## 扩展性设计

### 插件架构

为未来支持更多AI提供商预留扩展接口：

```typescript
interface AIProviderPlugin {
  id: string
  name: string
  version: string
  
  testConnection(config: any): Promise<boolean>
  getModels(config: any): Promise<AIModel[]>
  validateConfig(config: any): ValidationResult
}
```

### 配置迁移

支持配置格式的版本升级和迁移：

```typescript
interface ConfigMigration {
  fromVersion: string
  toVersion: string
  migrate(oldConfig: any): any
}
```

## 部署和维护

### 配置管理

1. **环境配置**：开发、测试、生产环境的不同配置
2. **功能开关**：通过配置控制功能的启用/禁用
3. **版本兼容**：向后兼容旧版本的配置格式

### 更新策略

1. **渐进式更新**：逐步推出新功能
2. **回滚机制**：支持快速回滚到稳定版本
3. **用户通知**：及时通知用户重要更新