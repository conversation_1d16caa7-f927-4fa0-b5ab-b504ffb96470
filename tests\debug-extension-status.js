// 扩展状态诊断脚本
// 在浏览器控制台中运行此脚本来诊断扩展问题

console.log('🔍 Universe Bag 扩展状态诊断')
console.log('================================')

// 检查Chrome扩展API可用性
function checkChromeAPI() {
  console.log('\n📋 检查Chrome扩展API...')
  
  if (typeof chrome === 'undefined') {
    console.error('❌ Chrome API不可用')
    return false
  }
  
  if (!chrome.runtime) {
    console.error('❌ chrome.runtime不可用')
    return false
  }
  
  if (!chrome.storage) {
    console.error('❌ chrome.storage不可用')
    return false
  }
  
  console.log('✅ Chrome扩展API可用')
  return true
}

// 检查IndexedDB可用性
function checkIndexedDB() {
  console.log('\n💾 检查IndexedDB...')
  
  if (typeof indexedDB === 'undefined') {
    console.warn('⚠️ IndexedDB不可用，将使用降级存储')
    return false
  }
  
  console.log('✅ IndexedDB可用')
  return true
}

// 检查扩展连接
async function checkExtensionConnection() {
  console.log('\n🔗 检查扩展连接...')
  
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'PING',
      data: {}
    })
    
    if (response && response.success) {
      console.log('✅ 扩展连接正常')
      console.log('📊 响应数据:', response.data)
      return true
    } else {
      console.error('❌ 扩展响应异常:', response)
      return false
    }
  } catch (error) {
    console.error('❌ 扩展连接失败:', error)
    return false
  }
}

// 检查存储权限
async function checkStoragePermissions() {
  console.log('\n🔐 检查存储权限...')
  
  try {
    // 测试Chrome Storage
    await chrome.storage.local.set({ test: 'value' })
    const result = await chrome.storage.local.get(['test'])
    
    if (result.test === 'value') {
      console.log('✅ Chrome Storage权限正常')
      await chrome.storage.local.remove(['test'])
      return true
    } else {
      console.error('❌ Chrome Storage权限异常')
      return false
    }
  } catch (error) {
    console.error('❌ 存储权限检查失败:', error)
    return false
  }
}

// 检查当前存储数据
async function checkStorageData() {
  console.log('\n📦 检查存储数据...')
  
  try {
    const data = await chrome.storage.local.get(null)
    const keys = Object.keys(data)
    
    console.log(`📊 存储项数量: ${keys.length}`)
    
    if (keys.length > 0) {
      console.log('🗂️ 存储项列表:')
      keys.forEach(key => {
        const value = data[key]
        const type = Array.isArray(value) ? 'array' : typeof value
        const size = JSON.stringify(value).length
        console.log(`  - ${key}: ${type} (${size} bytes)`)
      })
    } else {
      console.log('📭 暂无存储数据')
    }
    
    return true
  } catch (error) {
    console.error('❌ 检查存储数据失败:', error)
    return false
  }
}

// 测试收藏功能
async function testBookmarkFunction() {
  console.log('\n🧪 测试收藏功能...')
  
  try {
    const testData = {
      title: '诊断测试页面',
      url: window.location.href,
      favIconUrl: '/favicon.ico',
      timestamp: new Date().toISOString()
    }
    
    const response = await chrome.runtime.sendMessage({
      type: 'QUICK_BOOKMARK',
      data: testData
    })
    
    if (response && response.success) {
      console.log('✅ 收藏功能正常')
      console.log('📝 收藏ID:', response.data.bookmarkId)
      
      // 测试收藏状态检查
      const statusResponse = await chrome.runtime.sendMessage({
        type: 'CHECK_BOOKMARK_STATUS',
        data: { url: testData.url }
      })
      
      if (statusResponse && statusResponse.success) {
        console.log('✅ 收藏状态检查正常')
        console.log('📊 收藏状态:', statusResponse.data)
      }
      
      return true
    } else {
      console.error('❌ 收藏功能异常:', response)
      return false
    }
  } catch (error) {
    console.error('❌ 收藏功能测试失败:', error)
    return false
  }
}

// 获取扩展信息
async function getExtensionInfo() {
  console.log('\n📋 扩展信息...')
  
  try {
    const manifest = chrome.runtime.getManifest()
    console.log('📦 扩展名称:', manifest.name)
    console.log('🔢 版本:', manifest.version)
    console.log('📄 Manifest版本:', manifest.manifest_version)
    console.log('🔗 扩展ID:', chrome.runtime.id)
    
    return true
  } catch (error) {
    console.error('❌ 获取扩展信息失败:', error)
    return false
  }
}

// 运行完整诊断
async function runFullDiagnosis() {
  console.log('🚀 开始完整诊断...\n')
  
  const results = {
    chromeAPI: checkChromeAPI(),
    indexedDB: checkIndexedDB(),
    extensionConnection: false,
    storagePermissions: false,
    storageData: false,
    bookmarkFunction: false,
    extensionInfo: false
  }
  
  if (results.chromeAPI) {
    results.extensionInfo = await getExtensionInfo()
    results.extensionConnection = await checkExtensionConnection()
    results.storagePermissions = await checkStoragePermissions()
    results.storageData = await checkStorageData()
    
    if (results.extensionConnection) {
      results.bookmarkFunction = await testBookmarkFunction()
    }
  }
  
  // 生成诊断报告
  console.log('\n📊 诊断报告')
  console.log('================================')
  
  const passed = Object.values(results).filter(Boolean).length
  const total = Object.keys(results).length
  
  console.log(`✅ 通过: ${passed}/${total}`)
  
  if (passed === total) {
    console.log('🎉 所有检查通过！扩展功能正常')
  } else {
    console.log('⚠️ 发现问题，请查看上述详细信息')
    
    // 提供解决建议
    console.log('\n💡 解决建议:')
    
    if (!results.chromeAPI) {
      console.log('- 请确保在Chrome扩展环境中运行此脚本')
    }
    
    if (!results.extensionConnection) {
      console.log('- 请检查扩展是否正确加载')
      console.log('- 尝试重新加载扩展')
    }
    
    if (!results.storagePermissions) {
      console.log('- 请检查扩展的存储权限')
      console.log('- 确认manifest.json中包含storage权限')
    }
    
    if (!results.bookmarkFunction) {
      console.log('- 收藏功能异常，请查看控制台错误信息')
      console.log('- 尝试重新构建和加载扩展')
    }
  }
  
  return results
}

// 如果在扩展环境中运行
if (typeof chrome !== 'undefined' && chrome.runtime) {
  runFullDiagnosis()
} else {
  console.log('⚠️ 请在Chrome扩展环境中运行此诊断脚本')
  console.log('\n使用方法:')
  console.log('1. 构建扩展: npm run build')
  console.log('2. 在Chrome中加载扩展 (chrome://extensions/)')
  console.log('3. 在任意网页的控制台中运行此脚本')
}