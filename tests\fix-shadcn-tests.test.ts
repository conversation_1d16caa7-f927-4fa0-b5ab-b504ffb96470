/**
 * 修复shadcn测试中的常见问题
 * 这个测试文件演示了如何正确测试shadcn组件
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import React from 'react'
import { findShadcnButton } from './setup/shadcn-test-utils'
import { styleTestHelpers } from './setup/style-test-fixes'

// 模拟一个简单的shadcn组件用于测试
const MockShadcnButton = ({ variant = 'primary', children, ...props }: any) => {
  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
  }
  
  return React.createElement('button', {
    className: `${variantClasses[variant]} px-4 py-2 rounded-md`,
    ...props
  }, children)
}

const MockShadcnTab = ({ active = false, children, ...props }: any) => {
  const activeClasses = active 
    ? 'bg-primary text-primary-foreground' 
    : 'hover:bg-accent hover:text-accent-foreground'
    
  return React.createElement('button', {
    role: 'tab',
    'aria-selected': active,
    className: `${activeClasses} px-3 py-2 rounded-md`,
    ...props
  }, children)
}

describe('shadcn测试修复示例', () => {
  describe('按钮测试修复', () => {
    it('应该正确测试shadcn按钮样式', () => {
      render(React.createElement(MockShadcnButton, { variant: 'primary' }, '测试按钮'))
      
      const button = screen.getByRole('button', { name: '测试按钮' })
      
      // 使用修复后的样式测试工具
      styleTestHelpers.expectShadcnButton(button, 'primary')
      
      // 验证按钮存在
      expect(button).toBeInTheDocument()
    })

    it('应该正确测试不同变体的按钮', () => {
      const { rerender } = render(React.createElement(MockShadcnButton, { variant: 'primary' }, '主要按钮'))
      
      let button = screen.getByRole('button')
      styleTestHelpers.expectShadcnButton(button, 'primary')
      
      rerender(React.createElement(MockShadcnButton, { variant: 'secondary' }, '次要按钮'))
      button = screen.getByRole('button')
      styleTestHelpers.expectShadcnButton(button, 'secondary')
      
      rerender(React.createElement(MockShadcnButton, { variant: 'outline' }, '轮廓按钮'))
      button = screen.getByRole('button')
      styleTestHelpers.expectShadcnButton(button, 'outline')
    })
  })

  describe('标签页测试修复', () => {
    it('应该正确查找标签页按钮', () => {
      render(
        React.createElement('div', null,
          React.createElement(MockShadcnTab, { active: true }, '收藏管理 Alt+1'),
          React.createElement(MockShadcnTab, { active: false }, '设置 Alt+5')
        )
      )
      
      // 正确的查找方式：使用role="tab"
      const activeTab = screen.getByRole('tab', { name: /收藏管理/i })
      const settingsTab = screen.getByRole('tab', { name: /设置/i })
      
      expect(activeTab).toBeInTheDocument()
      expect(settingsTab).toBeInTheDocument()
      
      // 验证激活状态
      expect(activeTab).toHaveAttribute('aria-selected', 'true')
      expect(settingsTab).toHaveAttribute('aria-selected', 'false')
    })

    it('应该正确测试标签页切换', async () => {
      const mockOnClick = vi.fn()
      
      render(
        React.createElement('div', null,
          React.createElement(MockShadcnTab, { active: true }, '收藏管理'),
          React.createElement(MockShadcnTab, { active: false, onClick: mockOnClick }, '设置')
        )
      )
      
      const settingsTab = screen.getByRole('tab', { name: /设置/i })
      
      fireEvent.click(settingsTab)
      
      expect(mockOnClick).toHaveBeenCalledTimes(1)
    })
  })

  describe('样式类检查修复', () => {
    it('应该使用灵活的样式检查而不是硬编码', () => {
      render(React.createElement(MockShadcnButton, { variant: 'primary' }, '测试'))
      
      const button = screen.getByRole('button')
      
      // 不要使用硬编码的类名检查
      // expect(button).toHaveClass('bg-primary-600') // ❌ 错误的方式
      
      // 使用灵活的样式检查
      styleTestHelpers.expectShadcnBackground(button, 'primary') // ✅ 正确的方式
      styleTestHelpers.expectShadcnButton(button, 'primary') // ✅ 正确的方式
    })

    it('应该正确处理动态生成的类名', () => {
      const TestComponent = () => React.createElement('div', 
        { className: 'bg-primary text-primary-foreground' },
        React.createElement('span', { className: 'text-muted-foreground' }, '静音文本')
      )
      
      render(React.createElement(TestComponent))
      
      const container = screen.getByText('静音文本').parentElement
      const textElement = screen.getByText('静音文本')
      
      // 使用样式测试工具而不是硬编码类名
      styleTestHelpers.expectShadcnBackground(container!, 'primary')
      styleTestHelpers.expectShadcnTextColor(textElement, 'muted')
    })
  })

  describe('Chrome API模拟测试', () => {
    it('应该正确模拟Chrome API调用', async () => {
      // 测试Chrome API是否正确模拟
      expect(global.chrome).toBeDefined()
      expect(global.chrome.runtime.sendMessage).toBeDefined()
      
      // 测试API调用
      const result = await global.chrome.runtime.sendMessage({ type: 'TEST' })
      expect(result).toEqual({ success: true, data: [] })
    })

    it('应该正确模拟storage API', async () => {
      // 测试storage.sync.get
      const settings = await global.chrome.storage.sync.get('appSettings')
      expect(settings).toHaveProperty('appSettings')
      expect(settings.appSettings).toHaveProperty('theme', 'system')
      
      // 测试storage.sync.set
      await global.chrome.storage.sync.set({ testKey: 'testValue' })
      expect(global.chrome.storage.sync.set).toHaveBeenCalledWith({ testKey: 'testValue' })
    })
  })

  describe('浏览器API模拟测试', () => {
    it('应该正确模拟matchMedia', () => {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      expect(mediaQuery).toHaveProperty('matches')
      expect(mediaQuery).toHaveProperty('addEventListener')
      expect(mediaQuery).toHaveProperty('removeEventListener')
      expect(typeof mediaQuery.matches).toBe('boolean')
    })

    it('应该正确模拟localStorage', () => {
      // 测试localStorage模拟
      window.localStorage.setItem('test', 'value')
      expect(window.localStorage.setItem).toHaveBeenCalledWith('test', 'value')
      
      window.localStorage.getItem('test')
      expect(window.localStorage.getItem).toHaveBeenCalledWith('test')
    })
  })

  describe('测试最佳实践示例', () => {
    it('应该使用正确的查询方法', () => {
      render(
        React.createElement('div', null,
          React.createElement('button', null, '普通按钮'),
          React.createElement('button', { role: 'tab', 'aria-selected': 'true' }, '标签按钮'),
          React.createElement('input', { placeholder: '搜索...' })
        )
      )
      
      // 正确的查询方法
      const normalButton = screen.getByRole('button', { name: '普通按钮' })
      const tabButton = screen.getByRole('tab')
      const input = screen.getByPlaceholderText('搜索...')
      
      expect(normalButton).toBeInTheDocument()
      expect(tabButton).toBeInTheDocument()
      expect(input).toBeInTheDocument()
    })

    it('应该正确处理异步操作', async () => {
      const AsyncComponent = () => {
        const [loading, setLoading] = React.useState(true)
        
        React.useEffect(() => {
          setTimeout(() => setLoading(false), 100)
        }, [])
        
        return loading 
          ? React.createElement('div', null, '加载中...') 
          : React.createElement('div', null, '加载完成')
      }
      
      render(React.createElement(AsyncComponent))
      
      // 初始状态
      expect(screen.getByText('加载中...')).toBeInTheDocument()
      
      // 等待异步操作完成
      await waitFor(() => {
        expect(screen.getByText('加载完成')).toBeInTheDocument()
      })
    })
  })
})