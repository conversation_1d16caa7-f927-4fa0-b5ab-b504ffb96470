#!/usr/bin/env node

/**
 * 构建后处理脚本
 * 复制必要的文件到dist目录
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')
const distDir = path.resolve(rootDir, 'dist')

console.log('📦 执行构建后处理...')

// 复制manifest.json
function copyManifest() {
  const manifestSrc = path.resolve(rootDir, 'manifest.json')
  const manifestDest = path.resolve(distDir, 'manifest.json')
  
  if (fs.existsSync(manifestSrc)) {
    fs.copyFileSync(manifestSrc, manifestDest)
    console.log('✅ 复制 manifest.json')
  } else {
    console.log('❌ manifest.json 不存在')
  }
}

// 复制图标文件
function copyIcons() {
  const iconsDir = path.resolve(rootDir, 'public/icons')
  const distIconsDir = path.resolve(distDir, 'icons')
  
  if (fs.existsSync(iconsDir)) {
    // 创建icons目录
    if (!fs.existsSync(distIconsDir)) {
      fs.mkdirSync(distIconsDir, { recursive: true })
    }
    
    // 复制所有图标文件
    const iconFiles = fs.readdirSync(iconsDir)
    iconFiles.forEach(file => {
      const srcFile = path.resolve(iconsDir, file)
      const destFile = path.resolve(distIconsDir, file)
      fs.copyFileSync(srcFile, destFile)
    })
    
    console.log(`✅ 复制 ${iconFiles.length} 个图标文件`)
  } else {
    console.log('⚠️  icons目录不存在')
  }
}

// 创建简单的图标占位符
function createIconPlaceholders() {
  const distIconsDir = path.resolve(distDir, 'icons')
  
  if (!fs.existsSync(distIconsDir)) {
    fs.mkdirSync(distIconsDir, { recursive: true })
  }
  
  const iconSizes = [16, 32, 48, 128]
  iconSizes.forEach(size => {
    const iconPath = path.resolve(distIconsDir, `icon-${size}.png`)
    if (!fs.existsSync(iconPath)) {
      // 创建一个简单的占位符文件
      fs.writeFileSync(iconPath, '# Icon placeholder')
      console.log(`✅ 创建图标占位符: icon-${size}.png`)
    }
  })
}

// 复制content script的CSS文件
function copyContentCSS() {
  const cssSrc = path.resolve(rootDir, 'src/content/style.css')
  const cssDestDir = path.resolve(distDir, 'src/content')
  const cssDest = path.resolve(cssDestDir, 'style.css')
  
  if (fs.existsSync(cssSrc)) {
    // 确保目录存在
    if (!fs.existsSync(cssDestDir)) {
      fs.mkdirSync(cssDestDir, { recursive: true })
    }
    
    fs.copyFileSync(cssSrc, cssDest)
    console.log('✅ 复制 content script CSS文件')
  } else {
    console.log('⚠️  content script CSS文件不存在')
  }
}

// 主函数
function main() {
  try {
    copyManifest()
    copyIcons()
    copyContentCSS()
    createIconPlaceholders()
    
    console.log('🎉 构建后处理完成！')
    console.log('\n📋 下一步:')
    console.log('1. 打开Chrome浏览器')
    console.log('2. 访问 chrome://extensions/')
    console.log('3. 开启"开发者模式"')
    console.log('4. 点击"加载已解压的扩展程序"')
    console.log('5. 选择 dist 文件夹')
  } catch (error) {
    console.error('❌ 构建后处理失败:', error.message)
    process.exit(1)
  }
}

main()