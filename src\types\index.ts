// 核心数据类型定义

// 书签类型枚举
export type BookmarkType = 'url' | 'text' | 'image'

// 基础实体接口
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

export interface Bookmark extends BaseEntity {
  type: BookmarkType
  title: string
  url?: string
  content?: string
  description?: string
  tags: string[]
  category: string
  favicon?: string
  thumbnail?: string
  metadata: BookmarkMetadata
}

// 创建书签时的输入数据类型
export interface BookmarkInput {
  type: BookmarkType
  title: string
  url?: string
  content?: string
  description?: string
  tags?: string[]
  category?: string
  favicon?: string
  thumbnail?: string
  metadata?: Partial<BookmarkMetadata>
}

// 更新书签时的数据类型
export interface BookmarkUpdate {
  title?: string
  url?: string
  description?: string
  tags?: string[]
  category?: string
  metadata?: Partial<BookmarkMetadata>
}

export interface BookmarkMetadata {
  pageTitle?: string
  siteName?: string
  author?: string
  publishDate?: Date
  wordCount?: number
  language?: string
  aiGenerated: boolean
}

export interface Category extends BaseEntity {
  name: string
  description?: string
  color?: string
  parentId?: string
  bookmarkCount: number
}

// 创建分类时的输入数据类型
export interface CategoryInput {
  name: string
  description?: string
  color?: string
  parentId?: string
}

// 更新分类时的数据类型
export interface CategoryUpdate {
  name?: string
  description?: string
  color?: string
  parentId?: string
}

export interface Tag extends BaseEntity {
  name: string
  color?: string
  usageCount: number
}

// 创建标签时的输入数据类型
export interface TagInput {
  name: string
  color?: string
}

// 更新标签时的数据类型
export interface TagUpdate {
  name?: string
  color?: string
}

// AI 提供商类型
export type AIProvider = 'openai' | 'claude' | 'gemini' | 'local' | 'custom'

// 本地模型类型
export type LocalModelType = 'ollama' | 'llamacpp' | 'other'

export interface AIConfig extends BaseEntity {
  provider: AIProvider
  baseUrl?: string
  apiKey?: string
  model: string
  autoTagging: boolean
  autoCategories: boolean
  autoDescription: boolean
  temperature?: number
  maxTokens?: number
  timeout?: number
  localModelPath?: string
  localModelType?: LocalModelType
  customHeaders?: Record<string, string>
  customParams?: Record<string, any>
  isConnected: boolean
  lastTestDate?: Date
  availableModels?: string[]
}

export interface SyncConfig {
  enabled: boolean
  provider: 'notion' | 'airtable' | 'custom'
  credentials: Record<string, any>
  syncFrequency: number
  lastSync?: Date
}

// 扩展消息类型
export interface ExtensionMessage {
  type: string
  data?: any
}

// AI相关消息类型
export interface AITagGenerationMessage extends ExtensionMessage {
  type: 'AI_GENERATE_TAGS'
  data: {
    content: string
    title?: string
    url?: string
    existingTags?: string[]
    maxTags?: number
  }
}

export interface AICategoryGenerationMessage extends ExtensionMessage {
  type: 'AI_GENERATE_CATEGORY'
  data: {
    content: string
    title?: string
    url?: string
    existingCategories?: string[]
  }
}

export interface AIDescriptionGenerationMessage extends ExtensionMessage {
  type: 'AI_GENERATE_DESCRIPTION'
  data: {
    content: string
    title?: string
    url?: string
    maxLength?: number
    style?: 'brief' | 'detailed' | 'summary'
  }
}

export interface AIConfigTestMessage extends ExtensionMessage {
  type: 'AI_TEST_CONNECTION'
  data?: {
    config?: Partial<AIConfig>
  }
}

export interface AIConfigUpdateMessage extends ExtensionMessage {
  type: 'AI_UPDATE_CONFIG'
  data: Partial<AIConfig>
}

// 页面信息类型
export interface PageInfo {
  title: string
  url: string
  favicon?: string
  selectedText?: string
  timestamp: Date
}

// 视图模式类型
export type ViewMode = 'list' | 'card' | 'gallery' | 'simple'

// 导出布局相关类型
export type { 
  TruncateOptions, 
  TruncateResult, 
  ContainerSize, 
  LayoutConfig, 
  ExtendedViewMode, 
  ViewModeConfig,
  ResponsiveBreakpoints,
  TextMeasurement,
  LayoutCalculation,
  DebounceConfig,
  ResizeObserverConfig,
  LineClampConfig,
  UrlBeautifyOptions
} from './layout'

// 导出导入导出相关类型
export type { 
  ExportOptions,
  ExportAllOptions, 
  ExportBookmarksOptions,
  ExportCategoriesOptions,
  ExportTagsOptions,
  ImportData,
  ImportMetadata,
  ImportOptions,
  ImportResult,
  ExportResult,
  ProgressCallback,
  ImportExportProgress
} from './import-export'

// 日期范围类型
export interface DateRange {
  start: Date
  end: Date
}

// 数据类型枚举
export type DataType = 'bookmark' | 'category' | 'tag'

// 冲突类型枚举
export type ConflictType = 'duplicate' | 'name_conflict' | 'data_mismatch'

// 冲突解决动作枚举
export type ConflictAction = 'keep_existing' | 'use_imported' | 'merge' | 'manual_edit'

// 冲突项
export interface ConflictItem {
  id: string
  type: DataType
  conflictType: ConflictType
  existingData: any
  importData: any
  conflictFields: string[]
  similarity: number
}

// 冲突解决方案
export interface ConflictResolution {
  conflictId: string
  action: ConflictAction
  mergedData?: any
  manualData?: any
}

// 冲突检测结果
export interface ConflictDetectionResult {
  hasConflicts: boolean
  conflicts: ConflictItem[]
  summary: {
    bookmarkConflicts: number
    categoryConflicts: number
    tagConflicts: number
  }
}

// 解决后的数据
export interface ResolvedData {
  bookmarks: BookmarkInput[]
  categories: CategoryInput[]
  tags: TagInput[]
}

// 批量操作类型
export interface BatchOperation {
  type: 'delete' | 'updateTags' | 'moveCategory'
  bookmarkIds: string[]
  data?: any
}

// 搜索和筛选类型
export interface BookmarkFilter {
  query?: string
  tags?: string[]
  categories?: string[]
  type?: BookmarkType
  dateRange?: DateRange
  limit?: number
  offset?: number
}

// 排序选项
export interface SortOptions {
  field: 'createdAt' | 'updatedAt' | 'title' | 'category'
  direction: 'asc' | 'desc'
}

// 重复检测结果
export interface DuplicateGroup {
  id: string
  bookmarks: Bookmark[]
  similarity: number
  type: 'url' | 'content' | 'title'
}

// 链接验证结果
export interface LinkValidationResult {
  bookmarkId: string
  url: string
  status: 'valid' | 'invalid' | 'redirect' | 'timeout'
  statusCode?: number
  redirectUrl?: string
  error?: string
  checkedAt: Date
}

// AI建议结果
export interface AISuggestions {
  tags: string[]
  category: string
  description?: string
  confidence: number
}

// 导出验证相关类型
export type {
  ValidationError,
  ValidationResult,
  BatchValidationResult,
  ValidationRule,
  ValidationRuleConfig,
  FieldValidationConfig,
  ValidationSchema
} from './validation'

// 导出shadcn组件相关类型
export type {
  ButtonVariant,
  ButtonSize,
  ComponentProps,
  DialogProps,
  DialogContentProps,
  FormFieldProps,
  FormItemProps,
  FormLabelProps,
  FormControlProps,
  FormDescriptionProps,
  FormMessageProps,
  InputProps,
  TextareaProps,
  CardProps,
  CardHeaderProps,
  CardTitleProps,
  CardDescriptionProps,
  CardContentProps,
  CardFooterProps,
  BadgeProps,
  SelectProps,
  SelectTriggerProps,
  SelectValueProps,
  SelectContentProps,
  SelectItemProps,
  DropdownMenuProps,
  DropdownMenuTriggerProps,
  DropdownMenuContentProps,
  DropdownMenuItemProps,
  DropdownMenuSeparatorProps,
  DropdownMenuLabelProps,
  TooltipProps,
  TooltipTriggerProps,
  TooltipContentProps,
  AlertDialogProps,
  AlertDialogTriggerProps,
  AlertDialogContentProps,
  AlertDialogHeaderProps,
  AlertDialogTitleProps,
  AlertDialogDescriptionProps,
  AlertDialogFooterProps,
  AlertDialogActionProps,
  AlertDialogCancelProps,
  Theme,
  ThemeProviderProps,
  EventHandler,
  ChangeHandler,
  ComponentState,
  Size,
  ColorVariant,
  Position,
  Alignment,
  Breakpoint,
  AnimationType,
  ComponentRef,
  RenderFunction,
  ComponentFactory,
  ClassNameGenerator,
  ComponentConfig,
  FieldConfig,
  TableColumn,
  TableProps,
  VirtualScrollProps,
  InfiniteScrollProps
} from './shadcn'