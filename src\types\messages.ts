// 扩展消息通信类型定义

import { BookmarkInput, BookmarkUpdate, PageInfo, AISuggestions } from './index'

// 基础消息接口
export interface BaseMessage {
  type: string
  requestId?: string
  timestamp?: number
}

// 消息响应接口
export interface MessageResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  requestId?: string
}

// 页面信息提取消息
export interface GetPageInfoMessage extends BaseMessage {
  type: 'GET_PAGE_INFO'
  data?: {
    selectedText?: string
    includeMetadata?: boolean
  }
}

export interface GetPageInfoResponse extends MessageResponse<PageInfo> {}

// 链接信息提取消息
export interface GetLinkInfoMessage extends BaseMessage {
  type: 'GET_LINK_INFO'
  data: {
    url: string
  }
}

export interface GetLinkInfoResponse extends MessageResponse<{
  title?: string
  description?: string
  favicon?: string
}> {}

// 快速收藏消息
export interface QuickBookmarkMessage extends BaseMessage {
  type: 'QUICK_BOOKMARK'
  data: {
    title: string
    url: string
    favIconUrl?: string
    selectedText?: string
  }
}

export interface QuickBookmarkResponse extends MessageResponse<{
  bookmarkId: string
}> {}

// 收藏选中文字消息
export interface BookmarkSelectedTextMessage extends BaseMessage {
  type: 'BOOKMARK_SELECTED_TEXT'
  data: {
    selectedText: string
    url: string
    title: string
    context?: string
  }
}

export interface BookmarkSelectedTextResponse extends MessageResponse<{
  bookmarkId: string
}> {}

// 保存详细收藏消息
export interface SaveDetailedBookmarkMessage extends BaseMessage {
  type: 'SAVE_DETAILED_BOOKMARK'
  data: BookmarkInput
}

export interface SaveDetailedBookmarkResponse extends MessageResponse<{
  bookmarkId: string
}> {}

// 更新收藏消息
export interface UpdateBookmarkMessage extends BaseMessage {
  type: 'UPDATE_BOOKMARK'
  data: {
    id: string
    updates: BookmarkUpdate
  }
}

export interface UpdateBookmarkResponse extends MessageResponse<{
  bookmarkId: string
}> {}

// 删除收藏消息
export interface DeleteBookmarkMessage extends BaseMessage {
  type: 'DELETE_BOOKMARK'
  data: {
    id: string
  }
}

export interface DeleteBookmarkResponse extends MessageResponse<{
  bookmarkId: string
}> {}

// 检查收藏状态消息
export interface CheckBookmarkStatusMessage extends BaseMessage {
  type: 'CHECK_BOOKMARK_STATUS'
  data: {
    url: string
  }
}

export interface CheckBookmarkStatusResponse extends MessageResponse<{
  isBookmarked: boolean
  bookmarkId?: string
}> {}

// 更新图标状态消息
export interface UpdateIconStatusMessage extends BaseMessage {
  type: 'UPDATE_ICON_STATUS'
  data: {
    tabId: number
    isBookmarked: boolean
  }
}

export interface UpdateIconStatusResponse extends MessageResponse<{}> {}

// AI生成建议消息
export interface AIGenerateSuggestionsMessage extends BaseMessage {
  type: 'AI_GENERATE_SUGGESTIONS'
  data: {
    content: string
    url?: string
    title?: string
  }
}

export interface AIGenerateSuggestionsResponse extends MessageResponse<AISuggestions> {}

// AI生成标签消息
export interface AIGenerateTagsMessage extends BaseMessage {
  type: 'AI_GENERATE_TAGS'
  data: {
    content: string
    title?: string
    url?: string
    existingTags?: string[]
    maxTags?: number
  }
}

export interface AIGenerateTagsResponse extends MessageResponse<{
  tags: string[]
  confidence: number
  reasoning?: string
  processingTime: number
}> {}

// AI生成分类消息
export interface AIGenerateCategoryMessage extends BaseMessage {
  type: 'AI_GENERATE_CATEGORY'
  data: {
    content: string
    title?: string
    url?: string
    existingCategories?: string[]
    maxSuggestions?: number
  }
}

export interface AIGenerateCategoryResponse extends MessageResponse<{
  category: string
  confidence: number
  alternatives?: Array<{
    category: string
    confidence: number
  }>
  reasoning?: string
}> {}

// AI生成描述消息
export interface AIGenerateDescriptionMessage extends BaseMessage {
  type: 'AI_GENERATE_DESCRIPTION'
  data: {
    content: string
    title?: string
    url?: string
    maxLength?: number
    style?: 'brief' | 'detailed' | 'summary'
  }
}

export interface AIGenerateDescriptionResponse extends MessageResponse<{
  description: string
  confidence: number
  wordCount: number
}> {}

// AI配置测试消息
export interface AITestConnectionMessage extends BaseMessage {
  type: 'AI_TEST_CONNECTION'
  data?: {
    config?: Partial<any> // 使用any类型避免动态导入
  }
}

export interface AITestConnectionResponse extends MessageResponse<{
  success: boolean
  responseTime?: number
  availableModels?: string[]
  error?: string
  errorCode?: string
  testedAt: Date
}> {}

// AI配置更新消息
export interface AIUpdateConfigMessage extends BaseMessage {
  type: 'AI_UPDATE_CONFIG'
  data: Partial<any> // 使用any类型避免动态导入
}

export interface AIUpdateConfigResponse extends MessageResponse<{
  config: any // 使用any类型避免动态导入
}> {}

// AI获取配置消息
export interface AIGetConfigMessage extends BaseMessage {
  type: 'AI_GET_CONFIG'
}

export interface AIGetConfigResponse extends MessageResponse<{
  config: any // 使用any类型避免动态导入
}> {}

// AI获取统计信息消息
export interface AIGetStatsMessage extends BaseMessage {
  type: 'AI_GET_STATS'
}

export interface AIGetStatsResponse extends MessageResponse<{
  stats: any // 使用any类型避免动态导入
}> {}

// AI重置统计信息消息
export interface AIResetStatsMessage extends BaseMessage {
  type: 'AI_RESET_STATS'
}

export interface AIResetStatsResponse extends MessageResponse<{}> {}

// AI推荐标签消息
export interface AIRecommendTagsMessage extends BaseMessage {
  type: 'AI_RECOMMEND_TAGS'
  data: {
    title?: string
    url?: string
    content?: string
    description?: string
    maxRecommendations?: number
  }
}

export interface AIRecommendTagsResponse extends MessageResponse<{
  existingTags: string[]
  newTags: string[]
  confidence: number
  reasoning?: string
}> {}

// AI推荐文件夹消息
export interface AIRecommendFoldersMessage extends BaseMessage {
  type: 'AI_RECOMMEND_FOLDERS'
  data: {
    title?: string
    url?: string
    content?: string
    description?: string
    tags?: string[]
    maxRecommendations?: number
  }
}

export interface AIRecommendFoldersResponse extends MessageResponse<{
  recommendedFolders: Array<{
    name: string
    confidence: number
    reason?: string
  }>
  reasoning?: string
}> {}

// AI批量推荐消息
export interface AIRecommendBothMessage extends BaseMessage {
  type: 'AI_RECOMMEND_BOTH'
  data: {
    title?: string
    url?: string
    content?: string
    description?: string
    tags?: string[]
    maxRecommendations?: number
  }
}

export interface AIRecommendBothResponse extends MessageResponse<{
  tags: {
    existingTags: string[]
    newTags: string[]
    confidence: number
    reasoning?: string
  }
  folders: {
    recommendedFolders: Array<{
      name: string
      confidence: number
      reason?: string
    }>
    reasoning?: string
  }
}> {}

// 获取设置消息
export interface GetSettingsMessage extends BaseMessage {
  type: 'GET_SETTINGS'
}

export interface GetSettingsResponse extends MessageResponse<Record<string, any>> {}

// 更新设置消息
export interface UpdateSettingsMessage extends BaseMessage {
  type: 'UPDATE_SETTINGS'
  data: Record<string, any>
}

export interface UpdateSettingsResponse extends MessageResponse<Record<string, any>> {}

// 手动同步消息
export interface ManualSyncMessage extends BaseMessage {
  type: 'MANUAL_SYNC'
}

export interface ManualSyncResponse extends MessageResponse<{
  syncedCount: number
  lastSync: Date
}> {}

// Ping消息（用于测试连接）
export interface PingMessage extends BaseMessage {
  type: 'PING'
}

export interface PingResponse extends MessageResponse<{
  status: 'pong'
  timestamp: number
}> {}

// 联合类型：所有消息类型
export type ExtensionMessage = 
  | GetPageInfoMessage
  | GetLinkInfoMessage
  | QuickBookmarkMessage
  | BookmarkSelectedTextMessage
  | SaveDetailedBookmarkMessage
  | UpdateBookmarkMessage
  | DeleteBookmarkMessage
  | CheckBookmarkStatusMessage
  | UpdateIconStatusMessage
  | AIGenerateSuggestionsMessage
  | AIRecommendTagsMessage
  | AIRecommendFoldersMessage
  | AIRecommendBothMessage
  | GetSettingsMessage
  | UpdateSettingsMessage
  | ManualSyncMessage
  | PingMessage

// 联合类型：所有响应类型
export type ExtensionMessageResponse = 
  | GetPageInfoResponse
  | GetLinkInfoResponse
  | QuickBookmarkResponse
  | BookmarkSelectedTextResponse
  | SaveDetailedBookmarkResponse
  | UpdateBookmarkResponse
  | DeleteBookmarkResponse
  | CheckBookmarkStatusResponse
  | UpdateIconStatusResponse
  | AIGenerateSuggestionsResponse
  | AIRecommendTagsResponse
  | AIRecommendFoldersResponse
  | AIRecommendBothResponse
  | GetSettingsResponse
  | UpdateSettingsResponse
  | ManualSyncResponse
  | PingResponse

// 消息发送者类型
export type MessageSender = 'background' | 'popup' | 'content' | 'options'

// 消息处理器类型
export type MessageHandler<T extends ExtensionMessage, R extends MessageResponse> = (
  message: T,
  sender: chrome.runtime.MessageSender
) => Promise<R>

// 错误类型
export interface MessageError {
  code: string
  message: string
  details?: any
}

// 消息验证结果
export interface MessageValidationResult {
  isValid: boolean
  errors: MessageError[]
}