import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import ViewModeSelector from '../src/components/ViewModeSelector'
import { useViewMode } from '../src/hooks/useViewMode'
import OptionsApp from '../src/options/OptionsApp'

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// @ts-ignore
global.chrome = mockChrome

// Mock useViewMode hook
vi.mock('../src/hooks/useViewMode')
const mockUseViewMode = vi.mocked(useViewMode)

describe('ViewModeSelector组件集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })
  })

  test('ViewModeSelector应该有固定的尺寸', () => {
    const mockOnModeChange = vi.fn()
    
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )
    
    // 查找ViewModeSelector容器
    const container = screen.getByRole('group', { name: '视图模式选择器' })
    
    expect(container).toBeInTheDocument()
    
    // 检查是否有固定的样式属性
    const containerElement = container as HTMLElement
    
    // 验证最小宽度和高度设置
    expect(containerElement.style.minWidth).toBe('240px')
    expect(containerElement.style.height).toBe('40px')
  })

  test('加载状态占位符应该与ViewModeSelector尺寸匹配', async () => {
    // Mock loading state
    mockUseViewMode.mockReturnValue({
      viewMode: 'card',
      setViewMode: vi.fn(),
      resetViewMode: vi.fn(),
      isLoading: true
    })

    render(<OptionsApp />)
    
    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByRole('heading', { level: 2, name: '收藏管理' })).toBeInTheDocument()
    })
    
    // 查找加载占位符
    const placeholder = document.querySelector('.view-mode-placeholder')
    expect(placeholder).toBeInTheDocument()
    
    // 检查占位符尺寸
    if (placeholder) {
      // 占位符应该有相同的尺寸
      expect(placeholder).toHaveClass('view-mode-placeholder')
    }
  })

  test('从加载状态到正常状态的过渡应该平滑', async () => {
    // 开始时处于加载状态
    const mockSetViewMode = vi.fn()
    mockUseViewMode.mockReturnValue({
      viewMode: 'card',
      setViewMode: mockSetViewMode,
      resetViewMode: vi.fn(),
      isLoading: true
    })

    const { rerender } = render(<OptionsApp />)
    
    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByRole('heading', { level: 2, name: '收藏管理' })).toBeInTheDocument()
    })
    
    // 验证加载状态
    expect(document.querySelector('.view-mode-placeholder')).toBeInTheDocument()
    expect(screen.queryByText('卡片视图')).not.toBeInTheDocument()
    
    // 模拟加载完成
    mockUseViewMode.mockReturnValue({
      viewMode: 'card',
      setViewMode: mockSetViewMode,
      resetViewMode: vi.fn(),
      isLoading: false
    })

    rerender(<OptionsApp />)
    
    // 等待组件更新
    await waitFor(() => {
      expect(document.querySelector('.view-mode-placeholder')).not.toBeInTheDocument()
    })
    
    // 验证ViewModeSelector已渲染
    await waitFor(() => {
      expect(screen.getByText('卡片视图')).toBeInTheDocument()
    })
  })

  test('ViewModeSelector容器应该有正确的CSS类', async () => {
    mockUseViewMode.mockReturnValue({
      viewMode: 'card',
      setViewMode: vi.fn(),
      resetViewMode: vi.fn(),
      isLoading: false
    })

    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('卡片视图')).toBeInTheDocument()
    })
    
    // 查找容器
    const container = document.querySelector('.view-mode-selector-container')
    expect(container).toBeInTheDocument()
    expect(container).toHaveClass('view-mode-selector-container')
  })

  test('ViewModeSelector在不同视图模式下应该保持稳定布局', async () => {
    const mockSetViewMode = vi.fn()
    mockUseViewMode.mockReturnValue({
      viewMode: 'card',
      setViewMode: mockSetViewMode,
      resetViewMode: vi.fn(),
      isLoading: false
    })

    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('卡片视图')).toBeInTheDocument()
    })
    
    // 获取初始容器尺寸
    const container = document.querySelector('.view-mode-selector-container')
    const initialRect = container?.getBoundingClientRect()
    
    // 切换到行视图
    const rowButton = screen.getByText('行视图')
    fireEvent.click(rowButton)
    
    // 验证容器尺寸没有变化
    const newRect = container?.getBoundingClientRect()
    expect(newRect?.width).toBe(initialRect?.width)
    expect(newRect?.height).toBe(initialRect?.height)
  })

  test('加载状态应该显示适当的加载指示器', async () => {
    mockUseViewMode.mockReturnValue({
      viewMode: 'card',
      setViewMode: vi.fn(),
      resetViewMode: vi.fn(),
      isLoading: true
    })

    render(<OptionsApp />)
    
    // 等待页面加载完成
    await waitFor(() => {
      expect(screen.getByRole('heading', { level: 2, name: '收藏管理' })).toBeInTheDocument()
    })
    
    // 验证加载指示器存在
    const loadingSpinner = document.querySelector('.view-mode-placeholder .animate-spin')
    expect(loadingSpinner).toBeInTheDocument()
    
    // 验证加载文本
    expect(screen.getByText('加载视图选项...')).toBeInTheDocument()
  })

  test('ViewModeSelector应该正确处理模式切换', async () => {
    const mockSetViewMode = vi.fn()
    mockUseViewMode.mockReturnValue({
      viewMode: 'card',
      setViewMode: mockSetViewMode,
      resetViewMode: vi.fn(),
      isLoading: false
    })

    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('卡片视图')).toBeInTheDocument()
    })
    
    // 点击行视图按钮
    const rowButton = screen.getByText('行视图')
    fireEvent.click(rowButton)
    
    // 验证回调被调用
    expect(mockSetViewMode).toHaveBeenCalledWith('row')
  })

  test('ViewModeSelector在异步加载时不应该导致布局抖动', async () => {
    // 模拟异步加载过程
    let isLoading = true
    const mockSetViewMode = vi.fn()
    
    mockUseViewMode.mockImplementation(() => ({
      viewMode: 'card',
      setViewMode: mockSetViewMode,
      resetViewMode: vi.fn(),
      isLoading
    }))

    const { rerender } = render(<OptionsApp />)
    
    // 获取头部容器的初始位置
    const headerContainer = document.querySelector('.bookmark-header-controls')
    const initialRect = headerContainer?.getBoundingClientRect()
    
    // 模拟加载完成
    isLoading = false
    mockUseViewMode.mockReturnValue({
      viewMode: 'card',
      setViewMode: mockSetViewMode,
      resetViewMode: vi.fn(),
      isLoading: false
    })
    
    rerender(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('卡片视图')).toBeInTheDocument()
    })
    
    // 验证头部容器位置没有显著变化
    const finalRect = headerContainer?.getBoundingClientRect()
    
    // 允许1px的误差（由于浏览器渲染差异）
    if (initialRect && finalRect) {
      expect(Math.abs(finalRect.top - initialRect.top)).toBeLessThanOrEqual(1)
      expect(Math.abs(finalRect.left - initialRect.left)).toBeLessThanOrEqual(1)
    }
  })
})