# AIProviderService 公共工具类建议

## 问题
当前代码中存在大量重复的HTTP请求和错误处理逻辑。

## 解决方案

### 1. 创建HTTP请求工具类
```typescript
class HTTPClient {
  static async request(url: string, options: RequestInit & { timeout?: number }): Promise<Response> {
    const { timeout = 10000, ...fetchOptions } = options
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    
    try {
      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      return response
    } catch (error) {
      clearTimeout(timeoutId)
      throw this.handleFetchError(error)
    }
  }
  
  private static handleFetchError(error: any): Error {
    if (error.name === 'AbortError') {
      return new Error('连接超时，请检查服务是否运行')
    } else if (error.message.includes('fetch')) {
      return new Error('无法连接到服务，请检查地址和端口')
    } else if (error.message.includes('ECONNREFUSED')) {
      return new Error('服务未运行或端口不正确')
    }
    return error
  }
}
```

### 2. 创建通用连接测试基类
```typescript
abstract class BaseConnectionTester {
  protected async testEndpoint(
    url: string, 
    options: RequestInit = {},
    timeout: number = 5000
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await HTTPClient.request(url, { 
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        ...options,
        timeout 
      })
      
      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        }
      }
      
      const data = await response.json()
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }
}
```

### 3. 使用示例
```typescript
class OllamaProvider extends BaseConnectionTester {
  async testConnection(baseUrl: string): Promise<ConnectionResult> {
    // 测试版本端点
    const versionResult = await this.testEndpoint(`${baseUrl}/api/version`)
    
    if (versionResult.success) {
      // 获取模型列表
      const modelsResult = await this.testEndpoint(`${baseUrl}/api/tags`, {}, 10000)
      return {
        success: true,
        modelCount: modelsResult.data?.models?.length || 0
      }
    }
    
    return {
      success: false,
      error: versionResult.error
    }
  }
}
```

## 优势
1. 减少代码重复
2. 统一错误处理逻辑
3. 便于维护和测试
4. 提高代码一致性