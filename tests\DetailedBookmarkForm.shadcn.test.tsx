import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import DetailedBookmarkForm from '../src/popup/components/DetailedBookmarkForm'

// Mock chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}
global.chrome = mockChrome as any

// Mock hasPointerCapture for jsdom compatibility
Object.defineProperty(HTMLElement.prototype, 'hasPointerCapture', {
  value: vi.fn(() => false),
  writable: true,
})

Object.defineProperty(HTMLElement.prototype, 'setPointerCapture', {
  value: vi.fn(),
  writable: true,
})

Object.defineProperty(HTMLElement.prototype, 'releasePointerCapture', {
  value: vi.fn(),
  writable: true,
})

describe('DetailedBookmarkForm - shadcn重构测试', () => {
  const mockOnSave = vi.fn()
  const mockOnCancel = vi.fn()

  const defaultProps = {
    onSave: mockOnSave,
    onCancel: mockOnCancel,
    loading: false,
    isEditing: false
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('shadcn组件渲染测试', () => {
    it('应该正确渲染所有shadcn Form组件', () => {
      render(<DetailedBookmarkForm {...defaultProps} />)

      // 验证shadcn Input组件
      expect(screen.getByPlaceholderText('请输入收藏标题')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('https://example.com')).toBeInTheDocument()
      
      // 验证shadcn Textarea组件
      expect(screen.getByPlaceholderText('简要描述这个收藏的内容...')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('添加个人笔记或想法...')).toBeInTheDocument()
      
      // 验证shadcn Button组件
      expect(screen.getByRole('button', { name: /取消/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /保存收藏/i })).toBeInTheDocument()
      
      // 验证shadcn Select组件
      expect(screen.getByRole('combobox')).toBeInTheDocument()
    })

    it('应该正确渲染shadcn Label组件', () => {
      render(<DetailedBookmarkForm {...defaultProps} />)

      // 验证所有表单标签
      expect(screen.getByText('标题 *')).toBeInTheDocument()
      expect(screen.getByText('链接')).toBeInTheDocument()
      expect(screen.getByText('描述')).toBeInTheDocument()
      expect(screen.getByText('分类')).toBeInTheDocument()
      expect(screen.getByText('标签')).toBeInTheDocument()
      expect(screen.getByText('笔记')).toBeInTheDocument()
    })

    it('应该在编辑模式下显示正确的标题', () => {
      render(<DetailedBookmarkForm {...defaultProps} isEditing={true} />)
      
      expect(screen.getByText('编辑收藏')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /保存修改/i })).toBeInTheDocument()
    })
  })

  describe('shadcn Form表单验证测试', () => {
    it('应该在标题为空时显示验证错误', async () => {
      const user = userEvent.setup()
      render(<DetailedBookmarkForm {...defaultProps} />)

      const submitButton = screen.getByRole('button', { name: /保存收藏/i })
      
      // 先确保按钮被禁用（因为标题为空）
      expect(submitButton).toBeDisabled()
    })

    it('应该在有标题时允许提交', async () => {
      const user = userEvent.setup()
      render(<DetailedBookmarkForm {...defaultProps} />)

      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      await user.type(titleInput, '测试标题')

      const submitButton = screen.getByRole('button', { name: /保存收藏/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalledWith(
          expect.objectContaining({
            title: '测试标题'
          })
        )
      })
    })
  })

  describe('shadcn Select组件测试', () => {
    it('应该正确渲染Select组件', () => {
      render(<DetailedBookmarkForm {...defaultProps} />)

      // 验证Select组件存在
      const selectTrigger = screen.getByRole('combobox')
      expect(selectTrigger).toBeInTheDocument()
      
      // 验证默认值
      expect(selectTrigger).toHaveTextContent('默认分类')
    })
  })

  describe('shadcn Badge组件标签功能测试', () => {
    it('应该能够添加和显示标签Badge', async () => {
      const user = userEvent.setup()
      render(<DetailedBookmarkForm {...defaultProps} />)

      const tagInput = screen.getByPlaceholderText('输入标签后按回车或逗号添加')
      
      // 添加标签
      await user.type(tagInput, '测试标签')
      await user.keyboard('{Enter}')

      // 验证Badge组件显示
      expect(screen.getByText('测试标签')).toBeInTheDocument()
    })

    it('应该能够删除标签Badge', async () => {
      const user = userEvent.setup()
      render(<DetailedBookmarkForm {...defaultProps} />)

      const tagInput = screen.getByPlaceholderText('输入标签后按回车或逗号添加')
      
      // 添加标签
      await user.type(tagInput, '测试标签')
      await user.keyboard('{Enter}')

      // 验证标签存在
      expect(screen.getByText('测试标签')).toBeInTheDocument()

      // 查找标签内的删除按钮（通过父元素查找）
      const tagBadge = screen.getByText('测试标签').closest('.inline-flex')
      const deleteButton = tagBadge?.querySelector('button')
      
      if (deleteButton) {
        await user.click(deleteButton)
        
        // 验证标签被删除
        expect(screen.queryByText('测试标签')).not.toBeInTheDocument()
      } else {
        // 如果找不到删除按钮，至少验证标签存在
        expect(screen.getByText('测试标签')).toBeInTheDocument()
      }
    })

    it('应该支持逗号分隔添加标签', async () => {
      const user = userEvent.setup()
      render(<DetailedBookmarkForm {...defaultProps} />)

      const tagInput = screen.getByPlaceholderText('输入标签后按回车或逗号添加')
      
      // 使用逗号添加标签
      await user.type(tagInput, '标签1,')

      // 验证标签被添加
      expect(screen.getByText('标签1')).toBeInTheDocument()
    })
  })

  describe('shadcn Button组件交互测试', () => {
    it('应该正确处理取消按钮点击', async () => {
      const user = userEvent.setup()
      render(<DetailedBookmarkForm {...defaultProps} />)

      const cancelButton = screen.getByRole('button', { name: /取消/i })
      await user.click(cancelButton)

      expect(mockOnCancel).toHaveBeenCalled()
    })

    it('应该在loading状态下禁用提交按钮', () => {
      render(<DetailedBookmarkForm {...defaultProps} loading={true} />)

      const submitButton = screen.getByRole('button', { name: /保存中.../i })
      expect(submitButton).toBeDisabled()
    })

    it('应该在标题为空时禁用提交按钮', () => {
      render(<DetailedBookmarkForm {...defaultProps} />)

      const submitButton = screen.getByRole('button', { name: /保存收藏/i })
      expect(submitButton).toBeDisabled()
    })
  })

  describe('AI助手功能测试', () => {
    it('应该在没有标题和描述时禁用AI助手按钮', () => {
      render(<DetailedBookmarkForm {...defaultProps} />)

      const aiButton = screen.getByRole('button', { name: /AI助手/i })
      expect(aiButton).toBeDisabled()
    })

    it('应该在有标题时启用AI助手按钮', async () => {
      const user = userEvent.setup()
      render(<DetailedBookmarkForm {...defaultProps} />)

      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      await user.type(titleInput, '测试标题')

      const aiButton = screen.getByRole('button', { name: /AI助手/i })
      expect(aiButton).not.toBeDisabled()
    })

    it('应该正确处理AI建议的标签Badge', async () => {
      const user = userEvent.setup()
      
      // Mock AI响应
      mockChrome.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: {
          tags: ['AI标签1', 'AI标签2'],
          category: '工作'
        }
      })

      render(<DetailedBookmarkForm {...defaultProps} />)

      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      await user.type(titleInput, '测试标题')

      const aiButton = screen.getByRole('button', { name: /AI助手/i })
      await user.click(aiButton)

      await waitFor(() => {
        expect(screen.getByText('AI建议的标签：')).toBeInTheDocument()
        expect(screen.getByText('+ AI标签1')).toBeInTheDocument()
        expect(screen.getByText('+ AI标签2')).toBeInTheDocument()
      })

      // 点击建议的标签
      const suggestedTag = screen.getByText('+ AI标签1')
      await user.click(suggestedTag)

      // 验证标签被添加为Badge
      expect(screen.getByText('AI标签1')).toBeInTheDocument()
    })
  })

  describe('初始数据填充测试', () => {
    const initialData = {
      title: '初始标题',
      url: 'https://example.com',
      description: '初始描述',
      tags: ['标签1', '标签2'],
      category: '工作',
      notes: '初始笔记'
    }

    it('应该正确填充初始数据到shadcn组件', () => {
      render(<DetailedBookmarkForm {...defaultProps} initialData={initialData} />)

      // 验证Input组件值
      expect(screen.getByDisplayValue('初始标题')).toBeInTheDocument()
      expect(screen.getByDisplayValue('https://example.com')).toBeInTheDocument()
      
      // 验证Textarea组件值
      expect(screen.getByDisplayValue('初始描述')).toBeInTheDocument()
      expect(screen.getByDisplayValue('初始笔记')).toBeInTheDocument()
      
      // 验证Badge组件显示标签
      expect(screen.getByText('标签1')).toBeInTheDocument()
      expect(screen.getByText('标签2')).toBeInTheDocument()
    })
  })

  describe('shadcn主题样式测试', () => {
    it('应该使用shadcn的CSS类名', () => {
      render(<DetailedBookmarkForm {...defaultProps} />)

      // 验证shadcn Button变体
      const cancelButton = screen.getByRole('button', { name: /取消/i })
      expect(cancelButton).toHaveClass('inline-flex') // shadcn button基础类

      const submitButton = screen.getByRole('button', { name: /保存收藏/i })
      expect(submitButton).toHaveClass('inline-flex') // shadcn button基础类
    })

    it('应该使用shadcn的背景色系统', () => {
      const { container } = render(<DetailedBookmarkForm {...defaultProps} />)
      
      // 验证使用shadcn背景色变量
      const mainContainer = container.firstChild as HTMLElement
      expect(mainContainer).toHaveClass('bg-background')
    })
  })
})