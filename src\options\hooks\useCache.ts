/**
 * 缓存管理 Hook
 * 提供内存缓存和持久化缓存功能
 */

import { useState, useEffect, useCallback, useRef } from 'react'

interface CacheOptions {
  ttl?: number // 缓存过期时间（毫秒）
  maxSize?: number // 最大缓存条目数
  persistent?: boolean // 是否持久化到 localStorage
  keyPrefix?: string // 缓存键前缀
}

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl?: number
}

/**
 * 内存缓存管理类
 */
class MemoryCache {
  private cache = new Map<string, CacheItem<any>>()
  private maxSize: number

  constructor(maxSize = 100) {
    this.maxSize = maxSize
  }

  set<T>(key: string, data: T, ttl?: number): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      if (firstKey) {
        this.cache.delete(firstKey)
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    // 检查是否过期
    if (item.ttl && Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  has(key: string): boolean {
    return this.cache.has(key) && this.get(key) !== null
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }

  // 清理过期条目
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (item.ttl && now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

// 全局内存缓存实例
const globalMemoryCache = new MemoryCache()

/**
 * 持久化缓存工具
 */
class PersistentCache {
  private keyPrefix: string

  constructor(keyPrefix = 'cache_') {
    this.keyPrefix = keyPrefix
  }

  private getStorageKey(key: string): string {
    return `${this.keyPrefix}${key}`
  }

  set<T>(key: string, data: T, ttl?: number): void {
    try {
      const item: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl
      }
      localStorage.setItem(this.getStorageKey(key), JSON.stringify(item))
    } catch (error) {
      console.warn('持久化缓存写入失败:', error)
    }
  }

  get<T>(key: string): T | null {
    try {
      const stored = localStorage.getItem(this.getStorageKey(key))
      if (!stored) return null

      const item: CacheItem<T> = JSON.parse(stored)
      
      // 检查是否过期
      if (item.ttl && Date.now() - item.timestamp > item.ttl) {
        this.delete(key)
        return null
      }

      return item.data
    } catch (error) {
      console.warn('持久化缓存读取失败:', error)
      return null
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  delete(key: string): boolean {
    try {
      localStorage.removeItem(this.getStorageKey(key))
      return true
    } catch (error) {
      console.warn('持久化缓存删除失败:', error)
      return false
    }
  }

  clear(): void {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(this.keyPrefix)) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.warn('持久化缓存清理失败:', error)
    }
  }
}

/**
 * 缓存管理 Hook
 */
export const useCache = <T>(options: CacheOptions = {}) => {
  const {
    ttl,
    maxSize = 100,
    persistent = false,
    keyPrefix = 'options_cache_'
  } = options

  const memoryCache = useRef(new MemoryCache(maxSize))
  const persistentCache = useRef(new PersistentCache(keyPrefix))

  // 定期清理过期缓存
  useEffect(() => {
    const cleanup = () => {
      memoryCache.current.cleanup()
    }

    const interval = setInterval(cleanup, 60000) // 每分钟清理一次
    return () => clearInterval(interval)
  }, [])

  const set = useCallback((key: string, data: T, customTtl?: number) => {
    const effectiveTtl = customTtl ?? ttl
    
    // 内存缓存
    memoryCache.current.set(key, data, effectiveTtl)
    
    // 持久化缓存
    if (persistent) {
      persistentCache.current.set(key, data, effectiveTtl)
    }
  }, [ttl, persistent])

  const get = useCallback((key: string): T | null => {
    // 优先从内存缓存获取
    let data = memoryCache.current.get<T>(key)
    
    if (data === null && persistent) {
      // 从持久化缓存获取
      data = persistentCache.current.get<T>(key)
      
      // 如果从持久化缓存获取到数据，同步到内存缓存
      if (data !== null) {
        memoryCache.current.set(key, data, ttl)
      }
    }
    
    return data
  }, [ttl, persistent])

  const has = useCallback((key: string): boolean => {
    return memoryCache.current.has(key) || 
           (persistent && persistentCache.current.has(key))
  }, [persistent])

  const remove = useCallback((key: string): boolean => {
    const memoryResult = memoryCache.current.delete(key)
    const persistentResult = persistent ? persistentCache.current.delete(key) : true
    
    return memoryResult || persistentResult
  }, [persistent])

  const clear = useCallback(() => {
    memoryCache.current.clear()
    if (persistent) {
      persistentCache.current.clear()
    }
  }, [persistent])

  const size = useCallback(() => {
    return memoryCache.current.size()
  }, [])

  return {
    set,
    get,
    has,
    remove,
    clear,
    size
  }
}

/**
 * 带缓存的数据获取 Hook
 */
export const useCachedData = <T>(
  key: string,
  fetchFn: () => Promise<T>,
  options: CacheOptions & { 
    refreshInterval?: number
    staleWhileRevalidate?: boolean
  } = {}
) => {
  const {
    refreshInterval,
    staleWhileRevalidate = false,
    ...cacheOptions
  } = options

  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [lastFetch, setLastFetch] = useState<number>(0)

  const cache = useCache<T>(cacheOptions)

  const fetchData = useCallback(async (forceRefresh = false) => {
    try {
      // 如果不是强制刷新，先尝试从缓存获取
      if (!forceRefresh) {
        const cachedData = cache.get(key)
        if (cachedData !== null) {
          setData(cachedData)
          
          // 如果启用了 stale-while-revalidate，在后台更新数据
          if (staleWhileRevalidate) {
            fetchData(true).catch(console.error)
          }
          return cachedData
        }
      }

      setLoading(true)
      setError(null)

      const result = await fetchFn()
      
      // 缓存结果
      cache.set(key, result)
      setData(result)
      setLastFetch(Date.now())
      
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('获取数据失败')
      setError(error)
      console.error('缓存数据获取失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }, [key, fetchFn, cache, staleWhileRevalidate])

  // 初始加载
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // 定期刷新
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(() => {
        fetchData(true)
      }, refreshInterval)
      
      return () => clearInterval(interval)
    }
  }, [refreshInterval, fetchData])

  const refresh = useCallback(() => {
    return fetchData(true)
  }, [fetchData])

  const invalidate = useCallback(() => {
    cache.remove(key)
    setData(null)
    setError(null)
  }, [cache, key])

  return {
    data,
    loading,
    error,
    lastFetch,
    refresh,
    invalidate
  }
}

/**
 * 搜索结果缓存 Hook
 */
export const useSearchCache = (maxSize = 50) => {
  const cache = useCache<any>({ 
    maxSize, 
    ttl: 5 * 60 * 1000, // 5分钟过期
    persistent: false 
  })

  const getCachedResults = useCallback((query: string) => {
    return cache.get(`search_${query}`)
  }, [cache])

  const setCachedResults = useCallback((query: string, results: any) => {
    cache.set(`search_${query}`, results)
  }, [cache])

  const clearSearchCache = useCallback(() => {
    cache.clear()
  }, [cache])

  return {
    getCachedResults,
    setCachedResults,
    clearSearchCache
  }
}