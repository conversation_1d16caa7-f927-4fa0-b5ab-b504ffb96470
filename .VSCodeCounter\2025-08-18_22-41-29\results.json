{"file:///d%3A/mydev/Qiankun-Pouch/src/hooks/useViewMode.ts": {"language": "TypeScript", "code": 44, "comment": 12, "blank": 9}, "file:///d%3A/mydev/Qiankun-Pouch/src/workers/DataProcessingWorker.ts": {"language": "TypeScript", "code": 410, "comment": 152, "blank": 104}, "file:///d%3A/mydev/Qiankun-Pouch/src/hooks/useTagColors.ts": {"language": "TypeScript", "code": 48, "comment": 26, "blank": 12}, "file:///d%3A/mydev/Qiankun-Pouch/src/hooks/useToast.ts": {"language": "TypeScript", "code": 72, "comment": 21, "blank": 15}, "file:///d%3A/mydev/Qiankun-Pouch/src/hooks/useShadcn.ts": {"language": "TypeScript", "code": 308, "comment": 16, "blank": 61}, "file:///d%3A/mydev/Qiankun-Pouch/src/hooks/useLoadingState.ts": {"language": "TypeScript", "code": 63, "comment": 17, "blank": 11}, "file:///d%3A/mydev/Qiankun-Pouch/src/hooks/useConflictResolution.ts": {"language": "TypeScript", "code": 143, "comment": 18, "blank": 23}, "file:///d%3A/mydev/Qiankun-Pouch/src/hooks/useAdvancedSearch.ts": {"language": "TypeScript", "code": 249, "comment": 44, "blank": 48}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/virtualScroll.ts": {"language": "TypeScript", "code": 287, "comment": 80, "blank": 57}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/validation.ts": {"language": "TypeScript", "code": 274, "comment": 114, "blank": 62}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/textUtils.ts": {"language": "TypeScript", "code": 232, "comment": 73, "blank": 61}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/serialization.ts": {"language": "TypeScript", "code": 237, "comment": 97, "blank": 34}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/tagUtils.ts": {"language": "TypeScript", "code": 289, "comment": 109, "blank": 53}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/searchUtils.ts": {"language": "TypeScript", "code": 328, "comment": 118, "blank": 72}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/modelFactory.ts": {"language": "TypeScript", "code": 195, "comment": 111, "blank": 47}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/performance.ts": {"language": "TypeScript", "code": 258, "comment": 69, "blank": 65}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/messaging.ts": {"language": "TypeScript", "code": 252, "comment": 66, "blank": 54}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/layoutUtils.ts": {"language": "TypeScript", "code": 233, "comment": 98, "blank": 48}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/layoutStability.ts": {"language": "TypeScript", "code": 213, "comment": 45, "blank": 53}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/fallbackStorage.ts": {"language": "TypeScript", "code": 263, "comment": 98, "blank": 67}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/indexedDB.ts": {"language": "TypeScript", "code": 574, "comment": 169, "blank": 154}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/errorHandler.tsx": {"language": "TypeScript JSX", "code": 254, "comment": 89, "blank": 50}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/errorHandler.ts": {"language": "TypeScript", "code": 60, "comment": 27, "blank": 10}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/debounce.ts": {"language": "TypeScript", "code": 80, "comment": 37, "blank": 17}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/dataMigration.ts": {"language": "TypeScript", "code": 270, "comment": 79, "blank": 56}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/colorUtils.ts": {"language": "TypeScript", "code": 290, "comment": 134, "blank": 69}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/chromeTestUtils.ts": {"language": "TypeScript", "code": 176, "comment": 31, "blank": 16}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/chromeStorage.ts": {"language": "TypeScript", "code": 427, "comment": 117, "blank": 65}, "file:///d%3A/mydev/Qiankun-Pouch/src/utils/bookmarkSortUtils.ts": {"language": "TypeScript", "code": 113, "comment": 36, "blank": 20}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/ViewModeSelectorExample.tsx": {"language": "TypeScript JSX", "code": 99, "comment": 8, "blank": 11}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/TagModalDemo.tsx": {"language": "TypeScript JSX", "code": 133, "comment": 18, "blank": 18}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/TagManagementTabDemo.tsx": {"language": "TypeScript JSX", "code": 14, "comment": 5, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/TagListDemo.tsx": {"language": "TypeScript JSX", "code": 184, "comment": 18, "blank": 23}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/TagFormDemo.tsx": {"language": "TypeScript JSX", "code": 238, "comment": 25, "blank": 21}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/TagColorPickerDemo.tsx": {"language": "TypeScript JSX", "code": 163, "comment": 12, "blank": 11}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/TagCardDemo.tsx": {"language": "TypeScript JSX", "code": 267, "comment": 22, "blank": 17}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/CategoryModalDemo.tsx": {"language": "TypeScript JSX", "code": 203, "comment": 20, "blank": 22}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/CategoryCardDemo.tsx": {"language": "TypeScript JSX", "code": 147, "comment": 2, "blank": 12}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/CategoryFormDemo.tsx": {"language": "TypeScript JSX", "code": 208, "comment": 13, "blank": 27}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/BookmarkToolbarDemo.tsx": {"language": "TypeScript JSX", "code": 242, "comment": 17, "blank": 36}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/BookmarkTagColorDemo.tsx": {"language": "TypeScript JSX", "code": 233, "comment": 12, "blank": 13}, "file:///d%3A/mydev/Qiankun-Pouch/src/examples/BookmarkSortDemo.tsx": {"language": "TypeScript JSX", "code": 279, "comment": 11, "blank": 16}, "file:///d%3A/mydev/Qiankun-Pouch/src/types/validation.ts": {"language": "TypeScript", "code": 40, "comment": 9, "blank": 8}, "file:///d%3A/mydev/Qiankun-Pouch/src/types/messages.ts": {"language": "TypeScript", "code": 262, "comment": 31, "blank": 53}, "file:///d%3A/mydev/Qiankun-Pouch/src/types/shadcn.ts": {"language": "TypeScript", "code": 185, "comment": 52, "blank": 62}, "file:///d%3A/mydev/Qiankun-Pouch/src/types/layout.ts": {"language": "TypeScript", "code": 91, "comment": 38, "blank": 15}, "file:///d%3A/mydev/Qiankun-Pouch/src/types/import-export.ts": {"language": "TypeScript", "code": 129, "comment": 22, "blank": 22}, "file:///d%3A/mydev/Qiankun-Pouch/src/types/index.ts": {"language": "TypeScript", "code": 337, "comment": 33, "blank": 42}, "file:///d%3A/mydev/Qiankun-Pouch/src/types/ai.ts": {"language": "TypeScript", "code": 191, "comment": 70, "blank": 28}, "file:///d%3A/mydev/Qiankun-Pouch/src/demo/addBookmarkModalTest.tsx": {"language": "TypeScript JSX", "code": 12, "comment": 2, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/demo/addBookmarkModalTest.html": {"language": "HTML", "code": 26, "comment": 0, "blank": 1}, "file:///d%3A/mydev/Qiankun-Pouch/src/demo/addBookmarkModal.html": {"language": "HTML", "code": 20, "comment": 0, "blank": 1}, "file:///d%3A/mydev/Qiankun-Pouch/src/styles/responsive.css": {"language": "CSS", "code": 294, "comment": 18, "blank": 87}, "file:///d%3A/mydev/Qiankun-Pouch/src/styles/globals.css": {"language": "CSS", "code": 347, "comment": 35, "blank": 68}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/VirtualScrollList.tsx": {"language": "TypeScript JSX", "code": 230, "comment": 36, "blank": 38}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/VirtualBookmarkList.tsx": {"language": "TypeScript JSX", "code": 276, "comment": 22, "blank": 24}, "file:///d%3A/mydev/Qiankun-Pouch/src/content/style.css": {"language": "CSS", "code": 72, "comment": 7, "blank": 11}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ViewModeSelector.tsx": {"language": "TypeScript JSX", "code": 104, "comment": 15, "blank": 10}, "file:///d%3A/mydev/Qiankun-Pouch/src/constants/shadcn.ts": {"language": "TypeScript", "code": 250, "comment": 19, "blank": 25}, "file:///d%3A/mydev/Qiankun-Pouch/src/content/index.ts": {"language": "TypeScript", "code": 78, "comment": 12, "blank": 20}, "file:///d%3A/mydev/Qiankun-Pouch/src/constants/index.ts": {"language": "TypeScript", "code": 1, "comment": 2, "blank": 1}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/bookmarkStatusService.ts": {"language": "TypeScript", "code": 134, "comment": 69, "blank": 30}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/bookmarkService.ts": {"language": "TypeScript", "code": 393, "comment": 167, "blank": 84}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/BookmarkImportExportService.ts": {"language": "TypeScript", "code": 1008, "comment": 180, "blank": 193}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/aiService.ts": {"language": "TypeScript", "code": 706, "comment": 188, "blank": 177}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/aiModelService.ts": {"language": "TypeScript", "code": 537, "comment": 193, "blank": 119}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/aiIntegrationService.ts": {"language": "TypeScript", "code": 606, "comment": 122, "blank": 81}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/aiConfigService.ts": {"language": "TypeScript", "code": 493, "comment": 111, "blank": 82}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/aiChatService.ts": {"language": "TypeScript", "code": 253, "comment": 55, "blank": 51}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/aiCacheService.ts": {"language": "TypeScript", "code": 353, "comment": 142, "blank": 76}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/WorkerManager.ts": {"language": "TypeScript", "code": 286, "comment": 121, "blank": 57}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/ViewPreferenceService.ts": {"language": "TypeScript", "code": 175, "comment": 46, "blank": 21}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/tagService.ts": {"language": "TypeScript", "code": 446, "comment": 171, "blank": 95}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/tabStatusManager.ts": {"language": "TypeScript", "code": 186, "comment": 75, "blank": 44}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/pageInfoService.ts": {"language": "TypeScript", "code": 277, "comment": 100, "blank": 62}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/MemoryOptimizedProcessor.ts": {"language": "TypeScript", "code": 436, "comment": 126, "blank": 95}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/SecurityValidator.ts": {"language": "TypeScript", "code": 476, "comment": 117, "blank": 87}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/aiProviderService.ts": {"language": "TypeScript", "code": 3353, "comment": 839, "blank": 598}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/ErrorRecoveryService.ts": {"language": "TypeScript", "code": 341, "comment": 102, "blank": 60}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/localAIServiceAdapter.ts": {"language": "TypeScript", "code": 387, "comment": 120, "blank": 74}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/defaultAIModelService.ts": {"language": "TypeScript", "code": 270, "comment": 72, "blank": 41}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/ErrorFeedbackService.ts": {"language": "TypeScript", "code": 478, "comment": 95, "blank": 73}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/defaultAIModelAPI.ts": {"language": "TypeScript", "code": 186, "comment": 83, "blank": 39}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/ConflictResolverService.ts": {"language": "TypeScript", "code": 333, "comment": 123, "blank": 81}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/CacheManager.ts": {"language": "TypeScript", "code": 467, "comment": 223, "blank": 98}, "file:///d%3A/mydev/Qiankun-Pouch/src/services/categoryService.ts": {"language": "TypeScript", "code": 431, "comment": 179, "blank": 91}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/OptionsPageErrorBoundary.tsx": {"language": "TypeScript JSX", "code": 238, "comment": 40, "blank": 36}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ObsidianIntegrationTab.tsx": {"language": "TypeScript JSX", "code": 667, "comment": 23, "blank": 52}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/NotionSyncTab.tsx": {"language": "TypeScript JSX", "code": 366, "comment": 18, "blank": 23}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/LoadingIndicator.tsx": {"language": "TypeScript JSX", "code": 86, "comment": 19, "blank": 13}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ModelSelector.tsx": {"language": "TypeScript JSX", "code": 332, "comment": 10, "blank": 21}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/LazyLoadWrapper.tsx": {"language": "TypeScript JSX", "code": 302, "comment": 31, "blank": 53}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/MCPSettingsTab.tsx": {"language": "TypeScript JSX", "code": 681, "comment": 35, "blank": 61}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/tooltip.tsx": {"language": "TypeScript JSX", "code": 22, "comment": 0, "blank": 7}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/HelpTooltip.tsx": {"language": "TypeScript JSX", "code": 243, "comment": 27, "blank": 29}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/textarea.tsx": {"language": "TypeScript JSX", "code": 20, "comment": 0, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ImportExportTab.tsx": {"language": "TypeScript JSX", "code": 719, "comment": 43, "blank": 78}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/tabs.tsx": {"language": "TypeScript JSX", "code": 47, "comment": 0, "blank": 6}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/switch.tsx": {"language": "TypeScript JSX", "code": 24, "comment": 0, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/separator.tsx": {"language": "TypeScript JSX", "code": 26, "comment": 0, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/select.tsx": {"language": "TypeScript JSX", "code": 145, "comment": 0, "blank": 13}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/progress.tsx": {"language": "TypeScript JSX", "code": 23, "comment": 0, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/input.tsx": {"language": "TypeScript JSX", "code": 19, "comment": 0, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/label.tsx": {"language": "TypeScript JSX", "code": 20, "comment": 0, "blank": 5}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/form.tsx": {"language": "TypeScript JSX", "code": 152, "comment": 0, "blank": 25}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/dropdown-menu.tsx": {"language": "TypeScript JSX", "code": 181, "comment": 0, "blank": 18}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/dialog.tsx": {"language": "TypeScript JSX", "code": 108, "comment": 0, "blank": 13}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/card.tsx": {"language": "TypeScript JSX", "code": 71, "comment": 0, "blank": 9}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/checkbox.tsx": {"language": "TypeScript JSX", "code": 25, "comment": 0, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/badge.tsx": {"language": "TypeScript JSX", "code": 31, "comment": 0, "blank": 6}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/button.tsx": {"language": "TypeScript JSX", "code": 51, "comment": 0, "blank": 6}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/alert.tsx": {"language": "TypeScript JSX", "code": 53, "comment": 0, "blank": 7}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ui/alert-dialog.tsx": {"language": "TypeScript JSX", "code": 126, "comment": 0, "blank": 14}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TruncatedTitle.tsx": {"language": "TypeScript JSX", "code": 138, "comment": 31, "blank": 27}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ToastContainer.tsx": {"language": "TypeScript JSX", "code": 38, "comment": 8, "blank": 5}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/Toast.tsx": {"language": "TypeScript JSX", "code": 119, "comment": 21, "blank": 17}, "file:///d%3A/mydev/Qiankun-Pouch/src/popup/index.tsx": {"language": "TypeScript JSX", "code": 12, "comment": 1, "blank": 2}, "file:///d%3A/mydev/Qiankun-Pouch/src/popup/PopupApp.tsx": {"language": "TypeScript JSX", "code": 557, "comment": 50, "blank": 59}, "file:///d%3A/mydev/Qiankun-Pouch/src/popup/index.html": {"language": "HTML", "code": 12, "comment": 0, "blank": 0}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/HelpTooltip/useTooltip.ts": {"language": "TypeScript", "code": 93, "comment": 16, "blank": 18}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/HelpTooltip/helpContent.ts": {"language": "TypeScript", "code": 73, "comment": 10, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/popup/components/Toggle.tsx": {"language": "TypeScript JSX", "code": 55, "comment": 0, "blank": 5}, "file:///d%3A/mydev/Qiankun-Pouch/src/popup/components/index.ts": {"language": "TypeScript", "code": 2, "comment": 0, "blank": 0}, "file:///d%3A/mydev/Qiankun-Pouch/src/popup/components/DetailedBookmarkForm.tsx": {"language": "TypeScript JSX", "code": 382, "comment": 25, "blank": 29}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/VirtualBookmarkListTestPage.tsx": {"language": "TypeScript JSX", "code": 252, "comment": 12, "blank": 12}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/VirtualBookmarkListTest.tsx": {"language": "TypeScript JSX", "code": 352, "comment": 26, "blank": 35}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/TestErrorBoundary.tsx": {"language": "TypeScript JSX", "code": 106, "comment": 7, "blank": 15}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/ShadcnTest.tsx": {"language": "TypeScript JSX", "code": 26, "comment": 7, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/ShadcnModalTest.tsx": {"language": "TypeScript JSX", "code": 173, "comment": 9, "blank": 15}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/PopupAppTestPage.tsx": {"language": "TypeScript JSX", "code": 6, "comment": 5, "blank": 2}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/PopupAppTest.tsx": {"language": "TypeScript JSX", "code": 150, "comment": 18, "blank": 18}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/OtherComponentsTestPage.tsx": {"language": "TypeScript JSX", "code": 274, "comment": 9, "blank": 21}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/LocalAIServiceTestPage.tsx": {"language": "TypeScript JSX", "code": 540, "comment": 27, "blank": 67}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/DeleteConfirmModalTestPage.tsx": {"language": "TypeScript JSX", "code": 418, "comment": 23, "blank": 30}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/DeleteConfirmModalTest.tsx": {"language": "TypeScript JSX", "code": 321, "comment": 20, "blank": 26}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/BookmarkToolbarTest.tsx": {"language": "TypeScript JSX", "code": 10, "comment": 1, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/CardImportTest.tsx": {"language": "TypeScript JSX", "code": 18, "comment": 1, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/CloudAIServiceTest.tsx": {"language": "TypeScript JSX", "code": 814, "comment": 35, "blank": 85}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/BookmarkTagColorTest.tsx": {"language": "TypeScript JSX", "code": 10, "comment": 1, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/BookmarksTabTestPage.tsx": {"language": "TypeScript JSX", "code": 233, "comment": 19, "blank": 18}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/BookmarkSortTest.tsx": {"language": "TypeScript JSX", "code": 10, "comment": 1, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/BookmarkCompactTest.tsx": {"language": "TypeScript JSX", "code": 10, "comment": 1, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/BookmarkEditModalTest.tsx": {"language": "TypeScript JSX", "code": 223, "comment": 12, "blank": 18}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/AIModelChatTest.tsx": {"language": "TypeScript JSX", "code": 659, "comment": 35, "blank": 75}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagsTab.tsx": {"language": "TypeScript JSX", "code": 133, "comment": 20, "blank": 18}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagModal.tsx": {"language": "TypeScript JSX", "code": 212, "comment": 34, "blank": 25}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/test/AIGeneratorTest.tsx": {"language": "TypeScript JSX", "code": 361, "comment": 28, "blank": 30}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagManagementTab.tsx": {"language": "TypeScript JSX", "code": 344, "comment": 48, "blank": 61}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagList.tsx": {"language": "TypeScript JSX", "code": 415, "comment": 55, "blank": 46}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagForm.tsx": {"language": "TypeScript JSX", "code": 285, "comment": 66, "blank": 40}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagColorPicker.tsx": {"language": "TypeScript JSX", "code": 232, "comment": 48, "blank": 23}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagCloud.tsx": {"language": "TypeScript JSX", "code": 149, "comment": 28, "blank": 20}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagCard.tsx": {"language": "TypeScript JSX", "code": 192, "comment": 39, "blank": 30}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/TagBatchActions.tsx": {"language": "TypeScript JSX", "code": 328, "comment": 26, "blank": 24}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/SuperMarketTab.tsx": {"language": "TypeScript JSX", "code": 445, "comment": 23, "blank": 29}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/PopupAppDemo.tsx": {"language": "TypeScript JSX", "code": 56, "comment": 9, "blank": 8}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/VirtualBookmarkListDemo.tsx": {"language": "TypeScript JSX", "code": 202, "comment": 16, "blank": 22}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/BookmarkRowDemo.tsx": {"language": "TypeScript JSX", "code": 149, "comment": 6, "blank": 11}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/DeleteConfirmModalDemo.tsx": {"language": "TypeScript JSX", "code": 178, "comment": 14, "blank": 21}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/BookmarkEditModalDemo.tsx": {"language": "TypeScript JSX", "code": 108, "comment": 10, "blank": 13}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/BookmarkCompactDemo.tsx": {"language": "TypeScript JSX", "code": 291, "comment": 10, "blank": 21}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/BasicComponentsDemo.tsx": {"language": "TypeScript JSX", "code": 176, "comment": 8, "blank": 8}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/AdvancedComponentsDemo.tsx": {"language": "TypeScript JSX", "code": 167, "comment": 9, "blank": 11}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/examples/AddBookmarkModalDemo.tsx": {"language": "TypeScript JSX", "code": 165, "comment": 17, "blank": 18}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/DeleteConfirmModal.tsx": {"language": "TypeScript JSX", "code": 221, "comment": 25, "blank": 24}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/DefaultAIModelsTab.tsx": {"language": "TypeScript JSX", "code": 409, "comment": 20, "blank": 38}, "file:///d%3A/mydev/Qiankun-Pouch/src/lib/utils.ts": {"language": "TypeScript", "code": 5, "comment": 0, "blank": 2}, "file:///d%3A/mydev/Qiankun-Pouch/src/pages/test-deleteconfirmmodal.tsx": {"language": "TypeScript JSX", "code": 6, "comment": 5, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/lib/shadcn-config.ts": {"language": "TypeScript", "code": 227, "comment": 39, "blank": 34}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ConflictResolutionDialog.tsx": {"language": "TypeScript JSX", "code": 283, "comment": 19, "blank": 26}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/BookmarkRow.tsx": {"language": "TypeScript JSX", "code": 229, "comment": 35, "blank": 22}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/BookmarkCompact.tsx": {"language": "TypeScript JSX", "code": 265, "comment": 39, "blank": 25}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/BookmarkEditModal.tsx": {"language": "TypeScript JSX", "code": 351, "comment": 28, "blank": 27}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/AIIntegrationTab.tsx": {"language": "TypeScript JSX", "code": 705, "comment": 49, "blank": 75}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/AITextGenerator.tsx": {"language": "TypeScript JSX", "code": 270, "comment": 39, "blank": 30}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/AIConfigPanel.tsx": {"language": "TypeScript JSX", "code": 411, "comment": 19, "blank": 45}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/AddBookmarkModal.tsx": {"language": "TypeScript JSX", "code": 551, "comment": 45, "blank": 48}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/BookmarksTab.tsx": {"language": "TypeScript JSX", "code": 424, "comment": 92, "blank": 58}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/CategoryManagementTab.tsx": {"language": "TypeScript JSX", "code": 231, "comment": 35, "blank": 39}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/CategoryCard.tsx": {"language": "TypeScript JSX", "code": 171, "comment": 38, "blank": 26}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/CategoryForm.tsx": {"language": "TypeScript JSX", "code": 294, "comment": 33, "blank": 33}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/BookmarkSortSelector.tsx": {"language": "TypeScript JSX", "code": 45, "comment": 18, "blank": 9}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/BookmarkToolbar.tsx": {"language": "TypeScript JSX", "code": 227, "comment": 31, "blank": 31}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/CategoryList.tsx": {"language": "TypeScript JSX", "code": 114, "comment": 20, "blank": 14}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ConflictResolution/ConflictListPanel.tsx": {"language": "TypeScript JSX", "code": 74, "comment": 1, "blank": 8}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ConflictResolution/ManualEditForm.tsx": {"language": "TypeScript JSX", "code": 72, "comment": 1, "blank": 7}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/CategoryModal.tsx": {"language": "TypeScript JSX", "code": 205, "comment": 32, "blank": 24}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ConflictResolution/BatchActionsPanel.tsx": {"language": "TypeScript JSX", "code": 34, "comment": 1, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/components/ConflictResolution/utils.ts": {"language": "TypeScript", "code": 36, "comment": 5, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/index.html": {"language": "HTML", "code": 12, "comment": 0, "blank": 0}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/OptionsApp.tsx": {"language": "TypeScript JSX", "code": 61, "comment": 13, "blank": 13}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/index.tsx": {"language": "TypeScript JSX", "code": 15, "comment": 1, "blank": 2}, "file:///d%3A/mydev/Qiankun-Pouch/src/background/index.ts": {"language": "TypeScript", "code": 92, "comment": 18, "blank": 22}, "file:///d%3A/mydev/Qiankun-Pouch/src/background/messageHandler.ts": {"language": "TypeScript", "code": 721, "comment": 177, "blank": 128}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/constants/tabsConfig.ts": {"language": "TypeScript", "code": 66, "comment": 10, "blank": 7}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/hooks/useLazyLoad.ts": {"language": "TypeScript", "code": 158, "comment": 18, "blank": 26}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/hooks/useCache.ts": {"language": "TypeScript", "code": 277, "comment": 35, "blank": 64}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/hooks/useTabNavigation.ts": {"language": "TypeScript", "code": 77, "comment": 11, "blank": 14}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/hooks/useAppInitialization.ts": {"language": "TypeScript", "code": 40, "comment": 7, "blank": 8}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/hooks/useResponsive.ts": {"language": "TypeScript", "code": 119, "comment": 34, "blank": 23}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/hooks/useTheme.ts": {"language": "TypeScript", "code": 78, "comment": 33, "blank": 19}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/ThemeToggle.tsx": {"language": "TypeScript JSX", "code": 129, "comment": 8, "blank": 11}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/TabContentRenderer.tsx": {"language": "TypeScript JSX", "code": 101, "comment": 5, "blank": 8}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/SyncTab.tsx": {"language": "TypeScript JSX", "code": 77, "comment": 2, "blank": 6}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/PageErrorBoundary.tsx": {"language": "TypeScript JSX", "code": 231, "comment": 36, "blank": 39}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/SettingsTab.tsx": {"language": "TypeScript JSX", "code": 84, "comment": 2, "blank": 6}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/LoadingState.tsx": {"language": "TypeScript JSX", "code": 22, "comment": 1, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/NavigationSidebar.tsx": {"language": "TypeScript JSX", "code": 118, "comment": 2, "blank": 5}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/HelpSearchBox.tsx": {"language": "TypeScript JSX", "code": 217, "comment": 26, "blank": 32}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/HelpCenterTab.tsx": {"language": "TypeScript JSX", "code": 469, "comment": 33, "blank": 38}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/ErrorState.tsx": {"language": "TypeScript JSX", "code": 62, "comment": 1, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/AIAssistantTab.tsx": {"language": "TypeScript JSX", "code": 66, "comment": 2, "blank": 6}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/AboutTab.tsx": {"language": "TypeScript JSX", "code": 260, "comment": 19, "blank": 25}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/components/AppHeader.tsx": {"language": "TypeScript JSX", "code": 33, "comment": 3, "blank": 4}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/data/helpContent.ts": {"language": "TypeScript", "code": 228, "comment": 10, "blank": 56}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/data/aboutInfo.ts": {"language": "TypeScript", "code": 52, "comment": 5, "blank": 3}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/utils/helpSearch.ts": {"language": "TypeScript", "code": 133, "comment": 39, "blank": 30}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/utils/manifestReader.ts": {"language": "TypeScript", "code": 127, "comment": 32, "blank": 24}, "file:///d%3A/mydev/Qiankun-Pouch/src/options/utils/performance.ts": {"language": "TypeScript", "code": 220, "comment": 72, "blank": 41}}