// 最终的标签创建功能测试脚本

console.log('🎯 开始最终的标签创建功能测试...')

function testTagCreationFinal() {
  console.log('=== 标签创建功能最终测试 ===')
  
  // 步骤1: 检查是否在标签管理页面
  const tagManagementTab = document.querySelector('[data-testid="tag-management"], .tag-management, h2:contains("标签管理")')
  if (!tagManagementTab) {
    console.log('❌ 请先导航到标签管理页面')
    return false
  }
  console.log('✅ 步骤1: 已在标签管理页面')
  
  // 步骤2: 查找并点击"新建标签"按钮
  const createButton = document.querySelector('button:contains("新建标签"), [data-testid="create-tag-button"]')
  if (!createButton) {
    console.log('❌ 未找到"新建标签"按钮')
    return false
  }
  
  console.log('✅ 步骤2: 找到"新建标签"按钮')
  console.log('点击"新建标签"按钮...')
  createButton.click()
  
  // 等待模态窗口出现
  setTimeout(() => {
    // 步骤3: 检查模态窗口是否出现
    const modal = document.querySelector('[role="dialog"], .tag-modal, [data-testid="tag-modal"]')
    if (!modal) {
      console.log('❌ 步骤3: 模态窗口未出现')
      return false
    }
    console.log('✅ 步骤3: 模态窗口已出现')
    
    // 步骤4: 查找表单元素
    const nameInput = modal.querySelector('input[id="tag-name"], input[type="text"]')
    const submitButton = modal.querySelector('button[type="submit"]')
    
    if (!nameInput || !submitButton) {
      console.log('❌ 步骤4: 表单元素缺失')
      console.log('  - 输入框:', nameInput ? '✅' : '❌')
      console.log('  - 提交按钮:', submitButton ? '✅' : '❌')
      return false
    }
    console.log('✅ 步骤4: 表单元素完整')
    
    // 步骤5: 测试输入
    const testTagName = 'TestTag' + Date.now()
    console.log('✅ 步骤5: 开始输入测试标签名称:', testTagName)
    
    nameInput.focus()
    nameInput.value = testTagName
    nameInput.dispatchEvent(new Event('input', { bubbles: true }))
    nameInput.dispatchEvent(new Event('change', { bubbles: true }))
    
    // 等待状态更新
    setTimeout(() => {
      // 步骤6: 检查按钮状态
      console.log('🔍 步骤6: 检查按钮状态')
      console.log('  - 输入框值:', `"${nameInput.value}"`)
      console.log('  - 输入框长度:', nameInput.value.length)
      console.log('  - 按钮禁用状态:', submitButton.disabled)
      console.log('  - 按钮文本:', `"${submitButton.textContent.trim()}"`)
      console.log('  - 按钮类名:', submitButton.className)
      
      // 检查错误信息
      const errorElements = modal.querySelectorAll('.text-red-600, .text-red-500, .error')
      const hasErrors = Array.from(errorElements).some(el => el.textContent.trim())
      console.log('  - 有错误信息:', hasErrors ? '❌' : '✅')
      
      if (hasErrors) {
        errorElements.forEach((el, index) => {
          if (el.textContent.trim()) {
            console.log(`    错误 ${index + 1}:`, el.textContent.trim())
          }
        })
      }
      
      // 步骤7: 尝试点击按钮
      if (!submitButton.disabled) {
        console.log('✅ 步骤7: 按钮已启用，尝试点击...')
        submitButton.click()
        
        setTimeout(() => {
          console.log('🎉 测试完成！如果看到成功消息或模态窗口关闭，说明功能正常')
        }, 1000)
        
        return true
      } else {
        console.log('❌ 步骤7: 按钮仍然被禁用')
        
        // 分析原因
        console.log('🔍 分析按钮禁用原因:')
        const hasValidName = nameInput.value.trim().length > 0 && nameInput.value.trim().length <= 50
        console.log('  - 名称有效:', hasValidName ? '✅' : '❌')
        console.log('  - 名称长度:', nameInput.value.trim().length)
        
        if (!hasValidName) {
          console.log('💡 建议: 请输入1-50个字符的标签名称')
        } else if (hasErrors) {
          console.log('💡 建议: 请修复上述错误信息')
        } else {
          console.log('💡 建议: 可能存在代码逻辑问题，请检查控制台是否有其他错误')
        }
        
        return false
      }
    }, 500)
  }, 500)
}

// 辅助函数：查找包含特定文本的元素
function findElementByText(text, selector = '*') {
  const elements = document.querySelectorAll(selector)
  for (let element of elements) {
    if (element.textContent && element.textContent.includes(text)) {
      return element
    }
  }
  return null
}

// 扩展querySelector以支持:contains伪选择器
if (!document.querySelector.toString().includes('contains')) {
  const originalQuerySelector = document.querySelector
  document.querySelector = function(selector) {
    if (selector.includes(':contains(')) {
      const match = selector.match(/(.+):contains\(["']?([^"')]+)["']?\)/)
      if (match) {
        const [, baseSelector, text] = match
        return findElementByText(text, baseSelector || '*')
      }
    }
    return originalQuerySelector.call(this, selector)
  }
}

// 自动执行测试
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(testTagCreationFinal, 2000)
  })
} else {
  setTimeout(testTagCreationFinal, 2000)
}

// 导出函数供手动调用
window.testTagCreationFinal = testTagCreationFinal

console.log('🚀 测试脚本已加载')
console.log('📋 使用说明:')
console.log('1. 确保你在标签管理页面')
console.log('2. 脚本将自动运行，或手动调用 testTagCreationFinal()')
console.log('3. 观察控制台输出，了解测试结果')