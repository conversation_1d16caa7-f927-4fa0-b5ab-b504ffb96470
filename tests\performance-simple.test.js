#!/usr/bin/env node

/**
 * 性能监控工具简化测试
 * 
 * 功能说明:
 * - 测试性能监控工具的基本功能
 * - 验证API接口的正确性
 * - 确保核心功能正常工作
 */

console.log('🧪 开始测试性能监控工具...\n')

/**
 * 轻量级测试框架类
 */
class TestRunner {
  constructor() {
    this.tests = []
    this.passed = 0
    this.failed = 0
  }

  test(name, testFn) {
    this.tests.push({ name, testFn })
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  assertEqual(actual, expected, message) {
    if (actual !== expected) {
      throw new Error(`${message} - 期望: ${expected}, 实际: ${actual}`)
    }
  }

  assertNotNull(value, message) {
    if (value === null || value === undefined) {
      throw new Error(`${message} - 值不应为null或undefined`)
    }
  }

  async run() {
    console.log(`运行 ${this.tests.length} 个测试用例...\n`)

    for (const test of this.tests) {
      try {
        await test.testFn()
        console.log(`✅ ${test.name}`)
        this.passed++
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`)
        this.failed++
      }
    }

    console.log('\n' + '='.repeat(50))
    console.log(`测试结果: ${this.passed} 通过, ${this.failed} 失败`)

    if (this.failed > 0) {
      process.exit(1)
    }
  }
}

const runner = new TestRunner()

// 模拟性能监控类
class MockPerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.timers = new Map()
  }

  static getInstance() {
    if (!MockPerformanceMonitor.instance) {
      MockPerformanceMonitor.instance = new MockPerformanceMonitor()
    }
    return MockPerformanceMonitor.instance
  }

  startTimer(label) {
    this.timers.set(label, Date.now())
  }

  endTimer(label) {
    const startTime = this.timers.get(label)
    if (!startTime) {
      console.warn(`计时器 ${label} 未找到`)
      return 0
    }

    const duration = Date.now() - startTime
    this.timers.delete(label)

    if (!this.metrics.has(label)) {
      this.metrics.set(label, [])
    }
    this.metrics.get(label).push(duration)

    return duration
  }

  recordMetric(label, value) {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, [])
    }
    this.metrics.get(label).push(value)
  }

  getMetricStats(label) {
    const values = this.metrics.get(label)
    if (!values || values.length === 0) {
      return null
    }

    const total = values.reduce((sum, val) => sum + val, 0)
    const average = total / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)

    return {
      count: values.length,
      average: Math.round(average * 100) / 100,
      min: Math.round(min * 100) / 100,
      max: Math.round(max * 100) / 100,
      total: Math.round(total * 100) / 100
    }
  }

  clearMetrics(label) {
    if (label) {
      this.metrics.delete(label)
    } else {
      this.metrics.clear()
    }
  }
}

// 模拟内存监控类
class MockMemoryMonitor {
  constructor() {
    this.intervalId = null
  }

  static getInstance() {
    if (!MockMemoryMonitor.instance) {
      MockMemoryMonitor.instance = new MockMemoryMonitor()
    }
    return MockMemoryMonitor.instance
  }

  startMonitoring(interval = 30000) {
    if (this.intervalId) {
      this.stopMonitoring()
    }
    this.intervalId = setInterval(() => {
      this.checkMemoryUsage()
    }, interval)
  }

  stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }

  checkMemoryUsage() {
    // 模拟内存检查
    console.log('检查内存使用情况...')
  }

  getCurrentMemoryUsage() {
    // 模拟内存信息
    return {
      used: 48,
      total: 95,
      limit: 191,
      percentage: 25
    }
  }
}

// 模拟防抖函数
function mockDebounce(func, delay) {
  let timeoutId = null
  return function(...args) {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      func(...args)
      timeoutId = null
    }, delay)
  }
}

// 模拟节流函数
function mockThrottle(func, delay) {
  let lastCallTime = 0
  return function(...args) {
    const now = Date.now()
    if (now - lastCallTime >= delay) {
      func(...args)
      lastCallTime = now
    }
  }
}

// 测试用例
runner.test('PerformanceMonitor - 单例模式', () => {
  const instance1 = MockPerformanceMonitor.getInstance()
  const instance2 = MockPerformanceMonitor.getInstance()
  
  runner.assert(instance1 === instance2, '应该返回相同的实例')
})

runner.test('PerformanceMonitor - 计时器功能', async () => {
  const monitor = MockPerformanceMonitor.getInstance()
  
  // 开始计时
  monitor.startTimer('test-operation')
  
  // 模拟操作耗时
  await new Promise(resolve => setTimeout(resolve, 10))
  
  // 结束计时
  const duration = monitor.endTimer('test-operation')
  
  runner.assert(duration >= 0, '计时器应该返回非负数')
})

runner.test('PerformanceMonitor - 指标记录', () => {
  const monitor = MockPerformanceMonitor.getInstance()
  
  // 清除之前的指标
  monitor.clearMetrics()
  
  // 记录指标
  monitor.recordMetric('test-metric', 100)
  monitor.recordMetric('test-metric', 200)
  monitor.recordMetric('test-metric', 150)
  
  // 获取统计信息
  const stats = monitor.getMetricStats('test-metric')
  
  runner.assertNotNull(stats, '应该返回统计信息')
  runner.assertEqual(stats.count, 3, '记录次数应该正确')
  runner.assertEqual(stats.total, 450, '总和应该正确')
  runner.assertEqual(stats.average, 150, '平均值应该正确')
  runner.assertEqual(stats.min, 100, '最小值应该正确')
  runner.assertEqual(stats.max, 200, '最大值应该正确')
})

runner.test('PerformanceMonitor - 清除指标', () => {
  const monitor = MockPerformanceMonitor.getInstance()
  
  // 记录指标
  monitor.recordMetric('test-clear', 100)
  runner.assertNotNull(monitor.getMetricStats('test-clear'), '指标应该存在')
  
  // 清除特定指标
  monitor.clearMetrics('test-clear')
  runner.assertEqual(monitor.getMetricStats('test-clear'), null, '指标应该被清除')
  
  // 记录多个指标
  monitor.recordMetric('metric1', 100)
  monitor.recordMetric('metric2', 200)
  
  // 清除所有指标
  monitor.clearMetrics()
  runner.assertEqual(monitor.getMetricStats('metric1'), null, '所有指标应该被清除')
  runner.assertEqual(monitor.getMetricStats('metric2'), null, '所有指标应该被清除')
})

runner.test('MemoryMonitor - 单例模式', () => {
  const instance1 = MockMemoryMonitor.getInstance()
  const instance2 = MockMemoryMonitor.getInstance()
  
  runner.assert(instance1 === instance2, '应该返回相同的实例')
})

runner.test('MemoryMonitor - 获取内存使用情况', () => {
  const monitor = MockMemoryMonitor.getInstance()
  
  const memoryInfo = monitor.getCurrentMemoryUsage()
  
  runner.assertNotNull(memoryInfo, '应该返回内存信息')
  runner.assertEqual(typeof memoryInfo.used, 'number', 'used应该是数字')
  runner.assertEqual(typeof memoryInfo.total, 'number', 'total应该是数字')
  runner.assertEqual(typeof memoryInfo.limit, 'number', 'limit应该是数字')
  runner.assertEqual(typeof memoryInfo.percentage, 'number', 'percentage应该是数字')
})

runner.test('MemoryMonitor - 开始和停止监控', () => {
  const monitor = MockMemoryMonitor.getInstance()
  
  // 开始监控
  monitor.startMonitoring(1000)
  runner.assertNotNull(monitor.intervalId, '应该设置监控间隔')
  
  // 停止监控
  monitor.stopMonitoring()
  runner.assertEqual(monitor.intervalId, null, '应该清除监控间隔')
})

runner.test('debounce - 基本功能', async () => {
  let callCount = 0
  const mockFn = () => { callCount++ }
  const debouncedFn = mockDebounce(mockFn, 50)
  
  // 快速连续调用
  debouncedFn()
  debouncedFn()
  debouncedFn()
  
  // 等待防抖延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  
  runner.assertEqual(callCount, 1, '防抖函数应该只被调用一次')
})

runner.test('throttle - 基本功能', async () => {
  let callCount = 0
  const mockFn = () => { callCount++ }
  const throttledFn = mockThrottle(mockFn, 50)
  
  // 第一次调用应该立即执行
  throttledFn()
  runner.assertEqual(callCount, 1, '第一次调用应该立即执行')
  
  // 在节流时间内的调用应该被忽略
  throttledFn()
  runner.assertEqual(callCount, 1, '节流时间内的调用应该被忽略')
  
  // 等待节流时间过去
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // 超过节流时间的调用应该执行
  throttledFn()
  runner.assertEqual(callCount, 2, '超过节流时间的调用应该执行')
})

runner.test('性能监控集成测试', async () => {
  const monitor = MockPerformanceMonitor.getInstance()
  
  // 清除之前的数据
  monitor.clearMetrics()
  
  // 模拟完整的性能监控流程
  monitor.startTimer('integration-test')
  
  // 模拟操作执行
  await new Promise(resolve => setTimeout(resolve, 20))
  
  // 结束监控
  const duration = monitor.endTimer('integration-test')
  
  // 验证结果
  runner.assert(duration >= 0, '集成测试耗时应该为非负数')
  
  // 获取统计信息
  const stats = monitor.getMetricStats('integration-test')
  runner.assertNotNull(stats, '应该有统计信息')
  runner.assertEqual(stats.count, 1, '应该有一次记录')
})

runner.test('内存监控集成测试', () => {
  const monitor = MockMemoryMonitor.getInstance()
  
  // 获取内存信息
  const memoryInfo = monitor.getCurrentMemoryUsage()
  
  runner.assertNotNull(memoryInfo, '应该返回内存信息')
  
  // 验证内存使用百分比计算
  const expectedPercentage = Math.round((memoryInfo.used / memoryInfo.limit) * 100)
  runner.assertEqual(memoryInfo.percentage, expectedPercentage, '内存使用百分比应该正确计算')
})

runner.test('API接口完整性测试', () => {
  const performanceMonitor = MockPerformanceMonitor.getInstance()
  const memoryMonitor = MockMemoryMonitor.getInstance()
  
  // 验证PerformanceMonitor的方法存在
  runner.assert(typeof performanceMonitor.startTimer === 'function', 'startTimer方法应该存在')
  runner.assert(typeof performanceMonitor.endTimer === 'function', 'endTimer方法应该存在')
  runner.assert(typeof performanceMonitor.recordMetric === 'function', 'recordMetric方法应该存在')
  runner.assert(typeof performanceMonitor.getMetricStats === 'function', 'getMetricStats方法应该存在')
  runner.assert(typeof performanceMonitor.clearMetrics === 'function', 'clearMetrics方法应该存在')
  
  // 验证MemoryMonitor的方法存在
  runner.assert(typeof memoryMonitor.startMonitoring === 'function', 'startMonitoring方法应该存在')
  runner.assert(typeof memoryMonitor.stopMonitoring === 'function', 'stopMonitoring方法应该存在')
  runner.assert(typeof memoryMonitor.getCurrentMemoryUsage === 'function', 'getCurrentMemoryUsage方法应该存在')
  
  // 验证工具函数存在
  runner.assert(typeof mockDebounce === 'function', 'debounce函数应该存在')
  runner.assert(typeof mockThrottle === 'function', 'throttle函数应该存在')
})

// 运行所有测试
runner.run().catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})