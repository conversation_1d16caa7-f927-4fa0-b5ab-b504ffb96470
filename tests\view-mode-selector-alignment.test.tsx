/**
 * ViewModeSelector对齐修复测试
 * 验证视图按钮在不同情况下的正确对齐
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi } from 'vitest'
import ViewModeSelector from '../src/components/ViewModeSelector'
import type { ViewMode } from '../src/components/ViewModeSelector'

describe('ViewModeSelector 对齐修复测试', () => {
  const mockOnModeChange = vi.fn()
  
  beforeEach(() => {
    mockOnModeChange.mockClear()
  })

  test('应该正确渲染下拉框和所有视图模式选项', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    // 验证下拉框存在
    const select = screen.getByLabelText('选择视图模式')
    expect(select).toBeInTheDocument()
    
    // 验证所有三个选项都存在
    expect(screen.getByRole('option', { name: '卡片视图' })).toBeInTheDocument()
    expect(screen.getByRole('option', { name: '行视图' })).toBeInTheDocument()
    expect(screen.getByRole('option', { name: '紧凑视图' })).toBeInTheDocument()
  })

  test('应该应用正确的CSS类以确保布局稳定', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式')
    
    // 验证下拉框的CSS类
    expect(select).toHaveClass('appearance-none', 'bg-white', 'border', 'border-gray-300', 'rounded-lg')
    expect(select).toHaveClass('px-4', 'py-2', 'pr-8', 'text-sm', 'font-medium', 'text-gray-700')
    
    // 验证下拉框有最小宽度
    expect(select).toHaveStyle({ minWidth: '140px' })
  })

  test('下拉框应该显示当前选中的模式', () => {
    render(
      <ViewModeSelector
        currentMode="row"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式') as HTMLSelectElement
    expect(select.value).toBe('row')
    
    // 验证选中的选项
    const selectedOption = screen.getByRole('option', { name: '行视图' }) as HTMLOptionElement
    expect(selectedOption.selected).toBe(true)
  })

  test('应该显示当前模式的图标', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    // 验证图标容器存在
    const iconContainer = document.querySelector('.absolute.inset-y-0.left-0')
    expect(iconContainer).toBeInTheDocument()
    
    // 验证SVG图标存在
    const svg = iconContainer?.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })

  test('选择下拉框选项应该触发模式切换', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式')
    fireEvent.change(select, { target: { value: 'compact' } })

    expect(mockOnModeChange).toHaveBeenCalledWith('compact')
  })

  test('选择当前模式不应该触发切换', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式')
    fireEvent.change(select, { target: { value: 'card' } })

    expect(mockOnModeChange).not.toHaveBeenCalled()
  })

  test('应该保存选择到localStorage', () => {
    const setItemSpy = vi.spyOn(Storage.prototype, 'setItem')
    
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式')
    fireEvent.change(select, { target: { value: 'compact' } })

    expect(setItemSpy).toHaveBeenCalledWith('bookmark-view-mode', 'compact')
    
    setItemSpy.mockRestore()
  })

  test('应该有正确的可访问性属性', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式') as HTMLSelectElement
    
    // 验证下拉框的可访问性属性
    expect(select).toHaveAttribute('aria-label', '选择视图模式')
    expect(select.value).toBe('card')

    // 验证所有选项都有正确的值
    const options = screen.getAllByRole('option')
    expect(options).toHaveLength(3)
    
    const expectedValues = ['card', 'row', 'compact']
    options.forEach((option, index) => {
      expect(option).toHaveAttribute('value', expectedValues[index])
    })
  })

  test('自定义className应该正确应用', () => {
    const customClass = 'custom-selector-class'
    const { container } = render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
        className={customClass}
      />
    )

    const selectorContainer = container.firstChild as HTMLElement
    expect(selectorContainer).toHaveClass(customClass)
  })

  test('应该显示下拉箭头图标', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    // 验证下拉箭头容器存在
    const arrowContainer = document.querySelector('.absolute.inset-y-0.right-0')
    expect(arrowContainer).toBeInTheDocument()
    
    // 验证箭头SVG图标存在
    const arrowSvg = arrowContainer?.querySelector('svg')
    expect(arrowSvg).toBeInTheDocument()
    expect(arrowSvg).toHaveClass('w-4', 'h-4', 'text-gray-400')
  })

  test('应该有键盘导航支持', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式')
    
    // 验证select元素默认支持键盘导航
    expect(select.tagName).toBe('SELECT')
    
    // 模拟键盘选择
    fireEvent.keyDown(select, { key: 'ArrowDown' })
    fireEvent.change(select, { target: { value: 'row' } })
    
    expect(mockOnModeChange).toHaveBeenCalledWith('row')
  })
})

describe('ViewModeSelector 下拉框稳定性测试', () => {
  const mockOnModeChange = vi.fn()

  test('下拉框应该有稳定的尺寸', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式')
    
    // 验证最小宽度设置
    expect(select).toHaveStyle({ minWidth: '140px' })
  })

  test('下拉框应该有正确的焦点样式', () => {
    render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const select = screen.getByLabelText('选择视图模式')
    
    // 验证焦点相关的CSS类
    expect(select).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-primary-500', 'focus:border-primary-500')
  })

  test('下拉框在所有屏幕尺寸下都应该稳定显示', () => {
    const { container } = render(
      <ViewModeSelector
        currentMode="card"
        onModeChange={mockOnModeChange}
      />
    )

    const wrapper = container.firstChild as HTMLElement
    
    // 验证容器使用相对定位
    expect(wrapper).toHaveClass('relative')
    
    // 验证下拉框不会因为屏幕尺寸变化而错位
    const select = screen.getByLabelText('选择视图模式')
    expect(select).toHaveClass('appearance-none') // 移除默认样式，确保一致性
  })
})