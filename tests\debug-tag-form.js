// 调试标签表单的详细脚本

console.log('🔍 开始调试标签表单...')

function debugTagForm() {
  console.log('=== 标签表单调试报告 ===')
  
  // 1. 检查模态窗口是否存在
  const modal = document.querySelector('[role="dialog"], .tag-modal, [data-testid="tag-modal"]')
  console.log('1. 模态窗口状态:', modal ? '✅ 存在' : '❌ 不存在')
  
  if (!modal) {
    console.log('请先点击"新建标签"按钮打开模态窗口')
    return
  }
  
  // 2. 检查表单元素
  const form = modal.querySelector('form, .tag-form')
  const nameInput = modal.querySelector('input[id="tag-name"], input[placeholder*="标签名称"], input[type="text"]')
  const submitButton = modal.querySelector('button[type="submit"], button:contains("创建标签"), button:contains("保存")')
  
  console.log('2. 表单元素检查:')
  console.log('   - 表单:', form ? '✅ 存在' : '❌ 不存在')
  console.log('   - 名称输入框:', nameInput ? '✅ 存在' : '❌ 不存在')
  console.log('   - 提交按钮:', submitButton ? '✅ 存在' : '❌ 不存在')
  
  if (!nameInput || !submitButton) {
    console.log('❌ 关键表单元素缺失')
    return
  }
  
  // 3. 检查输入框状态
  console.log('3. 输入框详细状态:')
  console.log('   - 值:', `"${nameInput.value}"`)
  console.log('   - 占位符:', nameInput.placeholder)
  console.log('   - 禁用状态:', nameInput.disabled)
  console.log('   - 只读状态:', nameInput.readOnly)
  console.log('   - 类名:', nameInput.className)
  
  // 4. 检查提交按钮状态
  console.log('4. 提交按钮详细状态:')
  console.log('   - 文本内容:', `"${submitButton.textContent.trim()}"`)
  console.log('   - 禁用状态:', submitButton.disabled)
  console.log('   - 类名:', submitButton.className)
  console.log('   - 类型:', submitButton.type)
  
  // 5. 检查错误信息
  const errorElements = modal.querySelectorAll('.text-red-600, .text-red-500, .error, [class*="error"]')
  console.log('5. 错误信息检查:')
  if (errorElements.length > 0) {
    errorElements.forEach((el, index) => {
      console.log(`   - 错误 ${index + 1}:`, el.textContent.trim())
    })
  } else {
    console.log('   - 无错误信息')
  }
  
  // 6. 检查验证状态
  const validationElements = modal.querySelectorAll('[class*="validating"], .animate-spin')
  console.log('6. 验证状态:', validationElements.length > 0 ? '🔄 验证中' : '✅ 验证完成')
  
  // 7. 模拟输入测试
  console.log('7. 执行输入测试...')
  const testValue = '测试标签' + Date.now()
  nameInput.value = testValue
  
  // 触发各种事件
  nameInput.dispatchEvent(new Event('input', { bubbles: true }))
  nameInput.dispatchEvent(new Event('change', { bubbles: true }))
  nameInput.dispatchEvent(new Event('blur', { bubbles: true }))
  
  setTimeout(() => {
    console.log('8. 输入测试结果:')
    console.log('   - 输入框值:', `"${nameInput.value}"`)
    console.log('   - 按钮禁用状态:', submitButton.disabled)
    
    // 9. 检查React状态（如果可能）
    const reactFiber = nameInput._reactInternalFiber || nameInput._reactInternals
    if (reactFiber) {
      console.log('9. React状态检查:')
      console.log('   - React Fiber存在:', '✅')
      // 尝试获取组件状态
      try {
        let current = reactFiber
        while (current && !current.stateNode?.state) {
          current = current.return
        }
        if (current?.stateNode?.state) {
          console.log('   - 组件状态:', current.stateNode.state)
        }
      } catch (e) {
        console.log('   - 无法获取组件状态')
      }
    }
    
    // 10. 尝试点击按钮
    if (!submitButton.disabled) {
      console.log('10. 尝试点击提交按钮...')
      submitButton.click()
      
      setTimeout(() => {
        console.log('11. 点击结果检查:')
        const loadingIndicator = modal.querySelector('.animate-spin, [data-testid="loading"]')
        console.log('   - 显示加载状态:', loadingIndicator ? '✅' : '❌')
      }, 100)
    } else {
      console.log('10. ❌ 按钮被禁用，无法点击')
      
      // 分析禁用原因
      console.log('11. 分析按钮禁用原因:')
      console.log('   - 输入框是否为空:', nameInput.value.trim() === '')
      console.log('   - 是否有验证错误:', errorElements.length > 0)
      console.log('   - 是否在验证中:', validationElements.length > 0)
    }
  }, 1000)
}

// 自动执行或提供手动调用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(debugTagForm, 1000)
  })
} else {
  setTimeout(debugTagForm, 1000)
}

// 导出调试函数
window.debugTagForm = debugTagForm

console.log('调试脚本已加载，可以手动调用 debugTagForm() 函数')