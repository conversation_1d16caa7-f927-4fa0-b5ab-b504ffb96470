<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AddBookmarkModal shadcn重构演示</title>
    <script type="module" crossorigin src="../assets/globals-49bf6f85.js"></script>
    <link rel="stylesheet" crossorigin href="../assets/globals-e341fa95.css">
</head>
<body>
    <div id="root"></div>
    <script type="module">
        import React from 'react'
        import { createRoot } from 'react-dom/client'
        import AddBookmarkModalDemo from '../components/examples/AddBookmarkModalDemo.tsx'

        const root = createRoot(document.getElementById('root'))
        root.render(React.createElement(AddBookmarkModalDemo))
    </script>
</body>
</html>