// shadcn组件测试页面

import React, { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import BookmarkCompactDemo from '../examples/BookmarkCompactDemo'
import BookmarkRowDemo from '../examples/BookmarkRowDemo'
import BookmarkEditModalTest from './BookmarkEditModalTest'
import AIGeneratorTest from './AIGeneratorTest'

/**
 * shadcn组件测试页面
 * 用于验证shadcn组件是否正常工作，包括重构后的BookmarkCompact组件
 */
const ShadcnModalTest: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [name, setName] = useState('')
  const [activeDemo, setActiveDemo] = useState<'modal' | 'compact' | 'row' | 'editmodal' | 'aigenerator'>('aigenerator')

  return (
    <div className="p-8 space-y-6">
      <div className="mb-6">
        <h2 className="text-3xl font-bold mb-2">shadcn组件测试</h2>
        <p className="text-muted-foreground">验证shadcn组件集成和重构后的组件效果</p>
      </div>

      {/* 演示选择器 */}
      <div className="flex flex-wrap gap-2 mb-6">
        <Button 
          variant={activeDemo === 'aigenerator' ? 'default' : 'outline'}
          onClick={() => setActiveDemo('aigenerator')}
        >
          ✨ AI生成功能测试
        </Button>
        <Button 
          variant={activeDemo === 'editmodal' ? 'default' : 'outline'}
          onClick={() => setActiveDemo('editmodal')}
        >
          📝 BookmarkEditModal测试
        </Button>
        <Button 
          variant={activeDemo === 'compact' ? 'default' : 'outline'}
          onClick={() => setActiveDemo('compact')}
        >
          BookmarkCompact演示
        </Button>
        <Button 
          variant={activeDemo === 'row' ? 'default' : 'outline'}
          onClick={() => setActiveDemo('row')}
        >
          BookmarkRow演示
        </Button>
        <Button 
          variant={activeDemo === 'modal' ? 'default' : 'outline'}
          onClick={() => setActiveDemo('modal')}
        >
          Modal测试
        </Button>
      </div>

      {/* 演示内容 */}
      {activeDemo === 'aigenerator' && (
        <div>
          <AIGeneratorTest />
        </div>
      )}

      {activeDemo === 'editmodal' && (
        <div>
          <h3 className="text-xl font-semibold mb-4">BookmarkEditModal shadcn重构测试</h3>
          <BookmarkEditModalTest />
        </div>
      )}

      {activeDemo === 'compact' && (
        <div>
          <h3 className="text-xl font-semibold mb-4">BookmarkCompact组件演示</h3>
          <BookmarkCompactDemo />
        </div>
      )}

      {activeDemo === 'row' && (
        <div>
          <h3 className="text-xl font-semibold mb-4">BookmarkRow组件演示</h3>
          <BookmarkRowDemo />
        </div>
      )}

      {activeDemo === 'modal' && (
        <div>
          <h3 className="text-xl font-semibold mb-4">shadcn Modal测试</h3>
      
      {/* 使用DialogTrigger的方式 */}
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="outline" className="bg-red-500 text-white hover:bg-red-600">
            打开shadcn Dialog (红色按钮)
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px] bg-yellow-100 border-4 border-red-500">
          <DialogHeader>
            <DialogTitle className="text-red-600 text-xl">
              🎉 这是shadcn Dialog！
            </DialogTitle>
            <DialogDescription className="text-red-500">
              如果你看到这个黄色背景和红色边框，说明shadcn组件正在工作！
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right font-bold">
                姓名
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3 border-2 border-blue-500"
                placeholder="输入你的姓名"
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="submit" 
              className="bg-green-500 hover:bg-green-600 text-white"
            >
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 手动控制的方式 */}
      <div className="space-y-2">
        <Button 
          onClick={() => setIsOpen(true)}
          className="bg-purple-500 hover:bg-purple-600 text-white"
        >
          打开手动控制的Dialog (紫色按钮)
        </Button>
        
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="sm:max-w-[425px] bg-blue-100 border-4 border-purple-500">
            <DialogHeader>
              <DialogTitle className="text-purple-600 text-xl">
                🚀 手动控制的shadcn Dialog！
              </DialogTitle>
              <DialogDescription className="text-purple-500">
                这个Dialog是通过state手动控制的，就像AddBookmarkModal一样！
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p className="text-gray-700">
                当前输入的姓名：<strong className="text-purple-600">{name || '(空)'}</strong>
              </p>
            </div>
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsOpen(false)}
                className="border-purple-500 text-purple-600 hover:bg-purple-50"
              >
                关闭
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

          <div className="mt-8 p-4 bg-muted rounded-lg">
            <h4 className="font-bold mb-2">Modal测试说明：</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 如果按钮显示为红色和紫色，说明Tailwind CSS正常工作</li>
              <li>• 如果模态窗口有黄色/蓝色背景和彩色边框，说明shadcn Dialog正常工作</li>
              <li>• 如果输入框有蓝色边框，说明shadcn Input正常工作</li>
              <li>• 如果所有样式都正常，那么AddBookmarkModal的shadcn重构也应该正常工作</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  )
}

export default ShadcnModalTest