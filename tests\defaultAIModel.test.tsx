// 默认AI模型服务测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { defaultAIModelService, DefaultAIModelUsage } from '../src/services/defaultAIModelService'
import { DefaultAIModelAPI } from '../src/services/defaultAIModelAPI'
import { ChromeStorageService } from '../src/utils/chromeStorage'

// Mock Chrome Storage Service
vi.mock('../src/utils/chromeStorage', () => ({
  ChromeStorageService: {
    getSyncSetting: vi.fn(),
    saveSyncSetting: vi.fn(),
  }
}))

// Mock AI Integration Service
vi.mock('../src/services/aiIntegrationService', () => ({
  aiIntegrationService: {
    getConfiguredProviders: vi.fn().mockResolvedValue([
      {
        id: 'test-provider-1',
        name: 'Test Provider 1',
        type: 'openai',
        enabled: true
      },
      {
        id: 'test-provider-2',
        name: 'Test Provider 2',
        type: 'claude',
        enabled: true
      }
    ]),
    testConnection: vi.fn().mockResolvedValue({ success: true }),
    getAvailableModels: vi.fn().mockImplementation((providerId) => {
      if (providerId === 'test-provider-1') {
        return Promise.resolve([
          {
            id: 'model-1',
            name: 'gpt-4',
            displayName: 'GPT-4',
            isRecommended: true,
            isPopular: true
          }
        ])
      } else if (providerId === 'test-provider-2') {
        return Promise.resolve([
          {
            id: 'model-2',
            name: 'claude-3',
            displayName: 'Claude 3',
            isRecommended: true,
            isPopular: false
          }
        ])
      }
      return Promise.resolve([])
    })
  }
}))

describe('DefaultAIModelService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getDefaultModelUsages', () => {
    it('应该返回默认配置当没有保存的配置时', async () => {
      // Mock 返回空数组表示没有保存的配置
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue([])
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue()

      const usages = await defaultAIModelService.getDefaultModelUsages()

      expect(usages).toHaveLength(6) // 默认有6个使用场景
      expect(usages[0]).toMatchObject({
        id: 'default-chat',
        name: '默认聊天',
        category: 'chat',
        enabled: true,
        priority: 1
      })
    })

    it('应该返回保存的配置', async () => {
      const savedUsages: DefaultAIModelUsage[] = [
        {
          id: 'default-chat',
          name: '默认聊天',
          description: '通用对话和问答场景',
          category: 'chat',
          selectedModelId: 'test-provider-1_model-1',
          fallbackModelId: 'test-provider-2_model-2',
          enabled: true,
          priority: 1
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(savedUsages)

      const usages = await defaultAIModelService.getDefaultModelUsages()

      expect(usages).toEqual(savedUsages)
    })
  })

  describe('updateUsageModel', () => {
    it('应该更新指定使用场景的模型配置', async () => {
      const initialUsages: DefaultAIModelUsage[] = [
        {
          id: 'default-chat',
          name: '默认聊天',
          description: '通用对话和问答场景',
          category: 'chat',
          selectedModelId: null,
          fallbackModelId: null,
          enabled: true,
          priority: 1
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(initialUsages)
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue()

      await defaultAIModelService.updateUsageModel(
        'default-chat',
        'test-provider-1_model-1',
        'test-provider-2_model-2'
      )

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'default_ai_models',
        expect.arrayContaining([
          expect.objectContaining({
            id: 'default-chat',
            selectedModelId: 'test-provider-1_model-1',
            fallbackModelId: 'test-provider-2_model-2'
          })
        ])
      )
    })
  })

  describe('setRecommendedConfiguration', () => {
    it('应该为所有场景设置推荐模型', async () => {
      const initialUsages: DefaultAIModelUsage[] = [
        {
          id: 'default-chat',
          name: '默认聊天',
          description: '通用对话和问答场景',
          category: 'chat',
          selectedModelId: null,
          fallbackModelId: null,
          enabled: true,
          priority: 1
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(initialUsages)
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue()

      await defaultAIModelService.setRecommendedConfiguration()

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'default_ai_models',
        expect.arrayContaining([
          expect.objectContaining({
            id: 'default-chat',
            selectedModelId: 'test-provider-1_model-1', // 推荐模型
            fallbackModelId: 'test-provider-2_model-1'  // 备用模型
          })
        ])
      )
    })

    it('应该在没有可用模型时抛出错误', async () => {
      // Mock 返回空的可用模型列表
      const mockService = await import('../src/services/aiIntegrationService')
      vi.mocked(mockService.aiIntegrationService.getConfiguredProviders).mockResolvedValue([])

      await expect(defaultAIModelService.setRecommendedConfiguration())
        .rejects.toThrow('没有可用的AI模型，请先在AI集成页面配置模型')
    })
  })

  describe('getUsageStats', () => {
    it('应该返回正确的统计信息', async () => {
      const usages: DefaultAIModelUsage[] = [
        {
          id: 'default-chat',
          name: '默认聊天',
          description: '通用对话和问答场景',
          category: 'chat',
          selectedModelId: 'test-provider-1_model-1',
          fallbackModelId: null,
          enabled: true,
          priority: 1
        },
        {
          id: 'translation',
          name: '翻译模型',
          description: '文本翻译和多语言处理',
          category: 'translation',
          selectedModelId: null,
          fallbackModelId: null,
          enabled: false,
          priority: 2
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(usages)

      const stats = await defaultAIModelService.getUsageStats()

      expect(stats).toEqual({
        total: 2,
        configured: 1, // 只有一个配置了模型
        enabled: 1     // 只有一个启用
      })
    })
  })
})

describe('DefaultAIModelAPI', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 为每个测试重新设置mock，确保返回正确的可用模型数据
    vi.mocked(ChromeStorageService.getSyncSetting).mockImplementation((key) => {
      if (key === 'default_ai_models') {
        return Promise.resolve([
          {
            id: 'default-chat',
            name: '默认聊天',
            description: '通用对话和问答场景',
            category: 'chat',
            selectedModelId: 'test-provider-1_model-1',
            fallbackModelId: 'test-provider-2_model-2',
            enabled: true,
            priority: 1
          }
        ])
      }
      return Promise.resolve([])
    })
  })

  describe('getDefaultModel', () => {
    it('应该返回指定使用场景的默认模型', async () => {
      const model = await DefaultAIModelAPI.getDefaultModel('default-chat')

      expect(model).toMatchObject({
        id: 'test-provider-1_model-1',
        displayName: 'GPT-4',
        provider: 'Test Provider 1'
      })
    })

    it('应该在主要模型不可用时返回备用模型', async () => {
      // Mock返回无效的主要模型ID
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue([
        {
          id: 'default-chat',
          name: '默认聊天',
          description: '通用对话和问答场景',
          category: 'chat',
          selectedModelId: 'invalid-model',
          fallbackModelId: 'test-provider-2_model-2',
          enabled: true,
          priority: 1
        }
      ])

      const model = await DefaultAIModelAPI.getDefaultModel('default-chat')

      expect(model).toMatchObject({
        id: 'test-provider-2_model-2',
        displayName: 'Claude 3',
        provider: 'Test Provider 2'
      })
    })
  })

  describe('getDefaultChatModel', () => {
    it('应该返回默认聊天模型', async () => {
      const model = await DefaultAIModelAPI.getDefaultChatModel()

      expect(model).toMatchObject({
        id: 'test-provider-1_model-1',
        displayName: 'GPT-4'
      })
    })
  })

  describe('isModelConfigured', () => {
    it('应该正确检查模型是否已配置', async () => {
      // Mock返回混合配置状态
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue([
        {
          id: 'default-chat',
          name: '默认聊天',
          description: '通用对话和问答场景',
          category: 'chat',
          selectedModelId: 'test-provider-1_model-1',
          fallbackModelId: null,
          enabled: true,
          priority: 1
        },
        {
          id: 'translation',
          name: '翻译模型',
          description: '文本翻译和多语言处理',
          category: 'translation',
          selectedModelId: null,
          fallbackModelId: null,
          enabled: true,
          priority: 2
        }
      ])

      const chatConfigured = await DefaultAIModelAPI.isModelConfigured('default-chat')
      const translationConfigured = await DefaultAIModelAPI.isModelConfigured('translation')

      expect(chatConfigured).toBe(true)
      expect(translationConfigured).toBe(false)
    })
  })

  describe('getUsageStatistics', () => {
    it('应该返回正确的统计信息', async () => {
      const stats = await DefaultAIModelAPI.getUsageStatistics()

      expect(stats).toEqual({
        total: 1,
        configured: 1,
        available: 2 // test-provider-1_model-1 + test-provider-2_model-2
      })
    })
  })
})