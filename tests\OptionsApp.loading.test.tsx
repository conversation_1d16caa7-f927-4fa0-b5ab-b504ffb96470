// OptionsApp加载状态管理测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi } from 'vitest'
import OptionsApp from '../src/options/OptionsApp'

// 模拟Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// 设置全局Chrome对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

// 模拟window.location
const mockLocation = {
  hash: '',
  reload: vi.fn()
}

Object.defineProperty(global.window, 'location', {
  value: mockLocation,
  writable: true
})

describe('OptionsApp 加载状态管理', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocation.hash = ''
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('应该显示初始化加载状态', async () => {
    render(<OptionsApp />)

    // 检查加载状态是否显示
    expect(screen.getByText('正在初始化收藏管理页面...')).toBeInTheDocument()
    expect(document.querySelector('.animate-spin')).toBeInTheDocument() // 加载动画

    // 等待加载完成
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })
  })

  it('应该在初始化成功后显示正常界面', async () => {
    render(<OptionsApp />)

    // 等待初始化完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      expect(screen.getByText('智能收藏管理工具')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /收藏管理/ })).toBeInTheDocument()
    })
  })

  it('应该根据URL hash设置默认标签页', async () => {
    mockLocation.hash = '#settings'

    render(<OptionsApp />)

    await waitFor(() => {
      // 检查设置标签页是否被激活
      const settingsTab = screen.getByRole('button', { name: /设置/ })
      expect(settingsTab).toHaveClass('bg-primary-50')
    })
  })

  it('应该在Chrome API不可用时显示错误', async () => {
    // 临时移除Chrome API
    const originalChrome = global.chrome
    // @ts-ignore
    global.chrome = undefined

    render(<OptionsApp />)

    await waitFor(() => {
      expect(screen.getByText('初始化失败')).toBeInTheDocument()
      expect(screen.getByText('Chrome扩展API不可用，请确保在扩展环境中运行')).toBeInTheDocument()
    })

    // 恢复Chrome API
    global.chrome = originalChrome
  })

  it('应该提供重试功能', async () => {
    // 临时移除Chrome API
    const originalChrome = global.chrome
    // @ts-ignore
    global.chrome = undefined

    render(<OptionsApp />)

    await waitFor(() => {
      expect(screen.getByText('初始化失败')).toBeInTheDocument()
    })

    // 恢复Chrome API
    global.chrome = originalChrome

    // 点击重试按钮
    const retryButton = screen.getByText(/重试/)
    fireEvent.click(retryButton)

    // 应该显示加载状态
    expect(screen.getByText('正在初始化收藏管理页面...')).toBeInTheDocument()

    // 等待重试完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })
  })

  it('应该显示重试次数', async () => {
    // 临时移除Chrome API
    const originalChrome = global.chrome
    // @ts-ignore
    global.chrome = undefined

    render(<OptionsApp />)

    await waitFor(() => {
      expect(screen.getByText('初始化失败')).toBeInTheDocument()
    })

    // 第一次重试
    const retryButton = screen.getByText(/重试/)
    fireEvent.click(retryButton)

    await waitFor(() => {
      expect(screen.getByText('重试次数: 1/3')).toBeInTheDocument()
    })

    // 恢复Chrome API
    global.chrome = originalChrome
  })

  it('应该在达到最大重试次数后隐藏重试按钮', async () => {
    // 临时移除Chrome API
    const originalChrome = global.chrome
    // @ts-ignore
    global.chrome = undefined

    render(<OptionsApp />)

    await waitFor(() => {
      expect(screen.getByText('初始化失败')).toBeInTheDocument()
    })

    // 进行3次重试
    for (let i = 0; i < 3; i++) {
      const retryButton = screen.getByText(/重试/)
      fireEvent.click(retryButton)
      
      await waitFor(() => {
        expect(screen.getByText(`重试次数: ${i + 1}/3`)).toBeInTheDocument()
      })
    }

    // 检查重试按钮是否消失
    expect(screen.queryByText(/重试/)).not.toBeInTheDocument()
    expect(screen.getByText('已达到最大重试次数，请尝试刷新页面或重新加载扩展')).toBeInTheDocument()

    // 恢复Chrome API
    global.chrome = originalChrome
  })

  it('应该提供刷新页面功能', async () => {
    // 临时移除Chrome API
    const originalChrome = global.chrome
    // @ts-ignore
    global.chrome = undefined

    render(<OptionsApp />)

    await waitFor(() => {
      expect(screen.getByText('初始化失败')).toBeInTheDocument()
    })

    // 点击刷新页面按钮
    const refreshButton = screen.getByText('刷新页面')
    fireEvent.click(refreshButton)

    expect(mockLocation.reload).toHaveBeenCalled()

    // 恢复Chrome API
    global.chrome = originalChrome
  })

  it('应该处理无效的URL hash', async () => {
    mockLocation.hash = '#invalid-tab'

    render(<OptionsApp />)

    await waitFor(() => {
      // 应该默认显示收藏管理标签页
      const bookmarksTab = screen.getByRole('button', { name: /收藏管理/ })
      expect(bookmarksTab).toHaveClass('bg-primary-50')
    })
  })

  it('应该处理空的URL hash', async () => {
    mockLocation.hash = ''

    render(<OptionsApp />)

    await waitFor(() => {
      // 应该默认显示收藏管理标签页
      const bookmarksTab = screen.getByRole('button', { name: /收藏管理/ })
      expect(bookmarksTab).toHaveClass('bg-primary-50')
    })
  })

  it('应该正确处理所有有效的标签页hash', async () => {
    const validTabs = ['bookmarks', 'categories', 'tags', 'settings', 'import-export']
    const tabNames = ['收藏管理', '分类管理', '标签管理', '设置', '导入导出']

    for (let i = 0; i < validTabs.length; i++) {
      mockLocation.hash = `#${validTabs[i]}`

      const { unmount } = render(<OptionsApp />)

      await waitFor(() => {
        const tab = screen.getByRole('button', { name: new RegExp(tabNames[i]) })
        expect(tab).toHaveClass('bg-primary-50')
      })

      unmount()
    }
  })

  it('应该在初始化过程中显示适当的加载动画', async () => {
    render(<OptionsApp />)

    // 检查加载动画是否存在
    const loadingSpinner = document.querySelector('.animate-spin')
    expect(loadingSpinner).toBeInTheDocument()
    expect(loadingSpinner).toHaveClass('rounded-full', 'border-b-2', 'border-primary-600')

    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })
  })
})