import { 
  Bookmark, 
  BookmarkInput, 
  Category, 
  CategoryInput, 
  Tag, 
  TagInput,
  BookmarkMetadata,
  PageInfo
} from '../types'
import { ValidationUtils } from './validation'
import { SerializationUtils } from './serialization'

/**
 * 数据模型工厂类
 * 提供创建和管理数据模型的统一接口
 */
export class ModelFactory {

  /**
   * 创建书签对象
   * @param input 书签输入数据
   * @param validate 是否进行验证（默认为true）
   * @returns 创建的书签对象
   * @throws 如果验证失败则抛出错误
   */
  static createBookmark(input: BookmarkInput, validate: boolean = true): Bookmark {
    // 清理输入数据
    const sanitizedInput = ValidationUtils.sanitizeBookmarkInput(input)

    // 验证数据
    if (validate) {
      const validationResult = ValidationUtils.validateBookmarkInput(sanitizedInput)
      if (!validationResult.isValid) {
        throw new Error(`书签数据验证失败: ${validationResult.errors.map(e => e.message).join(', ')}`)
      }
    }

    // 创建书签对象
    return SerializationUtils.createBookmarkFromInput(sanitizedInput)
  }

  /**
   * 从页面信息创建书签
   * @param pageInfo 页面信息
   * @param additionalData 额外的书签数据
   * @returns 创建的书签对象
   */
  static createBookmarkFromPageInfo(
    pageInfo: PageInfo, 
    additionalData?: Partial<BookmarkInput>
  ): Bookmark {
    const input: BookmarkInput = {
      type: pageInfo.selectedText ? 'text' : 'url',
      title: pageInfo.title,
      url: pageInfo.url,
      content: pageInfo.selectedText,
      favicon: pageInfo.favicon,
      metadata: {
        pageTitle: pageInfo.title,
        siteName: this.extractSiteName(pageInfo.url),
        publishDate: pageInfo.timestamp,
        wordCount: pageInfo.selectedText ? pageInfo.selectedText.length : undefined,
        aiGenerated: false
      },
      ...additionalData
    }

    return this.createBookmark(input)
  }

  /**
   * 创建分类对象
   * @param input 分类输入数据
   * @param validate 是否进行验证（默认为true）
   * @returns 创建的分类对象
   * @throws 如果验证失败则抛出错误
   */
  static createCategory(input: CategoryInput, validate: boolean = true): Category {
    // 清理输入数据
    const sanitizedInput = ValidationUtils.sanitizeCategoryInput(input)

    // 验证数据
    if (validate) {
      const validationResult = ValidationUtils.validateCategoryInput(sanitizedInput)
      if (!validationResult.isValid) {
        throw new Error(`分类数据验证失败: ${validationResult.errors.map(e => e.message).join(', ')}`)
      }
    }

    // 创建分类对象
    return SerializationUtils.createCategoryFromInput(sanitizedInput)
  }

  /**
   * 创建标签对象
   * @param input 标签输入数据
   * @param validate 是否进行验证（默认为true）
   * @returns 创建的标签对象
   * @throws 如果验证失败则抛出错误
   */
  static createTag(input: TagInput, validate: boolean = true): Tag {
    // 清理输入数据
    const sanitizedInput = ValidationUtils.sanitizeTagInput(input)

    // 验证数据
    if (validate) {
      const validationResult = ValidationUtils.validateTagInput(sanitizedInput)
      if (!validationResult.isValid) {
        throw new Error(`标签数据验证失败: ${validationResult.errors.map(e => e.message).join(', ')}`)
      }
    }

    // 创建标签对象
    return SerializationUtils.createTagFromInput(sanitizedInput)
  }

  /**
   * 创建默认分类
   * @returns 默认分类对象
   */
  static createDefaultCategory(): Category {
    return this.createCategory({
      name: '默认分类',
      description: '系统默认分类',
      color: '#6B7280'
    }, false)
  }

  /**
   * 批量创建标签
   * @param tagNames 标签名称数组
   * @returns 创建的标签对象数组
   */
  static createTagsFromNames(tagNames: string[]): Tag[] {
    return tagNames
      .filter(name => name && name.trim().length > 0)
      .map(name => this.createTag({ name: name.trim() }, false))
  }

  /**
   * 更新书签对象
   * @param bookmark 原书签对象
   * @param updates 更新数据
   * @returns 更新后的书签对象
   */
  static updateBookmark(bookmark: Bookmark, updates: Partial<BookmarkInput>): Bookmark {
    const updatedBookmark = SerializationUtils.deepClone(bookmark)
    
    // 更新字段
    if (updates.title !== undefined) updatedBookmark.title = updates.title
    if (updates.description !== undefined) updatedBookmark.description = updates.description
    if (updates.tags !== undefined) updatedBookmark.tags = updates.tags
    if (updates.category !== undefined) updatedBookmark.category = updates.category
    if (updates.content !== undefined) updatedBookmark.content = updates.content
    if (updates.url !== undefined) updatedBookmark.url = updates.url

    // 更新元数据
    if (updates.metadata) {
      updatedBookmark.metadata = {
        ...updatedBookmark.metadata,
        ...updates.metadata
      }
    }

    // 更新时间戳
    updatedBookmark.updatedAt = new Date()

    return updatedBookmark
  }

  /**
   * 更新分类对象
   * @param category 原分类对象
   * @param updates 更新数据
   * @returns 更新后的分类对象
   */
  static updateCategory(category: Category, updates: Partial<CategoryInput>): Category {
    const updatedCategory = SerializationUtils.deepClone(category)
    
    // 更新字段
    if (updates.name !== undefined) updatedCategory.name = updates.name
    if (updates.description !== undefined) updatedCategory.description = updates.description
    if (updates.color !== undefined) updatedCategory.color = updates.color
    if (updates.parentId !== undefined) updatedCategory.parentId = updates.parentId

    // 更新时间戳
    updatedCategory.updatedAt = new Date()

    return updatedCategory
  }

  /**
   * 更新标签对象
   * @param tag 原标签对象
   * @param updates 更新数据
   * @returns 更新后的标签对象
   */
  static updateTag(tag: Tag, updates: Partial<TagInput>): Tag {
    const updatedTag = SerializationUtils.deepClone(tag)
    
    // 更新字段
    if (updates.name !== undefined) updatedTag.name = updates.name
    if (updates.color !== undefined) updatedTag.color = updates.color

    // 更新时间戳
    updatedTag.updatedAt = new Date()

    return updatedTag
  }

  /**
   * 从URL提取网站名称
   * @param url URL字符串
   * @returns 网站名称
   */
  private static extractSiteName(url: string): string {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace('www.', '')
    } catch {
      return '未知网站'
    }
  }

  /**
   * 生成书签摘要
   * @param bookmark 书签对象
   * @param maxLength 最大长度（默认100）
   * @returns 书签摘要
   */
  static generateBookmarkSummary(bookmark: Bookmark, maxLength: number = 100): string {
    let summary = ''

    if (bookmark.description) {
      summary = bookmark.description
    } else if (bookmark.content) {
      summary = bookmark.content
    } else if (bookmark.metadata.pageTitle && bookmark.metadata.pageTitle !== bookmark.title) {
      summary = bookmark.metadata.pageTitle
    } else {
      summary = bookmark.title
    }

    if (summary.length > maxLength) {
      return summary.substring(0, maxLength - 3) + '...'
    }

    return summary
  }

  /**
   * 检查两个书签是否相似
   * @param bookmark1 书签1
   * @param bookmark2 书签2
   * @returns 相似度（0-1之间的数值）
   */
  static calculateBookmarkSimilarity(bookmark1: Bookmark, bookmark2: Bookmark): number {
    let similarity = 0
    let factors = 0

    // URL相似度
    if (bookmark1.url && bookmark2.url) {
      factors++
      if (bookmark1.url === bookmark2.url) {
        similarity += 1
      } else if (this.normalizeUrl(bookmark1.url) === this.normalizeUrl(bookmark2.url)) {
        similarity += 0.9
      }
    }

    // 标题相似度
    factors++
    const titleSimilarity = this.calculateStringSimilarity(bookmark1.title, bookmark2.title)
    similarity += titleSimilarity

    // 内容相似度
    if (bookmark1.content && bookmark2.content) {
      factors++
      const contentSimilarity = this.calculateStringSimilarity(bookmark1.content, bookmark2.content)
      similarity += contentSimilarity
    }

    return factors > 0 ? similarity / factors : 0
  }

  /**
   * 标准化URL（移除查询参数和片段）
   * @param url 原始URL
   * @returns 标准化后的URL
   */
  private static normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`
    } catch {
      return url
    }
  }

  /**
   * 计算字符串相似度
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 相似度（0-1之间的数值）
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1
    if (!str1 || !str2) return 0

    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1

    if (longer.length === 0) return 1

    const editDistance = this.calculateLevenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  /**
   * 计算编辑距离（Levenshtein距离）
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 编辑距离
   */
  private static calculateLevenshteinDistance(str1: string, str2: string): number {
    const matrix = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // 替换
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j] + 1      // 删除
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }
}