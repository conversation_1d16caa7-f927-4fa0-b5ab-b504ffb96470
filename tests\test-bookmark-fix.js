// 简单的收藏功能修复测试脚本
// 在浏览器控制台中运行此脚本

console.log('🧪 测试收藏功能修复...')

// 测试收藏功能
async function testBookmarkFunctionality() {
  try {
    console.log('1. 测试快速收藏...')
    
    // 收藏当前页面
    const bookmarkResponse = await chrome.runtime.sendMessage({
      type: 'QUICK_BOOKMARK',
      data: {
        title: document.title,
        url: window.location.href,
        favIconUrl: document.querySelector('link[rel="icon"]')?.href || '',
        timestamp: new Date().toISOString()
      }
    })
    
    console.log('收藏响应:', bookmarkResponse)
    
    if (bookmarkResponse.success) {
      console.log('✅ 收藏成功，ID:', bookmarkResponse.data.bookmarkId)
      
      // 等待1秒后检查状态
      setTimeout(async () => {
        console.log('2. 检查收藏状态...')
        
        const statusResponse = await chrome.runtime.sendMessage({
          type: 'CHECK_BOOKMARK_STATUS',
          data: { url: window.location.href }
        })
        
        console.log('状态响应:', statusResponse)
        
        if (statusResponse.success && statusResponse.data.isBookmarked) {
          console.log('✅ 收藏状态正确')
          
          // 检查收藏列表
          setTimeout(async () => {
            console.log('3. 检查收藏列表...')
            
            const listResponse = await chrome.runtime.sendMessage({
              type: 'GET_BOOKMARKS',
              data: {}
            })
            
            console.log('列表响应:', listResponse)
            
            if (listResponse.success) {
              console.log('✅ 收藏列表获取成功，数量:', listResponse.data.length)
              console.log('收藏列表:', listResponse.data)
            } else {
              console.log('❌ 收藏列表获取失败')
            }
          }, 1000)
          
        } else {
          console.log('❌ 收藏状态错误')
        }
      }, 1000)
      
    } else {
      console.log('❌ 收藏失败:', bookmarkResponse.error)
    }
    
  } catch (error) {
    console.error('❌ 测试异常:', error)
  }
}

// 运行测试
if (typeof chrome !== 'undefined' && chrome.runtime) {
  testBookmarkFunctionality()
} else {
  console.log('⚠️ 请在Chrome扩展环境中运行此测试')
}