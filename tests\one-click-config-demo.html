<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键设置推荐配置功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 一键设置推荐配置功能测试</h1>
            <p>测试"一键设置推荐配置"按钮是否能正确更新详细配置区域的模型选择器</p>
        </div>

        <div class="test-section">
            <h3>🔧 测试控制</h3>
            <button class="button" onclick="runFullTest()">运行完整测试</button>
            <button class="button" onclick="setupTestEnvironment()">设置测试环境</button>
            <button class="button" onclick="testOneClickConfig()">测试一键配置</button>
            <button class="button" onclick="testSyncFunction()">测试同步功能</button>
            <button class="button" onclick="cleanupTestData()">清理测试数据</button>
            <button class="button" onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>📊 测试状态</h3>
            <div id="testStatus" class="status info">准备运行测试...</div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        // 模拟Chrome扩展环境
        if (!window.chrome) {
            window.chrome = {
                storage: {
                    sync: {
                        get: (keys, callback) => {
                            const data = {}
                            if (typeof keys === 'string') {
                                data[keys] = localStorage.getItem(`sync_${keys}`) ? JSON.parse(localStorage.getItem(`sync_${keys}`)) : undefined
                            } else if (Array.isArray(keys)) {
                                keys.forEach(key => {
                                    data[key] = localStorage.getItem(`sync_${key}`) ? JSON.parse(localStorage.getItem(`sync_${key}`)) : undefined
                                })
                            } else if (typeof keys === 'object') {
                                Object.keys(keys).forEach(key => {
                                    const stored = localStorage.getItem(`sync_${key}`)
                                    data[key] = stored ? JSON.parse(stored) : keys[key]
                                })
                            }
                            callback(data)
                        },
                        set: (data, callback) => {
                            Object.keys(data).forEach(key => {
                                localStorage.setItem(`sync_${key}`, JSON.stringify(data[key]))
                            })
                            if (callback) callback()
                        }
                    }
                }
            }
        }

        let testLog = []

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}`
            testLog.push(logEntry)
            
            const logElement = document.getElementById('testLog')
            logElement.innerHTML = testLog.join('\n')
            logElement.scrollTop = logElement.scrollHeight
            
            console.log(message)
        }

        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('testStatus')
            statusElement.textContent = message
            statusElement.className = `status ${type}`
        }

        function clearLog() {
            testLog = []
            document.getElementById('testLog').innerHTML = '日志已清空...'
        }

        // 导入测试模块
        async function loadTestModule() {
            try {
                const module = await import('./one-click-config-test.ts')
                return module.OneClickConfigTest
            } catch (error) {
                log(`❌ 加载测试模块失败: ${error.message}`, 'error')
                throw error
            }
        }

        // 运行完整测试
        window.runFullTest = async function() {
            try {
                setStatus('正在运行完整测试...', 'info')
                log('🧪 开始运行完整测试...')
                
                const TestClass = await loadTestModule()
                await TestClass.runAllTests()
                
                setStatus('✅ 所有测试通过！', 'success')
                log('✅ 完整测试成功完成！')
            } catch (error) {
                setStatus(`❌ 测试失败: ${error.message}`, 'error')
                log(`❌ 完整测试失败: ${error.message}`, 'error')
            }
        }

        // 设置测试环境
        window.setupTestEnvironment = async function() {
            try {
                setStatus('正在设置测试环境...', 'info')
                log('🔧 开始设置测试环境...')
                
                const TestClass = await loadTestModule()
                await TestClass.setupTestEnvironment()
                
                setStatus('✅ 测试环境设置完成', 'success')
                log('✅ 测试环境设置成功！')
            } catch (error) {
                setStatus(`❌ 设置失败: ${error.message}`, 'error')
                log(`❌ 设置测试环境失败: ${error.message}`, 'error')
            }
        }

        // 测试一键配置
        window.testOneClickConfig = async function() {
            try {
                setStatus('正在测试一键配置功能...', 'info')
                log('🧪 开始测试一键配置功能...')
                
                const TestClass = await loadTestModule()
                await TestClass.testOneClickConfiguration()
                
                setStatus('✅ 一键配置测试通过', 'success')
                log('✅ 一键配置功能测试成功！')
            } catch (error) {
                setStatus(`❌ 测试失败: ${error.message}`, 'error')
                log(`❌ 一键配置测试失败: ${error.message}`, 'error')
            }
        }

        // 测试同步功能
        window.testSyncFunction = async function() {
            try {
                setStatus('正在测试同步功能...', 'info')
                log('🧪 开始测试同步功能...')
                
                const TestClass = await loadTestModule()
                await TestClass.testSyncAfterConfiguration()
                
                setStatus('✅ 同步功能测试通过', 'success')
                log('✅ 同步功能测试成功！')
            } catch (error) {
                setStatus(`❌ 测试失败: ${error.message}`, 'error')
                log(`❌ 同步功能测试失败: ${error.message}`, 'error')
            }
        }

        // 清理测试数据
        window.cleanupTestData = async function() {
            try {
                setStatus('正在清理测试数据...', 'info')
                log('🧹 开始清理测试数据...')
                
                const TestClass = await loadTestModule()
                await TestClass.cleanupTestData()
                
                setStatus('✅ 测试数据清理完成', 'success')
                log('✅ 测试数据清理成功！')
            } catch (error) {
                setStatus(`❌ 清理失败: ${error.message}`, 'error')
                log(`❌ 清理测试数据失败: ${error.message}`, 'error')
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 测试页面加载完成，准备运行测试...')
            setStatus('测试页面已准备就绪，可以开始测试', 'info')
        })
    </script>
</body>
</html>
