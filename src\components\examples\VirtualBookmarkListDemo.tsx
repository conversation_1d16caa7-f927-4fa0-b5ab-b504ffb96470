/**
 * VirtualBookmarkList shadcn集成示例组件
 * 展示重构后的VirtualBookmarkList组件使用shadcn组件的效果
 */

import React, { useState } from 'react'
import VirtualBookmarkList from '../VirtualBookmarkList'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import type { ViewMode } from '../ViewModeSelector'

// 模拟收藏数据
const mockBookmarks = [
  {
    id: '1',
    title: 'React官方文档',
    url: 'https://react.dev',
    description: 'React是一个用于构建用户界面的JavaScript库',
    content: '学习React的最佳资源，包含完整的API文档和教程',
    category: '前端开发',
    tags: ['React', 'JavaScript', '前端'],
    createdAt: new Date('2024-01-01').toISOString(),
    favicon: 'https://react.dev/favicon.ico'
  },
  {
    id: '2',
    title: 'TypeScript手册',
    url: 'https://www.typescriptlang.org/docs/',
    description: 'TypeScript是JavaScript的超集，添加了静态类型定义',
    category: '编程语言',
    tags: ['TypeScript', 'JavaScript'],
    createdAt: new Date('2024-01-02').toISOString(),
    favicon: 'https://www.typescriptlang.org/favicon-32x32.png'
  },
  {
    id: '3',
    title: 'Tailwind CSS文档',
    url: 'https://tailwindcss.com/docs',
    description: '实用优先的CSS框架，用于快速构建现代网站',
    content: 'Tailwind CSS提供了大量的实用类，让你可以快速构建美观的界面',
    category: 'CSS框架',
    tags: ['CSS', 'Tailwind', '样式'],
    createdAt: new Date('2024-01-03').toISOString()
  },
  {
    id: '4',
    title: 'shadcn/ui组件库',
    url: 'https://ui.shadcn.com',
    description: '基于Radix UI和Tailwind CSS构建的可重用组件',
    content: '提供了一套完整的UI组件，可以直接复制到项目中使用',
    category: 'UI组件',
    tags: ['shadcn', 'UI', 'Radix', 'Tailwind'],
    createdAt: new Date('2024-01-04').toISOString(),
    favicon: 'https://ui.shadcn.com/favicon.ico'
  },
  {
    id: '5',
    title: 'Vite构建工具',
    url: 'https://vitejs.dev',
    description: '下一代前端构建工具，提供极快的开发体验',
    category: '构建工具',
    tags: ['Vite', '构建', '开发工具'],
    createdAt: new Date('2024-01-05').toISOString()
  }
]

/**
 * VirtualBookmarkList shadcn集成示例
 */
const VirtualBookmarkListDemo: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('card')
  const [highlightId, setHighlightId] = useState<string | null>(null)
  const [selectedBookmarks, setSelectedBookmarks] = useState<string[]>([])

  // 处理编辑收藏
  const handleEdit = (bookmark: any) => {
    console.log('编辑收藏:', bookmark)
    alert(`编辑收藏: ${bookmark.title}`)
  }

  // 处理删除收藏
  const handleDelete = (bookmark: any) => {
    console.log('删除收藏:', bookmark)
    if (confirm(`确定要删除收藏 "${bookmark.title}" 吗？`)) {
      alert(`已删除收藏: ${bookmark.title}`)
    }
  }

  // 处理点击收藏
  const handleClick = (bookmark: any) => {
    console.log('点击收藏:', bookmark)
    setHighlightId(bookmark.id === highlightId ? null : bookmark.id)
    
    // 切换选中状态
    setSelectedBookmarks(prev => 
      prev.includes(bookmark.id)
        ? prev.filter(id => id !== bookmark.id)
        : [...prev, bookmark.id]
    )
  }

  // 清除高亮
  const clearHighlight = () => {
    setHighlightId(null)
    setSelectedBookmarks([])
  }

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold text-foreground">
          VirtualBookmarkList shadcn集成示例
        </h1>
        
        <div className="text-muted-foreground">
          <p>这个示例展示了重构后的VirtualBookmarkList组件如何使用shadcn组件：</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>使用shadcn Button组件替换自定义按钮</li>
            <li>使用shadcn Badge组件显示标签</li>
            <li>使用shadcn颜色系统替换自定义颜色</li>
            <li>确保与shadcn主题系统的一致性</li>
          </ul>
        </div>
      </div>

      {/* 控制面板 */}
      <div className="border border-border rounded-lg p-4 bg-card">
        <h2 className="text-lg font-semibold text-foreground mb-4">控制面板</h2>
        
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">视图模式:</span>
            <div className="flex space-x-2">
              {(['row', 'compact', 'card'] as ViewMode[]).map(mode => (
                <Button
                  key={mode}
                  variant={viewMode === mode ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                >
                  {mode === 'row' ? '行视图' : mode === 'compact' ? '紧凑视图' : '卡片视图'}
                </Button>
              ))}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearHighlight}
              disabled={!highlightId && selectedBookmarks.length === 0}
            >
              清除选择
            </Button>
          </div>
          
          {selectedBookmarks.length > 0 && (
            <Badge variant="secondary">
              已选择 {selectedBookmarks.length} 个收藏
            </Badge>
          )}
        </div>
      </div>

      {/* 收藏列表容器 */}
      <div className="border border-border rounded-lg bg-card">
        <div className="p-4 border-b border-border">
          <h2 className="text-lg font-semibold text-foreground">
            收藏列表 ({mockBookmarks.length} 个收藏)
          </h2>
          <p className="text-sm text-muted-foreground mt-1">
            点击收藏项可以高亮显示，使用编辑和删除按钮进行操作
          </p>
        </div>
        
        <div className="p-4">
          <VirtualBookmarkList
            bookmarks={mockBookmarks}
            viewMode={viewMode}
            containerHeight={600}
            highlightBookmarkId={highlightId}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onClick={handleClick}
          />
        </div>
      </div>

      {/* shadcn组件特性说明 */}
      <div className="border border-border rounded-lg p-4 bg-muted/50">
        <h2 className="text-lg font-semibold text-foreground mb-4">shadcn组件特性</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h3 className="font-medium text-foreground">Button组件特性</h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 使用ghost变体实现悬停效果</li>
              <li>• 支持icon尺寸和样式</li>
              <li>• 内置focus和disabled状态</li>
              <li>• 与主题系统完全集成</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-medium text-foreground">Badge组件特性</h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 使用secondary变体显示标签</li>
              <li>• 圆角边框和内边距</li>
              <li>• 响应式字体大小</li>
              <li>• 支持悬停和焦点状态</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-medium text-foreground">颜色系统</h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• text-foreground: 主要文本颜色</li>
              <li>• text-muted-foreground: 次要文本</li>
              <li>• text-primary: 链接和强调色</li>
              <li>• border-border: 边框颜色</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-medium text-foreground">主题一致性</h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 支持明暗主题切换</li>
              <li>• CSS变量驱动的颜色系统</li>
              <li>• 统一的间距和圆角</li>
              <li>• 可访问性友好的对比度</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VirtualBookmarkListDemo