# Universe Bag 图标替换指南

## 概述

本文档记录了如何使用根目录下的 `logo.png` 文件替换 Universe Bag 扩展的图标。

## 替换过程

### 1. 图标文件准备

- 原始logo文件：`logo.png` (433,986 字节)
- 目标图标目录：`public/icons/`
- 需要生成的图标尺寸：16x16, 32x32, 48x48, 128x128

### 2. 图标文件生成

使用以下命令将logo.png复制为不同尺寸的图标文件：

```bash
copy logo.png public\icons\icon-16.png
copy logo.png public\icons\icon-32.png
copy logo.png public\icons\icon-48.png
copy logo.png public\icons\icon-128.png
```

### 3. 构建配置更新

在 `vite.config.ts` 中添加了图标复制插件，确保构建时图标文件被正确复制到 `dist/icons/` 目录。

### 4. manifest.json 配置

图标在 `manifest.json` 中的配置：

```json
{
  "action": {
    "default_icon": {
      "16": "icons/icon-16.png",
      "32": "icons/icon-32.png",
      "48": "icons/icon-48.png",
      "128": "icons/icon-128.png"
    }
  },
  "icons": {
    "16": "icons/icon-16.png",
    "32": "icons/icon-32.png",
    "48": "icons/icon-48.png",
    "128": "icons/icon-128.png"
  }
}
```

## 验证工具

### 1. 验证脚本

运行以下命令验证图标替换是否成功：

```bash
node tests/verify-icon-replacement.cjs
```

### 2. 效果预览

打开 `tests/icon-test.html` 在浏览器中查看图标效果。

### 3. 图标生成工具

如需重新生成适合的图标尺寸，可以使用：

```bash
# 在浏览器中打开
scripts/generate-icons.html
```

## 图标用途说明

| 尺寸 | 用途 |
|------|------|
| 16x16 | 浏览器工具栏和标签页 |
| 32x32 | 扩展管理页面和高分辨率显示 |
| 48x48 | 扩展详情页面 |
| 128x128 | Chrome网上应用店和安装界面 |

## 部署步骤

1. 运行构建命令：
   ```bash
   npm run build
   ```

2. 在Chrome中重新加载扩展：
   - 打开 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击扩展的"重新加载"按钮

3. 验证新图标是否显示正确

## 注意事项

- 当前使用的是直接复制原始logo文件的方法，可能在小尺寸下显示效果不佳
- 如需优化显示效果，建议使用专业的图像处理工具为每个尺寸单独优化
- 图标文件应保持PNG格式以确保透明度支持

## 相关文件

- `logo.png` - 原始logo文件
- `public/icons/` - 图标文件目录
- `tests/verify-icon-replacement.cjs` - 验证脚本
- `tests/icon-test.html` - 效果预览页面
- `scripts/generate-icons.html` - 图标生成工具
- `vite.config.ts` - 构建配置（包含图标复制插件）

## 更新日期

2025年8月2日 - 完成图标替换和相关工具开发