import React from 'react'
import ReactDOM from 'react-dom/client'
import OptionsApp from './OptionsApp'
import OptionsPageErrorBoundary from '../components/OptionsPageErrorBoundary'
import '../styles/globals.css'

// 创建React根节点并渲染应用
const root = ReactDOM.createRoot(
  document.getElementById('options-root') as HTMLElement
)

root.render(
  <React.StrictMode>
    <OptionsPageErrorBoundary>
      <OptionsApp />
    </OptionsPageErrorBoundary>
  </React.StrictMode>
)