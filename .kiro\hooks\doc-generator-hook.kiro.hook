{"enabled": true, "name": "Documentation Generator", "description": "Automatically generates comprehensive documentation for modified files including function signatures, parameters, return types, usage examples, and updates README.md", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.js", "**/*.ts", "**/*.py", "**/*.java", "**/*.cpp", "**/*.c", "**/*.cs", "**/*.go", "**/*.rs", "**/*.php"]}, "then": {"type": "askAgent", "prompt": "为当前修改的文件生成全面的文档：\n1. 提取函数和类的签名\n2. 记录参数和返回类型\n3. 基于现有代码提供使用示例\n4. 更新README.md文件中的新导出内容\n5. 确保文档遵循项目标准\n\n请使用中文注释，采用模块化方法，确保文档的完整性和准确性。"}}