/**
 * 响应式设计 Hook
 * 提供屏幕尺寸检测和响应式断点管理
 * 
 * 功能特性：
 * - 实时监听窗口尺寸变化
 * - 提供设备类型判断（移动端、平板、桌面）
 * - 支持自定义断点配置
 * - 提供响应式值获取功能
 * - 防抖优化性能
 */

import { useState, useEffect, useCallback } from 'react'

export interface BreakpointConfig {
  sm: number   // 小屏幕 (手机)
  md: number   // 中等屏幕 (平板)
  lg: number   // 大屏幕 (桌面)
  xl: number   // 超大屏幕
  '2xl': number // 超超大屏幕
}

export interface ResponsiveState {
  width: number
  height: number
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isLargeDesktop: boolean
  breakpoint: keyof BreakpointConfig
  orientation: 'portrait' | 'landscape'
}

// 默认断点配置 (与 Tailwind CSS 保持一致)
const defaultBreakpoints: BreakpointConfig = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
}

/**
 * 获取当前断点
 */
const getCurrentBreakpoint = (width: number, breakpoints: BreakpointConfig): keyof BreakpointConfig => {
  if (width >= breakpoints['2xl']) return '2xl'
  if (width >= breakpoints.xl) return 'xl'
  if (width >= breakpoints.lg) return 'lg'
  if (width >= breakpoints.md) return 'md'
  if (width >= breakpoints.sm) return 'sm'
  return 'sm'
}

/**
 * 获取设备类型
 */
const getDeviceType = (width: number, breakpoints: BreakpointConfig) => {
  return {
    isMobile: width < breakpoints.md,
    isTablet: width >= breakpoints.md && width < breakpoints.lg,
    isDesktop: width >= breakpoints.lg && width < breakpoints.xl,
    isLargeDesktop: width >= breakpoints.xl
  }
}

/**
 * 响应式设计 Hook
 */
export const useResponsive = (customBreakpoints?: Partial<BreakpointConfig>) => {
  const breakpoints = { ...defaultBreakpoints, ...customBreakpoints }
  
  const [state, setState] = useState<ResponsiveState>(() => {
    const width = typeof window !== 'undefined' ? window.innerWidth : 1024
    const height = typeof window !== 'undefined' ? window.innerHeight : 768
    const deviceType = getDeviceType(width, breakpoints)
    
    return {
      width,
      height,
      ...deviceType,
      breakpoint: getCurrentBreakpoint(width, breakpoints),
      orientation: width > height ? 'landscape' : 'portrait'
    }
  })

  // 更新状态
  const updateState = useCallback(() => {
    const width = window.innerWidth
    const height = window.innerHeight
    const deviceType = getDeviceType(width, breakpoints)
    
    setState({
      width,
      height,
      ...deviceType,
      breakpoint: getCurrentBreakpoint(width, breakpoints),
      orientation: width > height ? 'landscape' : 'portrait'
    })
  }, [breakpoints])

  // 监听窗口大小变化
  useEffect(() => {
    if (typeof window === 'undefined') return

    // 使用防抖来优化性能
    let timeoutId: NodeJS.Timeout
    const debouncedUpdate = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(updateState, 100)
    }

    window.addEventListener('resize', debouncedUpdate)
    window.addEventListener('orientationchange', debouncedUpdate)

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', debouncedUpdate)
      window.removeEventListener('orientationchange', debouncedUpdate)
    }
  }, [updateState])

  // 检查是否匹配特定断点
  const matches = useCallback((breakpoint: keyof BreakpointConfig) => {
    return state.width >= breakpoints[breakpoint]
  }, [state.width, breakpoints])

  // 检查是否在断点范围内
  const between = useCallback((min: keyof BreakpointConfig, max: keyof BreakpointConfig) => {
    return state.width >= breakpoints[min] && state.width < breakpoints[max]
  }, [state.width, breakpoints])

  // 获取响应式值
  const getResponsiveValue = useCallback(<T>(values: {
    sm?: T
    md?: T
    lg?: T
    xl?: T
    '2xl'?: T
  }, defaultValue: T): T => {
    const { breakpoint } = state
    
    // 按优先级返回值
    if (breakpoint === '2xl' && values['2xl'] !== undefined) return values['2xl']
    if ((breakpoint === 'xl' || breakpoint === '2xl') && values.xl !== undefined) return values.xl
    if ((breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl') && values.lg !== undefined) return values.lg
    if ((breakpoint === 'md' || breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl') && values.md !== undefined) return values.md
    if (values.sm !== undefined) return values.sm
    
    return defaultValue
  }, [state])

  return {
    ...state,
    breakpoints,
    matches,
    between,
    getResponsiveValue
  }
}

/**
 * 简化的移动端检测 Hook
 */
export const useIsMobile = () => {
  const { isMobile } = useResponsive()
  return isMobile
}

/**
 * 断点匹配 Hook
 */
export const useBreakpoint = (breakpoint: keyof BreakpointConfig) => {
  const { matches } = useResponsive()
  return matches(breakpoint)
}