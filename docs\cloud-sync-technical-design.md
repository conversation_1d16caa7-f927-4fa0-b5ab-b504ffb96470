# 云存储同步技术设计方案

## 架构概览

```
用户书签数据 → 加密处理 → 云存储适配器 → 第三方云服务
                ↓
            本地缓存 ← 同步状态管理 ← 冲突解决机制
```

## 核心组件设计

### 1. 云存储适配器基类
```typescript
interface CloudStorageProvider {
  name: string;
  authenticate(): Promise<boolean>;
  uploadFile(filename: string, data: string): Promise<boolean>;
  downloadFile(filename: string): Promise<string>;
  listFiles(): Promise<string[]>;
  deleteFile(filename: string): Promise<boolean>;
  getQuota(): Promise<{used: number, total: number}>;
}
```

### 2. Google Drive 适配器
```typescript
class GoogleDriveAdapter implements CloudStorageProvider {
  private accessToken: string;
  
  async authenticate() {
    // 使用 Chrome Identity API 进行 OAuth
    return new Promise((resolve) => {
      chrome.identity.getAuthToken({
        interactive: true,
        scopes: ['https://www.googleapis.com/auth/drive.file']
      }, (token) => {
        this.accessToken = token;
        resolve(!!token);
      });
    });
  }
  
  async uploadFile(filename: string, data: string) {
    const metadata = {
      name: filename,
      parents: ['appDataFolder'] // 使用应用专用文件夹
    };
    
    const form = new FormData();
    form.append('metadata', new Blob([JSON.stringify(metadata)], {type: 'application/json'}));
    form.append('file', new Blob([data], {type: 'application/json'}));
    
    const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.accessToken}`
      },
      body: form
    });
    
    return response.ok;
  }
}
```

### 3. OneDrive 适配器
```typescript
class OneDriveAdapter implements CloudStorageProvider {
  private accessToken: string;
  
  async authenticate() {
    // Microsoft Graph API 认证
    const clientId = 'your-app-id';
    const redirectUri = chrome.identity.getRedirectURL();
    const scope = 'https://graph.microsoft.com/Files.ReadWrite';
    
    return new Promise((resolve) => {
      chrome.identity.launchWebAuthFlow({
        url: `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=${clientId}&response_type=token&redirect_uri=${redirectUri}&scope=${scope}`,
        interactive: true
      }, (responseUrl) => {
        const token = new URL(responseUrl).hash.match(/access_token=([^&]+)/)?.[1];
        this.accessToken = token;
        resolve(!!token);
      });
    });
  }
  
  async uploadFile(filename: string, data: string) {
    const response = await fetch(`https://graph.microsoft.com/v1.0/me/drive/special/approot:/${filename}:/content`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json'
      },
      body: data
    });
    
    return response.ok;
  }
}
```

### 4. 数据加密和同步管理
```typescript
class SyncManager {
  private providers: Map<string, CloudStorageProvider> = new Map();
  private encryptionKey: string;
  
  constructor() {
    this.initializeProviders();
    this.generateEncryptionKey();
  }
  
  private initializeProviders() {
    this.providers.set('googledrive', new GoogleDriveAdapter());
    this.providers.set('onedrive', new OneDriveAdapter());
    this.providers.set('dropbox', new DropboxAdapter());
    this.providers.set('webdav', new WebDAVAdapter());
  }
  
  async syncBookmarks(providerName: string) {
    const provider = this.providers.get(providerName);
    if (!provider) throw new Error(`Provider ${providerName} not found`);
    
    // 1. 获取本地书签数据
    const localBookmarks = await this.getLocalBookmarks();
    
    // 2. 加密数据
    const encryptedData = await this.encryptData(JSON.stringify(localBookmarks));
    
    // 3. 上传到云端
    const success = await provider.uploadFile('bookmarks.encrypted', encryptedData);
    
    if (success) {
      await this.updateSyncStatus(providerName, new Date());
    }
    
    return success;
  }
  
  async downloadBookmarks(providerName: string) {
    const provider = this.providers.get(providerName);
    if (!provider) throw new Error(`Provider ${providerName} not found`);
    
    try {
      // 1. 从云端下载
      const encryptedData = await provider.downloadFile('bookmarks.encrypted');
      
      // 2. 解密数据
      const decryptedData = await this.decryptData(encryptedData);
      const cloudBookmarks = JSON.parse(decryptedData);
      
      // 3. 合并冲突
      const mergedBookmarks = await this.mergeBookmarks(cloudBookmarks);
      
      // 4. 更新本地数据
      await this.updateLocalBookmarks(mergedBookmarks);
      
      return true;
    } catch (error) {
      console.error('Download failed:', error);
      return false;
    }
  }
  
  private async encryptData(data: string): Promise<string> {
    // 使用 Web Crypto API 进行 AES 加密
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(this.encryptionKey),
      { name: 'AES-GCM' },
      false,
      ['encrypt']
    );
    
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      dataBuffer
    );
    
    // 将 IV 和加密数据组合
    const result = new Uint8Array(iv.length + encrypted.byteLength);
    result.set(iv);
    result.set(new Uint8Array(encrypted), iv.length);
    
    return btoa(String.fromCharCode(...result));
  }
  
  private async mergeBookmarks(cloudBookmarks: any[]): Promise<any[]> {
    const localBookmarks = await this.getLocalBookmarks();
    
    // 简单的合并策略：以最新修改时间为准
    const merged = new Map();
    
    // 添加本地书签
    localBookmarks.forEach(bookmark => {
      merged.set(bookmark.id, bookmark);
    });
    
    // 合并云端书签
    cloudBookmarks.forEach(bookmark => {
      const existing = merged.get(bookmark.id);
      if (!existing || bookmark.lastModified > existing.lastModified) {
        merged.set(bookmark.id, bookmark);
      }
    });
    
    return Array.from(merged.values());
  }
}
```

## 用户界面设计

### 同步设置页面
```typescript
const SyncSettingsComponent = () => {
  const [selectedProvider, setSelectedProvider] = useState('');
  const [syncStatus, setSyncStatus] = useState({});
  
  const handleConnect = async (provider: string) => {
    const syncManager = new SyncManager();
    const success = await syncManager.authenticateProvider(provider);
    
    if (success) {
      setSelectedProvider(provider);
      // 显示成功消息
    }
  };
  
  return (
    <div className="sync-settings">
      <h3>选择云存储服务</h3>
      
      <div className="provider-list">
        <ProviderCard 
          name="Google Drive" 
          icon="google-drive"
          description="15GB 免费空间，全球访问稳定"
          onConnect={() => handleConnect('googledrive')}
        />
        
        <ProviderCard 
          name="OneDrive" 
          icon="onedrive"
          description="5GB 免费空间，Windows 集成"
          onConnect={() => handleConnect('onedrive')}
        />
        
        <ProviderCard 
          name="Dropbox" 
          icon="dropbox"
          description="2GB 免费空间，同步稳定"
          onConnect={() => handleConnect('dropbox')}
        />
        
        <ProviderCard 
          name="坚果云" 
          icon="jianguoyun"
          description="国内访问稳定，支持 WebDAV"
          onConnect={() => handleConnect('webdav')}
        />
      </div>
      
      <SyncStatusPanel status={syncStatus} />
    </div>
  );
};
```

## 安全考虑

### 1. 数据加密
- 使用 AES-256-GCM 加密
- 每个用户生成唯一的加密密钥
- 密钥存储在浏览器本地，不上传云端

### 2. 权限控制
- 只请求必要的云存储权限
- 使用应用专用文件夹（如 Google Drive 的 appDataFolder）
- 支持权限撤销和重新授权

### 3. 数据完整性
- 上传前计算数据哈希
- 下载后验证数据完整性
- 支持数据备份和恢复

## 实施计划

### 阶段一：基础同步功能
1. 实现 Google Drive 适配器
2. 基础加密和同步逻辑
3. 简单的用户界面

### 阶段二：多平台支持
1. 添加 OneDrive 支持
2. 添加 Dropbox 支持
3. 冲突解决机制

### 阶段三：高级功能
1. WebDAV 支持（坚果云等）
2. 自动同步和增量同步
3. 同步历史和版本管理

这个方案的优势是完全依赖用户自己的云存储，我们不需要维护任何服务器，同时用户的数据完全在自己控制之下。