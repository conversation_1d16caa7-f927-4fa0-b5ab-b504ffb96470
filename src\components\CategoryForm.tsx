// 分类表单组件 - 支持分类的创建和编辑

import React, { useState, useEffect } from 'react'
import { AlertCircle, Folder, Palette, FileText } from 'lucide-react'
import { categoryService } from '../services/categoryService'
import type { Category, CategoryInput, CategoryUpdate } from '../types'

interface CategoryFormProps {
  /** 要编辑的分类（创建时为undefined） */
  category?: Category
  /** 表单提交回调 */
  onSubmit: (data: CategoryInput | CategoryUpdate) => Promise<void>
  /** 取消回调 */
  onCancel: () => void
  /** 是否正在提交 */
  loading?: boolean
  /** 表单模式 */
  mode: 'create' | 'edit'
}

interface FormData {
  name: string
  description: string
  color: string
  parentId?: string
}

interface FormErrors {
  name?: string
  description?: string
  color?: string
  parentId?: string
}

// 预定义的颜色选项
const COLOR_OPTIONS = [
  { value: '#3B82F6', name: '蓝色', class: 'bg-blue-500' },
  { value: '#10B981', name: '绿色', class: 'bg-green-500' },
  { value: '#F59E0B', name: '黄色', class: 'bg-yellow-500' },
  { value: '#EF4444', name: '红色', class: 'bg-red-500' },
  { value: '#8B5CF6', name: '紫色', class: 'bg-purple-500' },
  { value: '#06B6D4', name: '青色', class: 'bg-cyan-500' },
  { value: '#F97316', name: '橙色', class: 'bg-orange-500' },
  { value: '#84CC16', name: '青绿色', class: 'bg-lime-500' },
  { value: '#EC4899', name: '粉色', class: 'bg-pink-500' },
  { value: '#6B7280', name: '灰色', class: 'bg-gray-500' }
]

/**
 * 分类表单组件
 * 支持分类的创建和编辑，包含表单验证和颜色选择功能
 */
const CategoryForm: React.FC<CategoryFormProps> = React.memo(({
  category,
  onSubmit,
  onCancel,
  loading = false,
  mode
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    color: COLOR_OPTIONS[0].value,
    parentId: undefined
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [isValidating, setIsValidating] = useState(false)

  // 当分类数据变化时更新表单
  useEffect(() => {
    if (category && mode === 'edit') {
      setFormData({
        name: category.name || '',
        description: category.description || '',
        color: category.color || COLOR_OPTIONS[0].value,
        parentId: category.parentId
      })
      setErrors({})
    } else if (mode === 'create') {
      // 创建模式时重置表单
      setFormData({
        name: '',
        description: '',
        color: COLOR_OPTIONS[0].value,
        parentId: undefined
      })
      setErrors({})
    }
  }, [category, mode])

  // 验证分类名称唯一性
  const validateNameUniqueness = async (name: string): Promise<boolean> => {
    if (!name.trim()) return true // 空名称由其他验证处理
    
    try {
      const excludeId = mode === 'edit' && category ? category.id : undefined
      const isUnique = await categoryService.validateCategoryName(name.trim(), excludeId)
      return isUnique
    } catch (error) {
      console.error('验证分类名称失败:', error)
      return true // 验证失败时允许提交，由服务端处理
    }
  }

  // 验证表单数据
  const validateForm = async (): Promise<boolean> => {
    const newErrors: FormErrors = {}
    setIsValidating(true)

    // 验证名称
    if (!formData.name.trim()) {
      newErrors.name = '分类名称不能为空'
    } else if (formData.name.trim().length < 2) {
      newErrors.name = '分类名称至少需要2个字符'
    } else if (formData.name.trim().length > 50) {
      newErrors.name = '分类名称不能超过50个字符'
    } else {
      // 验证名称唯一性
      const isUnique = await validateNameUniqueness(formData.name)
      if (!isUnique) {
        newErrors.name = '分类名称已存在，请使用其他名称'
      }
    }

    // 验证描述长度
    if (formData.description && formData.description.length > 200) {
      newErrors.description = '分类描述不能超过200个字符'
    }

    // 验证颜色
    if (!formData.color) {
      newErrors.color = '请选择分类颜色'
    } else if (!/^#[0-9A-Fa-f]{6}$/.test(formData.color)) {
      newErrors.color = '请选择有效的颜色值'
    }

    setErrors(newErrors)
    setIsValidating(false)
    return Object.keys(newErrors).length === 0
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const isValid = await validateForm()
    if (!isValid) {
      return
    }

    try {
      const submitData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        color: formData.color,
        parentId: formData.parentId || undefined
      }

      await onSubmit(submitData)
    } catch (error) {
      console.error('提交分类表单失败:', error)
      // 错误处理由父组件负责
    }
  }

  // 处理输入变化
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }))
    }
  }

  // 处理颜色选择
  const handleColorSelect = (color: string) => {
    handleInputChange('color', color)
  }

  // 获取颜色选项的显示名称
  const getColorName = (colorValue: string): string => {
    const colorOption = COLOR_OPTIONS.find(option => option.value === colorValue)
    return colorOption ? colorOption.name : '自定义颜色'
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 分类名称输入 */}
      <div>
        <label htmlFor="category-name" className="block text-sm font-medium text-gray-700 mb-2">
          <Folder className="w-4 h-4 inline mr-1" />
          分类名称 *
        </label>
        <input
          id="category-name"
          type="text"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors ${
            errors.name ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="请输入分类名称"
          disabled={loading || isValidating}
          maxLength={50}
        />
        {errors.name && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
            {errors.name}
          </p>
        )}
        <p className="mt-1 text-xs text-gray-500">
          {formData.name.length}/50 字符
        </p>
      </div>

      {/* 分类描述输入 */}
      <div>
        <label htmlFor="category-description" className="block text-sm font-medium text-gray-700 mb-2">
          <FileText className="w-4 h-4 inline mr-1" />
          分类描述
        </label>
        <textarea
          id="category-description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          rows={3}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none transition-colors ${
            errors.description ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="请输入分类描述（可选）"
          disabled={loading || isValidating}
          maxLength={200}
        />
        {errors.description && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
            {errors.description}
          </p>
        )}
        <p className="mt-1 text-xs text-gray-500">
          {formData.description.length}/200 字符
        </p>
      </div>

      {/* 颜色选择器 */}
      <div>
        <label htmlFor="category-color" className="block text-sm font-medium text-gray-700 mb-2">
          <Palette className="w-4 h-4 inline mr-1" />
          分类颜色 *
        </label>
        
        {/* 预定义颜色选项 */}
        <div className="grid grid-cols-5 gap-3 mb-3">
          {COLOR_OPTIONS.map((colorOption) => (
            <button
              key={colorOption.value}
              type="button"
              onClick={() => handleColorSelect(colorOption.value)}
              className={`relative w-full h-12 rounded-lg border-2 transition-all duration-200 ${
                formData.color === colorOption.value
                  ? 'border-gray-900 ring-2 ring-primary-500 ring-opacity-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              style={{ backgroundColor: colorOption.value }}
              disabled={loading || isValidating}
              title={colorOption.name}
            >
              {formData.color === colorOption.value && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-foreground rounded-full" />
                  </div>
                </div>
              )}
            </button>
          ))}
        </div>

        {/* 自定义颜色输入 */}
        <div className="flex items-center space-x-3">
          <input
            type="color"
            value={formData.color}
            onChange={(e) => handleColorSelect(e.target.value)}
            className="w-12 h-12 border border-gray-300 rounded-lg cursor-pointer disabled:cursor-not-allowed"
            disabled={loading || isValidating}
            title="选择自定义颜色"
          />
          <div className="flex-1">
            <input
              id="category-color"
              type="text"
              value={formData.color}
              onChange={(e) => handleInputChange('color', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-mono text-sm ${
                errors.color ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="#3B82F6"
              disabled={loading || isValidating}
              pattern="^#[0-9A-Fa-f]{6}$"
            />
          </div>
          <div className="text-sm text-gray-600 min-w-0">
            {getColorName(formData.color)}
          </div>
        </div>
        
        {errors.color && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
            {errors.color}
          </p>
        )}
      </div>

      {/* 表单按钮 */}
      <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          disabled={loading || isValidating}
        >
          取消
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          disabled={loading || isValidating}
        >
          {loading || isValidating ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {isValidating ? '验证中...' : '保存中...'}
            </div>
          ) : (
            mode === 'create' ? '创建分类' : '保存修改'
          )}
        </button>
      </div>
    </form>
  )
})

// 设置显示名称便于调试
CategoryForm.displayName = 'CategoryForm'

export default CategoryForm

// 导出类型定义
export type { CategoryFormProps }