// AI推荐服务测试

import { describe, it, expect, beforeEach, vi, Mock } from 'vitest'
import { aiRecommendationService } from '../src/services/aiRecommendationService'
import { aiChatService } from '../src/services/aiChatService'
import { tagService } from '../src/services/tagService'
import { categoryService } from '../src/services/categoryService'

// Mock依赖服务
vi.mock('../src/services/aiChatService')
vi.mock('../src/services/tagService')
vi.mock('../src/services/categoryService')

const mockAiChatService = aiChatService as {
  generateText: Mock
}

const mockTagService = tagService as {
  getTags: Mock
}

const mockCategoryService = categoryService as {
  getCategories: Mock
  getAllCategoriesWithStats: Mock
}

describe('AIRecommendationService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('recommendTags', () => {
    it('应该成功推荐标签（包含现有标签和新标签）', async () => {
      // 模拟现有标签
      mockTagService.getTags.mockResolvedValue([
        { id: '1', name: '技术', color: '#blue' },
        { id: '2', name: '学习', color: '#green' },
        { id: '3', name: '工具', color: '#orange' }
      ])

      // 模拟AI响应
      mockAiChatService.generateText.mockResolvedValue({
        content: `现有标签: 技术, 学习
新标签: JavaScript, 前端
理由: 基于内容分析，这是一个关于JavaScript前端技术的资源`,
        metadata: { model: 'gpt-4' }
      })

      const request = {
        title: 'React开发指南',
        content: '这是一个关于React前端开发的完整指南，包含组件、状态管理等内容',
        url: 'https://react.dev/guide',
        maxRecommendations: 6
      }

      const result = await aiRecommendationService.recommendTags(request)

      expect(result).toEqual({
        existingTags: ['技术', '学习'],
        newTags: ['JavaScript', '前端'],
        confidence: 0.8,
        reasoning: '使用 gpt-4 模型生成推荐'
      })

      expect(mockTagService.getTags).toHaveBeenCalled()
      expect(mockAiChatService.generateText).toHaveBeenCalledWith({
        prompt: expect.stringContaining('现有标签列表'),
        generationType: 'tags',
        context: expect.objectContaining({
          title: request.title,
          content: request.content
        }),
        maxLength: 500
      })
    })

    it('应该在AI服务失败时使用降级策略', async () => {
      // 模拟现有标签
      mockTagService.getTags.mockResolvedValue([
        { id: '1', name: '技术', color: '#blue' },
        { id: '2', name: '代码', color: '#green' }
      ])

      // 模拟AI服务失败
      mockAiChatService.generateText.mockRejectedValue(new Error('AI服务不可用'))

      const request = {
        title: 'JavaScript教程',
        content: '学习JavaScript编程语言的基础教程',
        url: 'https://javascript.info'
      }

      const result = await aiRecommendationService.recommendTags(request)

      expect(result.confidence).toBeLessThan(0.8) // 降级策略的置信度较低
      expect(result.reasoning).toContain('本地规则')
      expect(Array.isArray(result.existingTags)).toBe(true)
      expect(Array.isArray(result.newTags)).toBe(true)
    })

    it('应该正确限制推荐数量', async () => {
      mockTagService.getTags.mockResolvedValue([
        { id: '1', name: '技术', color: '#blue' },
        { id: '2', name: '学习', color: '#green' },
        { id: '3', name: '工具', color: '#orange' },
        { id: '4', name: '前端', color: '#red' }
      ])

      mockAiChatService.generateText.mockResolvedValue({
        content: `现有标签: 技术, 学习, 工具, 前端
新标签: JavaScript, React, Vue, Angular, TypeScript
理由: 前端技术相关`,
        metadata: { model: 'gpt-4' }
      })

      const request = {
        title: '前端开发资源',
        content: '前端开发相关的技术资源',
        maxRecommendations: 4
      }

      const result = await aiRecommendationService.recommendTags(request)

      const totalRecommendations = result.existingTags.length + result.newTags.length
      expect(totalRecommendations).toBeLessThanOrEqual(4)
    })
  })

  describe('recommendFolders', () => {
    it('应该成功推荐文件夹', async () => {
      // 模拟现有分类
      mockCategoryService.getCategories.mockResolvedValue([
        { id: '1', name: '技术文档', description: '技术相关文档' },
        { id: '2', name: '学习资源', description: '学习相关资源' },
        { id: '3', name: '工具软件', description: '实用工具' }
      ])

      // 模拟AI响应
      mockAiChatService.generateText.mockResolvedValue({
        content: `推荐1: 技术文档 - 内容包含技术相关信息
推荐2: 学习资源 - 适合学习参考
推荐3: 工具软件 - 包含实用工具信息`,
        metadata: { model: 'gpt-4' }
      })

      const request = {
        title: 'React开发工具',
        content: '介绍React开发中常用的工具和技术',
        tags: ['技术', 'React'],
        maxRecommendations: 3
      }

      const result = await aiRecommendationService.recommendFolders(request)

      expect(result.recommendedFolders).toHaveLength(3)
      expect(result.recommendedFolders[0]).toEqual({
        name: '技术文档',
        confidence: 0.9,
        reason: '内容包含技术相关信息'
      })
      expect(result.reasoning).toContain('gpt-4')
    })

    it('应该在没有现有分类时返回默认分类', async () => {
      mockCategoryService.getCategories.mockResolvedValue([])

      const request = {
        title: '测试内容',
        content: '测试内容描述'
      }

      const result = await aiRecommendationService.recommendFolders(request)

      expect(result.recommendedFolders).toHaveLength(1)
      expect(result.recommendedFolders[0].name).toBe('默认分类')
      expect(result.reasoning).toContain('暂无自定义分类')
    })

    it('应该在AI服务失败时使用降级策略', async () => {
      mockCategoryService.getCategories.mockResolvedValue([
        { id: '1', name: '技术', description: '技术分类' },
        { id: '2', name: '学习', description: '学习分类' }
      ])

      mockCategoryService.getAllCategoriesWithStats.mockResolvedValue([
        { id: '1', name: '技术', bookmarkCount: 10 },
        { id: '2', name: '学习', bookmarkCount: 5 }
      ])

      mockAiChatService.generateText.mockRejectedValue(new Error('AI服务不可用'))

      const request = {
        title: '技术文档',
        content: '关于技术的文档'
      }

      const result = await aiRecommendationService.recommendFolders(request)

      expect(result.recommendedFolders.length).toBeGreaterThan(0)
      expect(result.reasoning).toContain('关键词')
    })
  })

  describe('recommendBoth', () => {
    it('应该同时推荐标签和文件夹', async () => {
      // 模拟标签服务
      mockTagService.getTags.mockResolvedValue([
        { id: '1', name: '技术', color: '#blue' }
      ])

      // 模拟分类服务
      mockCategoryService.getCategories.mockResolvedValue([
        { id: '1', name: '技术文档', description: '技术文档' }
      ])

      // 模拟AI响应 - 标签推荐
      mockAiChatService.generateText
        .mockResolvedValueOnce({
          content: `现有标签: 技术
新标签: JavaScript
理由: 技术相关内容`,
          metadata: { model: 'gpt-4' }
        })
        .mockResolvedValueOnce({
          content: `推荐1: 技术文档 - 技术相关内容`,
          metadata: { model: 'gpt-4' }
        })

      const request = {
        title: 'JavaScript指南',
        content: 'JavaScript编程指南',
        maxRecommendations: 5
      }

      const result = await aiRecommendationService.recommendBoth(request)

      expect(result.tags).toBeDefined()
      expect(result.folders).toBeDefined()
      expect(result.tags.existingTags).toContain('技术')
      expect(result.tags.newTags).toContain('JavaScript')
      expect(result.folders.recommendedFolders[0].name).toBe('技术文档')
    })
  })

  describe('解析响应', () => {
    it('应该正确解析标签推荐响应', async () => {
      mockTagService.getTags.mockResolvedValue([
        { id: '1', name: '技术', color: '#blue' },
        { id: '2', name: '学习', color: '#green' }
      ])

      mockAiChatService.generateText.mockResolvedValue({
        content: `现有标签：技术，学习
新标签：JavaScript，前端开发
理由：基于内容分析`,
        metadata: { model: 'gpt-4' }
      })

      const request = {
        title: '前端技术',
        content: '前端开发技术介绍'
      }

      const result = await aiRecommendationService.recommendTags(request)

      expect(result.existingTags).toEqual(['技术', '学习'])
      expect(result.newTags).toEqual(['JavaScript', '前端开发'])
    })

    it('应该正确解析文件夹推荐响应', async () => {
      mockCategoryService.getCategories.mockResolvedValue([
        { id: '1', name: '技术文档', description: '技术文档' },
        { id: '2', name: '学习资料', description: '学习资料' }
      ])

      mockAiChatService.generateText.mockResolvedValue({
        content: `推荐1：技术文档 - 包含技术内容
推荐2：学习资料 - 适合学习参考`,
        metadata: { model: 'gpt-4' }
      })

      const request = {
        title: '技术教程',
        content: '技术学习教程'
      }

      const result = await aiRecommendationService.recommendFolders(request)

      expect(result.recommendedFolders).toHaveLength(2)
      expect(result.recommendedFolders[0].name).toBe('技术文档')
      expect(result.recommendedFolders[0].reason).toBe('包含技术内容')
      expect(result.recommendedFolders[1].name).toBe('学习资料')
    })
  })

  describe('降级策略', () => {
    it('标签降级策略应该基于关键词匹配', async () => {
      mockTagService.getTags.mockResolvedValue([
        { id: '1', name: '技术', color: '#blue' },
        { id: '2', name: 'JavaScript', color: '#yellow' }
      ])

      mockAiChatService.generateText.mockRejectedValue(new Error('AI不可用'))

      const request = {
        title: 'JavaScript开发指南',
        content: '这是一个关于JavaScript编程和开发的指南',
        url: 'https://javascript.info'
      }

      const result = await aiRecommendationService.recommendTags(request)

      // 应该匹配到现有的"JavaScript"标签
      expect(result.existingTags).toContain('JavaScript')
      // 应该基于关键词生成新的技术相关标签
      expect(result.newTags.some(tag => ['技术', '编程', '开发'].includes(tag))).toBe(true)
      expect(result.confidence).toBe(0.6)
    })

    it('文件夹降级策略应该基于关键词和使用频率', async () => {
      mockCategoryService.getCategories.mockResolvedValue([
        { id: '1', name: '技术', description: '技术分类' },
        { id: '2', name: '学习', description: '学习分类' }
      ])

      mockCategoryService.getAllCategoriesWithStats.mockResolvedValue([
        { id: '1', name: '技术', bookmarkCount: 15 },
        { id: '2', name: '学习', bookmarkCount: 8 }
      ])

      mockAiChatService.generateText.mockRejectedValue(new Error('AI不可用'))

      const request = {
        title: '技术文档',
        content: '关于技术开发的文档资料'
      }

      const result = await aiRecommendationService.recommendFolders(request)

      expect(result.recommendedFolders.length).toBeGreaterThan(0)
      // 应该推荐"技术"分类，因为内容匹配
      expect(result.recommendedFolders.some(folder => folder.name === '技术')).toBe(true)
    })
  })
})