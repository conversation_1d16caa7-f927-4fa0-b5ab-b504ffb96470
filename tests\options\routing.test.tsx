/**
 * 路由和状态管理测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import OptionsApp from '../../src/options/OptionsApp'

// 模拟 localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

// 模拟 Chrome 扩展 API
const mockChrome = {
  runtime: {
    sendMessage: jest.fn().mockResolvedValue({ success: true, data: [] }),
    getManifest: jest.fn(() => ({
      name: 'Test Extension',
      version: '1.0.0',
      description: 'Test description',
      manifest_version: 3
    }))
  }
}

// 模拟组件
jest.mock('../../src/components/ImportExportTab', () => {
  return function MockImportExportTab() {
    return <div data-testid="import-export-tab">导入导出页面</div>
  }
})

jest.mock('../../src/components/CategoryManagementTab', () => {
  return function MockCategoryManagementTab() {
    return <div data-testid="category-management-tab">分类管理页面</div>
  }
})

jest.mock('../../src/components/TagsTab', () => {
  return function MockTagsTab() {
    return <div data-testid="tags-tab">标签管理页面</div>
  }
})

jest.mock('../../src/options/components/AboutTab', () => {
  return function MockAboutTab() {
    return <div data-testid="about-tab">关于我们页面</div>
  }
})

jest.mock('../../src/options/components/HelpCenterTab', () => {
  return function MockHelpCenterTab() {
    return <div data-testid="help-center-tab">帮助中心页面</div>
  }
})

// 模拟其他依赖
jest.mock('../../src/hooks/useViewMode', () => ({
  useViewMode: () => ({
    viewMode: 'card',
    setViewMode: jest.fn(),
    isLoading: false
  })
}))

jest.mock('../../src/utils/layoutStability', () => ({
  useViewSwitchStability: () => ({
    containerRef: { current: null },
    displayView: 'card',
    isTransitioning: false
  }),
  useScrollPositionLock: () => ({
    lockScrollPosition: jest.fn()
  })
}))

jest.mock('../../src/hooks/useAdvancedSearch', () => ({
  useAdvancedSearch: () => ({
    query: '',
    setQuery: jest.fn(),
    results: [],
    suggestions: [],
    isSearching: false,
    hasResults: false,
    totalResults: 0,
    searchTime: 0,
    addFilter: jest.fn(),
    clearFilters: jest.fn()
  })
}))

describe('路由和状态管理', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(global as any).chrome = mockChrome
    mockLocalStorage.getItem.mockReturnValue(null)
    
    // 重置 location
    Object.defineProperty(window, 'location', {
      value: {
        hash: '',
        pathname: '/options.html'
      },
      writable: true
    })
    
    // 模拟 history API
    Object.defineProperty(window, 'history', {
      value: {
        replaceState: jest.fn(),
        pushState: jest.fn()
      },
      writable: true
    })
  })

  afterEach(() => {
    delete (global as any).chrome
  })

  describe('URL Hash 路由', () => {
    it('应该根据URL hash设置初始标签页', async () => {
      window.location.hash = '#about'
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('about-tab')).toBeInTheDocument()
      })
      
      expect(screen.getByText('关于我们')).toHaveAttribute('aria-selected', 'true')
    })

    it('应该支持帮助中心的URL hash', async () => {
      window.location.hash = '#help'
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('help-center-tab')).toBeInTheDocument()
      })
      
      expect(screen.getByText('帮助中心')).toHaveAttribute('aria-selected', 'true')
    })

    it('应该在标签页切换时更新URL hash', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 切换到关于我们页面
      await user.click(screen.getByText('关于我们'))
      
      expect(window.history.replaceState).toHaveBeenCalledWith(null, '', '#about')
    })

    it('应该处理无效的URL hash', async () => {
      window.location.hash = '#invalid-tab'
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        // 应该显示默认的收藏管理页面
        expect(screen.getByText('收藏管理')).toHaveAttribute('aria-selected', 'true')
      })
    })
  })

  describe('本地存储状态管理', () => {
    it('应该保存标签页状态到本地存储', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 切换到关于我们页面
      await user.click(screen.getByText('关于我们'))
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('options-active-tab', 'about')
    })

    it('应该从本地存储恢复标签页状态', async () => {
      mockLocalStorage.getItem.mockReturnValue('help')
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('help-center-tab')).toBeInTheDocument()
      })
      
      expect(screen.getByText('帮助中心')).toHaveAttribute('aria-selected', 'true')
      expect(window.history.replaceState).toHaveBeenCalledWith(null, '', '#help')
    })

    it('应该优先使用URL hash而不是本地存储', async () => {
      mockLocalStorage.getItem.mockReturnValue('help')
      window.location.hash = '#about'
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('about-tab')).toBeInTheDocument()
      })
      
      expect(screen.getByText('关于我们')).toHaveAttribute('aria-selected', 'true')
    })

    it('应该处理本地存储错误', async () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })
      
      // 应该不会抛出错误
      expect(() => render(<OptionsApp />)).not.toThrow()
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
    })
  })

  describe('浏览器前进后退', () => {
    it('应该监听popstate事件', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 模拟浏览器后退
      window.location.hash = '#about'
      fireEvent(window, new PopStateEvent('popstate'))
      
      await waitFor(() => {
        expect(screen.getByTestId('about-tab')).toBeInTheDocument()
      })
    })

    it('应该忽略无效的popstate hash', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      const initialTab = screen.getByText('收藏管理')
      expect(initialTab).toHaveAttribute('aria-selected', 'true')
      
      // 模拟无效的hash
      window.location.hash = '#invalid'
      fireEvent(window, new PopStateEvent('popstate'))
      
      // 应该保持当前标签页
      expect(initialTab).toHaveAttribute('aria-selected', 'true')
    })
  })

  describe('键盘导航', () => {
    it('应该支持Alt+数字键快速切换', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // Alt+6 切换到关于我们（第6个标签页）
      fireEvent.keyDown(document, { key: '6', altKey: true })
      
      await waitFor(() => {
        expect(screen.getByTestId('about-tab')).toBeInTheDocument()
      })
    })

    it('应该支持Ctrl+K聚焦搜索框', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 创建一个模拟的搜索框
      const searchInput = document.createElement('input')
      searchInput.placeholder = '搜索收藏...'
      searchInput.focus = jest.fn()
      document.body.appendChild(searchInput)
      
      // Ctrl+K
      fireEvent.keyDown(document, { key: 'k', ctrlKey: true })
      
      expect(searchInput.focus).toHaveBeenCalled()
      
      document.body.removeChild(searchInput)
    })

    it('应该支持方向键导航', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 聚焦到第一个导航按钮
      const firstButton = screen.getByText('收藏管理')
      firstButton.focus()
      
      // 按下向下箭头键
      await user.keyboard('{ArrowDown}')
      
      // 应该聚焦到下一个按钮
      expect(screen.getByText('分类管理')).toHaveFocus()
    })

    it('应该支持Enter和Space键激活按钮', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 聚焦到关于我们按钮
      const aboutButton = screen.getByText('关于我们')
      aboutButton.focus()
      
      // 按Enter键
      await user.keyboard('{Enter}')
      
      await waitFor(() => {
        expect(screen.getByTestId('about-tab')).toBeInTheDocument()
      })
    })
  })

  describe('无障碍访问', () => {
    it('应该设置正确的ARIA属性', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 检查导航区域
      const nav = screen.getByRole('navigation')
      expect(nav).toHaveAttribute('aria-label', '主导航')
      
      // 检查标签列表
      const tablist = screen.getByRole('tablist')
      expect(tablist).toBeInTheDocument()
      
      // 检查标签按钮
      const tabs = screen.getAllByRole('tab')
      expect(tabs).toHaveLength(7)
      
      // 检查主内容区域
      const tabpanel = screen.getByRole('tabpanel')
      expect(tabpanel).toHaveAttribute('aria-labelledby', 'tab-bookmarks')
    })

    it('应该正确设置aria-selected属性', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 初始状态
      expect(screen.getByText('收藏管理')).toHaveAttribute('aria-selected', 'true')
      expect(screen.getByText('关于我们')).toHaveAttribute('aria-selected', 'false')
      
      // 切换标签页
      await user.click(screen.getByText('关于我们'))
      
      expect(screen.getByText('收藏管理')).toHaveAttribute('aria-selected', 'false')
      expect(screen.getByText('关于我们')).toHaveAttribute('aria-selected', 'true')
    })

    it('应该显示键盘快捷键提示', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 检查快捷键提示
      expect(screen.getByText('使用 Alt + 数字键快速切换')).toBeInTheDocument()
      
      // 检查每个按钮的快捷键提示
      const aboutButton = screen.getByText('关于我们')
      expect(aboutButton).toHaveAttribute('title', expect.stringContaining('Alt+6'))
    })
  })
})