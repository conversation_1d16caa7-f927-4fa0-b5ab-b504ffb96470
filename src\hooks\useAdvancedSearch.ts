/**
 * 高级搜索Hook
 * 提供搜索状态管理、防抖处理、结果缓存等功能
 */

import { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { useDebounce } from '../utils/debounce'
import { 
  AdvancedSearch, 
  AdvancedFilter, 
  AdvancedSorter,
  SearchSuggestionGenerator,
  createAdvancedSearch,
  createAdvancedFilter,
  createAdvancedSorter,
  createSuggestionGenerator
} from '../utils/searchUtils'
import type { 
  Bookmark,
  SearchConfig, 
  SearchResult, 
  FilterCondition, 
  SortConfig 
} from '../utils/searchUtils'

// 搜索状态接口
export interface SearchState {
  query: string
  results: SearchResult[]
  filteredResults: SearchResult[]
  sortedResults: SearchResult[]
  suggestions: string[]
  isSearching: boolean
  hasResults: boolean
  totalResults: number
  searchTime: number
}

// 搜索选项接口
export interface UseAdvancedSearchOptions {
  /** 搜索配置 */
  searchConfig?: SearchConfig
  /** 防抖延迟 */
  debounceDelay?: number
  /** 是否启用搜索建议 */
  enableSuggestions?: boolean
  /** 最大建议数量 */
  maxSuggestions?: number
  /** 是否启用结果缓存 */
  enableCache?: boolean
  /** 缓存大小限制 */
  cacheSize?: number
}

// 默认选项
const DEFAULT_OPTIONS: Required<UseAdvancedSearchOptions> = {
  searchConfig: {},
  debounceDelay: 300,
  enableSuggestions: true,
  maxSuggestions: 10,
  enableCache: true,
  cacheSize: 50
}

/**
 * 高级搜索Hook
 * @param items 要搜索的项目列表
 * @param options 搜索选项
 * @returns 搜索状态和操作函数
 */
export function useAdvancedSearch(
  items: Bookmark[],
  options: UseAdvancedSearchOptions = {}
) {
  const opts = { ...DEFAULT_OPTIONS, ...options }
  
  // 基础状态
  const [query, setQuery] = useState('')
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])
  const [sortConfigs, setSortConfigs] = useState<SortConfig[]>([])
  const [isSearching, setIsSearching] = useState(false)
  
  // 防抖查询
  const debouncedQuery = useDebounce(query, opts.debounceDelay)
  
  // 搜索工具实例
  const searchRef = useRef<AdvancedSearch>()
  const filterRef = useRef<AdvancedFilter>()
  const sorterRef = useRef<AdvancedSorter>()
  const suggestionGeneratorRef = useRef<SearchSuggestionGenerator>()
  
  // 结果缓存
  const cacheRef = useRef<Map<string, SearchResult[]>>(new Map())
  
  // 性能监控
  const [searchTime, setSearchTime] = useState(0)
  
  // 初始化搜索工具
  useEffect(() => {
    searchRef.current = createAdvancedSearch(opts.searchConfig)
    filterRef.current = createAdvancedFilter()
    sorterRef.current = createAdvancedSorter()
    suggestionGeneratorRef.current = createSuggestionGenerator(items)
  }, [items, opts.searchConfig])
  
  // 更新建议生成器的项目列表
  useEffect(() => {
    if (suggestionGeneratorRef.current) {
      suggestionGeneratorRef.current.updateItems(items)
    }
  }, [items])
  
  // 执行搜索
  const performSearch = useCallback((searchQuery: string): SearchResult[] => {
    if (!searchRef.current) return []
    
    const startTime = performance.now()
    setIsSearching(true)
    
    try {
      // 检查缓存
      const cacheKey = `${searchQuery}|${JSON.stringify(filterConditions)}|${JSON.stringify(sortConfigs)}`
      
      if (opts.enableCache && cacheRef.current.has(cacheKey)) {
        const cachedResults = cacheRef.current.get(cacheKey)!
        setSearchTime(performance.now() - startTime)
        return cachedResults
      }
      
      // 执行搜索
      let results = searchRef.current.search(items, searchQuery)
      
      // 应用筛选
      if (filterConditions.length > 0 && filterRef.current) {
        const filteredItems = filterRef.current.filter(
          results.map(r => r.item), 
          filterConditions
        )
        results = results.filter(r => filteredItems.includes(r.item))
      }
      
      // 应用排序
      if (sortConfigs.length > 0 && sorterRef.current) {
        const sortedItems = sorterRef.current.sort(
          results.map(r => r.item), 
          sortConfigs
        )
        results = results.sort((a, b) => {
          const indexA = sortedItems.indexOf(a.item)
          const indexB = sortedItems.indexOf(b.item)
          return indexA - indexB
        })
      }
      
      // 缓存结果
      if (opts.enableCache) {
        if (cacheRef.current.size >= opts.cacheSize) {
          // 清理最旧的缓存项
          const firstKey = cacheRef.current.keys().next().value
          cacheRef.current.delete(firstKey)
        }
        cacheRef.current.set(cacheKey, results)
      }
      
      setSearchTime(performance.now() - startTime)
      return results
      
    } finally {
      setIsSearching(false)
    }
  }, [items, filterConditions, sortConfigs, opts.enableCache, opts.cacheSize])
  
  // 搜索结果
  const searchResults = useMemo(() => {
    if (!debouncedQuery.trim()) {
      // 无搜索查询时，返回所有项目
      let allResults: SearchResult[] = items.map(item => ({
        item,
        score: 0,
        matches: []
      }))
      
      // 应用筛选
      if (filterConditions.length > 0 && filterRef.current) {
        const filteredItems = filterRef.current.filter(items, filterConditions)
        allResults = allResults.filter(r => filteredItems.includes(r.item))
      }
      
      // 应用排序
      if (sortConfigs.length > 0 && sorterRef.current) {
        const sortedItems = sorterRef.current.sort(
          allResults.map(r => r.item), 
          sortConfigs
        )
        allResults = allResults.sort((a, b) => {
          const indexA = sortedItems.indexOf(a.item)
          const indexB = sortedItems.indexOf(b.item)
          return indexA - indexB
        })
      }
      
      return allResults
    }
    
    return performSearch(debouncedQuery)
  }, [debouncedQuery, performSearch, items, filterConditions, sortConfigs])
  
  // 搜索建议
  const suggestions = useMemo(() => {
    if (!opts.enableSuggestions || !query.trim() || !suggestionGeneratorRef.current) {
      return []
    }
    
    return suggestionGeneratorRef.current.generateSuggestions(query, opts.maxSuggestions)
  }, [query, opts.enableSuggestions, opts.maxSuggestions])
  
  // 搜索状态
  const searchState: SearchState = useMemo(() => ({
    query,
    results: searchResults,
    filteredResults: searchResults, // 已经在searchResults中处理了筛选
    sortedResults: searchResults, // 已经在searchResults中处理了排序
    suggestions,
    isSearching,
    hasResults: searchResults.length > 0,
    totalResults: searchResults.length,
    searchTime
  }), [query, searchResults, suggestions, isSearching, searchTime])
  
  // 操作函数
  const setSearchQuery = useCallback((newQuery: string) => {
    setQuery(newQuery)
  }, [])
  
  const addFilterCondition = useCallback((condition: FilterCondition) => {
    setFilterConditions(prev => [...prev, condition])
  }, [])
  
  const removeFilterCondition = useCallback((index: number) => {
    setFilterConditions(prev => prev.filter((_, i) => i !== index))
  }, [])
  
  const updateFilterCondition = useCallback((index: number, condition: FilterCondition) => {
    setFilterConditions(prev => prev.map((c, i) => i === index ? condition : c))
  }, [])
  
  const clearFilters = useCallback(() => {
    setFilterConditions([])
  }, [])
  
  const addSortConfig = useCallback((config: SortConfig) => {
    setSortConfigs(prev => [...prev, config])
  }, [])
  
  const removeSortConfig = useCallback((index: number) => {
    setSortConfigs(prev => prev.filter((_, i) => i !== index))
  }, [])
  
  const updateSortConfig = useCallback((index: number, config: SortConfig) => {
    setSortConfigs(prev => prev.map((c, i) => i === index ? config : c))
  }, [])
  
  const clearSorts = useCallback(() => {
    setSortConfigs([])
  }, [])
  
  const clearCache = useCallback(() => {
    cacheRef.current.clear()
  }, [])
  
  const updateSearchConfig = useCallback((config: Partial<SearchConfig>) => {
    if (searchRef.current) {
      searchRef.current.updateConfig(config)
      // 清理缓存，因为搜索配置已更改
      clearCache()
    }
  }, [clearCache])
  
  const reset = useCallback(() => {
    setQuery('')
    setFilterConditions([])
    setSortConfigs([])
    clearCache()
  }, [clearCache])
  
  return {
    // 状态
    ...searchState,
    filterConditions,
    sortConfigs,
    
    // 操作函数
    setQuery: setSearchQuery,
    addFilter: addFilterCondition,
    removeFilter: removeFilterCondition,
    updateFilter: updateFilterCondition,
    clearFilters,
    addSort: addSortConfig,
    removeSort: removeSortConfig,
    updateSort: updateSortConfig,
    clearSorts,
    updateSearchConfig,
    clearCache,
    reset
  }
}

// 便捷Hook：简单搜索
export function useSimpleSearch(
  items: Bookmark[],
  debounceDelay: number = 300
) {
  const [query, setQuery] = useState('')
  const debouncedQuery = useDebounce(query, debounceDelay)
  
  const results = useMemo(() => {
    if (!debouncedQuery.trim()) {
      return items
    }
    
    const normalizedQuery = debouncedQuery.toLowerCase()
    return items.filter(item => {
      return (
        item.title?.toLowerCase().includes(normalizedQuery) ||
        item.description?.toLowerCase().includes(normalizedQuery) ||
        item.content?.toLowerCase().includes(normalizedQuery) ||
        item.url?.toLowerCase().includes(normalizedQuery) ||
        item.category?.toLowerCase().includes(normalizedQuery) ||
        item.tags?.some(tag => tag.toLowerCase().includes(normalizedQuery))
      )
    })
  }, [items, debouncedQuery])
  
  return {
    query,
    setQuery,
    results,
    hasResults: results.length > 0,
    totalResults: results.length
  }
}