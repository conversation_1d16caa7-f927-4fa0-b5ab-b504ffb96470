/**
 * 防抖工具函数测试
 */

import { renderHook, act } from '@testing-library/react'
import { useDebounce, useDebouncedCallback, debounce, throttle } from '../src/utils/debounce'
import { vi } from 'vitest'

// Mock timers
beforeEach(() => {
  vi.useFakeTimers()
})

afterEach(() => {
  vi.runOnlyPendingTimers()
  vi.useRealTimers()
})

describe('useDebounce Hook', () => {
  test('应该返回初始值', () => {
    const { result } = renderHook(() => useDebounce('initial', 500))
    expect(result.current).toBe('initial')
  })

  test('应该在延迟后更新值', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    )

    expect(result.current).toBe('initial')

    // 更新值
    rerender({ value: 'updated', delay: 500 })
    expect(result.current).toBe('initial') // 应该还是初始值

    // 快进时间
    act(() => {
      vi.advanceTimersByTime(500)
    })

    expect(result.current).toBe('updated') // 现在应该是更新后的值
  })
})

describe('debounce 函数', () => {
  test('应该延迟执行函数', () => {
    const mockFn = vi.fn()
    const debouncedFn = debounce(mockFn, 500)

    debouncedFn('test')
    expect(mockFn).not.toHaveBeenCalled()

    vi.advanceTimersByTime(500)
    expect(mockFn).toHaveBeenCalledWith('test')
  })

  test('应该取消之前的执行当快速调用时', () => {
    const mockFn = vi.fn()
    const debouncedFn = debounce(mockFn, 500)

    debouncedFn('call1')
    debouncedFn('call2')
    debouncedFn('call3')

    vi.advanceTimersByTime(500)
    expect(mockFn).toHaveBeenCalledWith('call3')
    expect(mockFn).toHaveBeenCalledTimes(1)
  })
})

describe('搜索场景测试', () => {
  test('应该处理搜索值的防抖更新', () => {
    const { result, rerender } = renderHook(
      ({ query }) => useDebounce(query, 300),
      { initialProps: { query: '' } }
    )

    // 模拟用户输入
    rerender({ query: 'a' })
    rerender({ query: 'ap' })
    rerender({ query: 'app' })

    // 值应该还是初始值
    expect(result.current).toBe('')

    // 等待防抖时间
    act(() => {
      vi.advanceTimersByTime(300)
    })

    // 现在应该是最新的值
    expect(result.current).toBe('app')
  })
})