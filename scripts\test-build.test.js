#!/usr/bin/env node

/**
 * test-build.js 脚本的单元测试
 * 
 * 功能说明:
 * - 验证 test-build.js 脚本中各个验证函数的正确性
 * - 使用自定义轻量级测试框架进行测试
 * - 提供详细的测试报告和错误信息
 * - 确保项目配置验证脚本的可靠性
 * 
 * 测试覆盖:
 * - 必要文件存在性检查
 * - package.json 结构验证
 * - manifest.json 配置验证  
 * - TypeScript 配置验证
 * - 源代码结构验证
 * - 脚本自身结构验证
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// ES模块中获取当前文件路径的标准方法
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 开始测试 test-build.js 脚本...\n')

/**
 * 轻量级测试框架类
 * 
 * 提供基础的测试执行、断言验证和结果报告功能
 * 支持同步和异步测试函数，具有详细的错误报告机制
 */
class TestRunner {
  /**
   * 构造函数 - 初始化测试运行器
   * 
   * 初始化测试用例数组和统计计数器
   */
  constructor() {
    this.tests = []      // 存储所有测试用例的数组
    this.passed = 0      // 通过的测试数量计数器
    this.failed = 0      // 失败的测试数量计数器
  }

  /**
   * 添加测试用例到测试套件
   * 
   * @param {string} name - 测试用例的描述性名称
   * @param {Function} testFn - 测试执行函数，可以是同步或异步函数
   * 
   * 使用示例:
   * runner.test('验证配置文件', () => {
   *   runner.assert(configExists, '配置文件应该存在')
   * })
   */
  test(name, testFn) {
    this.tests.push({ name, testFn })
  }

  /**
   * 断言验证函数
   * 
   * @param {boolean} condition - 要验证的条件，为false时抛出错误
   * @param {string} message - 条件不满足时的错误消息
   * 
   * @throws {Error} 当condition为false时抛出包含message的错误
   * 
   * 使用示例:
   * runner.assert(fs.existsSync('file.txt'), '文件应该存在')
   * runner.assert(obj.property === 'expected', '属性值应该匹配')
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  /**
   * 执行所有注册的测试用例
   * 
   * @returns {Promise<void>} 异步执行所有测试并输出结果
   * 
   * 执行流程:
   * 1. 输出测试用例总数
   * 2. 依次执行每个测试用例
   * 3. 捕获并记录测试异常
   * 4. 输出最终统计结果
   * 5. 根据结果设置进程退出状态
   */
  async run() {
    console.log(`运行 ${this.tests.length} 个测试用例...\n`)

    // 依次执行每个测试用例
    for (const test of this.tests) {
      try {
        // 支持异步测试函数
        await test.testFn()
        console.log(`✅ ${test.name}`)
        this.passed++
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`)
        this.failed++
      }
    }

    // 输出测试结果统计
    console.log('\n' + '='.repeat(50))
    console.log(`测试结果: ${this.passed} 通过, ${this.failed} 失败`)

    // 如果有测试失败，设置进程退出码为1
    if (this.failed > 0) {
      process.exit(1)
    }
  }
}

// 创建测试运行器实例
const runner = new TestRunner()

/**
 * 测试用例1: 检查项目必要文件是否存在
 * 
 * 验证目标: 确保项目的核心配置文件都存在
 * 测试文件: package.json, manifest.json, vite.config.ts, tsconfig.json
 * 
 * 失败场景: 任何一个必要文件缺失时测试失败
 */
runner.test('检查必要文件是否存在', () => {
  // 定义项目必需的核心配置文件列表
  const requiredFiles = [
    'package.json',      // Node.js 项目配置文件
    'manifest.json',     // Chrome 扩展清单文件
    'vite.config.ts',    // Vite 构建工具配置
    'tsconfig.json'      // TypeScript 编译配置
  ]

  // 逐一检查每个必要文件是否存在
  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    runner.assert(
      fs.existsSync(filePath),
      `必要文件 ${file} 不存在`
    )
  })
})

/**
 * 测试用例2: 验证 package.json 文件结构完整性
 * 
 * 验证目标: 确保 package.json 包含所有必要字段和脚本
 * 检查内容:
 * - 必要字段: name, version, scripts, dependencies, devDependencies
 * - 关键脚本: dev, build
 * - JSON 格式正确性
 * 
 * 失败场景: 文件不存在、JSON格式错误、缺少必要字段或脚本
 */
runner.test('验证package.json结构', () => {
  const packagePath = path.join(process.cwd(), 'package.json')
  
  // 首先检查文件是否存在
  runner.assert(fs.existsSync(packagePath), 'package.json文件不存在')

  // 尝试解析 JSON 文件
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))

  // 检查必要的顶级字段
  const requiredFields = ['name', 'version', 'scripts', 'dependencies', 'devDependencies']
  requiredFields.forEach(field => {
    runner.assert(
      packageJson[field],
      `package.json缺少必要字段: ${field}`
    )
  })

  // 检查关键的 npm 脚本是否存在
  runner.assert(
    packageJson.scripts && packageJson.scripts.dev,
    'package.json缺少dev脚本'
  )
  runner.assert(
    packageJson.scripts && packageJson.scripts.build,
    'package.json缺少build脚本'
  )
})

/**
 * 测试用例3: 验证 Chrome 扩展 manifest.json 配置
 * 
 * 验证目标: 确保 manifest.json 符合 Manifest V3 规范
 * 检查内容:
 * - 使用 Manifest V3 版本
 * - 包含所有必要字段
 * - Service Worker 配置正确
 * - Chrome 扩展功能配置完整
 * 
 * 失败场景: 使用过时版本、缺少必要配置、Service Worker 配置错误
 */
runner.test('验证manifest.json结构', () => {
  const manifestPath = path.join(process.cwd(), 'manifest.json')
  
  // 检查清单文件是否存在
  runner.assert(fs.existsSync(manifestPath), 'manifest.json文件不存在')

  // 解析清单文件内容
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))

  // 检查是否使用 Manifest V3（Chrome 扩展的最新版本）
  runner.assert(
    manifest.manifest_version === 3,
    '应使用Manifest V3'
  )

  // 检查 Chrome 扩展必要的配置字段
  const requiredFields = [
    'name',             // 扩展名称
    'version',          // 扩展版本
    'description',      // 扩展描述
    'permissions',      // 权限声明
    'background',       // 后台脚本配置
    'content_scripts',  // 内容脚本配置
    'action'           // 扩展图标和弹窗配置
  ]

  requiredFields.forEach(field => {
    runner.assert(
      manifest[field],
      `manifest.json缺少必要字段: ${field}`
    )
  })

  // 检查 Service Worker 配置（Manifest V3 的后台脚本方式）
  runner.assert(
    manifest.background && manifest.background.service_worker,
    'manifest.json缺少service_worker配置'
  )
})

/**
 * 测试用例4: 验证 TypeScript 编译配置
 * 
 * 验证目标: 确保 TypeScript 配置适合 React 和 Chrome 扩展开发
 * 检查内容:
 * - compilerOptions 存在
 * - 重要编译选项配置正确
 * - 支持 JSX 语法
 * - 模块系统配置合适
 * 
 * 失败场景: 配置文件不存在、缺少关键编译选项、不支持 React 开发
 */
runner.test('验证TypeScript配置', () => {
  const tsconfigPath = path.join(process.cwd(), 'tsconfig.json')
  
  // 检查 TypeScript 配置文件是否存在
  runner.assert(fs.existsSync(tsconfigPath), 'tsconfig.json文件不存在')

  // 解析 TypeScript 配置
  const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'))

  // 检查编译选项配置是否存在
  runner.assert(
    tsconfig.compilerOptions,
    'tsconfig.json缺少compilerOptions'
  )

  // 检查重要的编译选项是否配置
  const importantOptions = ['target', 'module', 'jsx']
  importantOptions.forEach(option => {
    runner.assert(
      tsconfig.compilerOptions[option],
      `tsconfig.json缺少重要选项: ${option}`
    )
  })
})

// 测试源代码结构检查功能
runner.test('验证源代码结构', () => {
  const expectedDirs = [
    'src/background',
    'src/content',
    'src/popup',
    'src/options',
    'src/types',
    'src/styles'
  ]

  expectedDirs.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir)
    runner.assert(
      fs.existsSync(dirPath),
      `源代码目录不存在: ${dir}`
    )
  })

  // 检查关键文件
  const keyFiles = [
    'src/background/index.ts',
    'src/content/index.ts',
    'src/popup/index.tsx',
    'src/options/index.tsx',
    'src/types/index.ts'
  ]

  keyFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    runner.assert(
      fs.existsSync(filePath),
      `关键源文件不存在: ${file}`
    )
  })
})

// 测试脚本文件本身的结构
runner.test('验证test-build.js脚本结构', () => {
  const scriptPath = path.join(__dirname, 'test-build.js')
  runner.assert(fs.existsSync(scriptPath), 'test-build.js脚本不存在')

  const scriptContent = fs.readFileSync(scriptPath, 'utf8')

  // 检查关键函数是否存在
  const requiredFunctions = [
    'checkRequiredFiles',
    'validatePackageJson',
    'validateManifest',
    'validateTypeScript',
    'checkSourceStructure',
    'main'
  ]

  requiredFunctions.forEach(funcName => {
    runner.assert(
      scriptContent.includes(`function ${funcName}`),
      `test-build.js缺少函数: ${funcName}`
    )
  })

  // 检查是否使用ES模块
  runner.assert(
    scriptContent.includes('import fs from'),
    'test-build.js应使用ES模块语法'
  )
})

// 运行所有测试
runner.run().catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})