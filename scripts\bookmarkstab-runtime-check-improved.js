/**
 * BookmarksTab组件运行时检测脚本 - 改进版
 * 在浏览器控制台中运行，验证组件的实际运行状态
 * 
 * 使用方法：
 * 1. 在Chrome中加载扩展
 * 2. 打开扩展选项页面
 * 3. 打开开发者工具控制台
 * 4. 复制并运行此脚本
 */

(function() {
  'use strict';

  // 配置常量
  const CONFIG = {
    timeout: 5000,
    cacheTimeout: 5000,
    selectors: {
      tabPanel: '[role="tabpanel"]',
      searchInput: 'input[placeholder*="搜索"]',
      shadcnCard: '.rounded-lg.border.bg-card',
      shadcnButton: '.inline-flex.items-center.justify-center',
      combobox: '[role="combobox"]',
      buttons: 'button'
    },
    expectedTexts: {
      title: '收藏管理'
    }
  };

  // 安全检查器
  class SecurityChecker {
    static isSecureContext() {
      return window.location.protocol === 'chrome-extension:' || 
             window.location.protocol === 'https:' ||
             window.location.hostname === 'localhost';
    }

    static validateSelector(selector) {
      const dangerousPatterns = [/javascript:/i, /data:/i, /vbscript:/i];
      return !dangerousPatterns.some(pattern => pattern.test(selector));
    }
  }

  // DOM缓存工具
  class DOMCache {
    constructor() {
      this.cache = new Map();
    }

    getCachedElement(selector) {
      const cached = this.cache.get(selector);
      if (cached && Date.now() - cached.timestamp < CONFIG.cacheTimeout) {
        return cached.element;
      }
      
      const element = document.querySelector(selector);
      this.cache.set(selector, {
        element,
        timestamp: Date.now()
      });
      
      return element;
    }

    getCachedElements(selector) {
      const cacheKey = `${selector}_all`;
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < CONFIG.cacheTimeout) {
        return cached.elements;
      }
      
      const elements = Array.from(document.querySelectorAll(selector));
      this.cache.set(cacheKey, {
        elements,
        timestamp: Date.now()
      });
      
      return elements;
    }
  }

  // DOM查询工具
  class DOMChecker {
    constructor() {
      this.cache = new DOMCache();
    }

    safeQuerySelector(selector) {
      if (!SecurityChecker.validateSelector(selector)) {
        throw new Error(`不安全的选择器: ${selector}`);
      }
      
      try {
        return this.cache.getCachedElement(selector);
      } catch (error) {
        console.warn(`查询选择器失败: ${selector}`, error);
        return null;
      }
    }

    safeQuerySelectorAll(selector) {
      if (!SecurityChecker.validateSelector(selector)) {
        throw new Error(`不安全的选择器: ${selector}`);
      }
      
      try {
        return this.cache.getCachedElements(selector);
      } catch (error) {
        console.warn(`查询选择器失败: ${selector}`, error);
        return [];
      }
    }

    findElementsWithText(text, selectors = ['*']) {
      return selectors.flatMap(selector => 
        this.safeQuerySelectorAll(selector).filter(el => 
          el.textContent?.includes(text)
        )
      );
    }
  }

  // 检查策略基类
  class CheckStrategy {
    constructor(name, description) {
      this.name = name;
      this.description = description;
      this.domChecker = new DOMChecker();
    }

    execute() {
      throw new Error('子类必须实现execute方法');
    }
  }

  // 具体检查策略
  class ComponentRenderStrategy extends CheckStrategy {
    constructor() {
      super('BookmarksTab组件渲染', '验证BookmarksTab组件是否已正确渲染');
    }

    execute() {
      return this.domChecker.safeQuerySelector(CONFIG.selectors.tabPanel) !== null;
    }
  }

  class ShadcnStyleStrategy extends CheckStrategy {
    constructor() {
      super('shadcn样式应用', '验证shadcn组件样式是否正确应用');
    }

    execute() {
      const cardElements = this.domChecker.safeQuerySelectorAll(CONFIG.selectors.shadcnCard);
      return cardElements.length > 0;
    }
  }

  class InputComponentStrategy extends CheckStrategy {
    constructor() {
      super('shadcn Input组件', '验证shadcn Input组件是否存在且可用');
    }

    execute() {
      const searchInput = this.domChecker.safeQuerySelector(CONFIG.selectors.searchInput);
      return searchInput !== null && searchInput.classList.contains('flex');
    }
  }

  class SelectComponentStrategy extends CheckStrategy {
    constructor() {
      super('shadcn Select组件', '验证shadcn Select组件是否存在');
    }

    execute() {
      return this.domChecker.safeQuerySelector(CONFIG.selectors.combobox) !== null;
    }
  }

  class ButtonComponentStrategy extends CheckStrategy {
    constructor() {
      super('shadcn Button组件', '验证shadcn Button组件是否存在');
    }

    execute() {
      const buttons = this.domChecker.safeQuerySelectorAll(CONFIG.selectors.buttons);
      return buttons.some(btn => 
        btn.classList.contains('inline-flex') || 
        btn.classList.contains('justify-center')
      );
    }
  }

  class TitleDisplayStrategy extends CheckStrategy {
    constructor() {
      super('标题显示', '验证收藏管理标题是否正确显示');
    }

    execute() {
      const titleSelectors = ['h1', 'h2', 'h3', '[class*="title"]'];
      return this.domChecker.findElementsWithText(CONFIG.expectedTexts.title, titleSelectors).length > 0;
    }
  }

  class InteractionStrategy extends CheckStrategy {
    constructor() {
      super('交互功能', '验证用户交互功能是否正常');
    }

    execute() {
      return this.checkSearchInput() && this.checkButtons();
    }

    checkSearchInput() {
      const searchInput = this.domChecker.safeQuerySelector(CONFIG.selectors.searchInput);
      if (!searchInput) return false;
      
      try {
        searchInput.focus();
        const testValue = 'test_' + Date.now();
        searchInput.value = testValue;
        
        const inputEvent = new Event('input', { bubbles: true });
        searchInput.dispatchEvent(inputEvent);
        
        return searchInput.value === testValue;
      } catch (error) {
        console.warn('搜索框交互测试失败:', error);
        return false;
      }
    }

    checkButtons() {
      const buttons = this.domChecker.safeQuerySelectorAll(CONFIG.selectors.buttons);
      return buttons.some(btn => !btn.disabled && btn.offsetParent !== null);
    }
  }

  class ThemeVariablesStrategy extends CheckStrategy {
    constructor() {
      super('主题变量', '验证主题颜色变量是否正确应用');
    }

    execute() {
      const computedStyle = getComputedStyle(document.documentElement);
      const themeVars = ['--background', '--foreground', '--primary'];
      return themeVars.some(varName => computedStyle.getPropertyValue(varName) !== '');
    }
  }

  class ResponsiveLayoutStrategy extends CheckStrategy {
    constructor() {
      super('响应式布局', '验证响应式布局是否正确应用');
    }

    execute() {
      const layoutElements = this.domChecker.safeQuerySelectorAll('.flex, .grid');
      return layoutElements.length > 0;
    }
  }

  // 结果观察者
  class CheckResultObserver {
    constructor() {
      this.observers = [];
    }

    subscribe(observer) {
      this.observers.push(observer);
    }

    notify(result) {
      this.observers.forEach(observer => observer.update(result));
    }
  }

  // 控制台报告器
  class ConsoleReporter {
    update(result) {
      const status = result.passed ? '✅ 通过' : '❌ 失败';
      const timing = result.executionTime ? ` (${result.executionTime}ms)` : '';
      console.log(`${result.index}/${result.total}: ${result.name} - ${status}${timing}`);
      
      if (result.error) {
        console.warn(`  错误详情: ${result.error}`);
      }
    }
  }

  // 汇总报告器
  class SummaryReporter {
    constructor() {
      this.results = [];
    }

    update(result) {
      this.results.push(result);
    }

    generateSummary() {
      const passed = this.results.filter(r => r.passed).length;
      const total = this.results.length;
      const avgTime = this.results.reduce((sum, r) => sum + (r.executionTime || 0), 0) / total;
      
      console.log('\n' + '='.repeat(50));
      console.log(`📊 BookmarksTab运行时检测完成: ${passed}/${total} 项通过`);
      console.log(`⏱️  平均执行时间: ${Math.round(avgTime)}ms`);
      
      if (passed === total) {
        console.log('🎉 所有运行时检查都通过了！BookmarksTab组件运行正常。');
        this.showManualTestSuggestions();
      } else {
        console.log(`❌ 有 ${total - passed} 项检查失败。`);
        this.showFailedChecks();
      }
    }

    showManualTestSuggestions() {
      console.log('\n📋 建议进行的手动测试:');
      const suggestions = [
        '在搜索框中输入文字，观察搜索功能',
        '点击分类选择器，测试下拉选项',
        '点击"添加收藏"按钮，测试模态窗口',
        '点击"刷新"按钮，测试数据重新加载',
        '切换浏览器窗口大小，测试响应式布局',
        '切换主题模式，测试颜色变化'
      ];
      
      suggestions.forEach((suggestion, index) => {
        console.log(`${index + 1}. ${suggestion}`);
      });
    }

    showFailedChecks() {
      console.log('\n🔧 失败的检查项:');
      this.results.forEach(result => {
        if (!result.passed) {
          console.log(`- ${result.name}${result.error ? ` (${result.error})` : ''}`);
        }
      });
    }
  }

  // 主检查器
  class RuntimeChecker {
    constructor() {
      this.strategies = [];
      this.resultObserver = new CheckResultObserver();
      this.setupReporters();
      this.setupStrategies();
    }

    setupReporters() {
      this.resultObserver.subscribe(new ConsoleReporter());
      this.summaryReporter = new SummaryReporter();
      this.resultObserver.subscribe(this.summaryReporter);
    }

    setupStrategies() {
      const strategies = [
        ComponentRenderStrategy,
        ShadcnStyleStrategy,
        InputComponentStrategy,
        SelectComponentStrategy,
        ButtonComponentStrategy,
        TitleDisplayStrategy,
        InteractionStrategy,
        ThemeVariablesStrategy,
        ResponsiveLayoutStrategy
      ];

      strategies.forEach(StrategyClass => {
        this.strategies.push(new StrategyClass());
      });
    }

    async runChecks() {
      // 安全检查
      if (!SecurityChecker.isSecureContext()) {
        console.error('❌ 安全错误: 脚本只能在安全上下文中运行');
        return;
      }

      console.log('🔍 开始BookmarksTab组件运行时检测...\n');
      
      const results = [];
      const startTime = performance.now();
      
      for (const [index, strategy] of this.strategies.entries()) {
        try {
          const checkStartTime = performance.now();
          const passed = await this.executeWithTimeout(strategy, CONFIG.timeout);
          const checkEndTime = performance.now();
          
          const result = {
            name: strategy.name,
            description: strategy.description,
            passed,
            executionTime: Math.round(checkEndTime - checkStartTime),
            index: index + 1,
            total: this.strategies.length
          };
          
          results.push(result);
          this.resultObserver.notify(result);
          
        } catch (error) {
          const result = {
            name: strategy.name,
            description: strategy.description,
            passed: false,
            error: error.message,
            index: index + 1,
            total: this.strategies.length
          };
          
          results.push(result);
          this.resultObserver.notify(result);
        }
      }
      
      const totalTime = performance.now() - startTime;
      this.summaryReporter.generateSummary();
      this.saveResults(results, totalTime);
      
      return results;
    }

    async executeWithTimeout(strategy, timeout) {
      return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
          reject(new Error(`检查超时 (${timeout}ms)`));
        }, timeout);
        
        try {
          const result = strategy.execute();
          clearTimeout(timer);
          resolve(result);
        } catch (error) {
          clearTimeout(timer);
          reject(error);
        }
      });
    }

    saveResults(results, totalTime) {
      const summary = {
        timestamp: new Date().toISOString(),
        passed: results.filter(r => r.passed).length,
        total: results.length,
        success: results.every(r => r.passed),
        totalExecutionTime: Math.round(totalTime),
        results,
        environment: {
          userAgent: navigator.userAgent,
          url: window.location.href,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          }
        }
      };
      
      window.bookmarksTabCheckResults = summary;
      console.log('\n💡 检测结果已保存到 window.bookmarksTabCheckResults');
      console.log(`📊 总执行时间: ${Math.round(totalTime)}ms`);
    }
  }

  // 执行检查
  const checker = new RuntimeChecker();
  checker.runChecks().catch(error => {
    console.error('❌ 检查执行失败:', error);
  });

})();