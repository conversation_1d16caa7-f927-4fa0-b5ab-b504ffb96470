// 类型定义单元测试

import { describe, it, expect } from 'vitest'
import type {
  BookmarkType,
  BaseEntity,
  Bookmark,
  BookmarkInput,
  Category,
  Tag,
  AIProvider,
  LocalModelType,
  DataType,
  ConflictType,
  ConflictAction,
  ConflictItem,
  ConflictResolution,
  ValidationError,
  ValidationResult
} from '../src/types'

describe('类型定义测试', () => {
  describe('基础类型', () => {
    it('应该正确定义BookmarkType枚举', () => {
      const validTypes: BookmarkType[] = ['url', 'text', 'image']
      expect(validTypes).toHaveLength(3)
    })

    it('应该正确定义AIProvider枚举', () => {
      const validProviders: AIProvider[] = ['openai', 'claude', 'gemini', 'local', 'custom']
      expect(validProviders).toHaveLength(5)
    })

    it('应该正确定义LocalModelType枚举', () => {
      const validModelTypes: LocalModelType[] = ['ollama', 'llamacpp', 'other']
      expect(validModelTypes).toHaveLength(3)
    })
  })

  describe('实体接口', () => {
    it('应该正确定义BaseEntity接口', () => {
      const baseEntity: BaseEntity = {
        id: 'test-id',
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      expect(baseEntity.id).toBe('test-id')
      expect(baseEntity.createdAt).toBeInstanceOf(Date)
      expect(baseEntity.updatedAt).toBeInstanceOf(Date)
    })

    it('应该正确定义Bookmark接口', () => {
      const bookmark: Bookmark = {
        id: 'bookmark-1',
        type: 'url',
        title: '测试收藏',
        url: 'https://example.com',
        tags: ['测试'],
        category: '技术',
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: {
          aiGenerated: false
        }
      }
      
      expect(bookmark.type).toBe('url')
      expect(bookmark.title).toBe('测试收藏')
      expect(bookmark.tags).toContain('测试')
    })

    it('应该正确定义Category接口', () => {
      const category: Category = {
        id: 'category-1',
        name: '技术',
        bookmarkCount: 10,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      expect(category.name).toBe('技术')
      expect(category.bookmarkCount).toBe(10)
    })

    it('应该正确定义Tag接口', () => {
      const tag: Tag = {
        id: 'tag-1',
        name: 'JavaScript',
        usageCount: 5,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      expect(tag.name).toBe('JavaScript')
      expect(tag.usageCount).toBe(5)
    })
  })

  describe('输入类型', () => {
    it('应该正确定义BookmarkInput接口', () => {
      const bookmarkInput: BookmarkInput = {
        type: 'url',
        title: '新收藏',
        url: 'https://example.com',
        tags: ['新标签'],
        category: '新分类'
      }
      
      expect(bookmarkInput.type).toBe('url')
      expect(bookmarkInput.title).toBe('新收藏')
    })
  })

  describe('冲突处理类型', () => {
    it('应该正确定义DataType枚举', () => {
      const validDataTypes: DataType[] = ['bookmark', 'category', 'tag']
      expect(validDataTypes).toHaveLength(3)
    })

    it('应该正确定义ConflictType枚举', () => {
      const validConflictTypes: ConflictType[] = ['duplicate', 'name_conflict', 'data_mismatch']
      expect(validConflictTypes).toHaveLength(3)
    })

    it('应该正确定义ConflictAction枚举', () => {
      const validActions: ConflictAction[] = ['keep_existing', 'use_imported', 'merge', 'manual_edit']
      expect(validActions).toHaveLength(4)
    })

    it('应该正确定义ConflictItem接口', () => {
      const conflictItem: ConflictItem = {
        id: 'conflict-1',
        type: 'bookmark',
        conflictType: 'duplicate',
        existingData: { title: '现有数据' },
        importData: { title: '导入数据' },
        conflictFields: ['title'],
        similarity: 0.9
      }
      
      expect(conflictItem.type).toBe('bookmark')
      expect(conflictItem.conflictType).toBe('duplicate')
      expect(conflictItem.similarity).toBe(0.9)
    })

    it('应该正确定义ConflictResolution接口', () => {
      const resolution: ConflictResolution = {
        conflictId: 'conflict-1',
        action: 'merge',
        mergedData: { title: '合并后的数据' }
      }
      
      expect(resolution.action).toBe('merge')
      expect(resolution.mergedData).toEqual({ title: '合并后的数据' })
    })
  })

  describe('验证类型', () => {
    it('应该正确定义ValidationError接口', () => {
      const validationError: ValidationError = {
        field: 'title',
        message: '标题不能为空',
        code: 'REQUIRED_FIELD'
      }
      
      expect(validationError.field).toBe('title')
      expect(validationError.message).toBe('标题不能为空')
      expect(validationError.code).toBe('REQUIRED_FIELD')
    })

    it('应该正确定义ValidationResult接口', () => {
      const validationResult: ValidationResult = {
        isValid: false,
        errors: [{
          field: 'url',
          message: 'URL格式无效',
          code: 'INVALID_URL'
        }]
      }
      
      expect(validationResult.isValid).toBe(false)
      expect(validationResult.errors).toHaveLength(1)
      expect(validationResult.errors[0].field).toBe('url')
    })
  })

  describe('类型兼容性', () => {
    it('BookmarkInput应该与Bookmark兼容（除了BaseEntity字段）', () => {
      const bookmarkInput: BookmarkInput = {
        type: 'url',
        title: '测试收藏',
        url: 'https://example.com',
        tags: ['测试'],
        category: '技术',
        metadata: {
          aiGenerated: false
        }
      }
      
      // 应该能够创建一个Bookmark对象（添加BaseEntity字段）
      const bookmark: Bookmark = {
        ...bookmarkInput,
        id: 'generated-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: bookmarkInput.metadata || { aiGenerated: false }
      }
      
      expect(bookmark.type).toBe(bookmarkInput.type)
      expect(bookmark.title).toBe(bookmarkInput.title)
      expect(bookmark.url).toBe(bookmarkInput.url)
    })

    it('应该支持可选字段', () => {
      const minimalBookmarkInput: BookmarkInput = {
        type: 'text',
        title: '最小收藏'
      }
      
      expect(minimalBookmarkInput.url).toBeUndefined()
      expect(minimalBookmarkInput.tags).toBeUndefined()
      expect(minimalBookmarkInput.category).toBeUndefined()
    })
  })
})