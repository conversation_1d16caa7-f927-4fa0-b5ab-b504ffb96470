import React, { useEffect } from 'react'
import PopupApp from '../../popup/PopupApp'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { createMockChrome, safeSetChrome } from '../../utils/chromeTestUtils'

/**
 * PopupApp组件测试组件
 * 
 * 此组件用于在开发环境中测试重构后的PopupApp组件
 * 模拟Chrome扩展环境和API
 */
const PopupAppTest: React.FC = () => {
  // 模拟Chrome API
  useEffect(() => {
    // 创建模拟的chrome对象
    const mockChrome = createMockChrome()

    // 安全地设置chrome对象
    const cleanup = safeSetChrome(mockChrome)

    // 模拟window.close
    ;(window as any).close = () => {
      console.log('模拟 window.close 调用')
      alert('模拟关闭弹窗')
    }

    // 返回清理函数
    return cleanup
  }, [])

  const handleRefreshTest = () => {
    window.location.reload()
  }

  const handleToggleBookmarkStatus = () => {
    // 触发收藏状态变化事件
    const mockMessage = {
      type: 'BOOKMARK_STATUS_CHANGED',
      data: {
        url: 'https://github.com/test/repo',
        isBookmarked: Math.random() > 0.5,
        bookmarkId: Math.random() > 0.5 ? 'test-bookmark-id' : null
      }
    }
    
    // 模拟消息事件
    console.log('模拟收藏状态变化事件:', mockMessage)
    alert(`模拟收藏状态变化: ${mockMessage.data.isBookmarked ? '已收藏' : '未收藏'}`)
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 测试说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>PopupApp组件shadcn重构测试</span>
              <Badge variant="secondary">测试环境</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">测试说明</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 此页面模拟Chrome扩展环境，测试重构后的PopupApp组件</li>
                <li>• 所有Chrome API调用都会在控制台显示日志</li>
                <li>• 收藏状态会随机模拟，可以测试不同状态下的UI</li>
                <li>• 打开浏览器开发者工具查看详细的API调用日志</li>
              </ul>
            </div>
            
            <Separator />
            
            <div className="flex space-x-2">
              <Button onClick={handleRefreshTest} variant="outline" size="sm">
                刷新测试
              </Button>
              <Button onClick={handleToggleBookmarkStatus} variant="outline" size="sm">
                模拟状态变化
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* PopupApp组件测试区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 左侧：组件展示 */}
          <Card>
            <CardHeader>
              <CardTitle>PopupApp组件展示</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-background">
                <div className="max-w-sm mx-auto">
                  <PopupApp />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 右侧：重构要点 */}
          <Card>
            <CardHeader>
              <CardTitle>重构要点验证</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">✅ 已完成的重构</h4>
                <div className="space-y-1">
                  <Badge variant="secondary">Card组件容器</Badge>
                  <Badge variant="secondary">Button组件按钮</Badge>
                  <Badge variant="secondary">Switch组件开关</Badge>
                  <Badge variant="secondary">Separator分隔线</Badge>
                  <Badge variant="secondary">shadcn颜色系统</Badge>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium">🎯 测试重点</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 按钮样式和交互效果</li>
                  <li>• 收藏状态切换显示</li>
                  <li>• 设置开关功能</li>
                  <li>• 同步状态指示</li>
                  <li>• 颜色系统一致性</li>
                  <li>• 响应式布局</li>
                </ul>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium">📱 模拟数据</h4>
                <div className="text-xs bg-muted p-2 rounded font-mono">
                  <div>页面: 测试页面 - GitHub</div>
                  <div>URL: https://github.com/test/repo</div>
                  <div>选中文字: 这是一段选中的测试文字...</div>
                  <div>收藏状态: 随机模拟</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 功能测试指南 */}
        <Card>
          <CardHeader>
            <CardTitle>功能测试指南</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">基础功能测试</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>1. 点击"收藏当前页面"按钮</li>
                  <li>2. 点击"收藏选中文字"按钮</li>
                  <li>3. 点击"详细收藏"按钮</li>
                  <li>4. 测试设置开关切换</li>
                  <li>5. 点击同步按钮</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-medium">状态切换测试</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>1. 刷新页面观察初始状态</li>
                  <li>2. 点击"模拟状态变化"按钮</li>
                  <li>3. 观察已收藏/未收藏状态</li>
                  <li>4. 测试编辑收藏功能</li>
                  <li>5. 测试管理页面跳转</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default PopupAppTest