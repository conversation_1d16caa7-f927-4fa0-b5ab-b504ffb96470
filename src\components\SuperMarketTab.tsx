import React, { useState, useEffect } from 'react'
import { Search, Star, ExternalLink, Zap, Globe, TrendingUp, Filter, Grid, List, Heart, Eye } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Input } from './ui/input'
import { Badge } from './ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Separator } from './ui/separator'
import { Switch } from './ui/switch'
import { Label } from './ui/label'

interface Resource {
  id: string
  title: string
  description: string
  url: string
  category: string
  tags: string[]
  rating: number
  views: number
  likes: number
  featured: boolean
  sponsored: boolean
  thumbnail?: string
  addedDate: string
}

interface Category {
  id: string
  name: string
  icon: string
  count: number
}

interface SuperMarketTabProps {}

const SuperMarketTab: React.FC<SuperMarketTabProps> = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('featured')
  const [resources, setResources] = useState<Resource[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [aiSearchEnabled, setAiSearchEnabled] = useState(true)

  // 模拟分类数据
  useEffect(() => {
    setCategories([
      { id: 'all', name: '全部', icon: '🌟', count: 156 },
      { id: 'development', name: '开发工具', icon: '💻', count: 45 },
      { id: 'design', name: '设计资源', icon: '🎨', count: 32 },
      { id: 'productivity', name: '效率工具', icon: '⚡', count: 28 },
      { id: 'learning', name: '学习资源', icon: '📚', count: 35 },
      { id: 'entertainment', name: '娱乐休闲', icon: '🎮', count: 16 }
    ])
  }, [])

  // 模拟资源数据
  useEffect(() => {
    setResources([
      {
        id: '1',
        title: 'GitHub Copilot',
        description: 'AI驱动的代码助手，提高编程效率',
        url: 'https://github.com/features/copilot',
        category: 'development',
        tags: ['AI', '编程', '效率'],
        rating: 4.8,
        views: 12500,
        likes: 890,
        featured: true,
        sponsored: false,
        addedDate: '2024-01-15'
      },
      {
        id: '2',
        title: 'Figma',
        description: '协作式界面设计工具',
        url: 'https://figma.com',
        category: 'design',
        tags: ['设计', '协作', 'UI/UX'],
        rating: 4.9,
        views: 8900,
        likes: 654,
        featured: true,
        sponsored: true,
        addedDate: '2024-01-14'
      },
      {
        id: '3',
        title: 'Notion',
        description: '全能的工作空间和笔记应用',
        url: 'https://notion.so',
        category: 'productivity',
        tags: ['笔记', '协作', '项目管理'],
        rating: 4.7,
        views: 15600,
        likes: 1200,
        featured: true,
        sponsored: false,
        addedDate: '2024-01-13'
      },
      {
        id: '4',
        title: 'Coursera',
        description: '在线学习平台，提供高质量课程',
        url: 'https://coursera.org',
        category: 'learning',
        tags: ['在线学习', '课程', '认证'],
        rating: 4.6,
        views: 7800,
        likes: 456,
        featured: false,
        sponsored: false,
        addedDate: '2024-01-12'
      },
      {
        id: '5',
        title: 'VS Code',
        description: '微软开发的免费代码编辑器',
        url: 'https://code.visualstudio.com',
        category: 'development',
        tags: ['编辑器', '开发', '免费'],
        rating: 4.9,
        views: 20100,
        likes: 1500,
        featured: true,
        sponsored: false,
        addedDate: '2024-01-11'
      },
      {
        id: '6',
        title: 'Canva',
        description: '简单易用的在线设计平台',
        url: 'https://canva.com',
        category: 'design',
        tags: ['设计', '模板', '简单'],
        rating: 4.5,
        views: 11200,
        likes: 780,
        featured: false,
        sponsored: true,
        addedDate: '2024-01-10'
      }
    ])
  }, [])

  // 搜索功能
  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setLoading(true)
    try {
      // 模拟AI搜索
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 这里应该调用实际的搜索API
      console.log('搜索查询:', searchQuery, '启用AI:', aiSearchEnabled)
      
      // 模拟搜索结果过滤
      const filteredResources = resources.filter(resource =>
        resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
      
      setResources(filteredResources)
    } catch (error) {
      console.error('搜索失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 过滤资源
  const filteredResources = resources.filter(resource => {
    if (selectedCategory === 'all') return true
    return resource.category === selectedCategory
  })

  // 排序资源
  const sortedResources = [...filteredResources].sort((a, b) => {
    switch (sortBy) {
      case 'featured':
        if (a.featured && !b.featured) return -1
        if (!a.featured && b.featured) return 1
        return b.rating - a.rating
      case 'rating':
        return b.rating - a.rating
      case 'views':
        return b.views - a.views
      case 'likes':
        return b.likes - a.likes
      case 'newest':
        return new Date(b.addedDate).getTime() - new Date(a.addedDate).getTime()
      default:
        return 0
    }
  })

  // 收藏资源
  const handleLikeResource = (resourceId: string) => {
    setResources(prev => prev.map(resource =>
      resource.id === resourceId
        ? { ...resource, likes: resource.likes + 1 }
        : resource
    ))
  }

  // 打开资源
  const handleOpenResource = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  // 渲染资源卡片
  const renderResourceCard = (resource: Resource) => (
    <Card key={resource.id} className={`relative ${viewMode === 'grid' ? 'h-full' : ''}`}>
      {resource.sponsored && (
        <Badge className="absolute top-2 right-2 z-10 bg-yellow-500 text-yellow-900">
          广告
        </Badge>
      )}
      {resource.featured && !resource.sponsored && (
        <Badge className="absolute top-2 right-2 z-10 bg-primary">
          推荐
        </Badge>
      )}
      
      <CardHeader className={viewMode === 'list' ? 'pb-2' : ''}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center">
              {resource.title}
              <Button
                variant="ghost"
                size="sm"
                className="ml-2 p-1 h-auto"
                onClick={() => handleOpenResource(resource.url)}
              >
                <ExternalLink className="w-4 h-4" />
              </Button>
            </CardTitle>
            <CardDescription className="mt-1">
              {resource.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className={viewMode === 'list' ? 'pt-0' : ''}>
        <div className="space-y-3">
          {/* 标签 */}
          <div className="flex flex-wrap gap-1">
            {resource.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
          
          {/* 统计信息 */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>{resource.rating}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye className="w-4 h-4" />
                <span>{resource.views.toLocaleString()}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Heart className="w-4 h-4" />
                <span>{resource.likes}</span>
              </div>
            </div>
            <span>{resource.addedDate}</span>
          </div>
          
          {/* 操作按钮 */}
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => handleOpenResource(resource.url)}
              className="flex-1"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              访问
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleLikeResource(resource.id)}
            >
              <Heart className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="p-6">
      {/* 头部区域 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <Globe className="w-6 h-6 mr-3 text-primary" />
            超级市场
          </CardTitle>
          <CardDescription className="mt-1">
            发现优质资源，探索无限可能。通过AI智能搜索找到最适合您的工具和资源
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="space-y-6">
        {/* 搜索区域 */}
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* 主搜索框 */}
              <div className="flex space-x-2">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="使用自然语言搜索资源，例如：帮我找一个好用的代码编辑器"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <Button onClick={handleSearch} disabled={loading}>
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      搜索中...
                    </>
                  ) : (
                    <>
                      <Search className="w-4 h-4 mr-2" />
                      搜索
                    </>
                  )}
                </Button>
              </div>

              {/* AI搜索开关 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4 text-primary" />
                  <Label className="text-sm font-medium">AI智能搜索</Label>
                  <span className="text-xs text-muted-foreground">
                    启用后将使用AI理解您的搜索意图
                  </span>
                </div>
                <Switch
                  checked={aiSearchEnabled}
                  onCheckedChange={setAiSearchEnabled}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 分类和筛选 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
              {/* 分类选择 */}
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    className="flex items-center space-x-1"
                  >
                    <span>{category.icon}</span>
                    <span>{category.name}</span>
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {category.count}
                    </Badge>
                  </Button>
                ))}
              </div>

              {/* 视图和排序控制 */}
              <div className="flex items-center space-x-2">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="featured">推荐排序</SelectItem>
                    <SelectItem value="rating">评分最高</SelectItem>
                    <SelectItem value="views">浏览最多</SelectItem>
                    <SelectItem value="likes">点赞最多</SelectItem>
                    <SelectItem value="newest">最新添加</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex items-center border rounded-md">
                  <Button
                    variant={viewMode === 'grid' ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 资源列表 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">
                {selectedCategory === 'all' ? '全部资源' : 
                 categories.find(c => c.id === selectedCategory)?.name || '资源'}
              </CardTitle>
              <Badge variant="outline">
                {sortedResources.length} 个资源
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            {sortedResources.length === 0 ? (
              <div className="text-center py-12">
                <Globe className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">暂无资源</h3>
                <p className="text-muted-foreground">
                  {searchQuery ? '没有找到匹配的资源，请尝试其他关键词' : '该分类下暂无资源'}
                </p>
              </div>
            ) : (
              <div className={
                viewMode === 'grid' 
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : 'space-y-4'
              }>
                {sortedResources.map(renderResourceCard)}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 统计信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              平台统计
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">156</div>
                <div className="text-sm text-muted-foreground">总资源数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">6</div>
                <div className="text-sm text-muted-foreground">分类数量</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">12.5K</div>
                <div className="text-sm text-muted-foreground">总浏览量</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">890</div>
                <div className="text-sm text-muted-foreground">总点赞数</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SuperMarketTab