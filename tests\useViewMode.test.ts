import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useViewMode } from '../src/hooks/useViewMode'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

describe('useViewMode', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该使用默认视图模式初始化', async () => {
    mockLocalStorage.getItem.mockReturnValue(null)

    const { result } = renderHook(() => useViewMode())

    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    expect(result.current.viewMode).toBe('card')
    expect(result.current.isLoading).toBe(false)
  })

  it('应该从本地存储加载保存的视图模式', async () => {
    mockLocalStorage.getItem.mockReturnValue('row')

    const { result } = renderHook(() => useViewMode())

    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    expect(result.current.viewMode).toBe('row')
    expect(result.current.isLoading).toBe(false)
    expect(mockLocalStorage.getItem).toHaveBeenCalledWith('bookmark-view-mode')
  })

  it('应该在无效的保存值时使用默认模式', async () => {
    mockLocalStorage.getItem.mockReturnValue('invalid-mode')

    const { result } = renderHook(() => useViewMode())

    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    expect(result.current.viewMode).toBe('card')
    expect(result.current.isLoading).toBe(false)
  })

  it('应该正确更新视图模式', async () => {
    mockLocalStorage.getItem.mockReturnValue(null)

    const { result } = renderHook(() => useViewMode())

    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    // 更新视图模式
    act(() => {
      result.current.setViewMode('compact')
    })

    expect(result.current.viewMode).toBe('compact')
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('bookmark-view-mode', 'compact')
  })

  it('应该正确重置视图模式', async () => {
    mockLocalStorage.getItem.mockReturnValue('row')

    const { result } = renderHook(() => useViewMode())

    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    expect(result.current.viewMode).toBe('row')

    // 重置视图模式
    act(() => {
      result.current.resetViewMode()
    })

    expect(result.current.viewMode).toBe('card')
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('bookmark-view-mode', 'card')
  })

  it('应该处理localStorage错误', async () => {
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    mockLocalStorage.getItem.mockImplementation(() => {
      throw new Error('Storage error')
    })

    const { result } = renderHook(() => useViewMode())

    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    expect(result.current.viewMode).toBe('card')
    expect(result.current.isLoading).toBe(false)
    expect(consoleWarnSpy).toHaveBeenCalledWith('加载视图模式失败:', expect.any(Error))

    consoleWarnSpy.mockRestore()
  })

  it('应该处理setItem错误', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    mockLocalStorage.getItem.mockReturnValue(null)
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new Error('Storage error')
    })

    const { result } = renderHook(() => useViewMode())

    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    // 尝试更新视图模式
    act(() => {
      result.current.setViewMode('row')
    })

    expect(result.current.viewMode).toBe('row') // 状态仍然更新
    expect(consoleErrorSpy).toHaveBeenCalledWith('保存视图模式失败:', expect.any(Error))

    consoleErrorSpy.mockRestore()
  })

  it('应该在初始化时显示加载状态', async () => {
    mockLocalStorage.getItem.mockReturnValue(null)

    const { result } = renderHook(() => useViewMode())

    // 初始状态应该是加载中
    expect(result.current.isLoading).toBe(true)
    
    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    // 初始化完成后应该不再加载
    expect(result.current.isLoading).toBe(false)
  })

  it('应该支持所有有效的视图模式', async () => {
    const validModes = ['card', 'row', 'compact']

    for (const mode of validModes) {
      mockLocalStorage.getItem.mockReturnValue(mode)

      const { result } = renderHook(() => useViewMode())

      // 等待初始化完成
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0))
      })

      expect(result.current.viewMode).toBe(mode)
    }
  })

  it('应该在多次调用setViewMode时正确更新', async () => {
    mockLocalStorage.getItem.mockReturnValue(null)

    const { result } = renderHook(() => useViewMode())

    // 等待初始化完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    // 连续更新视图模式
    act(() => {
      result.current.setViewMode('row')
    })

    expect(result.current.viewMode).toBe('row')

    act(() => {
      result.current.setViewMode('compact')
    })

    expect(result.current.viewMode).toBe('compact')

    act(() => {
      result.current.setViewMode('card')
    })

    expect(result.current.viewMode).toBe('card')

    // 检查localStorage调用次数
    expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(3)
  })
})