# Universe Bag（乾坤袋）设计文档

## 概述

Universe Bag是一个功能丰富的Chrome浏览器扩展，采用现代Web技术栈构建。扩展使用Manifest V3规范，结合React前端框架和IndexedDB本地存储，提供智能收藏、AI标签生成、云端同步等核心功能。

### 核心特性
- 智能内容收藏（网址、文字摘录）
- AI驱动的自动分类和标签生成
- 内容去重和链接有效性检测
- 多种UI展示模式和现代化界面
- 云端同步备份（支持Notion等平台）
- 海报分享和二维码导入
- 页面内浮窗和信息展示

## 架构设计

### 整体架构

```mermaid
graph TB
    A[Content Script] --> B[Background Service Worker]
    B --> C[Popup Interface]
    B --> D[Options Page]
    B --> E[Management Page]
    
    B --> F[Storage Layer]
    F --> G[IndexedDB]
    F --> H[Chrome Storage API]
    
    B --> I[AI Service Layer]
    I --> J[Local AI Models]
    I --> K[Online AI APIs]
    
    B --> L[Sync Service Layer]
    L --> M[Notion API]
    L --> N[Other Cloud Services]
    
    B --> O[Utils Layer]
    O --> P[Duplicate Detection]
    O --> Q[Link Validation]
    O --> R[Icon Cache]
    O --> S[QR Code Generator]
```

### 技术栈选择

**前端技术：**
- React 18 + TypeScript - 现代化UI开发
- Tailwind CSS - 快速样式开发
- Lucide React - 图标库
- React Hook Form - 表单管理

**存储技术：**
- IndexedDB - 大量数据本地存储
- Chrome Storage API - 配置和小数据存储
- 本地缓存 - 图标和临时数据

**AI集成：**
- OpenAI API - 在线AI服务
- Ollama - 本地AI模型支持
- 自定义API接口 - 支持各种AI服务商

**构建工具：**
- Vite - 快速构建和热重载
- Chrome Extension Vite Plugin - 扩展开发优化

## 组件和接口设计

### 核心组件架构

#### 1. Background Service Worker
```typescript
interface BackgroundService {
  // 消息处理
  handleMessage(message: ExtensionMessage): Promise<any>
  
  // 收藏管理
  saveBookmark(data: BookmarkData): Promise<string>
  getBookmarks(filter?: BookmarkFilter): Promise<Bookmark[]>
  updateBookmark(id: string, data: Partial<BookmarkData>): Promise<void>
  deleteBookmark(id: string): Promise<void>
  
  // AI服务
  generateTags(content: string): Promise<string[]>
  categorizeContent(content: string): Promise<string>
  
  // 同步服务
  syncToCloud(): Promise<void>
  syncFromCloud(): Promise<void>
}
```

#### 2. Content Script
```typescript
interface ContentScript {
  // 页面信息提取
  extractPageInfo(): PageInfo
  extractSelectedText(): string
  
  // 浮窗管理
  createFloatingWidget(): void
  showBookmarkInfo(bookmark: Bookmark): void
  
  // 右键菜单处理
  handleContextMenu(action: string): void
}
```

#### 3. Popup Interface
```typescript
interface PopupInterface {
  // 快速收藏
  quickBookmark(): void
  showBookmarkForm(data?: Partial<BookmarkData>): void
  
  // AI辅助
  generateAISuggestions(content: string): Promise<AISuggestions>
  
  // 状态显示
  showCurrentPageStatus(): void
}
```

#### 4. Management Page
```typescript
interface ManagementPage {
  // 视图管理
  setViewMode(mode: ViewMode): void
  filterBookmarks(filter: BookmarkFilter): void
  
  // 批量操作
  selectMultiple(ids: string[]): void
  batchOperation(operation: BatchOperation): Promise<void>
  
  // 导入导出
  exportBookmarks(options: ExportOptions): Promise<string>
  importBookmarks(data: string, format: ImportFormat): Promise<void>
  
  // 维护功能
  detectDuplicates(): Promise<DuplicateGroup[]>
  validateLinks(): Promise<LinkValidationResult[]>
}
```

### 数据模型

#### 核心数据结构

```typescript
interface Bookmark {
  id: string
  type: 'url' | 'text' | 'image'
  title: string
  url?: string
  content?: string
  description?: string
  tags: string[]
  category: string
  favicon?: string
  thumbnail?: string
  createdAt: Date
  updatedAt: Date
  metadata: BookmarkMetadata
}

interface BookmarkMetadata {
  pageTitle?: string
  siteName?: string
  author?: string
  publishDate?: Date
  wordCount?: number
  language?: string
  aiGenerated: boolean
}

interface Category {
  id: string
  name: string
  description?: string
  color?: string
  parentId?: string
  createdAt: Date
}

interface Tag {
  id: string
  name: string
  color?: string
  usageCount: number
  createdAt: Date
}

interface AIConfig {
  provider: 'openai' | 'claude' | 'local' | 'custom'
  baseUrl?: string
  apiKey?: string
  model: string
  autoTagging: boolean
  autoCategories: boolean
}

interface SyncConfig {
  enabled: boolean
  provider: 'notion' | 'airtable' | 'custom'
  credentials: Record<string, any>
  syncFrequency: number
  lastSync?: Date
}
```

## 用户界面设计

### 1. Popup界面设计

**主要功能区域：**
- 快速收藏按钮
- 当前页面状态指示
- 功能开关（浮窗、自动标签等）
- 管理页面入口

**二级收藏界面：**
- 标题和描述编辑
- 标签输入（支持自动完成）
- 分类选择（树形结构）
- AI辅助生成按钮
- 保存和取消操作

### 2. 管理页面设计

**布局结构：**
```
┌─────────────────────────────────────────────────────────┐
│ Header: 搜索框 | 视图切换 | 批量操作 | 设置              │
├─────────────────────────────────────────────────────────┤
│ Sidebar: 分类树 | 标签云 | 快速筛选                    │
├─────────────────────────────────────────────────────────┤
│ Main Content: 收藏内容展示区域                          │
│ - 列表视图 / 卡片视图 / 画册视图                        │
│ - 分页或虚拟滚动                                        │
├─────────────────────────────────────────────────────────┤
│ Footer: 统计信息 | 同步状态 | 操作提示                  │
└─────────────────────────────────────────────────────────┘
```

**视图模式：**
- 简单列表：仅显示标题和图标
- 详细列表：包含描述、标签、时间
- 卡片视图：类似Pinterest的卡片布局
- 画册视图：重点展示缩略图

### 3. 浮窗界面设计

**微型图标：**
- 半透明圆形图标
- 可拖拽到页面任意位置
- 鼠标悬停显示功能提示

**展开菜单：**
- 与Popup界面保持一致的设计
- 适应页面环境的颜色主题
- 自动避让页面重要内容

**信息浮窗：**
- 显示当前页面收藏信息
- 相关推荐列表
- 快速编辑入口

## 错误处理

### 1. 网络错误处理
- AI服务连接失败时的降级策略
- 云端同步失败时的重试机制
- 离线模式下的功能限制提示

### 2. 数据错误处理
- 数据库操作失败的回滚机制
- 导入数据格式错误的验证和提示
- 存储空间不足时的清理策略

### 3. 用户操作错误
- 表单验证和友好的错误提示
- 批量操作的确认机制
- 误删除的撤销功能

## 性能优化

### 1. 数据加载优化
- 虚拟滚动处理大量收藏数据
- 图标和缩略图的懒加载
- 搜索结果的分页加载

### 2. 存储优化
- IndexedDB的索引优化
- 定期清理无用的缓存数据
- 压缩存储的文本内容

### 3. AI服务优化
- 批量处理减少API调用
- 本地缓存AI生成的标签
- 智能的重试和降级策略

## 安全考虑

### 1. 数据安全
- 敏感数据的本地加密存储
- API密钥的安全存储
- 用户数据的隐私保护

### 2. 网络安全
- HTTPS强制使用
- API调用的身份验证
- 防止XSS和注入攻击

### 3. 权限管理
- 最小权限原则
- 用户授权的透明化
- 敏感操作的二次确认

## 测试策略

### 1. 单元测试
- 核心业务逻辑的单元测试
- 数据模型和工具函数测试
- AI服务接口的模拟测试

### 2. 集成测试
- Chrome扩展API的集成测试
- 数据库操作的集成测试
- 云端同步功能的端到端测试

### 3. 用户体验测试
- 不同浏览器版本的兼容性测试
- 各种网站环境下的功能测试
- 性能和内存使用测试

## 部署和发布

### 1. 构建流程
- 自动化构建和打包
- 代码压缩和优化
- 多环境配置管理

### 2. 版本管理
- 语义化版本控制
- 自动更新机制
- 向后兼容性保证

### 3. 发布策略
- Chrome Web Store发布流程
- 用户反馈收集机制
- 快速问题修复和热更新