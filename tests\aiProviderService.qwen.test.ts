/**
 * 通义千问提供商服务测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'
import { AIProviderConfig } from '../src/types/ai'

// 模拟fetch
global.fetch = vi.fn()

describe('AIProviderService - 通义千问集成', () => {
  let service: AIProviderService
  const mockFetch = global.fetch as ReturnType<typeof vi.fn>

  beforeEach(() => {
    service = new AIProviderService()
    mockFetch.mockClear()
  })

  describe('testQwenConnection', () => {
    const baseUrl = 'https://dashscope.aliyuncs.com/api/v1'
    const apiKey = 'sk-test-key'

    it('应该成功测试通义千问连接', async () => {
      // 模拟成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            { id: 'qwen-plus', object: 'model' },
            { id: 'qwen-turbo', object: 'model' },
            { id: 'qwen-max', object: 'model' }
          ]
        })
      } as Response)

      const result = await service.testQwenConnection(baseUrl, apiKey)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(3)
      expect(result.error).toBeUndefined()

      // 验证API调用
      expect(mockFetch).toHaveBeenCalledWith(
        'https://dashscope.aliyuncs.com/api/v1/compatible-mode/v1/models',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Authorization': 'Bearer sk-test-key',
            'Content-Type': 'application/json'
          }
        })
      )
    })

    it('应该处理已包含compatible-mode的baseUrl', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: [] })
      } as Response)

      await service.testQwenConnection('https://dashscope.aliyuncs.com/compatible-mode/v1', apiKey)

      expect(mockFetch).toHaveBeenCalledWith(
        'https://dashscope.aliyuncs.com/compatible-mode/v1/models',
        expect.any(Object)
      )
    })

    it('应该处理空API密钥错误', async () => {
      const result = await service.testQwenConnection(baseUrl, '')

      expect(result.success).toBe(false)
      expect(result.error).toBe('通义千问API密钥不能为空')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理401未授权错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await service.testQwenConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥无效或已过期')
    })

    it('应该处理403权限不足错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      } as Response)

      const result = await service.testQwenConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥权限不足')
    })

    it('应该处理429频率限制错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response)

      const result = await service.testQwenConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API请求频率超限，请稍后重试')
    })

    it('应该处理500服务器错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const result = await service.testQwenConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('通义千问服务器错误，请稍后重试')
    })

    it('应该处理网络连接超时', async () => {
      const abortError = new Error('AbortError')
      abortError.name = 'AbortError'
      mockFetch.mockRejectedValueOnce(abortError)

      const result = await service.testQwenConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('连接超时，请检查网络连接')
    })

    it('应该处理DNS解析失败', async () => {
      mockFetch.mockRejectedValueOnce(new Error('ENOTFOUND dashscope.aliyuncs.com'))

      const result = await service.testQwenConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('DNS解析失败，请检查网络连接')
    })

    it('应该正确处理末尾有斜杠的baseUrl', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: [] })
      } as Response)

      await service.testQwenConnection('https://dashscope.aliyuncs.com/api/v1/', apiKey)

      expect(mockFetch).toHaveBeenCalledWith(
        'https://dashscope.aliyuncs.com/api/v1/compatible-mode/v1/models',
        expect.any(Object)
      )
    })
  })

  describe('getQwenModels', () => {
    it('应该返回通义千问模型列表', async () => {
      const models = await service.getQwenModels()

      expect(models).toHaveLength(6)
      
      // 验证qwen-plus模型
      const qwenPlusModel = models.find(m => m.id === 'qwen-plus')
      expect(qwenPlusModel).toBeDefined()
      expect(qwenPlusModel?.name).toBe('qwen-plus')
      expect(qwenPlusModel?.displayName).toBe('通义千问 Plus')
      expect(qwenPlusModel?.description).toContain('超大规模语言模型')
      expect(qwenPlusModel?.capabilities).toContain('chat')
      expect(qwenPlusModel?.capabilities).toContain('completion')
      expect(qwenPlusModel?.capabilities).toContain('reasoning')
      expect(qwenPlusModel?.capabilities).toContain('code')
      expect(qwenPlusModel?.tags).toContain('阿里云')
      expect(qwenPlusModel?.tags).toContain('通义千问')
      expect(qwenPlusModel?.providerId).toBe('qwen')
      expect(qwenPlusModel?.isRecommended).toBe(true)
      expect(qwenPlusModel?.isPopular).toBe(true)
      expect(qwenPlusModel?.maxTokens).toBe(32000)
      expect(qwenPlusModel?.contextLength).toBe(32000)

      // 验证qwen-turbo模型
      const qwenTurboModel = models.find(m => m.id === 'qwen-turbo')
      expect(qwenTurboModel).toBeDefined()
      expect(qwenTurboModel?.name).toBe('qwen-turbo')
      expect(qwenTurboModel?.displayName).toBe('通义千问 Turbo')
      expect(qwenTurboModel?.description).toContain('高效模型')
      expect(qwenTurboModel?.tags).toContain('高速')
      expect(qwenTurboModel?.providerId).toBe('qwen')
      expect(qwenTurboModel?.isRecommended).toBe(true)
      expect(qwenTurboModel?.isPopular).toBe(true)

      // 验证qwen-max模型
      const qwenMaxModel = models.find(m => m.id === 'qwen-max')
      expect(qwenMaxModel).toBeDefined()
      expect(qwenMaxModel?.name).toBe('qwen-max')
      expect(qwenMaxModel?.displayName).toBe('通义千问 Max')
      expect(qwenMaxModel?.description).toContain('最强模型')
      expect(qwenMaxModel?.capabilities).toContain('reasoning')
      expect(qwenMaxModel?.capabilities).toContain('analysis')
      expect(qwenMaxModel?.tags).toContain('最强')
      expect(qwenMaxModel?.providerId).toBe('qwen')
      expect(qwenMaxModel?.isRecommended).toBe(true)
      expect(qwenMaxModel?.isPopular).toBe(false)

      // 验证qwen-long模型
      const qwenLongModel = models.find(m => m.id === 'qwen-long')
      expect(qwenLongModel).toBeDefined()
      expect(qwenLongModel?.name).toBe('qwen-long')
      expect(qwenLongModel?.displayName).toBe('通义千问 Long')
      expect(qwenLongModel?.description).toContain('长文本模型')
      expect(qwenLongModel?.capabilities).toContain('long-context')
      expect(qwenLongModel?.tags).toContain('长文本')
      expect(qwenLongModel?.maxTokens).toBe(1000000)
      expect(qwenLongModel?.contextLength).toBe(1000000)
      expect(qwenLongModel?.providerId).toBe('qwen')

      // 验证qwen-coder-plus模型
      const qwenCoderModel = models.find(m => m.id === 'qwen-coder-plus')
      expect(qwenCoderModel).toBeDefined()
      expect(qwenCoderModel?.name).toBe('qwen-coder-plus')
      expect(qwenCoderModel?.displayName).toBe('通义千问 Coder Plus')
      expect(qwenCoderModel?.description).toContain('代码专用模型')
      expect(qwenCoderModel?.capabilities).toContain('code')
      expect(qwenCoderModel?.capabilities).toContain('debugging')
      expect(qwenCoderModel?.tags).toContain('代码生成')
      expect(qwenCoderModel?.tags).toContain('编程')
      expect(qwenCoderModel?.providerId).toBe('qwen')

      // 验证qwen-math-plus模型
      const qwenMathModel = models.find(m => m.id === 'qwen-math-plus')
      expect(qwenMathModel).toBeDefined()
      expect(qwenMathModel?.name).toBe('qwen-math-plus')
      expect(qwenMathModel?.displayName).toBe('通义千问 Math Plus')
      expect(qwenMathModel?.description).toContain('数学专用模型')
      expect(qwenMathModel?.capabilities).toContain('math')
      expect(qwenMathModel?.capabilities).toContain('calculation')
      expect(qwenMathModel?.tags).toContain('数学')
      expect(qwenMathModel?.tags).toContain('计算')
      expect(qwenMathModel?.providerId).toBe('qwen')
    })

    it('应该在出错时返回空数组', async () => {
      // 模拟控制台错误输出
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // 模拟getQwenModels内部出错（虽然当前实现不会出错）
      const originalConsoleLog = console.log
      console.log = vi.fn(() => {
        throw new Error('测试错误')
      })

      const models = await service.getQwenModels()

      expect(models).toEqual([])
      
      // 恢复console.log
      console.log = originalConsoleLog
      consoleSpy.mockRestore()
    })
  })

  describe('集成测试', () => {
    it('应该能够通过AIProviderService.testConnection调用通义千问测试', async () => {
      const config: AIProviderConfig = {
        id: 'qwen-test',
        name: '通义千问 Test',
        type: 'qwen',
        baseUrl: 'https://dashscope.aliyuncs.com/api/v1',
        apiKey: 'sk-test-key',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            { id: 'qwen-plus', object: 'model' },
            { id: 'qwen-turbo', object: 'model' }
          ]
        })
      } as Response)

      const result = await service.testConnection(config)

      expect(result.success).toBe(true)
      expect(result.providerId).toBe('qwen-test')
      expect(result.modelCount).toBe(2)
      expect(result.responseTime).toBeGreaterThanOrEqual(0)
      expect(result.testedAt).toBeInstanceOf(Date)
    })

    it('应该能够通过AIProviderService.getModels调用通义千问模型获取', async () => {
      const config: AIProviderConfig = {
        id: 'qwen-test',
        name: '通义千问 Test',
        type: 'qwen',
        baseUrl: 'https://dashscope.aliyuncs.com/api/v1',
        apiKey: 'sk-test-key',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await service.getModels(config)

      expect(models).toHaveLength(6)
      expect(models.every(m => m.providerId === 'qwen')).toBe(true)
    })
  })
})