// 智能文件夹选择组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import SmartFolderSelector from '../src/components/SmartFolderSelector'
import { categoryService } from '../src/services/categoryService'

// Mock categoryService
vi.mock('../src/services/categoryService', () => ({
  categoryService: {
    getCategories: vi.fn(),
    createCategory: vi.fn()
  }
}))

// Mock CategoryModal
vi.mock('../src/components/CategoryModal', () => ({
  default: ({ isOpen, onSave, onClose, initialData }) => (
    isOpen ? (
      <div data-testid="category-modal">
        <button onClick={() => onSave({ name: initialData?.name || '新文件夹' })}>
          保存
        </button>
        <button onClick={onClose}>关闭</button>
      </div>
    ) : null
  )
}))

// Mock UI components
vi.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }) => <button {...props}>{children}</button>
}))

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }) => <span {...props}>{children}</span>
}))

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }) => <div {...props}>{children}</div>,
  CardHeader: ({ children, ...props }) => <div {...props}>{children}</div>,
  CardTitle: ({ children, ...props }) => <h3 {...props}>{children}</h3>
}))

vi.mock('@/components/ui/alert', () => ({
  Alert: ({ children, ...props }) => <div {...props}>{children}</div>,
  AlertDescription: ({ children, ...props }) => <div {...props}>{children}</div>
}))

vi.mock('@/components/ui/separator', () => ({
  Separator: (props) => <hr {...props} />
}))

const mockCategories = [
  { id: '1', name: '技术', description: '', color: '#blue', createdAt: new Date(), updatedAt: new Date(), bookmarkCount: 10 },
  { id: '2', name: '学习', description: '', color: '#green', createdAt: new Date(), updatedAt: new Date(), bookmarkCount: 5 },
  { id: '3', name: '工具', description: '', color: '#red', createdAt: new Date(), updatedAt: new Date(), bookmarkCount: 8 }
]

const mockRecommendations = {
  recommendedFolders: [
    { name: '技术', confidence: 0.9, reason: '基于内容关键词匹配' },
    { name: 'AI研究', confidence: 0.7, reason: '相关主题推荐' },
    { name: '工具', confidence: 0.6, reason: '常用分类' }
  ],
  reasoning: '基于内容分析和使用频率推荐'
}

describe('SmartFolderSelector', () => {
  const defaultProps = {
    recommendations: mockRecommendations,
    selectedFolder: undefined,
    onFolderSelect: vi.fn(),
    onCreateFolder: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(categoryService.getCategories).mockResolvedValue(mockCategories)
  })

  it('应该正确渲染组件', async () => {
    render(<SmartFolderSelector {...defaultProps} />)

    expect(screen.getByText('推荐文件夹')).toBeInTheDocument()
    expect(screen.getByText('新建文件夹')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText('技术')).toBeInTheDocument()
      expect(screen.getByText('AI研究')).toBeInTheDocument()
      expect(screen.getByText('工具')).toBeInTheDocument()
    })
  })

  it('应该显示推荐文件夹列表', async () => {
    render(<SmartFolderSelector {...defaultProps} />)

    await waitFor(() => {
      // 检查所有推荐文件夹
      expect(screen.getByText('技术')).toBeInTheDocument()
      expect(screen.getByText('AI研究')).toBeInTheDocument()
      expect(screen.getByText('工具')).toBeInTheDocument()

      // 检查置信度显示
      expect(screen.getByText('高')).toBeInTheDocument() // 0.9 confidence
      expect(screen.getAllByText('中')).toHaveLength(2) // 0.7 and 0.6 confidence

      // 检查推荐理由
      expect(screen.getByText('基于内容关键词匹配')).toBeInTheDocument()
      expect(screen.getByText('相关主题推荐')).toBeInTheDocument()
    })
  })

  it('应该正确标识存在和不存在的文件夹', async () => {
    render(<SmartFolderSelector {...defaultProps} />)

    await waitFor(() => {
      // 技术和工具文件夹存在，AI研究不存在
      expect(screen.getByText('技术')).toBeInTheDocument()
      expect(screen.getByText('工具')).toBeInTheDocument()
      expect(screen.getByText('AI研究')).toBeInTheDocument()

      // 只有AI研究应该显示"不存在"标识
      const notExistElements = screen.getAllByText('不存在')
      expect(notExistElements).toHaveLength(1)
    })
  })

  it('应该在新建文件夹后更新存在状态', async () => {
    const mockNewCategory = {
      id: '4',
      name: 'AI研究',
      description: '',
      color: '#purple',
      createdAt: new Date(),
      updatedAt: new Date(),
      bookmarkCount: 0
    }

    vi.mocked(categoryService.createCategory).mockResolvedValue(mockNewCategory)

    render(<SmartFolderSelector {...defaultProps} />)

    await waitFor(() => {
      // 初始时AI研究不存在
      expect(screen.getByText('不存在')).toBeInTheDocument()
    })

    // 点击AI研究文件夹
    const aiFolder = screen.getByText('AI研究')
    fireEvent.click(aiFolder.closest('div'))

    await waitFor(() => {
      expect(screen.getByTestId('category-modal')).toBeInTheDocument()
    })

    // 在模态框中保存
    const saveButton = screen.getByText('保存')
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(categoryService.createCategory).toHaveBeenCalledWith({ name: 'AI研究' })
      expect(defaultProps.onFolderSelect).toHaveBeenCalledWith('AI研究')
    })
  })

  it('应该处理文件夹选择', async () => {
    const onFolderSelect = vi.fn()
    render(<SmartFolderSelector {...defaultProps} onFolderSelect={onFolderSelect} />)
    
    await waitFor(() => {
      const techFolder = screen.getByText('技术')
      fireEvent.click(techFolder.closest('div'))
      expect(onFolderSelect).toHaveBeenCalledWith('技术')
    })
  })

  it('应该处理不存在文件夹的点击', async () => {
    render(<SmartFolderSelector {...defaultProps} />)
    
    await waitFor(() => {
      const aiFolder = screen.getByText('AI研究')
      fireEvent.click(aiFolder.closest('div'))
      
      // 应该打开创建模态框
      expect(screen.getByTestId('category-modal')).toBeInTheDocument()
    })
  })

  it('应该处理新建文件夹', async () => {
    const onCreateFolder = vi.fn()
    const mockNewCategory = { id: '4', name: '新文件夹', description: '', color: '#purple', createdAt: new Date(), updatedAt: new Date(), bookmarkCount: 0 }
    
    vi.mocked(categoryService.createCategory).mockResolvedValue(mockNewCategory)
    
    render(<SmartFolderSelector {...defaultProps} onCreateFolder={onCreateFolder} />)
    
    // 点击新建文件夹按钮
    const createButton = screen.getByText('新建文件夹')
    fireEvent.click(createButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('category-modal')).toBeInTheDocument()
    })
    
    // 在模态框中保存
    const saveButton = screen.getByText('保存')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(categoryService.createCategory).toHaveBeenCalledWith({ name: '新文件夹' })
      expect(defaultProps.onFolderSelect).toHaveBeenCalledWith('新文件夹')
      expect(onCreateFolder).toHaveBeenCalledWith('新文件夹')
    })
  })

  it('应该显示选中状态', async () => {
    render(<SmartFolderSelector {...defaultProps} selectedFolder="技术" />)

    await waitFor(() => {
      // 查找包含"技术"文本的文件夹容器
      const techFolderContainer = screen.getByText('技术').closest('.flex.items-center.justify-between')
      expect(techFolderContainer).toHaveClass('border-primary')
    })
  })

  it('应该显示错误信息', async () => {
    render(<SmartFolderSelector {...defaultProps} error="测试错误信息" />)
    
    expect(screen.getByText('测试错误信息')).toBeInTheDocument()
  })

  it('应该显示成功信息', async () => {
    render(<SmartFolderSelector {...defaultProps} success="文件夹创建成功" />)
    
    expect(screen.getByText('文件夹创建成功')).toBeInTheDocument()
  })

  it('应该在禁用状态下不响应交互', async () => {
    const onFolderSelect = vi.fn()
    render(<SmartFolderSelector {...defaultProps} disabled={true} onFolderSelect={onFolderSelect} />)
    
    await waitFor(() => {
      const techFolder = screen.getByText('技术')
      fireEvent.click(techFolder.closest('div'))
      expect(onFolderSelect).not.toHaveBeenCalled()
    })
  })

  it('应该显示推荐理由', async () => {
    render(<SmartFolderSelector {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('基于内容分析和使用频率推荐')).toBeInTheDocument()
    })
  })

  it('应该处理创建文件夹失败', async () => {
    vi.mocked(categoryService.createCategory).mockRejectedValue(new Error('创建失败'))
    
    render(<SmartFolderSelector {...defaultProps} />)
    
    // 点击新建文件夹按钮
    const createButton = screen.getByText('新建文件夹')
    fireEvent.click(createButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('category-modal')).toBeInTheDocument()
    })
    
    // 在模态框中保存
    const saveButton = screen.getByText('保存')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(categoryService.createCategory).toHaveBeenCalled()
      // 错误应该被处理，但不会显示在这个组件中（由CategoryModal处理）
    })
  })

  it('应该在没有推荐时不渲染', () => {
    const { container } = render(<SmartFolderSelector {...defaultProps} recommendations={undefined} />)
    expect(container.firstChild).toBeNull()
  })

  it('应该在推荐列表为空时不渲染', () => {
    const emptyRecommendations = {
      recommendedFolders: [],
      reasoning: ''
    }
    const { container } = render(<SmartFolderSelector {...defaultProps} recommendations={emptyRecommendations} />)
    expect(container.firstChild).toBeNull()
  })
})
