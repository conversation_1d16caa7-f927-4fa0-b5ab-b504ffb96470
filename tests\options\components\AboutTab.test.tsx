/**
 * AboutTab 组件单元测试
 */

import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import AboutTab from '../../../src/options/components/AboutTab'
import type { AboutPageData } from '../../../src/options/data/aboutInfo'
import * as manifestReader from '../../../src/options/utils/manifestReader'

// 模拟 manifestReader 模块
vi.mock('../../../src/options/utils/manifestReader')

const mockManifestReader = manifestReader as any

// 测试数据
const mockAboutData: AboutPageData = {
  extensionInfo: {
    name: 'Test Extension',
    version: '2.0.0',
    description: 'Test extension description',
    developer: 'Test Developer'
  },
  buildInfo: {
    buildDate: '2024-01-01',
    buildVersion: '2.0.0'
  },
  developerInfo: {
    name: 'Test Developer',
    website: 'https://test.com',
    email: '<EMAIL>'
  },
  licenseInfo: {
    type: 'MIT License',
    text: 'Test license text',
    url: 'https://opensource.org/licenses/MIT'
  }
}

describe('AboutTab', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置默认的模拟返回值
    vi.mocked(mockManifestReader.isExtensionEnvironment).mockReturnValue(true)
    vi.mocked(mockManifestReader.getExtensionInfo).mockReturnValue(mockAboutData.extensionInfo)
    vi.mocked(mockManifestReader.getBuildInfo).mockReturnValue({
      buildDate: mockAboutData.buildInfo.buildDate,
      buildVersion: mockAboutData.buildInfo.buildVersion,
      manifestVersion: 3
    })
    vi.mocked(mockManifestReader.getExtensionPermissions).mockReturnValue(['storage', 'activeTab'])
  })

  it('应该正确渲染传入的关于数据', () => {
    render(<AboutTab aboutData={mockAboutData} />)
    
    // 检查扩展名称
    expect(screen.getByText('Test Extension')).toBeInTheDocument()
    
    // 检查版本信息（使用 getAllByText 因为版本号会出现多次）
    expect(screen.getAllByText('2.0.0')).toHaveLength(2)
    
    // 检查描述
    expect(screen.getByText('Test extension description')).toBeInTheDocument()
    
    // 检查开发者信息
    expect(screen.getByText('Test Developer')).toBeInTheDocument()
    
    // 注意：当前组件设计中不显示许可证信息
    // 如果将来需要显示许可证信息，可以取消注释下面的测试
    // expect(screen.getByText('MIT License')).toBeInTheDocument()
  })

  it('应该显示加载状态当没有传入数据时', () => {
    // 清除所有模拟，让组件进入加载状态
    vi.clearAllMocks()
    
    render(<AboutTab />)
    
    // 由于组件会立即加载数据，我们需要检查初始状态
    // 或者我们可以检查最终渲染的内容
    expect(screen.getByText('关于我们')).toBeInTheDocument()
  })

  it('应该从 manifest 加载数据当没有传入数据时', async () => {
    render(<AboutTab />)
    
    // 等待数据加载完成
    await waitFor(() => {
      expect(screen.getByText('Test Extension')).toBeInTheDocument()
    })
    
    // 验证调用了相关的函数
    expect(vi.mocked(mockManifestReader.isExtensionEnvironment)).toHaveBeenCalled()
    expect(vi.mocked(mockManifestReader.getExtensionInfo)).toHaveBeenCalled()
    expect(vi.mocked(mockManifestReader.getBuildInfo)).toHaveBeenCalled()
    expect(vi.mocked(mockManifestReader.getExtensionPermissions)).toHaveBeenCalled()
  })

  it('应该显示权限信息', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('权限详情')).toBeInTheDocument()
    })
    
    // 检查权限数量
    expect(screen.getByText('2 个')).toBeInTheDocument()
    
    // 检查具体权限（使用 getAllByText 因为权限会出现多次）
    expect(screen.getAllByText('storage')).toHaveLength(2)
    expect(screen.getAllByText('activeTab')).toHaveLength(2)
  })

  it('应该显示正确的环境状态', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('扩展环境')).toBeInTheDocument()
    })
  })

  it('应该显示开发环境状态当不在扩展环境时', async () => {
    vi.mocked(mockManifestReader.isExtensionEnvironment).mockReturnValue(false)
    
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('开发环境')).toBeInTheDocument()
    })
  })

  it('应该正确渲染外部链接', () => {
    render(<AboutTab aboutData={mockAboutData} />)
    
    // 检查网站链接
    const websiteLink = screen.getByRole('link', { name: 'https://test.com' })
    expect(websiteLink).toHaveAttribute('href', 'https://test.com')
    expect(websiteLink).toHaveAttribute('target', '_blank')
    
    // 注意：当前组件设计中不显示邮箱链接和许可证链接
    // 如果将来需要显示这些链接，可以取消注释下面的测试
    // const emailLink = screen.getByRole('link', { name: '<EMAIL>' })
    // expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>')
    
    // const licenseLink = screen.getByRole('link', { name: '查看完整许可证文本' })
    // expect(licenseLink).toHaveAttribute('href', 'https://opensource.org/licenses/MIT')
    // expect(licenseLink).toHaveAttribute('target', '_blank')
  })

  it('应该处理加载错误并使用默认数据', async () => {
    // 模拟加载错误
    vi.mocked(mockManifestReader.getExtensionInfo).mockImplementation(() => {
      throw new Error('加载失败')
    })
    
    render(<AboutTab />)
    
    await waitFor(() => {
      // 应该显示默认的扩展名称
      expect(screen.getByText('Universe Bag（乾坤袋）')).toBeInTheDocument()
    })
  })

  it('应该在没有权限时隐藏权限详情卡片', async () => {
    vi.mocked(mockManifestReader.getExtensionPermissions).mockReturnValue([])
    
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('0 个')).toBeInTheDocument()
    })
    
    // 权限详情卡片不应该显示
    expect(screen.queryByText('权限详情')).not.toBeInTheDocument()
  })

  it('应该正确显示权限描述', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('存储收藏数据和用户设置')).toBeInTheDocument()
      expect(screen.getByText('获取当前标签页信息')).toBeInTheDocument()
    })
  })

  it('应该显示版权信息', () => {
    render(<AboutTab aboutData={mockAboutData} />)
    
    expect(screen.getByText(/© 2024 Test Developer. 保留所有权利。/)).toBeInTheDocument()
    expect(screen.getByText('感谢您使用 Universe Bag，让收藏管理变得更简单！')).toBeInTheDocument()
  })
})