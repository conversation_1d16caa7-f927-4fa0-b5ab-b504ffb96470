import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { Alert, AlertDescription } from '../ui/alert'
import { Textarea } from '../ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { 
  MessageCircle, 
  Send, 
  Bot, 
  User, 
  Settings, 
  RefreshCw,
  Loader2,
  Copy,
  Trash2,
  Download,
  Upload,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react'

// 导入AI服务
import { localAIServiceAdapter } from '../../services/localAIServiceAdapter'
import { aiIntegrationService } from '../../services/aiIntegrationService'
import { aiChatService, ChatMessage as APIChatMessage } from '../../services/aiChatService'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  modelId?: string
  responseTime?: number
  error?: string
}

interface ModelOption {
  id: string
  name: string
  displayName: string
  providerId: string
  providerName: string
  serviceUrl?: string
  providerConfig?: any
  description?: string
  capabilities?: string[]
  isRecommended?: boolean
  isPopular?: boolean
}

interface ChatSettings {
  temperature: number
  maxTokens: number
  systemPrompt: string
  stream: boolean
}

const AIModelChatTest: React.FC = () => {
  // 状态管理
  const [availableModels, setAvailableModels] = useState<ModelOption[]>([])
  const [selectedModel, setSelectedModel] = useState<ModelOption | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingModels, setIsLoadingModels] = useState(false)
  const [chatSettings, setChatSettings] = useState<ChatSettings>({
    temperature: 0.7,
    maxTokens: 2048,
    systemPrompt: '你是一个有用的AI助手，请用中文回答用户的问题。',
    stream: false
  })
  const [logs, setLogs] = useState<string[]>([])
  const [showSettings, setShowSettings] = useState(false)

  // 引用
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 添加日志
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)])
  }, [])

  // 滚动到消息底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  // 加载可用模型
  const loadAvailableModels = useCallback(async () => {
    setIsLoadingModels(true)
    addLog('开始加载可用模型...')
    
    try {
      const models: ModelOption[] = []
      
      // 1. 从本地AI服务获取模型
      const discoveryResult = await localAIServiceAdapter.discoverLocalServices()
      
      for (const service of discoveryResult.services) {
        try {
          const serviceModels = await localAIServiceAdapter.getLocalServiceModels(service)
          
          serviceModels.forEach(model => {
            models.push({
              id: `${service.name.toLowerCase()}-${model.id}`,
              name: model.name,
              displayName: `${model.displayName} (${service.name})`,
              providerId: service.name.toLowerCase().replace(/\s+/g, '-'),
              providerName: service.name,
              serviceUrl: service.baseUrl,
              description: model.description,
              capabilities: model.capabilities,
              isRecommended: model.isRecommended,
              isPopular: model.isPopular
            })
          })
          
          addLog(`从 ${service.name} 加载了 ${serviceModels.length} 个模型`)
        } catch (error) {
          addLog(`从 ${service.name} 加载模型失败: ${error.message}`)
        }
      }
      
      // 2. 从配置的AI提供商获取模型
      try {
        const configuredProviders = await aiIntegrationService.getConfiguredProviders()
        
        for (const provider of configuredProviders) {
          if (provider.enabled) {
            try {
              const providerModels = await aiIntegrationService.getAvailableModels(provider.id)
              
              providerModels.forEach(model => {
                models.push({
                  id: `${provider.id}-${model.id}`,
                  name: model.name,
                  displayName: `${model.displayName} (${provider.name})`,
                  providerId: provider.id,
                  providerName: provider.name,
                  providerConfig: provider,
                  description: model.description,
                  capabilities: model.capabilities,
                  isRecommended: model.isRecommended,
                  isPopular: model.isPopular
                })
              })
              
              addLog(`从 ${provider.name} 加载了 ${providerModels.length} 个模型`)
            } catch (error) {
              addLog(`从 ${provider.name} 加载模型失败: ${error.message}`)
            }
          }
        }
      } catch (error) {
        addLog(`加载配置的提供商失败: ${error.message}`)
      }
      
      setAvailableModels(models)
      addLog(`总共加载了 ${models.length} 个可用模型`)
      
      // 如果没有选中的模型，自动选择第一个推荐模型
      if (!selectedModel && models.length > 0) {
        const recommendedModel = models.find(m => m.isRecommended) || models[0]
        setSelectedModel(recommendedModel)
        addLog(`自动选择模型: ${recommendedModel.displayName}`)
      }
      
    } catch (error) {
      addLog(`加载模型失败: ${error.message}`)
    } finally {
      setIsLoadingModels(false)
    }
  }, [selectedModel, addLog])

  // 发送消息
  const sendMessage = useCallback(async () => {
    if (!inputMessage.trim() || !selectedModel || isLoading) {
      return
    }

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)
    
    const startTime = Date.now()
    addLog(`发送消息到 ${selectedModel.displayName}: ${userMessage.content.substring(0, 50)}...`)

    try {
      // 构建对话历史
      const conversationHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))

      // 添加系统提示词
      if (chatSettings.systemPrompt.trim()) {
        conversationHistory.unshift({
          role: 'system',
          content: chatSettings.systemPrompt
        })
      }

      // 添加当前用户消息
      conversationHistory.push({
        role: 'user',
        content: userMessage.content
      })

      // 调用AI模型API
      const response = await callAIModel(selectedModel, conversationHistory, chatSettings)
      
      const responseTime = Date.now() - startTime
      
      const assistantMessage: ChatMessage = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: response,
        timestamp: new Date(),
        modelId: selectedModel.id,
        responseTime
      }

      setMessages(prev => [...prev, assistantMessage])
      addLog(`收到回复 (${responseTime}ms): ${response.substring(0, 50)}...`)
      
    } catch (error) {
      const responseTime = Date.now() - startTime
      
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: `错误: ${error.message}`,
        timestamp: new Date(),
        modelId: selectedModel.id,
        responseTime,
        error: error.message
      }

      setMessages(prev => [...prev, errorMessage])
      addLog(`请求失败 (${responseTime}ms): ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }, [inputMessage, selectedModel, isLoading, messages, chatSettings, addLog])

  // 调用AI模型API
  const callAIModel = async (
    model: ModelOption, 
    messages: Array<{role: string, content: string}>, 
    settings: ChatSettings
  ): Promise<string> => {
    try {
      // 转换消息格式
      const apiMessages: APIChatMessage[] = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content
      }))

      const chatSettings = {
        temperature: settings.temperature,
        maxTokens: settings.maxTokens,
        stream: settings.stream
      }

      let response
      
      // 根据模型类型调用不同的API
      if (model.serviceUrl) {
        // 本地AI服务
        response = await aiChatService.chatWithLocalService(
          model.serviceUrl,
          model.name,
          apiMessages,
          chatSettings
        )
      } else if (model.providerConfig) {
        // 云端AI服务
        response = await aiChatService.chatWithCloudService(
          model.providerConfig,
          model.name,
          apiMessages,
          chatSettings
        )
      } else {
        throw new Error('无法确定模型的服务类型')
      }

      return response.content
    } catch (error) {
      console.error('AI模型调用失败:', error)
      
      // 如果真实API调用失败，返回模拟响应以便测试
      const fallbackResponses = [
        `抱歉，${model.displayName} 暂时无法响应。这是一个测试回复。`,
        `${model.providerName} 服务连接失败，但这里是一个模拟响应：你的消息已收到。`,
        `API调用出现问题：${error.message}。这是一个备用响应。`
      ]
      
      return fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)]
    }
  }

  // 清空对话
  const clearChat = useCallback(() => {
    setMessages([])
    addLog('对话历史已清空')
  }, [addLog])

  // 复制消息
  const copyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      addLog('消息已复制到剪贴板')
    }).catch(() => {
      addLog('复制失败')
    })
  }, [addLog])

  // 导出对话
  const exportChat = useCallback(() => {
    const chatData = {
      model: selectedModel,
      settings: chatSettings,
      messages: messages,
      exportedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(chatData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `ai-chat-${selectedModel?.name || 'unknown'}-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    
    URL.revokeObjectURL(url)
    addLog('对话记录已导出')
  }, [selectedModel, chatSettings, messages, addLog])

  // 处理键盘事件
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }, [sendMessage])

  // 页面加载时自动加载模型
  useEffect(() => {
    loadAvailableModels()
  }, [loadAvailableModels])

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  // 渲染消息
  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user'
    const isError = !!message.error
    
    return (
      <div key={message.id} className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
        <div className={`max-w-[80%] ${isUser ? 'order-2' : 'order-1'}`}>
          <div className={`flex items-center space-x-2 mb-1 ${isUser ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex items-center space-x-1 text-xs text-muted-foreground`}>
              {isUser ? <User className="h-3 w-3" /> : <Bot className="h-3 w-3" />}
              <span>{isUser ? '用户' : (selectedModel?.displayName || 'AI助手')}</span>
              <span>{message.timestamp.toLocaleTimeString()}</span>
              {message.responseTime && (
                <>
                  <Clock className="h-3 w-3" />
                  <span>{message.responseTime}ms</span>
                </>
              )}
            </div>
          </div>
          
          <div className={`p-3 rounded-lg ${
            isUser 
              ? 'bg-primary text-primary-foreground' 
              : isError 
                ? 'bg-destructive/10 border border-destructive/20' 
                : 'bg-muted'
          }`}>
            <div className="whitespace-pre-wrap break-words">{message.content}</div>
            
            {!isUser && (
              <div className="flex items-center justify-end mt-2 space-x-1">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => copyMessage(message.content)}
                  className="h-6 px-2 text-xs"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <MessageCircle className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold">AI模型对话测试</h1>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
            className="flex items-center space-x-1"
          >
            <Settings className="h-4 w-4" />
            <span>设置</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={loadAvailableModels}
            disabled={isLoadingModels}
            className="flex items-center space-x-1"
          >
            {isLoadingModels ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            <span>刷新模型</span>
          </Button>
        </div>
      </div>

      {/* 模型选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <span>模型选择</span>
          </CardTitle>
          <CardDescription>
            选择要测试的AI模型，支持本地和云端模型
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="model-select">选择模型</Label>
              <Select
                value={selectedModel?.id || ''}
                onValueChange={(value) => {
                  const model = availableModels.find(m => m.id === value)
                  setSelectedModel(model || null)
                  if (model) {
                    addLog(`选择模型: ${model.displayName}`)
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder={isLoadingModels ? "加载中..." : "请选择模型"} />
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center space-x-2">
                        <span>{model.displayName}</span>
                        {model.isRecommended && (
                          <Badge variant="secondary" className="text-xs">推荐</Badge>
                        )}
                        {model.isPopular && (
                          <Badge variant="outline" className="text-xs">热门</Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>模型信息</Label>
              <div className="p-3 bg-muted rounded-md">
                {selectedModel ? (
                  <div className="space-y-1 text-sm">
                    <div><strong>名称:</strong> {selectedModel.name}</div>
                    <div><strong>提供商:</strong> {selectedModel.providerName}</div>
                    {selectedModel.description && (
                      <div><strong>描述:</strong> {selectedModel.description}</div>
                    )}
                    {selectedModel.capabilities && selectedModel.capabilities.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {selectedModel.capabilities.map((cap) => (
                          <Badge key={cap} variant="outline" className="text-xs">
                            {cap}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    {isLoadingModels ? '正在加载模型...' : '请选择一个模型'}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {availableModels.length === 0 && !isLoadingModels && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                未找到可用的AI模型。请确保：
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• 本地AI服务（如Ollama、LM Studio）正在运行</li>
                  <li>• 已配置云端AI提供商的API密钥</li>
                  <li>• 网络连接正常</li>
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 对话设置 */}
      {showSettings && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>对话设置</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="temperature">温度 (Temperature): {chatSettings.temperature}</Label>
                <input
                  id="temperature"
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={chatSettings.temperature}
                  onChange={(e) => setChatSettings(prev => ({
                    ...prev,
                    temperature: parseFloat(e.target.value)
                  }))}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground">
                  控制回复的随机性，0为确定性，2为高随机性
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="maxTokens">最大令牌数: {chatSettings.maxTokens}</Label>
                <input
                  id="maxTokens"
                  type="range"
                  min="100"
                  max="4000"
                  step="100"
                  value={chatSettings.maxTokens}
                  onChange={(e) => setChatSettings(prev => ({
                    ...prev,
                    maxTokens: parseInt(e.target.value)
                  }))}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground">
                  限制AI回复的最大长度
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="systemPrompt">系统提示词</Label>
              <Textarea
                id="systemPrompt"
                placeholder="输入系统提示词，定义AI的角色和行为..."
                value={chatSettings.systemPrompt}
                onChange={(e) => setChatSettings(prev => ({
                  ...prev,
                  systemPrompt: e.target.value
                }))}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* 对话区域 */}
      <Card className="flex flex-col h-[600px]">
        <CardHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <MessageCircle className="h-5 w-5" />
              <span>对话测试</span>
              {selectedModel && (
                <Badge variant="outline">{selectedModel.displayName}</Badge>
              )}
            </CardTitle>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={exportChat}
                disabled={messages.length === 0}
                className="flex items-center space-x-1"
              >
                <Download className="h-4 w-4" />
                <span>导出</span>
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={clearChat}
                disabled={messages.length === 0}
                className="flex items-center space-x-1"
              >
                <Trash2 className="h-4 w-4" />
                <span>清空</span>
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col p-0">
          {/* 消息列表 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.length === 0 ? (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>开始与AI模型对话吧！</p>
                  <p className="text-sm mt-2">选择一个模型，然后在下方输入消息</p>
                </div>
              </div>
            ) : (
              <>
                {messages.map(renderMessage)}
                <div ref={messagesEndRef} />
              </>
            )}
          </div>
          
          <Separator />
          
          {/* 输入区域 */}
          <div className="p-4">
            <div className="flex space-x-2">
              <Textarea
                ref={inputRef}
                placeholder={selectedModel ? "输入你的消息..." : "请先选择一个模型"}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={!selectedModel || isLoading}
                rows={2}
                className="flex-1 resize-none"
              />
              
              <Button
                onClick={sendMessage}
                disabled={!selectedModel || !inputMessage.trim() || isLoading}
                className="flex items-center space-x-2 px-6"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                <span>{isLoading ? '发送中...' : '发送'}</span>
              </Button>
            </div>
            
            {selectedModel && (
              <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                <div>
                  按 Enter 发送，Shift+Enter 换行
                </div>
                <div className="flex items-center space-x-4">
                  <span>温度: {chatSettings.temperature}</span>
                  <span>最大令牌: {chatSettings.maxTokens}</span>
                  <span>消息数: {messages.length}</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 测试日志 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5" />
              <span>测试日志</span>
            </CardTitle>
            <Button variant="outline" size="sm" onClick={() => setLogs([])}>
              清空日志
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-4 rounded-md max-h-40 overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <div className="text-muted-foreground">暂无日志</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Alert>
        <Zap className="h-4 w-4" />
        <AlertDescription>
          <strong>使用说明:</strong>
          <ul className="mt-2 space-y-1 text-sm">
            <li>• 首先点击"刷新模型"加载可用的AI模型</li>
            <li>• 选择要测试的模型，支持本地和云端模型</li>
            <li>• 在设置中调整对话参数，如温度和系统提示词</li>
            <li>• 在对话框中输入消息进行测试</li>
            <li>• 可以导出对话记录进行分析</li>
          </ul>
        </AlertDescription>
      </Alert>
    </div>
  )
}

export default AIModelChatTest