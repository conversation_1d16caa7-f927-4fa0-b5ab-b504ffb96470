// 收藏标签颜色演示组件

import React from 'react'
import BookmarkRow from '../components/BookmarkRow'
import BookmarkCompact from '../components/BookmarkCompact'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import type { Bookmark } from '../types'

/**
 * 创建演示用的收藏数据
 */
const createDemoBookmarks = (): Bookmark[] => {
  const now = new Date()
  
  return [
    {
      id: '1',
      type: 'url',
      title: 'React 官方文档',
      url: 'https://react.dev',
      description: 'React 官方文档，学习现代 React 开发',
      tags: ['React', 'JavaScript', '前端开发', '文档'],
      category: '技术文档',
      favicon: 'https://react.dev/favicon.ico',
      metadata: {
        pageTitle: 'React',
        siteName: 'React',
        aiGenerated: false
      },
      createdAt: now,
      updatedAt: now
    },
    {
      id: '2',
      type: 'url',
      title: 'Vue.js 指南',
      url: 'https://vuejs.org',
      description: 'Vue.js 渐进式 JavaScript 框架',
      tags: ['Vue', 'JavaScript', '前端开发'],
      category: '技术文档',
      favicon: 'https://vuejs.org/logo.svg',
      metadata: {
        pageTitle: 'Vue.js',
        siteName: 'Vue.js',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 86400000), // 1天前
      updatedAt: new Date(now.getTime() - 86400000)
    },
    {
      id: '3',
      type: 'text',
      title: '学习笔记：TypeScript 高级类型',
      content: 'TypeScript 高级类型包括联合类型、交叉类型、条件类型等...',
      description: 'TypeScript 高级类型学习笔记',
      tags: ['TypeScript', 'JavaScript', '学习笔记', '类型系统'],
      category: '学习资料',
      metadata: {
        wordCount: 1500,
        language: 'zh-CN',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 172800000), // 2天前
      updatedAt: new Date(now.getTime() - 172800000)
    },
    {
      id: '4',
      type: 'url',
      title: 'Node.js 最佳实践',
      url: 'https://nodejs.org/en/docs/guides/',
      description: 'Node.js 开发最佳实践和指南',
      tags: ['Node.js', 'JavaScript', '后端开发', '最佳实践'],
      category: '技术文档',
      favicon: 'https://nodejs.org/static/images/favicons/favicon.ico',
      metadata: {
        pageTitle: 'Node.js Guides',
        siteName: 'Node.js',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 259200000), // 3天前
      updatedAt: new Date(now.getTime() - 259200000)
    },
    {
      id: '5',
      type: 'url',
      title: 'CSS Grid 完整指南',
      url: 'https://css-tricks.com/snippets/css/complete-guide-grid/',
      description: 'CSS Grid 布局的完整指南和示例',
      tags: ['CSS', 'Grid', '前端开发', '布局', '响应式设计'],
      category: '技术文档',
      favicon: 'https://css-tricks.com/favicon.ico',
      metadata: {
        pageTitle: 'A Complete Guide to Grid',
        siteName: 'CSS-Tricks',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 345600000), // 4天前
      updatedAt: new Date(now.getTime() - 345600000)
    }
  ]
}

/**
 * 收藏标签颜色演示组件
 * 展示在收藏管理中标签颜色的显示效果
 */
const BookmarkTagColorDemo: React.FC = () => {
  const demoBookmarks = createDemoBookmarks()

  const handleEdit = (bookmark: Bookmark) => {
    console.log('编辑收藏:', bookmark.title)
  }

  const handleDelete = (bookmark: Bookmark) => {
    console.log('删除收藏:', bookmark.title)
  }

  const handleClick = (bookmark: Bookmark) => {
    console.log('点击收藏:', bookmark.title)
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">收藏标签颜色演示</h1>
        <p className="text-muted-foreground mb-4">
          展示在收藏管理中标签颜色的显示效果，标签颜色与标签管理中的颜色保持一致
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">功能特点：</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 标签显示与标签管理中设置的颜色一致</li>
            <li>• 支持标签颜色的边框和背景色显示</li>
            <li>• 超过显示限制的标签在悬停时显示完整列表</li>
            <li>• 没有设置颜色的标签使用默认灰色</li>
          </ul>
        </div>
      </div>

      <div className="space-y-8">
        {/* BookmarkRow 组件演示 */}
        <Card>
          <CardHeader>
            <CardTitle>BookmarkRow 组件 - 行视图</CardTitle>
            <CardDescription>
              单行显示模式，标签以紧凑形式显示，最多显示2个标签，其余标签在悬停时显示
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {demoBookmarks.map((bookmark) => (
                <BookmarkRow
                  key={bookmark.id}
                  bookmark={bookmark}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onClick={handleClick}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* BookmarkCompact 组件演示 */}
        <Card>
          <CardHeader>
            <CardTitle>BookmarkCompact 组件 - 紧凑视图</CardTitle>
            <CardDescription>
              紧凑卡片显示模式，标签在底部信息栏显示，最多显示2个标签，其余标签在悬停时显示
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {demoBookmarks.map((bookmark) => (
                <BookmarkCompact
                  key={bookmark.id}
                  bookmark={bookmark}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onClick={handleClick}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 高亮状态演示 */}
        <Card>
          <CardHeader>
            <CardTitle>高亮状态演示</CardTitle>
            <CardDescription>
              展示收藏项目在高亮状态下的标签颜色显示效果
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">高亮的行视图</h4>
                <BookmarkRow
                  bookmark={demoBookmarks[0]}
                  isHighlighted={true}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onClick={handleClick}
                />
              </div>
              <div>
                <h4 className="font-medium mb-2">高亮的紧凑视图</h4>
                <div className="max-w-md">
                  <BookmarkCompact
                    bookmark={demoBookmarks[1]}
                    isHighlighted={true}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    onClick={handleClick}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 标签颜色说明 */}
        <Card>
          <CardHeader>
            <CardTitle>标签颜色说明</CardTitle>
            <CardDescription>
              标签颜色的显示规则和视觉效果说明
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">颜色显示规则</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 标签边框使用标签设置的颜色</li>
                  <li>• 标签背景使用标签颜色的15%透明度</li>
                  <li>• 标签文字使用标签设置的颜色</li>
                  <li>• 未设置颜色的标签使用默认灰色 (#6B7280)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">交互效果</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 超过显示限制的标签显示为 "+N" 形式</li>
                  <li>• 悬停在 "+N" 上会显示完整的标签列表</li>
                  <li>• 悬停提示中的标签显示颜色圆点和名称</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default BookmarkTagColorDemo