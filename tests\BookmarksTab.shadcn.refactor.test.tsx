import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import BookmarksTab from '../src/components/BookmarksTab'

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// @ts-ignore
global.chrome = mockChrome

// Mock hooks
vi.mock('../src/hooks/useViewMode', () => ({
  useViewMode: () => ({
    viewMode: 'list',
    setViewMode: vi.fn(),
    isLoading: false
  })
}))

vi.mock('../src/utils/layoutStability', () => ({
  useViewSwitchStability: () => ({
    containerRef: { current: null },
    displayView: 'list',
    isTransitioning: false
  }),
  useScrollPositionLock: () => ({
    lockScrollPosition: vi.fn()
  })
}))

vi.mock('../src/hooks/useAdvancedSearch', () => ({
  useAdvancedSearch: () => ({
    query: '',
    setQuery: vi.fn(),
    results: [],
    suggestions: [],
    isSearching: false,
    searchTime: 0,
    addFilter: vi.fn(),
    clearFilters: vi.fn()
  })
}))

// Mock components
vi.mock('../src/components/BookmarkEditModal', () => ({
  default: ({ isOpen }: { isOpen: boolean }) => 
    isOpen ? <div data-testid="bookmark-edit-modal">编辑收藏模态</div> : null
}))

vi.mock('../src/components/AddBookmarkModal', () => ({
  default: ({ isOpen }: { isOpen: boolean }) => 
    isOpen ? <div data-testid="add-bookmark-modal">添加收藏模态</div> : null
}))

vi.mock('../src/components/DeleteConfirmModal', () => ({
  default: ({ isOpen }: { isOpen: boolean }) => 
    isOpen ? <div data-testid="delete-confirm-modal">删除确认模态</div> : null
}))

vi.mock('../src/components/ViewModeSelector', () => ({
  default: ({ currentMode, onModeChange }: { currentMode: string, onModeChange: (mode: string) => void }) => (
    <div data-testid="view-mode-selector">
      <button onClick={() => onModeChange('list')}>列表视图</button>
      <button onClick={() => onModeChange('grid')}>网格视图</button>
    </div>
  )
}))

vi.mock('../src/components/VirtualBookmarkList', () => ({
  default: ({ bookmarks }: { bookmarks: any[] }) => (
    <div data-testid="virtual-bookmark-list">
      {bookmarks.map((bookmark, index) => (
        <div key={index} data-testid={`bookmark-item-${index}`}>
          {bookmark.title}
        </div>
      ))}
    </div>
  )
}))

describe('BookmarksTab - shadcn组件重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock successful bookmark loading
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: [
        {
          id: '1',
          title: '测试收藏1',
          url: 'https://example.com',
          category: '技术',
          tags: ['React', 'TypeScript'],
          description: '测试描述'
        },
        {
          id: '2',
          title: '测试收藏2',
          url: 'https://example2.com',
          category: '学习',
          tags: ['Vue', 'JavaScript'],
          description: '测试描述2'
        }
      ]
    })
  })

  it('应该渲染shadcn Card组件作为主容器', async () => {
    render(<BookmarksTab />)
    
    // 等待数据加载完成
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证Card组件的存在（通过其特有的类名）
    const cardElement = document.querySelector('.card')
    expect(cardElement).toBeInTheDocument()
  })

  it('应该使用shadcn Input组件作为搜索框', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证搜索输入框
    const searchInput = screen.getByPlaceholderText('搜索收藏...')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveClass('flex', 'h-10', 'w-full') // shadcn Input的典型类名
  })

  it('应该使用shadcn Select组件作为分类筛选器', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证Select组件的触发器
    const selectTrigger = document.querySelector('[role="combobox"]')
    expect(selectTrigger).toBeInTheDocument()
    expect(selectTrigger).toHaveClass('flex', 'h-10', 'w-full') // shadcn Select的典型类名
  })

  it('应该使用shadcn Button组件替换所有操作按钮', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证添加收藏按钮
    const addButton = screen.getByText('添加收藏')
    expect(addButton).toBeInTheDocument()
    expect(addButton).toHaveClass('inline-flex', 'items-center', 'justify-center') // shadcn Button的典型类名
    
    // 验证刷新按钮
    const refreshButton = screen.getByText('刷新')
    expect(refreshButton).toBeInTheDocument()
    expect(refreshButton).toHaveClass('inline-flex', 'items-center', 'justify-center')
  })

  it('应该正确处理搜索输入', async () => {
    const mockSetQuery = vi.fn()
    
    // 重新mock useAdvancedSearch以包含setQuery函数
    vi.mocked(require('../src/hooks/useAdvancedSearch').useAdvancedSearch).mockReturnValue({
      query: '',
      setQuery: mockSetQuery,
      results: [],
      suggestions: [],
      isSearching: false,
      searchTime: 0,
      addFilter: vi.fn(),
      clearFilters: vi.fn()
    })
    
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    const searchInput = screen.getByPlaceholderText('搜索收藏...')
    fireEvent.change(searchInput, { target: { value: '测试搜索' } })
    
    expect(mockSetQuery).toHaveBeenCalledWith('测试搜索')
  })

  it('应该正确处理分类筛选', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 点击Select触发器
    const selectTrigger = document.querySelector('[role="combobox"]')
    if (selectTrigger) {
      fireEvent.click(selectTrigger)
    }
    
    // 验证Select组件的交互
    expect(selectTrigger).toBeInTheDocument()
  })

  it('应该显示正确的加载状态', () => {
    // Mock loading state
    mockChrome.runtime.sendMessage.mockImplementation(() => new Promise(() => {}))
    
    render(<BookmarksTab />)
    
    // 验证加载状态
    expect(screen.getByText('加载收藏数据中...')).toBeInTheDocument()
    
    // 验证加载动画
    const loadingSpinner = document.querySelector('.animate-spin')
    expect(loadingSpinner).toBeInTheDocument()
  })

  it('应该显示空状态时的正确UI', async () => {
    // Mock empty data
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })
    
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.getByText('暂无收藏内容')).toBeInTheDocument()
    })
    
    expect(screen.getByText('开始收藏您感兴趣的网页和内容吧！')).toBeInTheDocument()
  })

  it('应该正确处理添加收藏按钮点击', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    const addButton = screen.getByText('添加收藏')
    fireEvent.click(addButton)
    
    // 验证添加模态是否显示
    await waitFor(() => {
      expect(screen.getByTestId('add-bookmark-modal')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn主题系统的颜色类', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证使用了shadcn的颜色系统
    const titleElement = screen.getByText('收藏管理')
    expect(titleElement.parentElement).toHaveClass('text-2xl') // shadcn CardTitle的典型类名
    
    const descriptionElement = screen.getByText('管理您的收藏内容，支持搜索、分类和多种视图模式')
    expect(descriptionElement).toHaveClass('text-sm', 'text-muted-foreground') // shadcn CardDescription的典型类名
  })

  it('应该正确集成ViewModeSelector组件', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证ViewModeSelector的存在
    expect(screen.getByTestId('view-mode-selector')).toBeInTheDocument()
    expect(screen.getByText('列表视图')).toBeInTheDocument()
    expect(screen.getByText('网格视图')).toBeInTheDocument()
  })

  it('应该正确处理Chrome API错误', async () => {
    // Mock API error
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: false,
      error: '获取数据失败'
    })
    
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证空状态显示
    expect(screen.getByText('暂无收藏内容')).toBeInTheDocument()
  })
})