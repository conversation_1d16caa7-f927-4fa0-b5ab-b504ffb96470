/**
 * DeepSeek AI提供商服务测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'
import { AIProviderConfig } from '../src/types/ai'

// 模拟fetch
global.fetch = vi.fn()

describe('AIProviderService - DeepSeek集成', () => {
  let service: AIProviderService
  const mockFetch = global.fetch as ReturnType<typeof vi.fn>

  beforeEach(() => {
    service = new AIProviderService()
    mockFetch.mockClear()
  })

  describe('testDeepSeekConnection', () => {
    const baseUrl = 'https://api.deepseek.com'
    const apiKey = 'sk-test-key'

    it('应该成功测试DeepSeek连接', async () => {
      // 模拟成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            { id: 'deepseek-chat', object: 'model' },
            { id: 'deepseek-reasoner', object: 'model' }
          ]
        })
      } as Response)

      const result = await service.testDeepSeekConnection(baseUrl, apiKey)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(result.error).toBeUndefined()

      // 验证API调用
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.deepseek.com/models',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Authorization': 'Bearer sk-test-key',
            'Content-Type': 'application/json'
          }
        })
      )
    })

    it('应该处理空API密钥错误', async () => {
      const result = await service.testDeepSeekConnection(baseUrl, '')

      expect(result.success).toBe(false)
      expect(result.error).toBe('DeepSeek API密钥不能为空')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理401未授权错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await service.testDeepSeekConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥无效或已过期')
    })

    it('应该处理403权限不足错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      } as Response)

      const result = await service.testDeepSeekConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥权限不足')
    })

    it('应该处理429频率限制错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response)

      const result = await service.testDeepSeekConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API请求频率超限，请稍后重试')
    })

    it('应该处理500服务器错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const result = await service.testDeepSeekConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('DeepSeek服务器错误，请稍后重试')
    })

    it('应该处理网络连接超时', async () => {
      const abortError = new Error('AbortError')
      abortError.name = 'AbortError'
      mockFetch.mockRejectedValueOnce(abortError)

      const result = await service.testDeepSeekConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('连接超时，请检查网络连接')
    })

    it('应该处理DNS解析失败', async () => {
      mockFetch.mockRejectedValueOnce(new Error('ENOTFOUND api.deepseek.com'))

      const result = await service.testDeepSeekConnection(baseUrl, apiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('DNS解析失败，请检查网络连接')
    })

    it('应该正确处理带/v1的baseUrl', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: [] })
      } as Response)

      await service.testDeepSeekConnection('https://api.deepseek.com/v1', apiKey)

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.deepseek.com/v1/models',
        expect.any(Object)
      )
    })

    it('应该正确处理末尾有斜杠的baseUrl', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: [] })
      } as Response)

      await service.testDeepSeekConnection('https://api.deepseek.com/', apiKey)

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.deepseek.com/models',
        expect.any(Object)
      )
    })
  })

  describe('getDeepSeekModels', () => {
    it('应该返回DeepSeek模型列表', async () => {
      const models = await service.getDeepSeekModels()

      expect(models).toHaveLength(2)
      
      // 验证deepseek-chat模型
      const chatModel = models.find(m => m.id === 'deepseek-chat')
      expect(chatModel).toBeDefined()
      expect(chatModel?.name).toBe('deepseek-chat')
      expect(chatModel?.displayName).toBe('DeepSeek Chat')
      expect(chatModel?.description).toContain('DeepSeek-V3-0324')
      expect(chatModel?.capabilities).toContain('chat')
      expect(chatModel?.capabilities).toContain('completion')
      expect(chatModel?.capabilities).toContain('reasoning')
      expect(chatModel?.tags).toContain('DeepSeek')
      expect(chatModel?.tags).toContain('对话')
      expect(chatModel?.providerId).toBe('deepseek')
      expect(chatModel?.isRecommended).toBe(true)
      expect(chatModel?.isPopular).toBe(true)
      expect(chatModel?.maxTokens).toBe(64000)
      expect(chatModel?.contextLength).toBe(64000)

      // 验证deepseek-reasoner模型
      const reasonerModel = models.find(m => m.id === 'deepseek-reasoner')
      expect(reasonerModel).toBeDefined()
      expect(reasonerModel?.name).toBe('deepseek-reasoner')
      expect(reasonerModel?.displayName).toBe('DeepSeek Reasoner')
      expect(reasonerModel?.description).toContain('DeepSeek-R1-0528')
      expect(reasonerModel?.capabilities).toContain('reasoning')
      expect(reasonerModel?.capabilities).toContain('math')
      expect(reasonerModel?.capabilities).toContain('logic')
      expect(reasonerModel?.tags).toContain('推理')
      expect(reasonerModel?.tags).toContain('数学')
      expect(reasonerModel?.providerId).toBe('deepseek')
      expect(reasonerModel?.isRecommended).toBe(true)
      expect(reasonerModel?.isPopular).toBe(false)
    })

    it('应该在出错时返回空数组', async () => {
      // 模拟控制台错误输出
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // 模拟getDeepSeekModels内部出错（虽然当前实现不会出错）
      const originalConsoleLog = console.log
      console.log = vi.fn(() => {
        throw new Error('测试错误')
      })

      const models = await service.getDeepSeekModels()

      expect(models).toEqual([])
      
      // 恢复console.log
      console.log = originalConsoleLog
      consoleSpy.mockRestore()
    })
  })

  describe('集成测试', () => {
    it('应该能够通过AIProviderService.testConnection调用DeepSeek测试', async () => {
      const config: AIProviderConfig = {
        id: 'deepseek-test',
        name: 'DeepSeek Test',
        type: 'deepseek',
        baseUrl: 'https://api.deepseek.com',
        apiKey: 'sk-test-key',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            { id: 'deepseek-chat', object: 'model' }
          ]
        })
      } as Response)

      const result = await service.testConnection(config)

      expect(result.success).toBe(true)
      expect(result.providerId).toBe('deepseek-test')
      expect(result.modelCount).toBe(1)
      expect(result.responseTime).toBeGreaterThan(0)
      expect(result.testedAt).toBeInstanceOf(Date)
    })

    it('应该能够通过AIProviderService.getModels调用DeepSeek模型获取', async () => {
      const config: AIProviderConfig = {
        id: 'deepseek-test',
        name: 'DeepSeek Test',
        type: 'deepseek',
        baseUrl: 'https://api.deepseek.com',
        apiKey: 'sk-test-key',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await service.getModels(config)

      expect(models).toHaveLength(2)
      expect(models[0].providerId).toBe('deepseek')
      expect(models[1].providerId).toBe('deepseek')
    })
  })
})