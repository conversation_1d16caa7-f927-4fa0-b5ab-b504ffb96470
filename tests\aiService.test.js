// AI服务单元测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { aiService } from '../src/services/aiService'
import { aiConfigService } from '../src/services/aiConfigService'

// Mock AI配置服务
vi.mock('../src/services/aiConfigService', () => ({
  aiConfigService: {
    getConfig: vi.fn()
  }
}))

// Mock fetch API
global.fetch = vi.fn()

describe('AIService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // 重置统计信息
    aiService.resetStats()
    aiService.clearErrorLog()
  })

  describe('generateTags', () => {
    const mockConfig = {
      provider: 'openai',
      baseUrl: 'https://api.openai.com/v1',
      apiKey: 'test-key',
      model: 'gpt-3.5-turbo',
      isConnected: true,
      autoTagging: true,
      temperature: 0.7,
      maxTokens: 1000,
      timeout: 30000
    }

    beforeEach(() => {
      aiConfigService.getConfig.mockResolvedValue(mockConfig)
    })

    it('应该成功生成标签', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '技术, 编程, JavaScript, 前端开发, Web开发'
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '这是一篇关于JavaScript前端开发的技术文章',
        title: 'JavaScript开发指南',
        maxTags: 5
      }

      const result = await aiService.generateTags(request)

      expect(result.tags).toEqual(['技术', '编程', 'JavaScript', '前端开发', 'Web开发'])
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.processingTime).toBeGreaterThan(0)
      expect(fetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-key',
            'Content-Type': 'application/json'
          })
        })
      )
    })

    it('应该处理AI服务未连接的情况', async () => {
      aiConfigService.getConfig.mockResolvedValue({
        ...mockConfig,
        isConnected: false
      })

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateTags(request)
      
      // 应该使用降级策略
      expect(result.tags).toEqual(['收藏', '资源'])
      expect(result.confidence).toBe(0.6)
      expect(result.reasoning).toContain('本地规则生成标签')
    })

    it('应该处理自动标签功能关闭的情况', async () => {
      aiConfigService.getConfig.mockResolvedValue({
        ...mockConfig,
        autoTagging: false
      })

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateTags(request)
      
      // 应该使用降级策略
      expect(result.tags).toEqual(['收藏', '资源'])
      expect(result.confidence).toBe(0.6)
      expect(result.reasoning).toContain('本地规则生成标签')
    })

    it('应该处理API调用失败', async () => {
      fetch.mockRejectedValue(new Error('网络错误'))

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateTags(request)
      
      // 应该使用降级策略
      expect(result.tags).toEqual(['收藏', '资源'])
      expect(result.confidence).toBe(0.6)
      expect(result.reasoning).toContain('本地规则生成标签')

      // 检查错误统计
      const stats = aiService.getStats()
      expect(stats.failedRequests).toBe(1)
      expect(stats.errorRate).toBe(1)
    })

    it('应该处理HTTP错误响应', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        text: () => Promise.resolve('Unauthorized')
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateTags(request)
      
      // 应该使用降级策略
      expect(result.tags).toEqual(['收藏', '资源'])
      expect(result.confidence).toBe(0.6)
      expect(result.reasoning).toContain('本地规则生成标签')
    })

    it('应该正确解析标签响应', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '标签: 技术，编程，JavaScript，前端开发，Web开发'
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateTags(request)

      expect(result.tags).toEqual(['技术', '编程', 'JavaScript', '前端开发', 'Web开发'])
    })

    it('应该处理空响应', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: ''
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateTags(request)

      expect(result.tags).toEqual(['未分类'])
    })

    it('应该更新统计信息', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '技术, 编程'
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '测试内容'
      }

      await aiService.generateTags(request)

      const stats = aiService.getStats()
      expect(stats.totalRequests).toBe(1)
      expect(stats.successfulRequests).toBe(1)
      expect(stats.tagGenerationCount).toBe(1)
      expect(stats.averageResponseTime).toBeGreaterThan(0)
    })
  })

  describe('generateCategory', () => {
    const mockConfig = {
      provider: 'openai',
      baseUrl: 'https://api.openai.com/v1',
      apiKey: 'test-key',
      model: 'gpt-3.5-turbo',
      isConnected: true,
      autoCategories: true
    }

    beforeEach(() => {
      aiConfigService.getConfig.mockResolvedValue(mockConfig)
    })

    it('应该成功生成分类建议', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '主分类: 技术文档\n备选分类: 编程教程, 开发指南, 技术博客\n推荐理由: 内容主要介绍编程技术'
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '这是一篇关于JavaScript的技术文档',
        title: 'JavaScript开发指南'
      }

      const result = await aiService.generateCategory(request)

      expect(result.category).toBe('技术文档')
      expect(result.alternatives).toEqual([
        { category: '编程教程', confidence: 0.8 },
        { category: '开发指南', confidence: 0.7 },
        { category: '技术博客', confidence: 0.6 }
      ])
      expect(result.reasoning).toBe('内容主要介绍编程技术')
    })

    it('应该处理自动分类功能关闭的情况', async () => {
      aiConfigService.getConfig.mockResolvedValue({
        ...mockConfig,
        autoCategories: false
      })

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateCategory(request)
      
      // 应该使用降级策略
      expect(result.category).toBe('默认分类')
      expect(result.confidence).toBe(0.6)
      expect(result.reasoning).toContain('本地规则生成分类')
    })

    it('应该处理解析失败返回默认分类', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '无效的响应格式'
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateCategory(request)

      expect(result.category).toBe('默认分类')
      expect(result.confidence).toBe(0.9)
    })
  })

  describe('generateDescription', () => {
    const mockConfig = {
      provider: 'openai',
      baseUrl: 'https://api.openai.com/v1',
      apiKey: 'test-key',
      model: 'gpt-3.5-turbo',
      isConnected: true,
      autoDescription: true
    }

    beforeEach(() => {
      aiConfigService.getConfig.mockResolvedValue(mockConfig)
    })

    it('应该成功生成描述', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '这是一篇详细介绍JavaScript前端开发技术的文章，涵盖了基础语法、框架使用和最佳实践。'
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '这是一篇关于JavaScript前端开发的技术文章...',
        title: 'JavaScript开发指南',
        maxLength: 100,
        style: 'brief'
      }

      const result = await aiService.generateDescription(request)

      expect(result.description).toContain('JavaScript前端开发')
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.wordCount).toBeGreaterThan(0)
    })

    it('应该处理自动描述功能关闭的情况', async () => {
      aiConfigService.getConfig.mockResolvedValue({
        ...mockConfig,
        autoDescription: false
      })

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateDescription(request)
      
      // 应该使用降级策略
      expect(result.description).toBe('这是一个收藏的资源，暂无详细描述。')
      expect(result.confidence).toBe(0.5)
      expect(result.wordCount).toBe(17)
    })

    it('应该限制描述长度', async () => {
      const longDescription = '这是一个非常长的描述'.repeat(50)
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: longDescription
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const request = {
        content: '测试内容'
      }

      const result = await aiService.generateDescription(request)

      expect(result.description.length).toBeLessThanOrEqual(503) // 500 + '...'
    })
  })

  describe('processBatch', () => {
    const mockConfig = {
      provider: 'openai',
      baseUrl: 'https://api.openai.com/v1',
      apiKey: 'test-key',
      model: 'gpt-3.5-turbo',
      isConnected: true,
      autoTagging: true,
      autoCategories: true,
      autoDescription: true
    }

    beforeEach(() => {
      aiConfigService.getConfig.mockResolvedValue(mockConfig)
    })

    it('应该处理批量请求', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '技术, 编程'
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      const batchRequest = {
        id: 'batch-1',
        items: [
          {
            id: 'item-1',
            type: 'tags',
            data: {
              content: '测试内容1'
            }
          },
          {
            id: 'item-2',
            type: 'tags',
            data: {
              content: '测试内容2'
            }
          }
        ],
        priority: 'normal',
        createdAt: new Date()
      }

      const result = await aiService.processBatch(batchRequest)

      expect(result.id).toBe('batch-1')
      expect(result.results).toHaveLength(2)
      expect(result.results[0].success).toBe(true)
      expect(result.results[1].success).toBe(true)
      expect(result.totalProcessingTime).toBeGreaterThan(0)
    })

    it('应该处理批量请求中的错误', async () => {
      // 第一个请求成功，第二个失败
      fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{ message: { content: '技术, 编程' } }]
          })
        })
        .mockRejectedValueOnce(new Error('网络错误'))

      const batchRequest = {
        id: 'batch-1',
        items: [
          {
            id: 'item-1',
            type: 'tags',
            data: { content: '测试内容1' }
          },
          {
            id: 'item-2',
            type: 'tags',
            data: { content: '测试内容2' }
          }
        ],
        priority: 'normal',
        createdAt: new Date()
      }

      const result = await aiService.processBatch(batchRequest)

      expect(result.results[0].success).toBe(true)
      expect(result.results[1].success).toBe(true) // 降级策略使其成功
      expect(result.results[1].data.tags).toEqual(['收藏', '资源']) // 降级结果
    })

    it('应该支持不同类型的批量请求', async () => {
      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: '测试响应' } }]
        })
      })

      const batchRequest = {
        id: 'batch-1',
        items: [
          {
            id: 'item-1',
            type: 'tags',
            data: { content: '测试内容' }
          },
          {
            id: 'item-2',
            type: 'category',
            data: { content: '测试内容' }
          },
          {
            id: 'item-3',
            type: 'description',
            data: { content: '测试内容' }
          }
        ],
        priority: 'normal',
        createdAt: new Date()
      }

      const result = await aiService.processBatch(batchRequest)

      expect(result.results).toHaveLength(3)
      expect(result.results.every(r => r.success)).toBe(true)
    })
  })

  describe('统计和错误处理', () => {
    it('应该正确记录统计信息', () => {
      const initialStats = aiService.getStats()
      
      expect(initialStats.totalRequests).toBe(0)
      expect(initialStats.successfulRequests).toBe(0)
      expect(initialStats.failedRequests).toBe(0)
      expect(initialStats.errorRate).toBe(0)
    })

    it('应该记录错误日志', async () => {
      aiConfigService.getConfig.mockResolvedValue({
        provider: 'openai',
        isConnected: true,
        autoTagging: true
      })
      
      fetch.mockRejectedValue(new Error('测试错误'))

      try {
        await aiService.generateTags({ content: '测试' })
      } catch (error) {
        // 预期的错误
      }

      const errorLog = aiService.getErrorLog()
      expect(errorLog).toHaveLength(1)
      expect(errorLog[0].message).toBe('测试错误')
      expect(errorLog[0].operation).toBe('generateTags')
    })

    it('应该清空错误日志', async () => {
      // 先产生一个错误
      aiConfigService.getConfig.mockResolvedValue({
        provider: 'openai',
        isConnected: true,
        autoTagging: true
      })
      
      fetch.mockRejectedValue(new Error('测试错误'))

      try {
        await aiService.generateTags({ content: '测试' })
      } catch (error) {
        // 预期的错误
      }

      expect(aiService.getErrorLog()).toHaveLength(1)

      aiService.clearErrorLog()
      expect(aiService.getErrorLog()).toHaveLength(0)
    })

    it('应该重置统计信息', async () => {
      // 先产生一些统计数据
      aiConfigService.getConfig.mockResolvedValue({
        provider: 'openai',
        isConnected: true,
        autoTagging: true
      })
      
      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: '测试' } }]
        })
      })

      await aiService.generateTags({ content: '测试' })

      expect(aiService.getStats().totalRequests).toBe(1)

      aiService.resetStats()
      expect(aiService.getStats().totalRequests).toBe(0)
    })
  })

  describe('不同AI提供商的支持', () => {
    it('应该支持Claude API调用', async () => {
      const claudeConfig = {
        provider: 'claude',
        baseUrl: 'https://api.anthropic.com/v1',
        apiKey: 'test-key',
        model: 'claude-3-sonnet-20240229',
        isConnected: true,
        autoTagging: true
      }

      aiConfigService.getConfig.mockResolvedValue(claudeConfig)

      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          content: [{ text: '技术, 编程' }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      await aiService.generateTags({ content: '测试内容' })

      expect(fetch).toHaveBeenCalledWith(
        'https://api.anthropic.com/v1/messages',
        expect.objectContaining({
          headers: expect.objectContaining({
            'x-api-key': 'test-key',
            'anthropic-version': '2023-06-01'
          })
        })
      )
    })

    it('应该支持Gemini API调用', async () => {
      const geminiConfig = {
        provider: 'gemini',
        baseUrl: 'https://generativelanguage.googleapis.com/v1',
        apiKey: 'test-key',
        model: 'gemini-pro',
        isConnected: true,
        autoTagging: true
      }

      aiConfigService.getConfig.mockResolvedValue(geminiConfig)

      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          candidates: [{
            content: {
              parts: [{ text: '技术, 编程' }]
            }
          }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      await aiService.generateTags({ content: '测试内容' })

      expect(fetch).toHaveBeenCalledWith(
        'https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent?key=test-key',
        expect.objectContaining({
          method: 'POST'
        })
      )
    })

    it('应该支持本地模型API调用', async () => {
      const localConfig = {
        provider: 'local',
        baseUrl: 'http://localhost:11434/v1',
        model: 'llama2',
        isConnected: true,
        autoTagging: true
      }

      aiConfigService.getConfig.mockResolvedValue(localConfig)

      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: '技术, 编程' } }]
        })
      }
      fetch.mockResolvedValue(mockResponse)

      await aiService.generateTags({ content: '测试内容' })

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:11434/v1/chat/completions',
        expect.objectContaining({
          method: 'POST'
        })
      )
    })
  })
})