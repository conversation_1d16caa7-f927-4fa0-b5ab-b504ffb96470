# 需求文档

## 介绍

Universe Bag（乾坤袋）是一个全功能的Chrome浏览器扩展，专门用于收藏和管理各种资源。它允许用户收藏网址、文字摘录等各种内容，通过AI自动分类和标签化，支持去重功能，提供管理界面，并可与Notion等云端服务同步备份。

## 需求

### 需求 1 - 内容收藏功能

**用户故事：** 作为用户，我希望能够快速收藏网页链接和文字摘录，以便保存有价值的信息。

#### 验收标准

1. 当用户在任何网页上右键点击时，系统应显示"收藏链接"选项
2. 当用户选中网页文字后右键点击时，系统应显示"收藏摘录"选项
3. 当用户点击浏览器工具栏的扩展图标时，系统应显示下拉菜单，包含收藏选项和功能开关
4. 当用户收藏内容时，系统应自动捕获页面标题、URL、时间戳和选中的文字内容
5. 当收藏操作完成时，系统应显示成功提示
6. 当当前网页已被收藏时，系统应在插件图标上显示对勾标识以区分收藏状态

### 需求 2 - AI智能分类和标签

**用户故事：** 作为用户，我希望收藏的内容能够自动分类和打标签，以便更好地组织和查找。

#### 验收标准

1. 当用户在二级收藏界面点击AI辅助生成时，系统应调用AI服务分析页面内容
2. 当AI分析完成时，系统应在二级界面中显示建议的标签和分类
3. 当用户接受AI建议时，系统应将建议的标签和分类填入对应字段
4. 当内容具有相似主题时，系统应建议将其归类到现有分类中
5. 当用户不满意AI建议时，系统应允许在二级界面中手动编辑标签和分类
6. 当用户选择自动模式时，系统应在后台自动为收藏内容生成标签（无需二级界面确认）

### 需求 3 - 内容去重功能

**用户故事：** 作为用户，我希望系统能够识别和处理重复的收藏内容，以保持收藏库的整洁。

#### 验收标准

1. 当用户尝试收藏已存在的URL时，系统应检测到重复并提示用户
2. 当检测到重复内容时，系统应显示现有收藏的详情
3. 当用户确认收藏重复内容时，系统应允许用户选择合并或创建新条目
4. 当文字摘录内容相似度超过80%时，系统应提示可能的重复
5. 当用户选择合并重复内容时，系统应保留所有相关信息和标签

### 需求 4 - 管理编辑界面

**用户故事：** 作为用户，我希望有一个完整的管理界面来查看、编辑和组织我的收藏内容。

#### 验收标准

1. 当用户点击扩展图标下拉菜单中的"管理"选项时，系统应打开管理页面
2. 当管理页面加载时，系统应显示所有收藏内容的列表视图
3. 当用户点击任意收藏项时，系统应显示详细信息和编辑选项
4. 当用户修改标签或分类时，系统应实时保存更改
5. 当用户搜索内容时，系统应支持按标题、标签、内容进行模糊搜索
6. 当用户选择多个项目时，系统应支持批量操作（删除、修改标签等）
7. 当管理页面显示时，系统应提供现代化的UI界面设计
8. 当用户访问分类管理时，系统应允许创建、编辑、删除分类文件夹
9. 当用户访问标签管理时，系统应显示所有标签的使用统计和管理选项
10. 当用户访问AI设置时，系统应提供大模型接入配置选项
11. 当用户点击去重检测时，系统应扫描所有收藏内容并识别重复项
12. 当检测到重复内容时，系统应显示重复列表并提供合并、删除等操作选项
13. 当用户点击链接检测时，系统应验证所有收藏链接的可访问性
14. 当检测到无效链接时，系统应标记并提供修复或删除建议
15. 当用户进行批量操作时，系统应支持多选、全选、按条件筛选等功能
16. 当用户执行批量操作时，系统应提供批量删除、批量修改标签、批量移动分类等选项
17. 当管理页面显示收藏内容时，系统应提供多种视图模式（列表、简单列表、详细列表、卡片、画册等）
18. 当用户切换视图模式时，系统应保存用户偏好并在下次访问时应用
19. 当收藏网页资源时，系统应自动抓取并缓存网站的favicon图标
20. 当显示收藏内容时，系统应使用缓存的图标来美化界面展示
21. 当图标抓取失败时，系统应使用默认图标或根据网站类型显示相应图标

### 需求 5 - 云端同步备份

**用户故事：** 作为用户，我希望能够将收藏内容备份到云端服务，并在不同设备间同步。

#### 验收标准

1. 当用户首次使用时，系统应提供Notion等云端服务的连接选项
2. 当用户授权云端服务后，系统应能够上传收藏数据
3. 当在新设备上安装扩展时，系统应能够从云端恢复收藏数据
4. 当本地数据发生变化时，系统应自动同步到云端
5. 当云端数据更新时，系统应能够检测并同步到本地
6. 当同步冲突发生时，系统应提供冲突解决选项

### 需求 6 - 数据安全和隐私

**用户故事：** 作为用户，我希望我的收藏数据是安全的，并且我能控制数据的使用方式。

#### 验收标准

1. 当用户收藏敏感内容时，系统应提供本地加密存储选项
2. 当与AI服务通信时，系统应使用安全的API调用
3. 当用户选择云端备份时，系统应明确说明数据使用政策
4. 当用户要求删除数据时，系统应完全清除本地和云端数据
5. 当扩展更新时，系统应保持数据完整性和兼容性

### 需求 7 - 插件界面设计

**用户故事：** 作为用户，我希望插件有一个清晰直观的下拉菜单界面，方便快速访问各种功能。

#### 验收标准

1. 当用户点击插件图标时，系统应显示一个下拉菜单面板
2. 当下拉菜单显示时，系统应包含"收藏当前页面"、"收藏选中文字"等快捷操作
3. 当下拉菜单显示时，系统应包含功能开关选项（如自动标签、去重检测等）
4. 当下拉菜单显示时，系统应包含"管理收藏"、"设置"、"同步状态"等导航选项
8. 当用户点击下拉菜单中的收藏选项时，系统应显示二级收藏界面
9. 当二级收藏界面显示时，系统应包含标签输入、注释编辑、分类选择等选项
10. 当二级收藏界面显示时，系统应提供"AI辅助生成"按钮来自动生成标签和分类
11. 当用户点击AI辅助生成时，系统应分析页面内容并建议相关标签和分类
5. 当用户在菜单外点击时，系统应自动关闭下拉菜单
6. 当当前页面已被收藏时，系统应在插件图标上显示视觉标识（如对勾）
7. 当页面收藏状态改变时，系统应实时更新插件图标的视觉状态

### 需求 8 - 页面内浮窗功能

**用户故事：** 作为用户，我希望能够在网页内直接访问收藏功能，而不需要每次都点击工具栏图标。

#### 验收标准

1. 当用户在设置中启用浮窗功能时，系统应在每个网页上显示可拖拽的微型图标
2. 当微型图标显示时，系统应允许用户拖拽到页面的任意位置
3. 当用户点击微型图标时，系统应展开为完整的弹窗菜单
4. 当浮窗菜单展开时，系统应显示与工具栏插件相同的界面和功能
5. 当用户在浮窗外点击时，系统应自动收起浮窗为微型图标
6. 当用户在设置中关闭浮窗功能时，系统应隐藏所有页面上的微型图标
7. 当微型图标显示时，系统应确保不遮挡页面重要内容且具有适当的透明度
8. 当当前页面已被收藏且启用信息显示功能时，系统应显示小型信息浮窗
9. 当信息浮窗显示时，系统应包含当前页面的收藏简介、标签、所属文件夹信息
10. 当信息浮窗显示时，系统应显示用户添加的批注内容
11. 当信息浮窗显示时，系统应推荐与当前页面相关的其他收藏内容
12. 当用户点击相关推荐时，系统应能够快速跳转到推荐的收藏页面

### 需求 9 - 导入导出和分享功能

**用户故事：** 作为用户，我希望能够导入导出收藏数据，并能够分享特定的收藏集合给其他人。

#### 验收标准

1. 当用户在管理页面点击导出时，系统应提供导出选项（全部、选中分类、选中项目）
2. 当用户选择导出格式时，系统应支持JSON、HTML、CSV等格式
3. 当用户导出时，系统应提供是否包含批注的选项
4. 当用户点击导入时，系统应支持从JSON文件、浏览器书签、其他收藏工具导入
5. 当用户选择远程导入时，系统应支持从云端服务（如Notion、Airtable等）导入数据
6. 当用户选择分享功能时，系统应允许选择特定分类或标签的内容进行分享
7. 当用户生成分享时，系统应创建可分享的链接或导出为JSON文件
8. 当其他用户接收分享时，系统应支持一键导入分享的收藏内容
9. 当用户选择海报分享时，系统应为单个资源生成美观的海报图片
10. 当生成海报时，系统应包含资源缩略图、标题、简介、标签等信息
11. 当生成海报时，系统应添加Universe Bag插件的水印和推广信息
12. 当生成海报时，系统应包含二维码用于快速导入该资源
13. 当用户扫描海报二维码时，系统应能够识别并导入对应的收藏资源
14. 当用户保存海报时，系统应支持多种图片格式（PNG、JPG等）

### 需求 10 - 设置和配置管理

**用户故事：** 作为用户，我希望有完善的设置选项来个性化我的收藏体验。

#### 验收标准

1. 当用户访问设置页面时，系统应提供界面主题、语言等基础设置
2. 当用户配置AI功能时，系统应提供本地模型和在线模型的选择选项
3. 当用户选择在线模型时，系统应提供常用服务商的预设Base URL（如OpenAI、Claude、国内大模型等）
4. 当用户配置API设置时，系统应提供API密钥输入和自定义Base URL选项
5. 当用户完成AI配置时，系统应提供连接测试按钮验证配置是否正确
6. 当连接测试通过时，系统应自动获取并显示可用的模型列表供用户选择
7. 当用户选择本地模型时，系统应提供本地模型路径配置和连接测试功能
8. 当AI配置完成时，系统应提供自动标签生成、智能分类等功能开关
3. 当用户配置浮窗功能时，系统应提供显示位置、透明度、自动隐藏等设置
4. 当用户配置同步功能时，系统应提供同步频率、冲突处理策略等选项
5. 当用户配置隐私设置时，系统应提供数据加密、匿名统计等选项
6. 当用户修改设置时，系统应实时保存并应用更改
7. 当用户需要重置时，系统应提供恢复默认设置的选项

### 需求 11 - 用户体验优化

**用户故事：** 作为用户，我希望扩展使用起来快速、直观，不影响我的正常浏览体验。

#### 验收标准

1. 当扩展加载时，系统应在2秒内完成初始化
2. 当用户执行收藏操作时，系统应在1秒内完成响应
3. 当右键菜单显示时，系统应只在相关上下文中显示收藏选项
4. 当扩展运行时，系统应占用最少的浏览器资源
5. 当网络连接不稳定时，系统应能够离线工作并稍后同步