# 导入导出管理功能 API 文档

## 概述

本文档详细介绍了Universe Bag扩展导入导出管理功能的API接口、类型定义和使用方法。

## 核心服务类

### ImportExportManagerService

主要的导入导出管理服务，提供完整的数据导入导出功能。

#### 方法

##### exportAllData(options: ExportAllOptions): Promise<string>

导出所有数据（收藏夹、分类、标签）。

**参数:**
- `options`: 导出选项配置

**返回值:**
- `Promise<string>`: JSON格式的导出数据

**示例:**
```typescript
const service = new ImportExportManagerService();
const data = await service.exportAllData({
  format: 'json',
  includeBookmarks: true,
  includeCategories: true,
  includeTags: true,
  includeMetadata: true,
  dateRange: {
    start: '2024-01-01',
    end: '2024-12-31'
  }
});
```

##### exportCategories(options: ExportCategoriesOptions): Promise<string>

导出分类数据。

**参数:**
- `options`: 分类导出选项

**返回值:**
- `Promise<string>`: JSON格式的分类数据

**示例:**
```typescript
const data = await service.exportCategories({
  format: 'json',
  includeHierarchy: true,
  includeStatistics: true,
  categoryIds: ['cat1', 'cat2'] // 可选，指定导出的分类
});
```

##### exportTags(options: ExportTagsOptions): Promise<string>

导出标签数据。

**参数:**
- `options`: 标签导出选项

**返回值:**
- `Promise<string>`: JSON格式的标签数据

**示例:**
```typescript
const data = await service.exportTags({
  format: 'json',
  includeUsageStats: true,
  includeRelatedBookmarks: false,
  tagIds: ['tag1', 'tag2'] // 可选，指定导出的标签
});
```

##### importData(data: ImportData, options: ImportOptions): Promise<ImportResult>

导入数据并处理冲突。

**参数:**
- `data`: 导入的数据
- `options`: 导入选项

**返回值:**
- `Promise<ImportResult>`: 导入结果，包含冲突信息

**示例:**
```typescript
const result = await service.importData(importData, {
  conflictResolution: 'prompt', // 'auto', 'prompt', 'skip'
  validateData: true,
  batchSize: 100
});

if (result.conflicts.length > 0) {
  // 处理冲突
  console.log('发现冲突:', result.conflicts);
}
```

### ConflictResolverService

冲突检测和解决服务。

#### 方法

##### detectConflicts(existingData: any[], importData: any[], type: string): ConflictItem[]

检测数据冲突。

**参数:**
- `existingData`: 现有数据
- `importData`: 导入数据
- `type`: 数据类型 ('bookmarks', 'categories', 'tags')

**返回值:**
- `ConflictItem[]`: 冲突项列表

**示例:**
```typescript
const resolver = new ConflictResolverService();
const conflicts = resolver.detectConflicts(
  existingBookmarks,
  importBookmarks,
  'bookmarks'
);
```

##### resolveConflicts(conflicts: ConflictItem[], resolutions: ConflictResolution[]): any[]

解决冲突。

**参数:**
- `conflicts`: 冲突项列表
- `resolutions`: 解决方案列表

**返回值:**
- `any[]`: 解决后的数据

**示例:**
```typescript
const resolutions: ConflictResolution[] = [
  {
    conflictId: 'conflict1',
    action: 'merge',
    mergeStrategy: 'smart'
  },
  {
    conflictId: 'conflict2',
    action: 'keep_existing'
  }
];

const resolvedData = resolver.resolveConflicts(conflicts, resolutions);
```

##### calculateSimilarity(item1: any, item2: any, type: string): number

计算两个数据项的相似度。

**参数:**
- `item1`: 第一个数据项
- `item2`: 第二个数据项
- `type`: 数据类型

**返回值:**
- `number`: 相似度分数 (0-1)

**示例:**
```typescript
const similarity = resolver.calculateSimilarity(
  bookmark1,
  bookmark2,
  'bookmarks'
);
console.log(`相似度: ${similarity * 100}%`);
```

### ValidationUtils

数据验证工具类。

#### 方法

##### validateImportData(data: any): ValidationResult

验证导入数据的格式和完整性。

**参数:**
- `data`: 待验证的导入数据

**返回值:**
- `ValidationResult`: 验证结果

**示例:**
```typescript
const result = ValidationUtils.validateImportData(importData);
if (!result.isValid) {
  console.error('数据验证失败:', result.errors);
}
```

##### validateExportOptions(options: any, type: string): ValidationResult

验证导出选项。

**参数:**
- `options`: 导出选项
- `type`: 导出类型

**返回值:**
- `ValidationResult`: 验证结果

**示例:**
```typescript
const result = ValidationUtils.validateExportOptions(options, 'all');
if (!result.isValid) {
  console.error('选项验证失败:', result.errors);
}
```

##### checkDataIntegrity(data: any): ValidationResult

检查数据完整性。

**参数:**
- `data`: 待检查的数据

**返回值:**
- `ValidationResult`: 检查结果

**示例:**
```typescript
const result = ValidationUtils.checkDataIntegrity(data);
if (!result.isValid) {
  console.warn('数据完整性问题:', result.errors);
}
```

## 类型定义

### 导出相关类型

#### ExportAllOptions

```typescript
interface ExportAllOptions {
  format: 'json';
  includeBookmarks: boolean;
  includeCategories: boolean;
  includeTags: boolean;
  includeMetadata: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}
```

#### ExportCategoriesOptions

```typescript
interface ExportCategoriesOptions {
  format: 'json';
  includeHierarchy: boolean;
  includeStatistics: boolean;
  categoryIds?: string[];
}
```

#### ExportTagsOptions

```typescript
interface ExportTagsOptions {
  format: 'json';
  includeUsageStats: boolean;
  includeRelatedBookmarks: boolean;
  tagIds?: string[];
}
```

### 导入相关类型

#### ImportData

```typescript
interface ImportData {
  version: string;
  exportDate: string;
  exportType: 'all' | 'bookmarks' | 'categories' | 'tags';
  bookmarks?: Bookmark[];
  categories?: Category[];
  tags?: Tag[];
  metadata?: ImportMetadata;
}
```

#### ImportOptions

```typescript
interface ImportOptions {
  conflictResolution: 'auto' | 'prompt' | 'skip';
  validateData: boolean;
  batchSize: number;
  onProgress?: (progress: ImportProgress) => void;
}
```

#### ImportResult

```typescript
interface ImportResult {
  success: boolean;
  imported: {
    bookmarks: number;
    categories: number;
    tags: number;
  };
  conflicts: ConflictItem[];
  errors: ValidationError[];
  summary: string;
}
```

### 冲突处理类型

#### ConflictItem

```typescript
interface ConflictItem {
  id: string;
  type: 'bookmark' | 'category' | 'tag';
  conflictType: 'duplicate' | 'similar' | 'name_conflict' | 'data_mismatch';
  existing: any;
  imported: any;
  similarity?: number;
  conflictFields: string[];
  suggestedResolution: ConflictResolutionAction;
}
```

#### ConflictResolution

```typescript
interface ConflictResolution {
  conflictId: string;
  action: ConflictResolutionAction;
  mergeStrategy?: 'smart' | 'prefer_existing' | 'prefer_imported';
  manualData?: any;
}
```

#### ConflictResolutionAction

```typescript
type ConflictResolutionAction = 
  | 'keep_existing'
  | 'use_imported'
  | 'merge'
  | 'manual_edit'
  | 'skip';
```

### 验证相关类型

#### ValidationResult

```typescript
interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}
```

#### ValidationError

```typescript
interface ValidationError {
  field: string;
  message: string;
  code: string;
}
```

## 性能优化类

### MemoryOptimizedProcessor

内存优化处理器，用于处理大数据量。

#### 方法

##### processBatch<T>(items: T[], processor: (batch: T[]) => Promise<void>): Promise<void>

分批处理数据。

**参数:**
- `items`: 待处理的数据项
- `processor`: 批处理函数

**示例:**
```typescript
const processor = new MemoryOptimizedProcessor();
await processor.processBatch(largeDataSet, async (batch) => {
  // 处理每个批次
  await processDataBatch(batch);
});
```

### CacheManager

缓存管理器，提供智能缓存功能。

#### 方法

##### set<T>(key: string, value: T, ttl?: number): void

设置缓存项。

**参数:**
- `key`: 缓存键
- `value`: 缓存值
- `ttl`: 生存时间（可选）

##### get<T>(key: string): T | null

获取缓存项。

**参数:**
- `key`: 缓存键

**返回值:**
- `T | null`: 缓存值或null

**示例:**
```typescript
const cache = new CacheManager();
cache.set('conflicts', conflictData, 300000); // 5分钟TTL
const cached = cache.get<ConflictItem[]>('conflicts');
```

## 错误处理

### ErrorRecoveryService

错误恢复服务，提供自动重试和错误处理功能。

#### 方法

##### withRetry<T>(operation: () => Promise<T>, options?: RetryOptions): Promise<T>

带重试的操作执行。

**参数:**
- `operation`: 要执行的操作
- `options`: 重试选项

**返回值:**
- `Promise<T>`: 操作结果

**示例:**
```typescript
const recovery = new ErrorRecoveryService();
const result = await recovery.withRetry(
  () => importLargeDataSet(data),
  {
    maxRetries: 3,
    backoffStrategy: 'exponential',
    retryCondition: (error) => error.code === 'NETWORK_ERROR'
  }
);
```

## 安全验证

### SecurityValidator

安全验证器，提供数据安全检查功能。

#### 方法

##### validateFile(file: File): Promise<ValidationResult>

验证上传文件的安全性。

**参数:**
- `file`: 上传的文件

**返回值:**
- `Promise<ValidationResult>`: 验证结果

**示例:**
```typescript
const validator = new SecurityValidator();
const result = await validator.validateFile(uploadedFile);
if (!result.isValid) {
  console.error('文件安全检查失败:', result.errors);
}
```

## 使用示例

### 完整的导入导出流程

```typescript
import { 
  ImportExportManagerService,
  ConflictResolverService,
  ValidationUtils 
} from './services';

// 1. 创建服务实例
const importExportService = new ImportExportManagerService();
const conflictResolver = new ConflictResolverService();

// 2. 导出数据
const exportOptions = {
  format: 'json' as const,
  includeBookmarks: true,
  includeCategories: true,
  includeTags: true,
  includeMetadata: true
};

const exportedData = await importExportService.exportAllData(exportOptions);

// 3. 验证导入数据
const importData = JSON.parse(importedFileContent);
const validationResult = ValidationUtils.validateImportData(importData);

if (!validationResult.isValid) {
  console.error('数据验证失败:', validationResult.errors);
  return;
}

// 4. 导入数据并处理冲突
const importResult = await importExportService.importData(importData, {
  conflictResolution: 'prompt',
  validateData: true,
  batchSize: 100,
  onProgress: (progress) => {
    console.log(`导入进度: ${progress.percentage}%`);
  }
});

// 5. 处理冲突（如果有）
if (importResult.conflicts.length > 0) {
  const resolutions = await showConflictResolutionDialog(importResult.conflicts);
  const resolvedData = conflictResolver.resolveConflicts(
    importResult.conflicts,
    resolutions
  );
  
  // 继续导入解决后的数据
  await importExportService.applyResolvedData(resolvedData);
}

console.log('导入完成:', importResult.summary);
```

## 最佳实践

### 1. 性能优化

- 对于大数据量，使用分批处理
- 启用缓存以提高重复操作的性能
- 使用Web Workers处理CPU密集型任务

### 2. 错误处理

- 始终验证导入数据
- 使用错误恢复服务处理网络错误
- 提供详细的错误信息给用户

### 3. 用户体验

- 提供实时的进度反馈
- 实现冲突解决的预览功能
- 支持操作的撤销和重做

### 4. 安全性

- 验证所有上传的文件
- 对导入数据进行脱敏处理
- 实施适当的权限控制

## 版本兼容性

当前API版本: `1.0.0`

支持的数据格式版本:
- `1.0`: 基础导入导出格式
- `1.1`: 增强的冲突检测格式
- `1.2`: 完整的元数据支持格式

## 更新日志

### v1.0.0 (2025-08-01)
- 初始版本发布
- 完整的导入导出功能
- 智能冲突检测和解决
- 性能优化和安全验证
- 全面的单元测试覆盖

---

如需更多信息或技术支持，请参考项目文档或联系开发团队。