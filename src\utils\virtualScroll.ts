// 虚拟滚动管理器

import { useEffect, useState, useRef, useCallback, useMemo } from 'react'

// 虚拟滚动配置接口
export interface VirtualScrollConfig {
  /** 容器高度 */
  containerHeight: number
  /** 项目高度（固定高度模式） */
  itemHeight?: number
  /** 动态高度计算函数 */
  getItemHeight?: (index: number, item: any) => number
  /** 缓冲区大小（渲染额外的项目数量） */
  overscan?: number
  /** 滚动节流延迟 */
  scrollThrottle?: number
}

// 虚拟滚动状态
export interface VirtualScrollState {
  /** 开始索引 */
  startIndex: number
  /** 结束索引 */
  endIndex: number
  /** 可见项目 */
  visibleItems: any[]
  /** 总高度 */
  totalHeight: number
  /** 偏移量 */
  offsetY: number
}

// 项目位置信息
interface ItemPosition {
  index: number
  top: number
  height: number
  bottom: number
}

/**
 * 虚拟滚动管理器类
 * 管理大列表的虚拟滚动，支持固定和动态高度
 */
export class VirtualScrollManager {
  private config: VirtualScrollConfig
  private scrollTop: number = 0
  private positions: ItemPosition[] = []
  private estimatedItemHeight: number = 50
  private lastMeasuredIndex: number = -1

  constructor(config: VirtualScrollConfig) {
    this.config = {
      overscan: 5,
      scrollThrottle: 16,
      ...config
    }
    this.estimatedItemHeight = config.itemHeight || 50
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<VirtualScrollConfig>) {
    this.config = { ...this.config, ...config }
    if (config.itemHeight) {
      this.estimatedItemHeight = config.itemHeight
    }
  }

  /**
   * 初始化位置信息
   */
  initPositions(itemCount: number) {
    this.positions = []
    for (let i = 0; i < itemCount; i++) {
      this.positions[i] = {
        index: i,
        height: this.estimatedItemHeight,
        top: i * this.estimatedItemHeight,
        bottom: (i + 1) * this.estimatedItemHeight
      }
    }
    this.lastMeasuredIndex = itemCount - 1
  }

  /**
   * 获取项目高度
   */
  private getItemHeight(index: number, item?: any): number {
    if (this.config.itemHeight) {
      return this.config.itemHeight
    }
    if (this.config.getItemHeight && item) {
      return this.config.getItemHeight(index, item)
    }
    return this.estimatedItemHeight
  }

  /**
   * 更新项目高度
   */
  updateItemHeight(index: number, height: number) {
    const oldHeight = this.positions[index]?.height || this.estimatedItemHeight
    const deltaHeight = height - oldHeight

    if (deltaHeight !== 0) {
      // 更新当前项目
      if (this.positions[index]) {
        this.positions[index].height = height
        this.positions[index].bottom = this.positions[index].top + height
      }

      // 更新后续项目的位置
      for (let i = index + 1; i < this.positions.length; i++) {
        this.positions[i].top += deltaHeight
        this.positions[i].bottom += deltaHeight
      }

      // 更新最后测量的索引
      this.lastMeasuredIndex = Math.max(this.lastMeasuredIndex, index)
    }
  }

  /**
   * 获取项目位置
   */
  private getItemPosition(index: number): ItemPosition {
    if (index > this.lastMeasuredIndex) {
      const lastMeasuredPosition = this.positions[this.lastMeasuredIndex] || {
        top: 0,
        bottom: 0,
        height: 0,
        index: -1
      }

      let top = lastMeasuredPosition.bottom

      for (let i = this.lastMeasuredIndex + 1; i <= index; i++) {
        const height = this.estimatedItemHeight
        this.positions[i] = {
          index: i,
          height,
          top,
          bottom: top + height
        }
        top += height
      }

      this.lastMeasuredIndex = index
    }

    return this.positions[index]
  }

  /**
   * 计算可见范围
   */
  calculateVisibleRange(items: any[]): VirtualScrollState {
    const { containerHeight, overscan = 5 } = this.config
    const itemCount = items.length

    if (itemCount === 0) {
      return {
        startIndex: 0,
        endIndex: 0,
        visibleItems: [],
        totalHeight: 0,
        offsetY: 0
      }
    }

    // 确保位置信息已初始化
    if (this.positions.length !== itemCount) {
      this.initPositions(itemCount)
      
      // 如果有动态高度计算函数，重新计算所有位置
      if (this.config.getItemHeight) {
        let top = 0
        for (let i = 0; i < itemCount; i++) {
          const height = this.getItemHeight(i, items[i])
          this.positions[i] = {
            index: i,
            height,
            top,
            bottom: top + height
          }
          top += height
        }
      }
    }

    // 二分查找开始索引
    let startIndex = this.findStartIndex(this.scrollTop)
    let endIndex = this.findEndIndex(this.scrollTop + containerHeight, startIndex)

    // 应用缓冲区
    startIndex = Math.max(0, startIndex - overscan)
    endIndex = Math.min(itemCount - 1, endIndex + overscan)

    // 获取可见项目
    const visibleItems = items.slice(startIndex, endIndex + 1)

    // 计算总高度
    const totalHeight = this.getTotalHeight(itemCount)

    // 计算偏移量
    const offsetY = startIndex > 0 ? this.getItemPosition(startIndex).top : 0

    return {
      startIndex,
      endIndex,
      visibleItems,
      totalHeight,
      offsetY
    }
  }

  /**
   * 二分查找开始索引
   */
  private findStartIndex(scrollTop: number): number {
    let low = 0
    let high = this.positions.length - 1

    while (low <= high) {
      const mid = Math.floor((low + high) / 2)
      const position = this.getItemPosition(mid)

      if (position.bottom <= scrollTop) {
        low = mid + 1
      } else if (position.top > scrollTop) {
        high = mid - 1
      } else {
        return mid
      }
    }

    return Math.max(0, low)
  }

  /**
   * 查找结束索引
   */
  private findEndIndex(scrollBottom: number, startIndex: number): number {
    for (let i = startIndex; i < this.positions.length; i++) {
      const position = this.getItemPosition(i)
      if (position.top >= scrollBottom) {
        return Math.max(startIndex, i - 1)
      }
    }
    return this.positions.length - 1
  }

  /**
   * 获取总高度
   */
  private getTotalHeight(itemCount: number): number {
    if (itemCount === 0) return 0

    const lastPosition = this.getItemPosition(itemCount - 1)
    return lastPosition.bottom
  }

  /**
   * 更新滚动位置
   */
  updateScrollTop(scrollTop: number) {
    this.scrollTop = Math.max(0, scrollTop)
  }

  /**
   * 滚动到指定索引
   */
  scrollToIndex(index: number): number {
    if (index < 0 || index >= this.positions.length) {
      return this.scrollTop
    }

    const position = this.getItemPosition(index)
    return position.top
  }

  /**
   * 获取当前滚动位置
   */
  getScrollTop(): number {
    return this.scrollTop
  }
}

/**
 * 虚拟滚动 Hook
 */
export function useVirtualScroll<T>(
  items: T[],
  config: VirtualScrollConfig
) {
  const [scrollTop, setScrollTop] = useState(0)
  const managerRef = useRef<VirtualScrollManager>()
  const containerRef = useRef<HTMLDivElement>(null)

  // 初始化管理器
  if (!managerRef.current) {
    managerRef.current = new VirtualScrollManager(config)
  }

  // 更新配置
  useEffect(() => {
    managerRef.current?.updateConfig(config)
  }, [config])

  // 计算可见状态
  const virtualState = useMemo(() => {
    if (!managerRef.current) {
      return {
        startIndex: 0,
        endIndex: 0,
        visibleItems: [],
        totalHeight: 0,
        offsetY: 0
      }
    }

    managerRef.current.updateScrollTop(scrollTop)
    return managerRef.current.calculateVisibleRange(items)
  }, [items, scrollTop, config])

  // 滚动事件处理
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = event.currentTarget.scrollTop
    setScrollTop(newScrollTop)
  }, [])

  // 项目高度更新
  const updateItemHeight = useCallback((index: number, height: number) => {
    managerRef.current?.updateItemHeight(index, height)
  }, [])

  // 滚动到指定索引
  const scrollToIndex = useCallback((index: number) => {
    if (managerRef.current && containerRef.current) {
      const scrollTop = managerRef.current.scrollToIndex(index)
      containerRef.current.scrollTop = scrollTop
      setScrollTop(scrollTop)
    }
  }, [])

  return {
    containerRef,
    virtualState,
    handleScroll,
    updateItemHeight,
    scrollToIndex
  }
}

/**
 * 项目高度测量 Hook
 */
export function useItemHeight(
  index: number,
  updateItemHeight: (index: number, height: number) => void
) {
  const itemRef = useRef<HTMLDivElement>(null)
  const [height, setHeight] = useState<number>()

  useEffect(() => {
    if (itemRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const newHeight = entry.contentRect.height
          if (newHeight !== height) {
            setHeight(newHeight)
            updateItemHeight(index, newHeight)
          }
        }
      })

      resizeObserver.observe(itemRef.current)

      // 初始测量
      const initialHeight = itemRef.current.offsetHeight
      if (initialHeight > 0 && initialHeight !== height) {
        setHeight(initialHeight)
        updateItemHeight(index, initialHeight)
      }

      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [index, height, updateItemHeight])

  return { itemRef, height }
}

/**
 * 节流函数
 */
function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  let lastExecTime = 0

  return (...args: Parameters<T>) => {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      func(...args)
      lastExecTime = currentTime
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = setTimeout(() => {
        func(...args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}