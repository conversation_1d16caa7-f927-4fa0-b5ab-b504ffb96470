/**
 * 关于页面数据配置
 * 包含扩展的基本信息和开发者信息
 */

export interface ExtensionInfo {
  name: string
  version: string
  description: string
  developer: string
  website?: string
  email?: string
  license?: string
  buildDate?: string
}

export interface AboutPageData {
  extensionInfo: ExtensionInfo
  buildInfo: {
    buildDate: string
    buildVersion: string
    manifestVersion?: number
  }
  developerInfo: {
    name: string
    website?: string
    email?: string
    communityUrl?: string
  }
  licenseInfo: {
    type: string
    text?: string
    url?: string
  }
}

// 默认的关于页面数据
export const defaultAboutData: AboutPageData = {
  extensionInfo: {
    name: 'Universe Bag（乾坤袋）',
    version: '1.0.0',
    description: '智能收藏管理工具，支持AI自动分类和云端同步',
    developer: 'coffeebean'
  },
  buildInfo: {
    buildDate: new Date().toISOString().split('T')[0],
    buildVersion: '1.0.0',
    manifestVersion: 3
  },
  developerInfo: {
    name: 'coffeebean',
    website: 'https://www.obsidian.vip',
    communityUrl: 'https://obsidian.vip/zh/documentation/community.html'
  },
  licenseInfo: {
    type: '',
    text: '',
    url: ''
  }
}