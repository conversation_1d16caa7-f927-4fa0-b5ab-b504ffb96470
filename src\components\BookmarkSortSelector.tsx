// 收藏排序选择器组件

import React from 'react'

/**
 * 排序选项类型
 */
export type BookmarkSortOption = 
  | 'created-desc'    // 创建时间降序（最新在前）
  | 'created-asc'     // 创建时间升序（最旧在前）
  | 'updated-desc'    // 更新时间降序（最近更新在前）
  | 'updated-asc'     // 更新时间升序（最久未更新在前）
  | 'title-asc'       // 标题升序（A-Z）
  | 'title-desc'      // 标题降序（Z-A）
  | 'category-asc'    // 分类升序
  | 'category-desc'   // 分类降序

/**
 * 排序选择器组件属性
 */
interface BookmarkSortSelectorProps {
  /** 当前排序选项 */
  value: BookmarkSortOption
  /** 排序选项变更回调 */
  onChange: (sortOption: BookmarkSortOption) => void
  /** 自定义CSS类名 */
  className?: string
  /** 是否禁用 */
  disabled?: boolean
}



/**
 * 收藏排序选择器组件
 * 提供多种排序方式选择，默认按创建时间降序排列
 * 参考标签管理页面的简洁实现
 */
const BookmarkSortSelector: React.FC<BookmarkSortSelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false
}) => {
  // 处理选择变化
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value as BookmarkSortOption)
  }

  return (
    <select
      value={value}
      onChange={handleChange}
      disabled={disabled}
      className={`px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${className}`}
    >
      <option value="created-desc">最新添加</option>
      <option value="created-asc">最早添加</option>
      <option value="updated-desc">最近更新</option>
      <option value="updated-asc">最久未更新</option>
      <option value="title-asc">标题 A-Z</option>
      <option value="title-desc">标题 Z-A</option>
      <option value="category-asc">分类 A-Z</option>
      <option value="category-desc">分类 Z-A</option>
    </select>
  )
}

export default BookmarkSortSelector

// 导出类型定义
export type { BookmarkSortSelectorProps }