/**
 * 测试弹出窗口二级菜单功能
 * 验证已收藏状态下的UI显示和操作功能
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🧪 开始测试弹出窗口二级菜单功能...\n')

/**
 * 测试1: 验证PopupApp组件的已收藏状态显示
 */
function testBookmarkedStateDisplay() {
  console.log('📋 测试1: 验证已收藏状态显示...')
  
  try {
    // 读取PopupApp组件文件
    const popupAppPath = path.join(__dirname, 'src/popup/PopupApp.tsx')
    const popupAppContent = fs.readFileSync(popupAppPath, 'utf8')
    
    // 检查关键功能是否存在
    const checks = [
      {
        name: '已收藏状态提示',
        found: popupAppContent.includes('已收藏')
      },
      {
        name: '编辑收藏按钮',
        found: popupAppContent.includes('编辑收藏')
      },
      {
        name: '在管理页面打开按钮',
        found: popupAppContent.includes('在管理页面打开')
      },
      {
        name: 'handleOpenManagementWithBookmark函数',
        found: popupAppContent.includes('handleOpenManagementWithBookmark')
      },
      {
        name: 'Star图标填充效果',
        found: popupAppContent.includes('fill-current')
      }
    ]
    
    let passedChecks = 0
    checks.forEach(check => {
      if (check.found) {
        console.log(`  ✅ ${check.name}: 通过`)
        passedChecks++
      } else {
        console.log(`  ❌ ${check.name}: 失败`)
      }
    })
    
    console.log(`  📊 通过率: ${passedChecks}/${checks.length} (${Math.round(passedChecks/checks.length*100)}%)\n`)
    return passedChecks === checks.length
    
  } catch (error) {
    console.error('  ❌ 测试失败:', error.message)
    return false
  }
}

/**
 * 测试2: 验证OptionsApp组件的高亮功能
 */
function testOptionsAppHighlight() {
  console.log('📋 测试2: 验证管理页面高亮功能...')
  
  try {
    // 读取OptionsApp组件文件
    const optionsAppPath = path.join(__dirname, 'src/options/OptionsApp.tsx')
    const optionsAppContent = fs.readFileSync(optionsAppPath, 'utf8')
    
    // 检查关键功能是否存在
    const checks = [
      {
        name: 'highlightBookmarkId状态',
        found: optionsAppContent.includes('highlightBookmarkId')
      },
      {
        name: 'checkHighlightParameter函数',
        found: optionsAppContent.includes('checkHighlightParameter')
      },
      {
        name: 'URL参数解析',
        found: optionsAppContent.includes('URLSearchParams')
      },
      {
        name: '高亮样式应用',
        found: optionsAppContent.includes('border-primary-500 bg-primary-50')
      }
    ]
    
    let passedChecks = 0
    checks.forEach(check => {
      if (check.found) {
        console.log(`  ✅ ${check.name}: 通过`)
        passedChecks++
      } else {
        console.log(`  ❌ ${check.name}: 失败`)
      }
    })
    
    console.log(`  📊 通过率: ${passedChecks}/${checks.length} (${Math.round(passedChecks/checks.length*100)}%)\n`)
    return passedChecks === checks.length
    
  } catch (error) {
    console.error('  ❌ 测试失败:', error.message)
    return false
  }
}

/**
 * 测试3: 验证需求文档更新
 */
function testRequirementsUpdate() {
  console.log('📋 测试3: 验证需求文档更新...')
  
  try {
    // 读取需求文档
    const requirementsPath = path.join(__dirname, '.kiro/specs/bookmark-ui-fixes/requirements.md')
    const requirementsContent = fs.readFileSync(requirementsPath, 'utf8')
    
    // 检查需求4是否存在
    const checks = [
      {
        name: '需求4存在',
        found: requirementsContent.includes('### 需求 4')
      },
      {
        name: '弹出窗口状态显示需求',
        found: requirementsContent.includes('弹出窗口中当页面已被收藏时')
      },
      {
        name: '编辑按钮需求',
        found: requirementsContent.includes('提供"编辑"按钮')
      },
      {
        name: '管理页面打开需求',
        found: requirementsContent.includes('在管理页面打开')
      }
    ]
    
    let passedChecks = 0
    checks.forEach(check => {
      if (check.found) {
        console.log(`  ✅ ${check.name}: 通过`)
        passedChecks++
      } else {
        console.log(`  ❌ ${check.name}: 失败`)
      }
    })
    
    console.log(`  📊 通过率: ${passedChecks}/${checks.length} (${Math.round(passedChecks/checks.length*100)}%)\n`)
    return passedChecks === checks.length
    
  } catch (error) {
    console.error('  ❌ 测试失败:', error.message)
    return false
  }
}

/**
 * 测试4: 验证任务列表更新
 */
function testTasksUpdate() {
  console.log('📋 测试4: 验证任务列表更新...')
  
  try {
    // 读取任务文档
    const tasksPath = path.join(__dirname, '.kiro/specs/bookmark-ui-fixes/tasks.md')
    const tasksContent = fs.readFileSync(tasksPath, 'utf8')
    
    // 检查任务6是否存在
    const checks = [
      {
        name: '任务6存在',
        found: tasksContent.includes('6. 优化弹出窗口二级菜单显示逻辑')
      },
      {
        name: '子任务6.1存在',
        found: tasksContent.includes('6.1 完善已收藏状态的显示逻辑')
      },
      {
        name: '子任务6.2存在',
        found: tasksContent.includes('6.2 实现"在管理页面打开"功能')
      },
      {
        name: '子任务6.3存在',
        found: tasksContent.includes('6.3 优化编辑按钮和操作流程')
      }
    ]
    
    let passedChecks = 0
    checks.forEach(check => {
      if (check.found) {
        console.log(`  ✅ ${check.name}: 通过`)
        passedChecks++
      } else {
        console.log(`  ❌ ${check.name}: 失败`)
      }
    })
    
    console.log(`  📊 通过率: ${passedChecks}/${checks.length} (${Math.round(passedChecks/checks.length*100)}%)\n`)
    return passedChecks === checks.length
    
  } catch (error) {
    console.error('  ❌ 测试失败:', error.message)
    return false
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始运行弹出窗口二级菜单功能测试...\n')
  
  const tests = [
    { name: '已收藏状态显示', fn: testBookmarkedStateDisplay },
    { name: '管理页面高亮功能', fn: testOptionsAppHighlight },
    { name: '需求文档更新', fn: testRequirementsUpdate },
    { name: '任务列表更新', fn: testTasksUpdate }
  ]
  
  let passedTests = 0
  const results = []
  
  tests.forEach(test => {
    try {
      const result = test.fn()
      results.push({ name: test.name, passed: result })
      if (result) passedTests++
    } catch (error) {
      console.error(`❌ 测试 "${test.name}" 执行失败:`, error.message)
      results.push({ name: test.name, passed: false, error: error.message })
    }
  })
  
  // 输出总结
  console.log('📊 测试总结:')
  console.log('=' .repeat(50))
  results.forEach(result => {
    const status = result.passed ? '✅ 通过' : '❌ 失败'
    console.log(`${status} ${result.name}`)
    if (result.error) {
      console.log(`   错误: ${result.error}`)
    }
  })
  
  console.log('=' .repeat(50))
  console.log(`总体通过率: ${passedTests}/${tests.length} (${Math.round(passedTests/tests.length*100)}%)`)
  
  if (passedTests === tests.length) {
    console.log('🎉 所有测试通过！弹出窗口二级菜单功能实现完成。')
  } else {
    console.log('⚠️  部分测试失败，请检查实现。')
  }
  
  return passedTests === tests.length
}

// 运行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  runAllTests,
  testBookmarkedStateDisplay,
  testOptionsAppHighlight,
  testRequirementsUpdate,
  testTasksUpdate
}