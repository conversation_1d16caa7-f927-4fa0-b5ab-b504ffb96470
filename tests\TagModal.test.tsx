// TagModal组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import TagModal from '../src/components/TagModal'
import type { Tag } from '../src/types'

// 模拟TagForm组件
vi.mock('../src/components/TagForm', () => ({
  TagForm: ({ onSave, onCancel, loading, tag }: any) => (
    <div data-testid="tag-form">
      <div>Tag Form Mock</div>
      <div>Loading: {loading ? 'true' : 'false'}</div>
      <div>Tag: {tag ? tag.name : 'new'}</div>
      <button onClick={() => onSave({ name: 'Test Tag', color: '#FF0000' })}>
        Save
      </button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  )
}))

describe('TagModal', () => {
  const mockTag: Tag = {
    id: '1',
    name: '测试标签',
    color: '#3B82F6',
    usageCount: 5,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  }

  const existingTags = [
    { id: '1', name: '测试标签' },
    { id: '2', name: '其他标签' }
  ]

  const defaultProps = {
    isOpen: true,
    type: 'create' as const,
    onClose: vi.fn(),
    loading: false,
    existingTags
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // 模拟document.body.style
    Object.defineProperty(document.body, 'style', {
      value: { overflow: '' },
      writable: true
    })
  })

  afterEach(() => {
    // 清理body样式
    document.body.style.overflow = 'unset'
  })

  describe('基本渲染', () => {
    it('当isOpen为false时不渲染任何内容', () => {
      render(<TagModal {...defaultProps} isOpen={false} />)
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })

    it('当isOpen为true时渲染模态窗口', () => {
      render(<TagModal {...defaultProps} />)
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByText('创建新标签')).toBeInTheDocument()
    })

    it('渲染关闭按钮', () => {
      render(<TagModal {...defaultProps} />)
      expect(screen.getByLabelText('关闭')).toBeInTheDocument()
    })
  })

  describe('模态窗口类型', () => {
    it('创建模式显示正确的标题和表单', () => {
      render(<TagModal {...defaultProps} type="create" onSave={vi.fn()} />)
      expect(screen.getByText('创建新标签')).toBeInTheDocument()
      expect(screen.getByTestId('tag-form')).toBeInTheDocument()
    })

    it('编辑模式显示正确的标题和预填充表单', () => {
      render(
        <TagModal 
          {...defaultProps} 
          type="edit" 
          tag={mockTag}
          onSave={vi.fn()} 
        />
      )
      expect(screen.getByText('编辑标签')).toBeInTheDocument()
      expect(screen.getByTestId('tag-form')).toBeInTheDocument()
      expect(screen.getByText('Tag: 测试标签')).toBeInTheDocument()
    })

    it('删除模式显示确认对话框', () => {
      render(
        <TagModal 
          {...defaultProps} 
          type="delete" 
          tag={mockTag}
          bookmarkCount={5}
          onDelete={vi.fn()} 
        />
      )
      expect(screen.getByText('删除标签')).toBeInTheDocument()
      expect(screen.getByText('确认删除标签')).toBeInTheDocument()
      expect(screen.getByText('测试标签')).toBeInTheDocument()
      expect(screen.getByText((content, element) => {
        return element?.textContent === '• 从 5 个书签中移除此标签'
      })).toBeInTheDocument()
    })
  })

  describe('删除确认对话框', () => {
    it('显示标签信息和影响说明', () => {
      render(
        <TagModal 
          {...defaultProps} 
          type="delete" 
          tag={mockTag}
          bookmarkCount={5}
          onDelete={vi.fn()} 
        />
      )
      
      expect(screen.getByText('测试标签')).toBeInTheDocument()
      expect(screen.getByText((content, element) => {
        return element?.textContent === '使用次数: 5'
      })).toBeInTheDocument()
      expect(screen.getByText((content, element) => {
        return element?.textContent === '• 永久删除标签 "测试标签"'
      })).toBeInTheDocument()
      expect(screen.getByText((content, element) => {
        return element?.textContent === '• 从 5 个书签中移除此标签'
      })).toBeInTheDocument()
    })

    it('当标签未被使用时显示相应提示', () => {
      render(
        <TagModal 
          {...defaultProps} 
          type="delete" 
          tag={mockTag}
          bookmarkCount={0}
          onDelete={vi.fn()} 
        />
      )
      
      expect(screen.getByText((content, element) => {
        return element?.textContent === '• 不会影响任何书签（此标签未被使用）'
      })).toBeInTheDocument()
    })

    it('点击确认删除按钮调用onDelete', async () => {
      const onDelete = vi.fn()
      render(
        <TagModal 
          {...defaultProps} 
          type="delete" 
          tag={mockTag}
          onDelete={onDelete} 
        />
      )
      
      const deleteButton = screen.getByText('确认删除')
      fireEvent.click(deleteButton)
      
      expect(onDelete).toHaveBeenCalledTimes(1)
    })

    it('删除时显示加载状态', () => {
      render(
        <TagModal 
          {...defaultProps} 
          type="delete" 
          tag={mockTag}
          loading={true}
          onDelete={vi.fn()} 
        />
      )
      
      expect(screen.getByText('删除中...')).toBeInTheDocument()
      // 在加载状态下，按钮文本变为"删除中..."，所以我们检查按钮是否被禁用
      const deleteButton = screen.getByRole('button', { name: /删除中/ })
      expect(deleteButton).toBeDisabled()
    })
  })

  describe('表单交互', () => {
    it('表单保存时调用onSave', async () => {
      const onSave = vi.fn()
      render(<TagModal {...defaultProps} type="create" onSave={onSave} />)
      
      const saveButton = screen.getByText('Save')
      fireEvent.click(saveButton)
      
      expect(onSave).toHaveBeenCalledWith({ name: 'Test Tag', color: '#FF0000' })
    })

    it('表单取消时调用onClose', async () => {
      const onClose = vi.fn()
      render(
        <TagModal 
          {...defaultProps} 
          type="create" 
          onSave={vi.fn()}
          onClose={onClose} 
        />
      )
      
      const cancelButton = screen.getByText('Cancel')
      fireEvent.click(cancelButton)
      
      expect(onClose).toHaveBeenCalledTimes(1)
    })

    it('传递existingTags给TagForm', () => {
      render(<TagModal {...defaultProps} type="create" onSave={vi.fn()} />)
      expect(screen.getByTestId('tag-form')).toBeInTheDocument()
    })
  })

  describe('关闭交互', () => {
    it('点击关闭按钮调用onClose', async () => {
      const onClose = vi.fn()
      render(<TagModal {...defaultProps} onClose={onClose} />)
      
      const closeButton = screen.getByLabelText('关闭')
      fireEvent.click(closeButton)
      
      expect(onClose).toHaveBeenCalledTimes(1)
    })

    it('点击背景遮罩调用onClose', async () => {
      const onClose = vi.fn()
      render(<TagModal {...defaultProps} onClose={onClose} />)
      
      // 点击背景遮罩（第一个div）
      const backdrop = screen.getByRole('dialog').firstChild as HTMLElement
      fireEvent.click(backdrop)
      
      expect(onClose).toHaveBeenCalledTimes(1)
    })

    it('按ESC键调用onClose', async () => {
      const onClose = vi.fn()
      render(<TagModal {...defaultProps} onClose={onClose} />)
      
      fireEvent.keyDown(document, { key: 'Escape' })
      
      expect(onClose).toHaveBeenCalledTimes(1)
    })

    it('加载时不能关闭模态窗口', async () => {
      const onClose = vi.fn()
      render(<TagModal {...defaultProps} loading={true} onClose={onClose} />)
      
      // 尝试点击关闭按钮
      const closeButton = screen.getByLabelText('关闭')
      expect(closeButton).toBeDisabled()
      
      // 尝试按ESC键
      fireEvent.keyDown(document, { key: 'Escape' })
      expect(onClose).not.toHaveBeenCalled()
    })
  })

  describe('无障碍性', () => {
    it('设置正确的ARIA属性', () => {
      render(<TagModal {...defaultProps} />)
      
      const dialog = screen.getByRole('dialog')
      expect(dialog).toHaveAttribute('aria-modal', 'true')
      expect(dialog).toHaveAttribute('aria-labelledby', 'modal-title')
    })

    it('标题具有正确的ID', () => {
      render(<TagModal {...defaultProps} />)
      
      const title = screen.getByText('创建新标签')
      expect(title).toHaveAttribute('id', 'modal-title')
    })
  })

  describe('错误处理', () => {
    it('创建/编辑模式缺少onSave时显示错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      render(<TagModal {...defaultProps} type="create" />)
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'TagModal: onSave is required for create/edit mode'
      )
      
      consoleSpy.mockRestore()
    })
  })

  describe('样式和布局', () => {
    it('打开时设置body overflow为hidden', () => {
      render(<TagModal {...defaultProps} />)
      expect(document.body.style.overflow).toBe('hidden')
    })

    it('关闭时恢复body overflow', () => {
      const { unmount } = render(<TagModal {...defaultProps} />)
      unmount()
      expect(document.body.style.overflow).toBe('unset')
    })

    it('标签颜色正确显示', () => {
      render(
        <TagModal 
          {...defaultProps} 
          type="delete" 
          tag={mockTag}
          onDelete={vi.fn()} 
        />
      )
      
      const colorIndicator = screen.getByRole('dialog').querySelector('[style*="background-color"]')
      expect(colorIndicator).toHaveStyle({ backgroundColor: '#3B82F6' })
    })
  })
})