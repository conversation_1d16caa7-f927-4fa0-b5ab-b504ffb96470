/**
 * 布局稳定性工具
 * 用于防止页面切换和内容更新时的布局跳动
 */

import { useEffect, useRef, useState, useCallback } from 'react'

/**
 * 容器尺寸监听Hook
 * 监听容器尺寸变化并提供稳定的布局处理
 */
export function useContainerSize() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [size, setSize] = useState({ width: 0, height: 0 })
  const [isStable, setIsStable] = useState(true)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        
        // 设置不稳定状态
        setIsStable(false)
        
        // 更新尺寸
        setSize({ width, height })
        
        // 短暂延迟后恢复稳定状态
        setTimeout(() => {
          setIsStable(true)
        }, 100)
      }
    })

    resizeObserver.observe(container)

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  return {
    containerRef,
    size,
    isStable
  }
}

/**
 * 平滑过渡Hook
 * 为状态变化提供平滑的过渡效果
 */
export function useSmoothTransition<T>(
  value: T,
  transitionDuration: number = 300
) {
  const [displayValue, setDisplayValue] = useState(value)
  const [isTransitioning, setIsTransitioning] = useState(false)

  useEffect(() => {
    if (value !== displayValue) {
      setIsTransitioning(true)
      
      const timer = setTimeout(() => {
        setDisplayValue(value)
        setIsTransitioning(false)
      }, transitionDuration / 2)

      return () => clearTimeout(timer)
    }
  }, [value, displayValue, transitionDuration])

  return {
    displayValue,
    isTransitioning
  }
}

/**
 * 布局锁定Hook
 * 在内容变化时锁定容器尺寸，防止布局跳动
 */
export function useLayoutLock(dependencies: any[] = []) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLocked, setIsLocked] = useState(false)
  const lockedSizeRef = useRef<{ width: number; height: number } | null>(null)

  const lockLayout = useCallback(() => {
    const container = containerRef.current
    if (!container) return

    const rect = container.getBoundingClientRect()
    lockedSizeRef.current = {
      width: rect.width,
      height: rect.height
    }

    // 设置固定尺寸
    container.style.width = `${rect.width}px`
    container.style.height = `${rect.height}px`
    container.style.overflow = 'hidden'
    
    setIsLocked(true)
  }, [])

  const unlockLayout = useCallback(() => {
    const container = containerRef.current
    if (!container) return

    // 移除固定尺寸
    container.style.width = ''
    container.style.height = ''
    container.style.overflow = ''
    
    lockedSizeRef.current = null
    setIsLocked(false)
  }, [])

  // 依赖变化时自动处理布局锁定
  useEffect(() => {
    lockLayout()
    
    const timer = setTimeout(() => {
      unlockLayout()
    }, 300)

    return () => {
      clearTimeout(timer)
      unlockLayout()
    }
  }, dependencies)

  return {
    containerRef,
    isLocked,
    lockLayout,
    unlockLayout,
    lockedSize: lockedSizeRef.current
  }
}

/**
 * 防抖布局更新Hook
 * 防抖处理布局更新，避免频繁的重新计算
 */
export function useDebouncedLayout(
  callback: () => void,
  delay: number = 100,
  dependencies: any[] = []
) {
  const timeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      callback()
    }, delay)

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, dependencies)
}

/**
 * 内容加载状态Hook
 * 管理内容加载时的占位和过渡状态
 */
export function useContentLoading(isLoading: boolean) {
  const [showPlaceholder, setShowPlaceholder] = useState(isLoading)
  const [isTransitioning, setIsTransitioning] = useState(false)

  useEffect(() => {
    if (isLoading) {
      setShowPlaceholder(true)
      setIsTransitioning(false)
    } else {
      setIsTransitioning(true)
      
      // 短暂延迟后隐藏占位符
      const timer = setTimeout(() => {
        setShowPlaceholder(false)
        setIsTransitioning(false)
      }, 150)

      return () => clearTimeout(timer)
    }
  }, [isLoading])

  return {
    showPlaceholder,
    isTransitioning
  }
}

/**
 * 滚动位置保持Hook
 * 在内容更新时保持滚动位置
 */
export function useScrollPositionLock() {
  const scrollPositionRef = useRef<number>(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const saveScrollPosition = useCallback(() => {
    const container = containerRef.current
    if (container) {
      scrollPositionRef.current = container.scrollTop
    }
  }, [])

  const restoreScrollPosition = useCallback(() => {
    const container = containerRef.current
    if (container && scrollPositionRef.current !== undefined) {
      container.scrollTop = scrollPositionRef.current
    }
  }, [])

  const lockScrollPosition = useCallback(() => {
    saveScrollPosition()
    
    // 在下一帧恢复滚动位置
    requestAnimationFrame(() => {
      restoreScrollPosition()
    })
  }, [saveScrollPosition, restoreScrollPosition])

  return {
    containerRef,
    saveScrollPosition,
    restoreScrollPosition,
    lockScrollPosition
  }
}

/**
 * 视图切换稳定性Hook
 * 专门用于处理视图模式切换时的布局稳定性
 */
export function useViewSwitchStability<T>(
  currentView: T,
  transitionDuration: number = 300
) {
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [displayView, setDisplayView] = useState(currentView)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (currentView !== displayView) {
      setIsTransitioning(true)

      // 锁定当前容器尺寸
      const container = containerRef.current
      let originalStyles: { width: string; height: string; minHeight: string } | null = null
      
      if (container) {
        const rect = container.getBoundingClientRect()
        originalStyles = {
          width: container.style.width,
          height: container.style.height,
          minHeight: container.style.minHeight
        }
        
        container.style.width = `${rect.width}px`
        container.style.minHeight = `${rect.height}px`
      }

      // 延迟切换视图
      const switchTimer = setTimeout(() => {
        setDisplayView(currentView)
      }, transitionDuration / 2)

      // 完成过渡
      const completeTimer = setTimeout(() => {
        setIsTransitioning(false)
        
        // 恢复原始样式
        if (container && originalStyles) {
          container.style.width = originalStyles.width
          container.style.height = originalStyles.height
          container.style.minHeight = originalStyles.minHeight
        }
      }, transitionDuration)

      return () => {
        clearTimeout(switchTimer)
        clearTimeout(completeTimer)
        
        // 清理时恢复样式
        if (container && originalStyles) {
          container.style.width = originalStyles.width
          container.style.height = originalStyles.height
          container.style.minHeight = originalStyles.minHeight
        }
      }
    }
  }, [currentView, displayView, transitionDuration])

  return {
    containerRef,
    displayView,
    isTransitioning
  }
}