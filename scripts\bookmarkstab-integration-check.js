#!/usr/bin/env node

/**
 * BookmarksTab组件集成检测脚本
 * 验证重构后的BookmarksTab组件是否正确集成到插件中
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 开始BookmarksTab组件集成检测...\n');

// 检查项目配置
const checks = [
  {
    name: '验证BookmarksTab组件文件存在',
    check: () => {
      const componentPath = path.join(__dirname, '../src/components/BookmarksTab.tsx');
      return fs.existsSync(componentPath);
    }
  },
  {
    name: '验证OptionsApp中正确导入BookmarksTab',
    check: () => {
      const optionsAppPath = path.join(__dirname, '../src/options/OptionsApp.tsx');
      if (!fs.existsSync(optionsAppPath)) return false;
      
      const content = fs.readFileSync(optionsAppPath, 'utf8');
      return content.includes("import BookmarksTab from '../components/BookmarksTab'");
    }
  },
  {
    name: '验证OptionsApp中使用BookmarksTab组件',
    check: () => {
      const optionsAppPath = path.join(__dirname, '../src/options/OptionsApp.tsx');
      if (!fs.existsSync(optionsAppPath)) return false;
      
      const content = fs.readFileSync(optionsAppPath, 'utf8');
      return content.includes('return <BookmarksTab />') && 
             content.includes("case 'bookmarks':");
    }
  },
  {
    name: '验证BookmarksTab使用shadcn Input组件',
    check: () => {
      const componentPath = path.join(__dirname, '../src/components/BookmarksTab.tsx');
      if (!fs.existsSync(componentPath)) return false;
      
      const content = fs.readFileSync(componentPath, 'utf8');
      return content.includes("import { Input }") && 
             content.includes('<Input');
    }
  },
  {
    name: '验证BookmarksTab使用shadcn Select组件',
    check: () => {
      const componentPath = path.join(__dirname, '../src/components/BookmarksTab.tsx');
      if (!fs.existsSync(componentPath)) return false;
      
      const content = fs.readFileSync(componentPath, 'utf8');
      return content.includes("import { Select,") && 
             content.includes('<Select') &&
             content.includes('<SelectTrigger') &&
             content.includes('<SelectContent');
    }
  },
  {
    name: '验证BookmarksTab使用shadcn Button组件',
    check: () => {
      const componentPath = path.join(__dirname, '../src/components/BookmarksTab.tsx');
      if (!fs.existsSync(componentPath)) return false;
      
      const content = fs.readFileSync(componentPath, 'utf8');
      return content.includes("import { Button }") && 
             content.includes('<Button');
    }
  },
  {
    name: '验证BookmarksTab使用shadcn Card组件',
    check: () => {
      const componentPath = path.join(__dirname, '../src/components/BookmarksTab.tsx');
      if (!fs.existsSync(componentPath)) return false;
      
      const content = fs.readFileSync(componentPath, 'utf8');
      return content.includes("import { Card,") && 
             content.includes('<Card') &&
             content.includes('<CardHeader') &&
             content.includes('<CardContent');
    }
  },
  {
    name: '验证BookmarksTab包含必要的功能逻辑',
    check: () => {
      const componentPath = path.join(__dirname, '../src/components/BookmarksTab.tsx');
      if (!fs.existsSync(componentPath)) return false;
      
      const content = fs.readFileSync(componentPath, 'utf8');
      return content.includes('loadBookmarks') && 
             content.includes('handleEditClick') &&
             content.includes('handleAddBookmark') &&
             content.includes('handleDeleteClick') &&
             content.includes('useAdvancedSearch');
    }
  },
  {
    name: '验证构建产物包含BookmarksTab',
    check: () => {
      const distPath = path.join(__dirname, '../dist');
      if (!fs.existsSync(distPath)) return false;
      
      // 检查是否有options相关的JS文件
      const files = fs.readdirSync(path.join(distPath, 'assets'));
      const optionsFile = files.find(file => file.startsWith('options-') && file.endsWith('.js'));
      
      if (!optionsFile) return false;
      
      const content = fs.readFileSync(path.join(distPath, 'assets', optionsFile), 'utf8');
      return content.includes('收藏管理') && content.includes('搜索收藏');
    }
  },
  {
    name: '验证测试页面正确配置',
    check: () => {
      const optionsAppPath = path.join(__dirname, '../src/options/OptionsApp.tsx');
      if (!fs.existsSync(optionsAppPath)) return false;
      
      const content = fs.readFileSync(optionsAppPath, 'utf8');
      return content.includes('bookmarkstab-test') && 
             content.includes('BookmarksTab测试') &&
             content.includes('BookmarksTabTestPage');
    }
  }
];

// 执行检查
let passedChecks = 0;
let totalChecks = checks.length;

checks.forEach((check, index) => {
  const checkNumber = `📋 检查 ${index + 1}/${totalChecks}`;
  try {
    const result = check.check();
    if (result) {
      console.log(`${checkNumber}: ${check.name}`);
      console.log(`✅ ${check.name} - 通过\n`);
      passedChecks++;
    } else {
      console.log(`${checkNumber}: ${check.name}`);
      console.log(`❌ ${check.name} - 失败\n`);
    }
  } catch (error) {
    console.log(`${checkNumber}: ${check.name}`);
    console.log(`❌ ${check.name} - 错误: ${error.message}\n`);
  }
});

// 输出结果
console.log('='.repeat(50));
console.log(`📊 BookmarksTab集成检测完成: ${passedChecks}/${totalChecks} 项通过`);

if (passedChecks === totalChecks) {
  console.log('🎉 所有检查都通过了！BookmarksTab组件已成功集成。');
  console.log('\n📋 下一步操作:');
  console.log('1. 在Chrome中加载扩展 (选择 dist 文件夹)');
  console.log('2. 打开扩展选项页面');
  console.log('3. 测试"收藏管理"标签页功能');
  console.log('4. 测试"BookmarksTab测试"标签页');
  console.log('5. 验证shadcn组件样式和交互');
} else {
  console.log(`❌ 有 ${totalChecks - passedChecks} 项检查失败，请修复后重新检测。`);
  process.exit(1);
}

console.log('');