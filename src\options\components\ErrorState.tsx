// 错误状态组件
import React from 'react'
import { Button } from '../../components/ui/button'
import { Card, CardContent } from '../../components/ui/card'

interface ErrorStateProps {
  initError: string
  retryCount: number
  maxRetries: number
  onRetry: () => void
}

const ErrorState: React.FC<ErrorStateProps> = ({ 
  initError, 
  retryCount, 
  maxRetries, 
  onRetry 
}) => {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardContent className="p-6">
          <div className="text-center">
            <div className="w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-foreground mb-2">初始化失败</h2>
            <p className="text-muted-foreground mb-4">{initError}</p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {retryCount < maxRetries && (
                <Button
                  onClick={onRetry}
                  className="flex items-center justify-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>重试 ({maxRetries - retryCount} 次剩余)</span>
                </Button>
              )}
              <Button
                variant="secondary"
                onClick={() => window.location.reload()}
                className="flex items-center justify-center space-x-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>刷新页面</span>
              </Button>
            </div>
            {retryCount >= maxRetries && (
              <p className="text-sm text-muted-foreground mt-4">
                已达到最大重试次数，请尝试刷新页面或重新加载扩展
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ErrorState