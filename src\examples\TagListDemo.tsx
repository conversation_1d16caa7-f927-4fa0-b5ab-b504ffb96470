// TagList 组件演示

import React, { useState } from 'react'
import TagList from '../components/TagList'
import type { TagWithStats, TagSortOption } from '../components/TagList'

// 创建演示用的标签数据
const createDemoTag = (id: string, name: string, usageCount: number, color?: string): TagWithStats => ({
  id,
  name,
  color: color || `#${Math.floor(Math.random()*16777215).toString(16)}`,
  usageCount,
  createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // 随机过去30天内的时间
  updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)   // 随机过去7天内的时间
})

const demoTags: TagWithStats[] = [
  createDemoTag('1', '技术', 25, '#3B82F6'),
  createDemoTag('2', '学习', 18, '#10B981'),
  createDemoTag('3', '工具', 12, '#F59E0B'),
  createDemoTag('4', '新闻', 8, '#EF4444'),
  createDemoTag('5', '娱乐', 15, '#8B5CF6'),
  createDemoTag('6', '其他', 0, '#6B7280'),
  createDemoTag('7', '前端开发', 22, '#06B6D4'),
  createDemoTag('8', '后端开发', 16, '#84CC16'),
  createDemoTag('9', '设计', 9, '#F97316'),
  createDemoTag('10', '产品', 5, '#EC4899'),
  createDemoTag('11', '管理', 3, '#14B8A6'),
  createDemoTag('12', '创业', 7, '#F43F5E')
]

/**
 * TagList 组件演示
 * 展示标签列表的各种功能和状态
 */
const TagListDemo: React.FC = () => {
  const [tags, setTags] = useState<TagWithStats[]>(demoTags)
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<TagSortOption>('name-asc')

  // 处理标签编辑
  const handleTagEdit = (tag: TagWithStats) => {
    console.log('编辑标签:', tag)
    alert(`编辑标签: ${tag.name}`)
  }

  // 处理标签删除
  const handleTagDelete = (tag: TagWithStats) => {
    console.log('删除标签:', tag)
    if (confirm(`确定要删除标签 "${tag.name}" 吗？`)) {
      setTags(prevTags => prevTags.filter(t => t.id !== tag.id))
    }
  }

  // 处理标签点击
  const handleTagClick = (tag: TagWithStats) => {
    console.log('点击标签:', tag)
    alert(`点击了标签: ${tag.name}`)
  }

  // 处理创建新标签
  const handleCreateTag = () => {
    const name = prompt('请输入新标签名称:')
    if (name && name.trim()) {
      const newTag = createDemoTag(
        Date.now().toString(),
        name.trim(),
        0
      )
      setTags(prevTags => [...prevTags, newTag])
    }
  }

  // 模拟加载状态
  const toggleLoading = () => {
    setLoading(true)
    setTimeout(() => setLoading(false), 2000)
  }

  // 重置演示数据
  const resetData = () => {
    setTags(demoTags)
    setSearchQuery('')
    setSortBy('name-asc')
  }

  // 清空标签数据
  const clearTags = () => {
    setTags([])
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          TagList 组件演示
        </h1>
        <p className="text-gray-600">
          展示标签列表组件的各种功能和交互效果
        </p>
      </div>

      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">控制面板</h2>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={toggleLoading}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {loading ? '加载中...' : '模拟加载'}
          </button>
          
          <button
            onClick={resetData}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            重置数据
          </button>
          
          <button
            onClick={clearTags}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            清空标签
          </button>
          
          <button
            onClick={handleCreateTag}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            添加标签
          </button>
        </div>
        
        <div className="mt-4 text-sm text-gray-600">
          <p>当前状态: 共 {tags.length} 个标签</p>
          <p>搜索查询: "{searchQuery}"</p>
          <p>排序方式: {sortBy}</p>
        </div>
      </div>

      {/* TagList 组件演示 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">标签列表</h2>
        
        <TagList
          tags={tags}
          onTagEdit={handleTagEdit}
          onTagDelete={handleTagDelete}
          onTagClick={handleTagClick}
          onCreateTag={handleCreateTag}
          loading={loading}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          sortBy={sortBy}
          onSortChange={setSortBy}
        />
      </div>

      {/* 功能说明 */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">功能说明</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">基本功能</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 网格和列表视图切换</li>
              <li>• 标签搜索和筛选</li>
              <li>• 多种排序方式</li>
              <li>• 标签统计信息显示</li>
              <li>• 加载状态和空状态</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">交互功能</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 点击标签卡片查看详情</li>
              <li>• 编辑和删除标签</li>
              <li>• 创建新标签</li>
              <li>• 实时搜索反馈</li>
              <li>• 响应式布局适配</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 使用示例 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">使用示例</h2>
        <pre className="bg-gray-100 rounded-lg p-4 text-sm overflow-x-auto">
{`import TagList from './components/TagList'
import type { TagWithStats } from './components/TagList'

const MyComponent = () => {
  const [tags, setTags] = useState<TagWithStats[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('name-asc')

  return (
    <TagList
      tags={tags}
      onTagEdit={(tag) => console.log('编辑:', tag)}
      onTagDelete={(tag) => console.log('删除:', tag)}
      onTagClick={(tag) => console.log('点击:', tag)}
      onCreateTag={() => console.log('创建新标签')}
      loading={false}
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      sortBy={sortBy}
      onSortChange={setSortBy}
    />
  )
}`}
        </pre>
      </div>
    </div>
  )
}

export default TagListDemo