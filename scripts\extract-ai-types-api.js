#!/usr/bin/env node

/**
 * AI类型定义API提取脚本
 * 
 * 使用方法:
 * node scripts/extract-ai-types-api.js
 */

import { readFileSync, writeFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const projectRoot = join(__dirname, '..')

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function success(message) {
  colorLog('green', `✅ ${message}`)
}

function error(message) {
  colorLog('red', `❌ ${message}`)
}

function info(message) {
  colorLog('blue', `ℹ️  ${message}`)
}

function warning(message) {
  colorLog('yellow', `⚠️  ${message}`)
}

function header(message) {
  console.log()
  colorLog('cyan', `${'='.repeat(60)}`)
  colorLog('cyan', `🔧 ${message}`)
  colorLog('cyan', `${'='.repeat(60)}`)
}

/**
 * 提取TypeScript类型定义
 */
function extractTypeDefinitions(content) {
  const types = []
  const interfaces = []
  const enums = []
  
  // 提取类型别名
  const typeRegex = /export\s+type\s+(\w+)\s*=\s*([^;\n]+)/g
  let match
  while ((match = typeRegex.exec(content)) !== null) {
    types.push({
      name: match[1],
      definition: match[2].trim(),
      type: 'type'
    })
  }
  
  // 提取接口定义
  const interfaceRegex = /export\s+interface\s+(\w+)\s*\{([^}]+)\}/g
  while ((match = interfaceRegex.exec(content)) !== null) {
    const properties = extractInterfaceProperties(match[2])
    interfaces.push({
      name: match[1],
      properties,
      type: 'interface'
    })
  }
  
  return { types, interfaces, enums }
}

/**
 * 提取接口属性
 */
function extractInterfaceProperties(propertiesText) {
  const properties = []
  const lines = propertiesText.split('\n')
  
  for (const line of lines) {
    const trimmed = line.trim()
    if (!trimmed || trimmed.startsWith('//') || trimmed.startsWith('*')) {
      continue
    }
    
    // 匹配属性定义
    const propMatch = trimmed.match(/(\w+)(\??):\s*([^\/\n]+)/)
    if (propMatch) {
      properties.push({
        name: propMatch[1],
        optional: propMatch[2] === '?',
        type: propMatch[3].trim(),
        description: extractPropertyComment(line)
      })
    }
  }
  
  return properties
}

/**
 * 提取属性注释
 */
function extractPropertyComment(line) {
  const commentMatch = line.match(/\/\/\s*(.+)$/)
  return commentMatch ? commentMatch[1].trim() : ''
}

/**
 * 提取JSDoc注释
 */
function extractJSDocComments(content) {
  const comments = new Map()
  const jsdocRegex = /\/\*\*\s*\n([\s\S]*?)\*\/\s*\n\s*export\s+(?:type|interface)\s+(\w+)/g
  
  let match
  while ((match = jsdocRegex.exec(content)) !== null) {
    const comment = match[1]
      .split('\n')
      .map(line => line.replace(/^\s*\*\s?/, '').trim())
      .filter(line => line)
      .join('\n')
    
    comments.set(match[2], comment)
  }
  
  return comments
}

/**
 * 生成函数签名文档
 */
function generateFunctionSignatures(definitions) {
  let output = '# AI类型定义函数签名\n\n'
  output += '## 概述\n\n'
  output += '本文档提供 `src/types/ai.ts` 中所有类型定义的详细签名和使用说明。\n\n'
  
  // 生成类型别名部分
  if (definitions.types.length > 0) {
    output += '## 类型别名\n\n'
    
    for (const type of definitions.types) {
      output += `### ${type.name}\n\n`
      output += '```typescript\n'
      output += `export type ${type.name} = ${type.definition}\n`
      output += '```\n\n'
      
      if (type.name === 'AIProvider') {
        output += '**支持的AI提供商**:\n'
        const providers = type.definition.match(/'([^']+)'/g) || []
        providers.forEach(provider => {
          const cleanProvider = provider.replace(/'/g, '')
          output += `- \`${cleanProvider}\` - ${getProviderDescription(cleanProvider)}\n`
        })
        output += '\n'
      }
    }
  }
  
  // 生成接口部分
  if (definitions.interfaces.length > 0) {
    output += '## 接口定义\n\n'
    
    for (const iface of definitions.interfaces) {
      output += `### ${iface.name} 接口\n\n`
      output += '```typescript\n'
      output += `export interface ${iface.name} {\n`
      
      for (const prop of iface.properties) {
        const optional = prop.optional ? '?' : ''
        output += `  ${prop.name}${optional}: ${prop.type}`
        if (prop.description) {
          output += ` // ${prop.description}`
        }
        output += '\n'
      }
      
      output += '}\n'
      output += '```\n\n'
      
      // 添加属性说明
      if (iface.properties.length > 0) {
        output += '**属性说明**:\n\n'
        for (const prop of iface.properties) {
          const required = prop.optional ? '可选' : '必需'
          output += `- \`${prop.name}\` (${required}): ${prop.type}`
          if (prop.description) {
            output += ` - ${prop.description}`
          }
          output += '\n'
        }
        output += '\n'
      }
      
      // 添加使用示例
      output += generateUsageExample(iface)
    }
  }
  
  return output
}

/**
 * 获取提供商描述
 */
function getProviderDescription(provider) {
  const descriptions = {
    'openai': 'OpenAI GPT系列模型',
    'azure-openai': 'Microsoft Azure部署的OpenAI模型',
    'claude': 'Anthropic Claude系列模型',
    'gemini': 'Google Gemini系列模型',
    'local': '本地AI服务',
    'custom': '自定义API服务',
    'ollama': 'Ollama本地部署服务',
    'lm-studio': 'LM Studio本地模型运行环境',
    'openrouter': 'OpenRouter多模型聚合服务',
    'xinference': 'Xinference分布式推理引擎',
    'deepseek': 'DeepSeek AI模型服务',
    'zhipu': '智谱AI GLM系列模型',
    'qwen': '阿里云通义千问模型',
    'bailian': '百炼大模型平台',
    'hunyuan': '腾讯混元大模型',
    'volcengine': '火山引擎AI服务',
    'together': 'Together AI模型聚合服务',
    'grok': 'xAI Grok模型'
  }
  
  return descriptions[provider] || '未知AI服务提供商'
}

/**
 * 生成使用示例
 */
function generateUsageExample(iface) {
  let example = '**使用示例**:\n\n```typescript\n'
  
  switch (iface.name) {
    case 'AIProviderConfig':
      example += `const azureConfig: ${iface.name} = {\n`
      example += `  id: 'azure_openai_main',\n`
      example += `  name: 'Azure OpenAI主服务',\n`
      example += `  type: 'azure-openai',\n`
      example += `  baseUrl: 'https://your-resource.openai.azure.com',\n`
      example += `  apiKey: 'your-api-key',\n`
      example += `  headers: {\n`
      example += `    'api-version': '2024-02-15-preview'\n`
      example += `  },\n`
      example += `  timeout: 30000,\n`
      example += `  enabled: true,\n`
      example += `  createdAt: new Date(),\n`
      example += `  updatedAt: new Date()\n`
      example += `}\n`
      break
      
    case 'AIModel':
      example += `const gpt4Model: ${iface.name} = {\n`
      example += `  id: 'gpt-4',\n`
      example += `  name: 'gpt-4',\n`
      example += `  displayName: 'GPT-4',\n`
      example += `  description: 'OpenAI最先进的大型语言模型',\n`
      example += `  parameters: '175B+',\n`
      example += `  tags: ['openai', 'gpt', 'multimodal'],\n`
      example += `  capabilities: ['chat', 'completion', 'reasoning'],\n`
      example += `  providerId: 'openai_main',\n`
      example += `  isRecommended: true,\n`
      example += `  isPopular: true\n`
      example += `}\n`
      break
      
    case 'AIConnectionResult':
      example += `const result: ${iface.name} = {\n`
      example += `  providerId: 'azure_openai_main',\n`
      example += `  success: true,\n`
      example += `  responseTime: 1250,\n`
      example += `  modelCount: 5,\n`
      example += `  testedAt: new Date()\n`
      example += `}\n`
      break
      
    default:
      example += `const example: ${iface.name} = {\n`
      for (const prop of iface.properties.slice(0, 3)) {
        const sampleValue = getSampleValue(prop.type)
        example += `  ${prop.name}: ${sampleValue},\n`
      }
      if (iface.properties.length > 3) {
        example += `  // ... 其他属性\n`
      }
      example += `}\n`
  }
  
  example += '```\n\n'
  return example
}

/**
 * 获取示例值
 */
function getSampleValue(type) {
  if (type.includes('string')) return "'示例值'"
  if (type.includes('number')) return '123'
  if (type.includes('boolean')) return 'true'
  if (type.includes('Date')) return 'new Date()'
  if (type.includes('[]')) return '[]'
  if (type.includes('Record')) return '{}'
  return "'示例值'"
}

/**
 * 主函数
 */
async function main() {
  header('AI类型定义API提取')
  
  try {
    // 读取AI类型定义文件
    const aiTypesPath = join(projectRoot, 'src/types/ai.ts')
    info(`读取文件: ${aiTypesPath}`)
    
    const content = readFileSync(aiTypesPath, 'utf-8')
    
    // 提取类型定义
    info('提取类型定义...')
    const definitions = extractTypeDefinitions(content)
    
    // 提取JSDoc注释
    const comments = extractJSDocComments(content)
    
    // 生成函数签名文档
    info('生成函数签名文档...')
    const signatures = generateFunctionSignatures(definitions)
    
    // 写入文档文件
    const outputPath = join(projectRoot, 'docs/ai-types-function-signatures.md')
    writeFileSync(outputPath, signatures, 'utf-8')
    
    success(`函数签名文档已生成: ${outputPath}`)
    
    // 输出统计信息
    console.log()
    info('提取统计:')
    console.log(`   - 类型别名: ${definitions.types.length}`)
    console.log(`   - 接口定义: ${definitions.interfaces.length}`)
    console.log(`   - JSDoc注释: ${comments.size}`)
    
    // 显示新增的Azure OpenAI支持
    const azureSupport = definitions.types.find(t => t.name === 'AIProvider')
    if (azureSupport && azureSupport.definition.includes('azure-openai')) {
      success('✨ 检测到新增的Azure OpenAI支持')
    }
    
  } catch (err) {
    error(`提取失败: ${err.message}`)
    console.error(err.stack)
    process.exit(1)
  }
}

// 检查是否直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(err => {
    error(`脚本执行失败: ${err.message}`)
    process.exit(1)
  })
}

export { main as extractAITypesAPI }