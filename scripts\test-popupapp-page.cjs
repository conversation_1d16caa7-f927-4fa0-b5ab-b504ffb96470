const { execSync } = require('child_process')
const path = require('path')

/**
 * 快速测试PopupApp组件页面
 */
function testPopupAppPage() {
  console.log('🚀 启动PopupApp组件测试页面...\n')
  
  try {
    console.log('📋 测试步骤:')
    console.log('1. 构建项目')
    console.log('2. 在浏览器中打开测试页面')
    console.log('3. 验证重构后的PopupApp组件功能\n')
    
    // 构建项目
    console.log('🔨 正在构建项目...')
    execSync('npm run build', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    })
    
    console.log('\n✅ 构建完成！')
    console.log('\n📱 PopupApp测试页面访问方式:')
    console.log('1. 打开Chrome浏览器')
    console.log('2. 访问 chrome://extensions/')
    console.log('3. 开启"开发者模式"')
    console.log('4. 点击"加载已解压的扩展程序"')
    console.log('5. 选择 dist 文件夹')
    console.log('6. 点击扩展程序的"选项"按钮')
    console.log('7. 在左侧导航中点击"PopupApp测试"')
    
    console.log('\n🎯 测试重点:')
    console.log('• 验证shadcn Button组件样式和交互')
    console.log('• 验证shadcn Card组件布局')
    console.log('• 验证shadcn Switch组件功能')
    console.log('• 验证shadcn颜色系统应用')
    console.log('• 验证Separator组件分隔效果')
    console.log('• 测试收藏状态切换')
    console.log('• 测试设置开关交互')
    console.log('• 测试同步功能')
    
    console.log('\n💡 提示:')
    console.log('• 打开浏览器开发者工具查看详细日志')
    console.log('• 所有Chrome API调用都会在控制台显示')
    console.log('• 可以点击"模拟状态变化"按钮测试不同状态')
    console.log('• 收藏状态会随机模拟，多次刷新可看到不同效果')
    
  } catch (error) {
    console.error('❌ 构建失败:', error.message)
    process.exit(1)
  }
}

// 运行测试
testPopupAppPage()