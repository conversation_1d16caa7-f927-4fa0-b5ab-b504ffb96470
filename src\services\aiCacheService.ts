// AI缓存服务类 - 负责AI结果的缓存管理

import {
  AITagGenerationRequest,
  AITagGenerationResponse,
  AICategoryRequest,
  AICategoryResponse,
  AIDescriptionRequest,
  AIDescriptionResponse
} from '../types/ai'
import { IndexedDBService } from '../utils/indexedDB'

/**
 * AI缓存项接口
 */
interface AICacheItem<T> {
  id: string
  requestHash: string
  request: any
  response: T
  createdAt: Date
  expiresAt: Date
  hitCount: number
  lastAccessedAt: Date
}

/**
 * AI缓存统计信息
 */
interface AICacheStats {
  totalItems: number
  hitRate: number
  totalHits: number
  totalMisses: number
  cacheSize: number
  oldestItem?: Date
  newestItem?: Date
}

/**
 * AI缓存服务类
 * 提供AI结果的缓存存储、检索和管理功能
 */
export class AICacheService {
  
  /**
   * 缓存存储名称
   */
  private static readonly CACHE_STORE_NAME = 'ai_cache'
  
  /**
   * 默认缓存过期时间（7天）
   */
  private static readonly DEFAULT_CACHE_DURATION = 7 * 24 * 60 * 60 * 1000
  
  /**
   * 最大缓存项数量
   */
  private static readonly MAX_CACHE_ITEMS = 1000
  
  /**
   * 缓存统计信息
   */
  private stats: AICacheStats = {
    totalItems: 0,
    hitRate: 0,
    totalHits: 0,
    totalMisses: 0,
    cacheSize: 0
  }

  /**
   * 初始化缓存服务
   */
  async initialize(): Promise<void> {
    try {
      // 确保数据库已初始化
      await IndexedDBService.initialize()

      // 清理过期缓存
      await this.cleanExpiredCache()

      // 更新统计信息
      await this.updateStats()

      console.log('AI缓存服务初始化完成')
    } catch (error) {
      console.error('AI缓存服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 获取标签缓存
   * @param request 标签生成请求
   * @returns Promise<AITagGenerationResponse | null>
   */
  async getTagsCache(request: AITagGenerationRequest): Promise<AITagGenerationResponse | null> {
    try {
      const requestHash = this.generateRequestHash(request, 'tags')
      const cacheItem = await this.getCacheItem<AITagGenerationResponse>('tags', requestHash)
      
      if (cacheItem) {
        // 更新访问统计
        await this.updateCacheItemAccess(cacheItem.id)
        this.stats.totalHits++
        
        console.log('AI标签缓存命中:', requestHash)
        return cacheItem.response
      }
      
      this.stats.totalMisses++
      return null
    } catch (error) {
      console.error('获取标签缓存失败:', error)
      this.stats.totalMisses++
      return null
    }
  }

  /**
   * 保存标签缓存
   * @param request 标签生成请求
   * @param response 标签生成响应
   * @returns Promise<void>
   */
  async saveTagsCache(request: AITagGenerationRequest, response: AITagGenerationResponse): Promise<void> {
    try {
      const requestHash = this.generateRequestHash(request, 'tags')
      
      const cacheItem: AICacheItem<AITagGenerationResponse> = {
        id: `tags_${requestHash}`,
        requestHash,
        request,
        response,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + AICacheService.DEFAULT_CACHE_DURATION),
        hitCount: 0,
        lastAccessedAt: new Date()
      }
      
      await this.saveCacheItem('tags', cacheItem)
      console.log('AI标签缓存已保存:', requestHash)
    } catch (error) {
      console.error('保存标签缓存失败:', error)
    }
  }

  /**
   * 获取分类缓存
   * @param request 分类建议请求
   * @returns Promise<AICategoryResponse | null>
   */
  async getCategoryCache(request: AICategoryRequest): Promise<AICategoryResponse | null> {
    try {
      const requestHash = this.generateRequestHash(request, 'category')
      const cacheItem = await this.getCacheItem<AICategoryResponse>('category', requestHash)
      
      if (cacheItem) {
        // 更新访问统计
        await this.updateCacheItemAccess(cacheItem.id)
        this.stats.totalHits++
        
        console.log('AI分类缓存命中:', requestHash)
        return cacheItem.response
      }
      
      this.stats.totalMisses++
      return null
    } catch (error) {
      console.error('获取分类缓存失败:', error)
      this.stats.totalMisses++
      return null
    }
  }

  /**
   * 保存分类缓存
   * @param request 分类建议请求
   * @param response 分类建议响应
   * @returns Promise<void>
   */
  async saveCategoryCache(request: AICategoryRequest, response: AICategoryResponse): Promise<void> {
    try {
      const requestHash = this.generateRequestHash(request, 'category')
      
      const cacheItem: AICacheItem<AICategoryResponse> = {
        id: `category_${requestHash}`,
        requestHash,
        request,
        response,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + AICacheService.DEFAULT_CACHE_DURATION),
        hitCount: 0,
        lastAccessedAt: new Date()
      }
      
      await this.saveCacheItem('category', cacheItem)
      console.log('AI分类缓存已保存:', requestHash)
    } catch (error) {
      console.error('保存分类缓存失败:', error)
    }
  }

  /**
   * 获取描述缓存
   * @param request 描述生成请求
   * @returns Promise<AIDescriptionResponse | null>
   */
  async getDescriptionCache(request: AIDescriptionRequest): Promise<AIDescriptionResponse | null> {
    try {
      const requestHash = this.generateRequestHash(request, 'description')
      const cacheItem = await this.getCacheItem<AIDescriptionResponse>('description', requestHash)
      
      if (cacheItem) {
        // 更新访问统计
        await this.updateCacheItemAccess(cacheItem.id)
        this.stats.totalHits++
        
        console.log('AI描述缓存命中:', requestHash)
        return cacheItem.response
      }
      
      this.stats.totalMisses++
      return null
    } catch (error) {
      console.error('获取描述缓存失败:', error)
      this.stats.totalMisses++
      return null
    }
  }

  /**
   * 保存描述缓存
   * @param request 描述生成请求
   * @param response 描述生成响应
   * @returns Promise<void>
   */
  async saveDescriptionCache(request: AIDescriptionRequest, response: AIDescriptionResponse): Promise<void> {
    try {
      const requestHash = this.generateRequestHash(request, 'description')
      
      const cacheItem: AICacheItem<AIDescriptionResponse> = {
        id: `description_${requestHash}`,
        requestHash,
        request,
        response,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + AICacheService.DEFAULT_CACHE_DURATION),
        hitCount: 0,
        lastAccessedAt: new Date()
      }
      
      await this.saveCacheItem('description', cacheItem)
      console.log('AI描述缓存已保存:', requestHash)
    } catch (error) {
      console.error('保存描述缓存失败:', error)
    }
  }

  /**
   * 清理过期缓存
   * @returns Promise<number> 清理的项目数量
   */
  async cleanExpiredCache(): Promise<number> {
    try {
      const now = new Date()
      let cleanedCount = 0
      
      // 获取所有缓存项
      const allItems = await IndexedDBService.getAll<AICacheItem<any>>(AICacheService.CACHE_STORE_NAME)
      
      for (const item of allItems) {
        if (item.expiresAt < now) {
          await IndexedDBService.delete(AICacheService.CACHE_STORE_NAME, item.id)
          cleanedCount++
        }
      }
      
      if (cleanedCount > 0) {
        console.log(`清理了 ${cleanedCount} 个过期的AI缓存项`)
        await this.updateStats()
      }
      
      return cleanedCount
    } catch (error) {
      console.error('清理过期缓存失败:', error)
      return 0
    }
  }

  /**
   * 清理最少使用的缓存项
   * @param count 要清理的数量
   * @returns Promise<number> 实际清理的数量
   */
  async cleanLeastUsedCache(count: number): Promise<number> {
    try {
      // 获取所有缓存项并按使用频率排序
      const allItems = await IndexedDBService.getAll<AICacheItem<any>>(AICacheService.CACHE_STORE_NAME)
      
      // 按命中次数和最后访问时间排序
      allItems.sort((a, b) => {
        if (a.hitCount !== b.hitCount) {
          return a.hitCount - b.hitCount // 命中次数少的排前面
        }
        return a.lastAccessedAt.getTime() - b.lastAccessedAt.getTime() // 最后访问时间早的排前面
      })
      
      const itemsToDelete = allItems.slice(0, Math.min(count, allItems.length))
      let cleanedCount = 0
      
      for (const item of itemsToDelete) {
        await IndexedDBService.delete(AICacheService.CACHE_STORE_NAME, item.id)
        cleanedCount++
      }
      
      if (cleanedCount > 0) {
        console.log(`清理了 ${cleanedCount} 个最少使用的AI缓存项`)
        await this.updateStats()
      }
      
      return cleanedCount
    } catch (error) {
      console.error('清理最少使用缓存失败:', error)
      return 0
    }
  }

  /**
   * 清空所有缓存
   * @returns Promise<void>
   */
  async clearAllCache(): Promise<void> {
    try {
      await IndexedDBService.clear(AICacheService.CACHE_STORE_NAME)
      await this.updateStats()
      console.log('所有AI缓存已清空')
    } catch (error) {
      console.error('清空AI缓存失败:', error)
      throw error
    }
  }

  /**
   * 获取缓存统计信息
   * @returns Promise<AICacheStats>
   */
  async getStats(): Promise<AICacheStats> {
    await this.updateStats()
    return { ...this.stats }
  }

  /**
   * 获取缓存项详情
   * @param type 缓存类型
   * @param limit 限制数量
   * @returns Promise<AICacheItem<any>[]>
   */
  async getCacheItems(type?: string, limit: number = 50): Promise<AICacheItem<any>[]> {
    try {
      const allItems = await IndexedDBService.getAll<AICacheItem<any>>(AICacheService.CACHE_STORE_NAME)
      
      let filteredItems = allItems
      
      if (type) {
        filteredItems = allItems.filter(item => item.id.startsWith(`${type}_`))
      }
      
      // 按创建时间倒序排列
      filteredItems.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      
      return filteredItems.slice(0, limit)
    } catch (error) {
      console.error('获取缓存项失败:', error)
      return []
    }
  }

  /**
   * 删除特定缓存项
   * @param id 缓存项ID
   * @returns Promise<void>
   */
  async deleteCacheItem(id: string): Promise<void> {
    try {
      await IndexedDBService.delete(AICacheService.CACHE_STORE_NAME, id)
      await this.updateStats()
      console.log('缓存项已删除:', id)
    } catch (error) {
      console.error('删除缓存项失败:', error)
      throw error
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 生成请求哈希
   * @param request 请求对象
   * @param type 请求类型
   * @returns string
   */
  private generateRequestHash(request: any, type: string): string {
    // 创建一个标准化的请求对象用于哈希
    const normalizedRequest = {
      type,
      content: request.content?.substring(0, 1000), // 限制内容长度
      title: request.title,
      url: request.url,
      maxTags: request.maxTags,
      maxSuggestions: request.maxSuggestions,
      maxLength: request.maxLength,
      style: request.style,
      language: request.language
    }
    
    // 简单的哈希函数
    const str = JSON.stringify(normalizedRequest)
    let hash = 0
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }

  /**
   * 获取缓存项
   * @param type 缓存类型
   * @param requestHash 请求哈希
   * @returns Promise<AICacheItem<T> | null>
   */
  private async getCacheItem<T>(type: string, requestHash: string): Promise<AICacheItem<T> | null> {
    try {
      const id = `${type}_${requestHash}`
      const item = await IndexedDBService.get<AICacheItem<T>>(AICacheService.CACHE_STORE_NAME, id)
      
      if (!item) {
        return null
      }
      
      // 检查是否过期
      if (item.expiresAt < new Date()) {
        await IndexedDBService.delete(AICacheService.CACHE_STORE_NAME, id)
        return null
      }
      
      return item
    } catch (error) {
      console.error('获取缓存项失败:', error)
      return null
    }
  }

  /**
   * 保存缓存项
   * @param type 缓存类型
   * @param item 缓存项
   * @returns Promise<void>
   */
  private async saveCacheItem<T>(type: string, item: AICacheItem<T>): Promise<void> {
    try {
      // 检查缓存数量限制
      const currentCount = await this.getCacheCount()
      
      if (currentCount >= AICacheService.MAX_CACHE_ITEMS) {
        // 清理一些最少使用的缓存项
        await this.cleanLeastUsedCache(Math.floor(AICacheService.MAX_CACHE_ITEMS * 0.1))
      }
      
      await IndexedDBService.save(AICacheService.CACHE_STORE_NAME, item)
      await this.updateStats()
    } catch (error) {
      console.error('保存缓存项失败:', error)
      throw error
    }
  }

  /**
   * 更新缓存项访问信息
   * @param id 缓存项ID
   * @returns Promise<void>
   */
  private async updateCacheItemAccess(id: string): Promise<void> {
    try {
      const item = await IndexedDBService.get<AICacheItem<any>>(AICacheService.CACHE_STORE_NAME, id)
      
      if (item) {
        item.hitCount++
        item.lastAccessedAt = new Date()
        await IndexedDBService.save(AICacheService.CACHE_STORE_NAME, item)
      }
    } catch (error) {
      console.error('更新缓存项访问信息失败:', error)
    }
  }

  /**
   * 获取缓存项数量
   * @returns Promise<number>
   */
  private async getCacheCount(): Promise<number> {
    try {
      const allItems = await IndexedDBService.getAll<AICacheItem<any>>(AICacheService.CACHE_STORE_NAME)
      return allItems.length
    } catch (error) {
      console.error('获取缓存数量失败:', error)
      return 0
    }
  }

  /**
   * 更新统计信息
   * @returns Promise<void>
   */
  private async updateStats(): Promise<void> {
    try {
      const allItems = await IndexedDBService.getAll<AICacheItem<any>>(AICacheService.CACHE_STORE_NAME)
      
      this.stats.totalItems = allItems.length
      this.stats.cacheSize = this.calculateCacheSize(allItems)
      
      if (allItems.length > 0) {
        const dates = allItems
          .map(item => item.createdAt)
          .filter(date => date instanceof Date && !isNaN(date.getTime()))
        
        if (dates.length > 0) {
          this.stats.oldestItem = new Date(Math.min(...dates.map(d => d.getTime())))
          this.stats.newestItem = new Date(Math.max(...dates.map(d => d.getTime())))
        } else {
          this.stats.oldestItem = undefined
          this.stats.newestItem = undefined
        }
      } else {
        this.stats.oldestItem = undefined
        this.stats.newestItem = undefined
      }
      
      // 计算命中率
      const totalRequests = this.stats.totalHits + this.stats.totalMisses
      this.stats.hitRate = totalRequests > 0 ? this.stats.totalHits / totalRequests : 0
    } catch (error) {
      console.error('更新统计信息失败:', error)
    }
  }

  /**
   * 计算缓存大小（估算）
   * @param items 缓存项列表
   * @returns number 大小（字节）
   */
  private calculateCacheSize(items: AICacheItem<any>[]): number {
    let totalSize = 0
    
    for (const item of items) {
      // 简单估算：JSON字符串长度 * 2（UTF-16编码）
      const jsonStr = JSON.stringify(item)
      totalSize += jsonStr.length * 2
    }
    
    return totalSize
  }
}

// 导出单例实例
export const aiCacheService = new AICacheService()