// 标签云组件 - 以云状布局显示标签

import React, { useMemo } from 'react'
import { Hash } from 'lucide-react'
import type { Tag } from '../types'

interface TagWithStats extends Tag {
  usageCount: number
}

interface TagCloudProps {
  /** 标签列表数据 */
  tags: TagWithStats[]
  /** 点击标签回调 */
  onTagClick?: (tag: Tag) => void
  /** 编辑标签回调 */
  onTagEdit?: (tag: Tag) => void
  /** 删除标签回调 */
  onTagDelete?: (tag: Tag) => void
  /** 最大字体大小 */
  maxFontSize?: number
  /** 最小字体大小 */
  minFontSize?: number
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 标签云组件
 * 根据使用频率以不同大小显示标签，形成云状布局
 */
const TagCloud: React.FC<TagCloudProps> = React.memo(({
  tags,
  onTagClick,
  onTagEdit,
  onTagDelete,
  maxFontSize = 32,
  minFontSize = 14,
  className = ''
}) => {
  // 计算标签的字体大小和样式
  const processedTags = useMemo(() => {
    if (tags.length === 0) return []

    // 找出最大和最小使用次数
    const maxUsage = Math.max(...tags.map(tag => tag.usageCount))
    const minUsage = Math.min(...tags.map(tag => tag.usageCount))
    const usageRange = maxUsage - minUsage || 1

    return tags.map(tag => {
      // 计算字体大小（基于使用次数）
      const normalizedUsage = (tag.usageCount - minUsage) / usageRange
      const fontSize = minFontSize + (maxFontSize - minFontSize) * normalizedUsage

      // 计算透明度（使用次数越高越不透明）
      const opacity = 0.6 + 0.4 * normalizedUsage

      // 获取使用频率等级
      const getUsageLevel = () => {
        if (tag.usageCount >= maxUsage * 0.8) return 'high'
        if (tag.usageCount >= maxUsage * 0.4) return 'medium'
        if (tag.usageCount > 0) return 'low'
        return 'none'
      }

      return {
        ...tag,
        fontSize: Math.round(fontSize),
        opacity,
        usageLevel: getUsageLevel()
      }
    })
  }, [tags, maxFontSize, minFontSize])

  // 处理标签点击
  const handleTagClick = (tag: Tag, event: React.MouseEvent) => {
    event.preventDefault()
    onTagClick?.(tag)
  }

  // 处理标签右键菜单
  const handleTagContextMenu = (tag: Tag, event: React.MouseEvent) => {
    event.preventDefault()
    // 这里可以显示上下文菜单，暂时先调用编辑
    onTagEdit?.(tag)
  }

  // 获取标签的悬停效果类
  const getHoverClasses = (usageLevel: string) => {
    switch (usageLevel) {
      case 'high':
        return 'hover:bg-red-100 hover:text-red-700 hover:shadow-md'
      case 'medium':
        return 'hover:bg-yellow-100 hover:text-yellow-700 hover:shadow-md'
      case 'low':
        return 'hover:bg-green-100 hover:text-green-700 hover:shadow-md'
      default:
        return 'hover:bg-gray-100 hover:text-gray-700 hover:shadow-md'
    }
  }

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <Hash className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        暂无标签数据
      </h3>
      <p className="text-gray-500">
        创建一些标签后，它们将以云状形式显示在这里
      </p>
    </div>
  )

  if (processedTags.length === 0) {
    return (
      <div className={className}>
        {renderEmptyState()}
      </div>
    )
  }

  return (
    <div className={`${className}`}>
      {/* 标签云容器 */}
      <div className="flex flex-wrap items-center justify-center gap-2 p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg min-h-[300px]">
        {processedTags.map((tag) => (
          <button
            key={tag.id}
            onClick={(e) => handleTagClick(tag, e)}
            onContextMenu={(e) => handleTagContextMenu(tag, e)}
            className={`
              inline-flex items-center px-3 py-1 rounded-full border transition-all duration-200 cursor-pointer
              ${getHoverClasses(tag.usageLevel)}
              transform hover:scale-105 active:scale-95
            `}
            style={{
              fontSize: `${tag.fontSize}px`,
              backgroundColor: tag.color ? `${tag.color}20` : '#F3F4F6',
              borderColor: tag.color ? `${tag.color}40` : '#D1D5DB',
              color: tag.color || '#374151',
              opacity: tag.opacity
            }}
            title={`${tag.name} (使用 ${tag.usageCount} 次)`}
          >
            {/* 标签名称 */}
            <span className="font-medium">
              {tag.name}
            </span>

            {/* 使用次数指示器 */}
            {tag.usageCount > 0 && (
              <span 
                className="ml-2 text-xs bg-white bg-opacity-70 px-1.5 py-0.5 rounded-full"
                style={{ fontSize: `${Math.max(10, tag.fontSize * 0.6)}px` }}
              >
                {tag.usageCount}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* 图例 */}
      <div className="mt-4 flex items-center justify-center space-x-6 text-sm text-gray-600">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-200 rounded-full"></div>
          <span>高频使用</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-yellow-200 rounded-full"></div>
          <span>中频使用</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-200 rounded-full"></div>
          <span>低频使用</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
          <span>未使用</span>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-2 text-center text-xs text-gray-500">
        标签大小反映使用频率，点击标签查看详情，右键编辑标签
      </div>
    </div>
  )
})

// 设置显示名称便于调试
TagCloud.displayName = 'TagCloud'

export default TagCloud