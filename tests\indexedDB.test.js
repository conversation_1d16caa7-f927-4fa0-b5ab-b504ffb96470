import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { IndexedDBService } from '../src/utils/indexedDB.ts'
import { ModelFactory } from '../src/utils/modelFactory.ts'

// 模拟IndexedDB环境
import 'fake-indexeddb/auto'

describe('IndexedDBService', () => {
  let dbService

  beforeEach(async () => {
    dbService = new IndexedDBService()
    await dbService.init()
  })

  afterEach(async () => {
    await dbService.clearAllData()
    dbService.close()
  })

  describe('数据库初始化', () => {
    it('应该成功初始化数据库', async () => {
      const stats = await dbService.getStats()
      expect(stats).toEqual({
        bookmarkCount: 0,
        categoryCount: 0,
        tagCount: 0
      })
    })
  })

  describe('书签操作', () => {
    it('应该保存和获取书签', async () => {
      const bookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        description: '测试描述',
        tags: ['测试'],
        category: 'default'
      })

      // 保存书签
      const savedId = await dbService.saveBookmark(bookmark)
      expect(savedId).toBe(bookmark.id)

      // 获取书签
      const retrieved = await dbService.getBookmark(bookmark.id)
      expect(retrieved).toBeTruthy()
      expect(retrieved.title).toBe(bookmark.title)
      expect(retrieved.url).toBe(bookmark.url)
    })

    it('应该获取所有书签', async () => {
      const bookmark1 = ModelFactory.createBookmark({
        type: 'url',
        title: '书签1',
        url: 'https://example1.com',
        category: 'default'
      })

      const bookmark2 = ModelFactory.createBookmark({
        type: 'text',
        title: '书签2',
        content: '文本内容',
        category: 'default'
      })

      await dbService.saveBookmark(bookmark1)
      await dbService.saveBookmark(bookmark2)

      const bookmarks = await dbService.getBookmarks()
      expect(bookmarks).toHaveLength(2)
    })

    it('应该根据筛选条件获取书签', async () => {
      const urlBookmark = ModelFactory.createBookmark({
        type: 'url',
        title: 'URL书签',
        url: 'https://example.com',
        category: 'work'
      })

      const textBookmark = ModelFactory.createBookmark({
        type: 'text',
        title: '文本书签',
        content: '文本内容',
        category: 'personal'
      })

      await dbService.saveBookmark(urlBookmark)
      await dbService.saveBookmark(textBookmark)

      // 按类型筛选
      const urlBookmarks = await dbService.getBookmarks({ type: 'url' })
      expect(urlBookmarks).toHaveLength(1)
      expect(urlBookmarks[0].type).toBe('url')

      // 按分类筛选
      const workBookmarks = await dbService.getBookmarks({ categories: ['work'] })
      expect(workBookmarks).toHaveLength(1)
      expect(workBookmarks[0].category).toBe('work')
    })

    it('应该更新书签', async () => {
      const bookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '原标题',
        url: 'https://example.com',
        category: 'default'
      })

      await dbService.saveBookmark(bookmark)

      // 更新书签
      await dbService.updateBookmark(bookmark.id, {
        title: '新标题',
        description: '新描述'
      })

      const updated = await dbService.getBookmark(bookmark.id)
      expect(updated.title).toBe('新标题')
      expect(updated.description).toBe('新描述')
      expect(updated.url).toBe(bookmark.url) // 未更新的字段保持不变
    })

    it('应该删除书签', async () => {
      const bookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        category: 'default'
      })

      await dbService.saveBookmark(bookmark)
      
      // 确认书签存在
      let retrieved = await dbService.getBookmark(bookmark.id)
      expect(retrieved).toBeTruthy()

      // 删除书签
      await dbService.deleteBookmark(bookmark.id)

      // 确认书签已删除
      retrieved = await dbService.getBookmark(bookmark.id)
      expect(retrieved).toBeNull()
    })

    it('应该批量删除书签', async () => {
      const bookmark1 = ModelFactory.createBookmark({
        type: 'url',
        title: '书签1',
        url: 'https://example1.com',
        category: 'default'
      })

      const bookmark2 = ModelFactory.createBookmark({
        type: 'url',
        title: '书签2',
        url: 'https://example2.com',
        category: 'default'
      })

      await dbService.saveBookmark(bookmark1)
      await dbService.saveBookmark(bookmark2)

      // 批量删除
      await dbService.deleteBookmarks([bookmark1.id, bookmark2.id])

      // 确认都已删除
      const retrieved1 = await dbService.getBookmark(bookmark1.id)
      const retrieved2 = await dbService.getBookmark(bookmark2.id)
      expect(retrieved1).toBeNull()
      expect(retrieved2).toBeNull()
    })
  })

  describe('分类操作', () => {
    it('应该保存和获取分类', async () => {
      const category = ModelFactory.createCategory({
        name: '工作',
        description: '工作相关',
        color: '#FF5733'
      })

      // 保存分类
      const savedId = await dbService.saveCategory(category)
      expect(savedId).toBe(category.id)

      // 获取分类
      const retrieved = await dbService.getCategory(category.id)
      expect(retrieved).toBeTruthy()
      expect(retrieved.name).toBe(category.name)
      expect(retrieved.description).toBe(category.description)
    })

    it('应该获取所有分类', async () => {
      const category1 = ModelFactory.createCategory({ name: '工作' })
      const category2 = ModelFactory.createCategory({ name: '学习' })

      await dbService.saveCategory(category1)
      await dbService.saveCategory(category2)

      const categories = await dbService.getCategories()
      expect(categories).toHaveLength(2)
    })

    it('应该更新分类', async () => {
      const category = ModelFactory.createCategory({
        name: '原名称',
        description: '原描述'
      })

      await dbService.saveCategory(category)

      // 更新分类
      await dbService.updateCategory(category.id, {
        name: '新名称',
        description: '新描述'
      })

      const updated = await dbService.getCategory(category.id)
      expect(updated.name).toBe('新名称')
      expect(updated.description).toBe('新描述')
    })

    it('应该删除分类', async () => {
      const category = ModelFactory.createCategory({ name: '测试分类' })

      await dbService.saveCategory(category)
      
      // 确认分类存在
      let retrieved = await dbService.getCategory(category.id)
      expect(retrieved).toBeTruthy()

      // 删除分类
      await dbService.deleteCategory(category.id)

      // 确认分类已删除
      retrieved = await dbService.getCategory(category.id)
      expect(retrieved).toBeNull()
    })
  })

  describe('标签操作', () => {
    it('应该保存和获取标签', async () => {
      const tag = ModelFactory.createTag({
        name: '重要',
        color: '#FF5733'
      })

      // 保存标签
      const savedId = await dbService.saveTag(tag)
      expect(savedId).toBe(tag.id)

      // 获取标签
      const retrieved = await dbService.getTag(tag.id)
      expect(retrieved).toBeTruthy()
      expect(retrieved.name).toBe(tag.name)
      expect(retrieved.color).toBe(tag.color)
    })

    it('应该根据名称获取标签', async () => {
      const tag = ModelFactory.createTag({ name: '重要' })

      await dbService.saveTag(tag)

      const retrieved = await dbService.getTagByName('重要')
      expect(retrieved).toBeTruthy()
      expect(retrieved.id).toBe(tag.id)
    })

    it('应该获取所有标签', async () => {
      const tag1 = ModelFactory.createTag({ name: '重要' })
      const tag2 = ModelFactory.createTag({ name: '紧急' })

      await dbService.saveTag(tag1)
      await dbService.saveTag(tag2)

      const tags = await dbService.getTags()
      expect(tags).toHaveLength(2)
    })

    it('应该更新标签', async () => {
      const tag = ModelFactory.createTag({
        name: '原名称',
        color: '#FF0000'
      })

      await dbService.saveTag(tag)

      // 更新标签
      await dbService.updateTag(tag.id, {
        name: '新名称',
        color: '#00FF00'
      })

      const updated = await dbService.getTag(tag.id)
      expect(updated.name).toBe('新名称')
      expect(updated.color).toBe('#00FF00')
    })

    it('应该删除标签', async () => {
      const tag = ModelFactory.createTag({ name: '测试标签' })

      await dbService.saveTag(tag)
      
      // 确认标签存在
      let retrieved = await dbService.getTag(tag.id)
      expect(retrieved).toBeTruthy()

      // 删除标签
      await dbService.deleteTag(tag.id)

      // 确认标签已删除
      retrieved = await dbService.getTag(tag.id)
      expect(retrieved).toBeNull()
    })
  })

  describe('设置操作', () => {
    it('应该保存和获取设置', async () => {
      const key = 'test_setting'
      const value = { option1: true, option2: 'value' }

      // 保存设置
      await dbService.saveSetting(key, value)

      // 获取设置
      const retrieved = await dbService.getSetting(key)
      expect(retrieved).toEqual(value)
    })

    it('应该返回null对于不存在的设置', async () => {
      const retrieved = await dbService.getSetting('non_existent_key')
      expect(retrieved).toBeNull()
    })
  })

  describe('数据统计', () => {
    it('应该返回正确的统计信息', async () => {
      // 添加测试数据
      const bookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        category: 'default'
      })
      const category = ModelFactory.createCategory({ name: '测试分类' })
      const tag = ModelFactory.createTag({ name: '测试标签' })

      await dbService.saveBookmark(bookmark)
      await dbService.saveCategory(category)
      await dbService.saveTag(tag)

      const stats = await dbService.getStats()
      expect(stats.bookmarkCount).toBe(1)
      expect(stats.categoryCount).toBe(1)
      expect(stats.tagCount).toBe(1)
    })
  })

  describe('数据清理', () => {
    it('应该清空所有数据', async () => {
      // 添加测试数据
      const bookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        category: 'default'
      })
      const category = ModelFactory.createCategory({ name: '测试分类' })
      const tag = ModelFactory.createTag({ name: '测试标签' })

      await dbService.saveBookmark(bookmark)
      await dbService.saveCategory(category)
      await dbService.saveTag(tag)

      // 确认数据存在
      let stats = await dbService.getStats()
      expect(stats.bookmarkCount).toBe(1)
      expect(stats.categoryCount).toBe(1)
      expect(stats.tagCount).toBe(1)

      // 清空数据
      await dbService.clearAllData()

      // 确认数据已清空
      stats = await dbService.getStats()
      expect(stats.bookmarkCount).toBe(0)
      expect(stats.categoryCount).toBe(0)
      expect(stats.tagCount).toBe(0)
    })
  })
})