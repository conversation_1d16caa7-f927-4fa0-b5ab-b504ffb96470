// 智能文件夹选择组件 - 支持推荐文件夹选择和新建文件夹

import React, { useState, useEffect } from 'react'
import { Folder, Plus, Check, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import CategoryModal from './CategoryModal'
import { categoryService } from '../services/categoryService'
import type { Category, CategoryInput } from '../types'

/**
 * 文件夹推荐项接口
 */
interface FolderRecommendation {
  name: string
  confidence: number
  reason: string
  exists?: boolean
}

/**
 * 文件夹推荐响应接口
 */
interface FolderRecommendationResponse {
  recommendedFolders: FolderRecommendation[]
  reasoning?: string
}

/**
 * 智能文件夹选择组件属性接口
 */
interface SmartFolderSelectorProps {
  /** 推荐的文件夹 */
  recommendations?: FolderRecommendationResponse
  /** 当前选中的文件夹 */
  selectedFolder?: string
  /** 文件夹选择回调 */
  onFolderSelect: (folder: string) => void
  /** 新建文件夹回调 */
  onCreateFolder?: (folderName: string) => void
  /** 是否禁用 */
  disabled?: boolean
  /** 错误信息 */
  error?: string
  /** 成功信息 */
  success?: string
}

/**
 * 智能文件夹选择组件
 * 提供推荐文件夹选择，支持新建不存在的文件夹
 */
const SmartFolderSelector: React.FC<SmartFolderSelectorProps> = ({
  recommendations,
  selectedFolder,
  onFolderSelect,
  onCreateFolder,
  disabled = false,
  error,
  success
}) => {
  const [existingFolders, setExistingFolders] = useState<Category[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [createModalLoading, setCreateModalLoading] = useState(false)
  const [pendingFolderName, setPendingFolderName] = useState<string>('')
  const [folderErrors, setFolderErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  const [processedRecommendations, setProcessedRecommendations] = useState<FolderRecommendation[]>([])

  // 获取现有文件夹
  useEffect(() => {
    const fetchFolders = async () => {
      try {
        setLoading(true)
        const folders = await categoryService.getCategories()
        setExistingFolders(folders)
      } catch (error) {
        console.error('获取文件夹列表失败:', error)
      } finally {
        setLoading(false)
      }
    }
    fetchFolders()
  }, [])

  // 检查推荐文件夹是否存在
  useEffect(() => {
    if (!recommendations?.recommendedFolders) {
      setProcessedRecommendations([])
      return
    }

    const updatedRecommendations = recommendations.recommendedFolders.map(folder => {
      const exists = existingFolders.some(existing =>
        existing.name.toLowerCase() === folder.name.toLowerCase()
      )
      console.log('📁 处理推荐文件夹:', {
        folderName: folder.name,
        exists,
        existingFolders: existingFolders.map(f => f.name)
      })
      return {
        ...folder,
        exists
      }
    })

    console.log('📋 更新处理后的推荐文件夹:', updatedRecommendations)
    setProcessedRecommendations(updatedRecommendations)

    // 自动选择置信度最高的存在的文件夹（仅当当前没有选择或选择的是默认分类时）
    if (updatedRecommendations.length > 0 && (!selectedFolder || selectedFolder === '默认分类')) {
      // 找到置信度最高且存在的文件夹
      const bestFolder = updatedRecommendations
        .filter(folder => folder.exists && folder.confidence >= 0.7) // 只考虑存在且置信度>=0.7的文件夹
        .sort((a, b) => b.confidence - a.confidence)[0] // 按置信度降序排序，取第一个

      if (bestFolder) {
        console.log('🎯 自动选择高置信度文件夹:', {
          folderName: bestFolder.name,
          confidence: bestFolder.confidence,
          currentSelected: selectedFolder
        })
        onFolderSelect(bestFolder.name)
      }
    }
  }, [existingFolders, recommendations, selectedFolder, onFolderSelect])

  /**
   * 处理文件夹点击
   */
  const handleFolderClick = async (folderName: string, exists: boolean = true) => {
    console.log('🔍 文件夹点击调试:', { folderName, exists, disabled, selectedFolder })

    if (disabled) {
      console.log('❌ 组件已禁用，无法选择文件夹')
      return
    }

    // 清除之前的错误
    setFolderErrors(prev => ({ ...prev, [folderName]: '' }))

    if (exists) {
      // 文件夹存在，直接选择
      console.log('✅ 选择现有文件夹:', folderName)
      console.log('📤 调用onFolderSelect回调:', { folderName, currentSelected: selectedFolder })
      onFolderSelect(folderName)
    } else {
      // 文件夹不存在，提示用户创建
      console.log('🆕 文件夹不存在，打开创建模态框:', folderName)
      setPendingFolderName(folderName)
      setShowCreateModal(true)
    }
  }

  /**
   * 检查文件夹是否存在
   */
  const checkFolderExists = (folderName: string): boolean => {
    const exists = existingFolders.some(existing =>
      existing.name.toLowerCase() === folderName.toLowerCase()
    )
    console.log('🔍 检查文件夹存在性:', {
      folderName,
      exists,
      existingFolders: existingFolders.map(f => f.name)
    })
    return exists
  }

  /**
   * 处理新建文件夹
   */
  const handleCreateFolder = async (categoryData: CategoryInput) => {
    try {
      setCreateModalLoading(true)
      const newCategory = await categoryService.createCategory(categoryData)

      // 更新本地文件夹列表
      setExistingFolders(prev => [...prev, newCategory])

      // 自动选中新创建的文件夹
      onFolderSelect(newCategory.name)

      // 关闭模态框
      setShowCreateModal(false)
      setPendingFolderName('')

      // 如果有回调，也调用它
      if (onCreateFolder) {
        onCreateFolder(newCategory.name)
      }

      console.log('新文件夹创建成功:', newCategory.name)
    } catch (error) {
      console.error('创建文件夹失败:', error)
      // 错误会由CategoryModal组件处理
    } finally {
      setCreateModalLoading(false)
    }
  }

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 border-green-200'
    if (confidence >= 0.6) return 'text-yellow-600 border-yellow-200'
    return 'text-red-600 border-red-200'
  }

  /**
   * 获取置信度文本
   */
  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  if (!recommendations?.recommendedFolders?.length && processedRecommendations.length === 0) {
    return null
  }

  return (
    <>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-sm">
            <Folder className="w-4 h-4 mr-2" />
            推荐文件夹
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* 错误和成功提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          {success && (
            <div className="text-sm text-green-600 bg-green-50 border border-green-200 rounded p-2">
              {success}
            </div>
          )}

          {/* 推荐文件夹列表 */}
          <div className="space-y-2">
            {processedRecommendations.map((folder, index) => {
              const isSelected = selectedFolder === folder.name
              const folderError = folderErrors[folder.name]
              const exists = folder.exists !== undefined ? folder.exists : checkFolderExists(folder.name)

              return (
                <div key={index} className="space-y-1">
                  <div
                    className={`
                      flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors
                      ${isSelected
                        ? 'border-primary bg-primary/5'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }
                      ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                      ${!exists ? 'border-dashed' : ''}
                    `}
                    onClick={() => handleFolderClick(folder.name, exists)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center">
                        {isSelected ? (
                          <Check className="w-4 h-4 text-primary" />
                        ) : (
                          <Folder className={`w-4 h-4 ${exists ? 'text-blue-500' : 'text-gray-400'}`} />
                        )}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{folder.name}</span>
                          {!exists && (
                            <Badge variant="outline" className="text-xs text-orange-600 border-orange-200">
                              不存在
                            </Badge>
                          )}
                          <Badge
                            variant="outline"
                            className={`text-xs ${getConfidenceColor(folder.confidence)}`}
                          >
                            {getConfidenceText(folder.confidence)}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {folder.reason}
                        </div>
                      </div>
                    </div>
                    {!exists && (
                      <Plus className="w-4 h-4 text-gray-400" />
                    )}
                  </div>

                  {/* 文件夹错误提示 */}
                  {folderError && (
                    <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded p-2 ml-4">
                      {folderError}
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* 推荐理由 */}
          {recommendations.reasoning && (
            <>
              <Separator />
              <div className="text-xs text-muted-foreground">
                {recommendations.reasoning}
              </div>
            </>
          )}

          {/* 新建文件夹按钮 */}
          <div className="pt-3 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setPendingFolderName('')
                setShowCreateModal(true)
              }}
              disabled={disabled}
              className="h-8 text-xs"
            >
              <Plus className="w-3 h-3 mr-1" />
              新建文件夹
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 新建文件夹模态框 */}
      <CategoryModal
        isOpen={showCreateModal}
        type="create"
        initialData={pendingFolderName ? { name: pendingFolderName } : undefined}
        onSave={handleCreateFolder}
        onClose={() => {
          setShowCreateModal(false)
          setPendingFolderName('')
        }}
        loading={createModalLoading}
      />
    </>
  )
}

export default SmartFolderSelector
