// AI服务类 - 负责AI功能的实际调用

import {
  AIConfig,
  AITagGenerationRequest,
  AITagGenerationResponse,
  AICategoryRequest,
  AICategoryResponse,
  AIDescriptionRequest,
  AIDescriptionResponse,
  AIServiceStats,
  AIServiceError,
  AIBatchRequest,
  AIBatchResponse
} from '../types/ai'
import { aiConfigService } from './aiConfigService'
import { aiCacheService } from './aiCacheService'
import { aiChatService } from './aiChatService'
import { aiIntegrationService } from './aiIntegrationService'
import { tagService } from './tagService'

/**
 * AI服务类
 * 提供标签生成、分类建议、描述生成等AI功能
 */
export class AIService {
  
  /**
   * 服务统计信息
   */
  private stats: AIServiceStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    errorRate: 0,
    tagGenerationCount: 0,
    categoryGenerationCount: 0,
    descriptionGenerationCount: 0,
    dailyStats: []
  }

  /**
   * 错误日志
   */
  private errorLog: AIServiceError[] = []

  /**
   * 批处理队列
   */
  private batchQueue: AIBatchRequest[] = []

  /**
   * 生成标签（改进版，支持并行处理和降级策略）
   * @param request 标签生成请求
   * @param useCache 是否使用缓存
   * @returns Promise<AITagGenerationResponse>
   */
  async generateTags(request: AITagGenerationRequest, useCache: boolean = true): Promise<AITagGenerationResponse> {
    const startTime = Date.now()

    try {
      console.log('开始生成AI标签:', request.title || request.content?.substring(0, 50))

      // 获取已有标签列表
      const existingTags = await tagService.getTags()
      const existingTagNames = existingTags.map(tag => tag.name)

      // 将已有标签添加到请求中
      const enhancedRequest = {
        ...request,
        existingTags: existingTagNames
      }

      // 构建提示词
      const prompt = this.buildTagGenerationPrompt(enhancedRequest)

      // 根据实际使用的AI提供商动态调整超时时间
      const actualProvider = await this.getActualAIProvider()
      const isLocalAI = this.isLocalAIProviderByName(actualProvider?.name || '')

      // 本地AI使用更长的超时时间
      const aiTimeout = isLocalAI ? 120000 : 30000 // 本地AI: 2分钟，云端AI: 30秒
      const fallbackDelay = isLocalAI ? 45000 : 15000 // 本地AI: 45秒后降级，云端AI: 15秒后降级

      console.log(`实际AI提供商: ${actualProvider?.name || '未知'}, 类型: ${isLocalAI ? '本地' : '云端'}AI, 超时时间: ${aiTimeout/1000}秒, 降级延迟: ${fallbackDelay/1000}秒`)

      // 创建AI调用Promise
      const aiPromise = this.callAIWithTimeout(prompt, enhancedRequest, aiTimeout)

      // 创建降级策略Promise
      const fallbackPromise = this.createDelayedFallback(enhancedRequest, fallbackDelay)

      // 使用Promise.race等待第一个完成的结果
      const result = await Promise.race([aiPromise, fallbackPromise])

      const processingTime = Date.now() - startTime

      // 如果是降级结果，启动后台AI优化
      if (result.reasoning?.includes('本地规则')) {
        console.log('使用降级策略，启动后台AI优化')
        this.startBackgroundAIOptimization(prompt, enhancedRequest, result, useCache)
      }

      // 更新统计
      this.updateStats('tags', true, processingTime)

      // 保存到缓存
      if (useCache && result.confidence > 0.7) {
        await aiCacheService.saveTagsCache(request, result)
      }

      console.log(`标签生成完成: ${result.tags.join(', ')}`)
      return result

    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error('AI标签生成失败:', error)

      // 记录错误
      try {
        const config = await aiConfigService.getConfig()
        this.logError(error, 'generateTags', config)
      } catch (configError) {
        console.warn('获取AI配置失败，跳过错误记录:', configError)
      }

      // 更新统计
      this.updateStats('tags', false, processingTime)

      // 使用降级策略作为最后的备选
      console.log('使用最终降级策略')
      return await this.fallbackTagGeneration(request)
    }
  }

  /**
   * 生成分类建议
   * @param request 分类建议请求
   * @param useCache 是否使用缓存
   * @returns Promise<AICategoryResponse>
   */
  async generateCategory(request: AICategoryRequest, useCache: boolean = true): Promise<AICategoryResponse> {
    const startTime = Date.now()

    try {
      console.log('开始生成AI分类建议:', request.title || request.content.substring(0, 50))

      // 检查缓存
      if (useCache) {
        const cachedResult = await aiCacheService.getCategoryCache(request)
        if (cachedResult) {
          console.log('使用缓存的AI分类结果')
          return cachedResult
        }
      }

      // 构建提示词
      const prompt = this.buildCategoryGenerationPrompt(request)

      // 使用aiChatService生成分类建议
      const aiResponse = await aiChatService.generateText({
        prompt,
        generationType: 'category',
        context: {
          title: request.title,
          content: request.content,
          url: request.url
        },
        maxLength: 100
      })

      // 解析响应
      const categoryData = this.parseCategoryFromResponse(aiResponse.content)

      const processingTime = Date.now() - startTime

      // 更新统计
      this.updateStats('category', true, processingTime)

      const response: AICategoryResponse = {
        category: categoryData.category,
        confidence: categoryData.confidence,
        alternatives: categoryData.alternatives,
        reasoning: this.extractReasoning(aiResponse.content)
      }

      // 保存到缓存
      if (useCache && response.confidence > 0.7) {
        await aiCacheService.saveCategoryCache(request, response)
      }

      console.log(`AI分类建议成功: ${categoryData.category}`)
      return response

    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error('AI分类建议失败:', error)

      // 记录错误
      this.logError(error, 'generateCategory', await aiConfigService.getConfig())

      // 更新统计
      this.updateStats('category', false, processingTime)

      // 尝试降级策略
      return await this.fallbackCategoryGeneration(request)
    }
  }

  /**
   * 生成描述
   * @param request 描述生成请求
   * @param useCache 是否使用缓存
   * @returns Promise<AIDescriptionResponse>
   */
  async generateDescription(request: AIDescriptionRequest, useCache: boolean = true): Promise<AIDescriptionResponse> {
    const startTime = Date.now()

    try {
      console.log('开始生成AI描述:', request.title || (request.content ? request.content.substring(0, 50) : '无内容'))

      // 检查缓存
      if (useCache) {
        const cachedResult = await aiCacheService.getDescriptionCache(request)
        if (cachedResult) {
          console.log('使用缓存的AI描述结果')
          return cachedResult
        }
      }

      // 构建提示词
      const prompt = this.buildDescriptionGenerationPrompt(request)

      // 根据实际使用的AI提供商动态调整超时时间
      const actualProvider = await this.getActualAIProvider()
      const isLocalAI = this.isLocalAIProviderByName(actualProvider?.name || '')

      // 本地AI使用更长的超时时间
      const aiTimeout = isLocalAI ? 120000 : 30000 // 本地AI: 2分钟，云端AI: 30秒
      const fallbackDelay = isLocalAI ? 45000 : 15000 // 本地AI: 45秒后降级，云端AI: 15秒后降级

      console.log(`实际AI提供商: ${actualProvider?.name || '未知'}, 类型: ${isLocalAI ? '本地' : '云端'}AI, 超时时间: ${aiTimeout/1000}秒, 降级延迟: ${fallbackDelay/1000}秒`)

      // 创建AI调用Promise
      const aiPromise = this.callAIWithTimeoutForDescription(prompt, request, aiTimeout)

      // 创建降级策略Promise
      const fallbackPromise = this.createDelayedFallbackForDescription(request, fallbackDelay)

      // 使用Promise.race等待第一个完成的结果
      const result = await Promise.race([aiPromise, fallbackPromise])

      const processingTime = Date.now() - startTime

      // 如果是降级结果，启动后台AI优化
      if (result.description === request.title || result.description.includes('暂无详细描述')) {
        console.log('使用降级策略，启动后台AI优化')
        this.startBackgroundAIOptimizationForDescription(prompt, request, result, useCache)
      }

      // 更新统计
      this.updateStats('description', true, processingTime)

      // 保存到缓存
      if (useCache && result.confidence > 0.7) {
        await aiCacheService.saveDescriptionCache(request, result)
      }

      console.log(`描述生成完成: ${result.description.substring(0, 100)}...`)
      return result

    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error('AI描述生成失败:', error)

      // 记录错误
      try {
        const config = await aiConfigService.getConfig()
        this.logError(error, 'generateDescription', config)
      } catch (configError) {
        console.warn('获取AI配置失败，跳过错误记录:', configError)
      }

      // 更新统计
      this.updateStats('description', false, processingTime)

      // 使用降级策略作为最后的备选
      console.log('使用最终降级策略')
      return await this.fallbackDescriptionGeneration(request)
    }
  }

  /**
   * 批量处理AI请求
   * @param batchRequest 批处理请求
   * @returns Promise<AIBatchResponse>
   */
  async processBatch(batchRequest: AIBatchRequest): Promise<AIBatchResponse> {
    const startTime = Date.now()
    
    try {
      console.log(`开始批量处理AI请求: ${batchRequest.items.length}个项目`)
      
      const results = []
      
      for (const item of batchRequest.items) {
        try {
          let result: any
          
          switch (item.type) {
            case 'tags':
              result = await this.generateTags(item.data as AITagGenerationRequest)
              break
            case 'category':
              result = await this.generateCategory(item.data as AICategoryRequest)
              break
            case 'description':
              result = await this.generateDescription(item.data as AIDescriptionRequest)
              break
            default:
              throw new Error(`不支持的批处理类型: ${item.type}`)
          }
          
          results.push({
            id: item.id,
            success: true,
            data: result
          })
          
        } catch (error) {
          results.push({
            id: item.id,
            success: false,
            error: (error as Error).message
          })
        }
        
        // 添加延迟以避免API限制
        await this.delay(100)
      }
      
      const totalProcessingTime = Date.now() - startTime
      
      const response: AIBatchResponse = {
        id: batchRequest.id,
        results,
        completedAt: new Date(),
        totalProcessingTime
      }

      console.log(`批量处理完成: ${results.filter(r => r.success).length}/${results.length} 成功`)
      return response

    } catch (error) {
      console.error('批量处理失败:', error)
      throw error
    }
  }

  /**
   * 获取服务统计信息
   * @returns AIServiceStats
   */
  getStats(): AIServiceStats {
    return { ...this.stats }
  }

  /**
   * 获取错误日志
   * @param limit 限制数量
   * @returns AIServiceError[]
   */
  getErrorLog(limit: number = 50): AIServiceError[] {
    return this.errorLog.slice(-limit)
  }

  /**
   * 清空错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
    console.log('AI服务错误日志已清空')
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      errorRate: 0,
      tagGenerationCount: 0,
      categoryGenerationCount: 0,
      descriptionGenerationCount: 0,
      dailyStats: []
    }
    console.log('AI服务统计信息已重置')
  }

  // ==================== 私有方法 ====================

  /**
   * 带重试机制的AI服务调用
   * @param config AI配置
   * @param prompt 提示词
   * @param operation 操作类型
   * @param maxRetries 最大重试次数
   * @returns Promise<string>
   */
  private async callAIServiceWithRetry(config: AIConfig, prompt: string, operation: string, maxRetries: number = 3): Promise<string> {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`AI服务调用尝试 ${attempt}/${maxRetries} (${operation})`)
        
        const result = await this.callAIService(config, prompt, operation)
        
        if (attempt > 1) {
          console.log(`AI服务调用在第 ${attempt} 次尝试后成功`)
        }
        
        return result
      } catch (error) {
        lastError = error as Error
        console.warn(`AI服务调用第 ${attempt} 次尝试失败:`, (error as Error).message)
        
        // 如果不是最后一次尝试，等待后重试
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // 指数退避，最大10秒
          console.log(`等待 ${delay}ms 后重试...`)
          await this.delay(delay)
        }
      }
    }
    
    console.error(`AI服务调用在 ${maxRetries} 次尝试后仍然失败`)
    throw lastError || new Error('AI服务调用失败')
  }

  /**
   * 调用AI服务
   * @param config AI配置
   * @param prompt 提示词
   * @param operation 操作类型
   * @returns Promise<string>
   */
  private async callAIService(config: AIConfig, prompt: string, operation: string): Promise<string> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...config.customHeaders
      }

      // 根据不同提供商设置认证头
      if (config.apiKey) {
        switch (config.provider) {
          case 'openai':
          case 'custom':
            headers['Authorization'] = `Bearer ${config.apiKey}`
            break
          case 'claude':
            headers['x-api-key'] = config.apiKey
            headers['anthropic-version'] = '2023-06-01'
            break
          case 'gemini':
            // Gemini使用URL参数传递API密钥
            break
        }
      }

      // 构建请求体
      const requestBody = this.buildRequestBody(config, prompt)
      
      // 构建URL
      const url = this.buildRequestUrl(config)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(config.timeout || 30000)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      return this.extractResponseContent(data, config.provider)

    } catch (error) {
      console.error(`AI服务调用失败 (${operation}):`, error)
      throw error
    }
  }

  /**
   * 构建请求URL
   */
  private buildRequestUrl(config: AIConfig): string {
    let url = config.baseUrl

    switch (config.provider) {
      case 'openai':
      case 'custom':
      case 'local':
        url += '/chat/completions'
        break
      case 'claude':
        url += '/messages'
        break
      case 'gemini':
        url += `/models/${config.model}:generateContent?key=${config.apiKey}`
        break
    }

    return url
  }

  /**
   * 构建请求体
   */
  private buildRequestBody(config: AIConfig, prompt: string): any {
    const baseBody = {
      model: config.model || 'gpt-3.5-turbo',
      temperature: config.temperature || 0.7,
      max_tokens: config.maxTokens || 1000,
      ...config.customParams
    }

    switch (config.provider) {
      case 'openai':
      case 'custom':
      case 'local':
        return {
          ...baseBody,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        }

      case 'claude':
        return {
          ...baseBody,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        }

      case 'gemini':
        return {
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: config.temperature || 0.7,
            maxOutputTokens: config.maxTokens || 1000
          }
        }

      default:
        throw new Error(`不支持的提供商: ${config.provider}`)
    }
  }

  /**
   * 提取响应内容
   */
  private extractResponseContent(data: any, provider: string): string {
    switch (provider) {
      case 'openai':
      case 'custom':
      case 'local':
        return data.choices?.[0]?.message?.content || ''

      case 'claude':
        return data.content?.[0]?.text || ''

      case 'gemini':
        return data.candidates?.[0]?.content?.parts?.[0]?.text || ''

      default:
        throw new Error(`不支持的提供商: ${provider}`)
    }
  }

  /**
   * 构建标签生成提示词
   */
  private buildTagGenerationPrompt(request: AITagGenerationRequest): string {
    const { content, title, url, existingTags, maxTags = 5, language = 'zh-CN' } = request

    let prompt = `你是一个专业的标签生成助手。请为以下内容生成${Math.min(maxTags, 4)}个相关标签。\n\n`

    // 添加内容信息
    if (title) {
      prompt += `标题: ${title}\n`
    }
    if (url) {
      prompt += `网址: ${url}\n`
    }
    prompt += `内容: ${content?.substring(0, 800) || '无'}\n\n`

    // 强调现有标签库的重要性
    if (existingTags && existingTags.length > 0) {
      prompt += `【重要】现有标签库:\n${existingTags.join(', ')}\n\n`
      prompt += `必须优先从现有标签库中选择！\n\n`
    }

    // 严格的输出格式要求
    prompt += `【严格要求】:\n`
    prompt += `1. 优先使用现有标签库中的标签\n`
    prompt += `2. 只生成1-4个标签\n`
    prompt += `3. 每个标签1-4个字\n`
    prompt += `4. 禁止任何解释、分析、思考过程\n`
    prompt += `5. 禁止使用<think>标签或任何思考标记\n`
    prompt += `6. 只输出标签，用逗号分隔\n\n`

    // 输出格式示例
    prompt += `正确输出格式示例:\n`
    prompt += `软件,开发资源,AI\n`
    prompt += `或\n`
    prompt += `网站,学习\n\n`

    prompt += `现在请直接输出标签（只要标签，不要任何其他内容）:`

    return prompt
  }

  /**
   * 构建分类生成提示词
   */
  private buildCategoryGenerationPrompt(request: AICategoryRequest): string {
    const { content, title, url, existingCategories, maxSuggestions = 3 } = request

    let prompt = `请为以下内容推荐一个最合适的分类，并提供${maxSuggestions}个备选分类。\n\n`

    if (title) {
      prompt += `标题: ${title}\n`
    }

    if (url) {
      prompt += `网址: ${url}\n`
    }

    prompt += `内容: ${content.substring(0, 1000)}\n\n`

    if (existingCategories && existingCategories.length > 0) {
      prompt += `现有分类: ${existingCategories.join(', ')}\n`
      prompt += `请优先从现有分类中选择，如果都不合适再创建新分类。\n\n`
    }

    prompt += `要求:\n`
    prompt += `1. 推荐一个最合适的主分类\n`
    prompt += `2. 提供${maxSuggestions}个备选分类\n`
    prompt += `3. 分类名称应该简洁明了\n`
    prompt += `4. 使用中文\n`
    prompt += `5. 按以下格式返回:\n`
    prompt += `主分类: [分类名称]\n`
    prompt += `备选分类: [分类1], [分类2], [分类3]\n`
    prompt += `推荐理由: [简要说明]\n\n`
    prompt += `分类建议:`

    return prompt
  }

  /**
   * 构建描述生成提示词
   */
  private buildDescriptionGenerationPrompt(request: AIDescriptionRequest): string {
    const { content, title, url, maxLength = 200, style = 'brief' } = request

    let prompt = `请为以下内容生成一个${style === 'brief' ? '简洁' : style === 'detailed' ? '详细' : '摘要'}的描述。\n\n`

    if (title) {
      prompt += `标题: ${title}\n`
    }

    if (url) {
      prompt += `网址: ${url}\n`
    }

    // 安全处理content字段，可能为undefined或空字符串
    const safeContent = content || ''
    if (safeContent.trim()) {
      prompt += `内容: ${safeContent.substring(0, 2000)}\n\n`
    } else {
      // 如果没有内容，基于标题和URL生成描述
      prompt += `内容: 暂无具体内容，请基于标题和网址生成描述\n\n`
    }

    prompt += `要求:\n`
    prompt += `1. 描述长度不超过${maxLength}字\n`
    prompt += `2. 准确概括内容要点\n`
    prompt += `3. 语言简洁流畅\n`
    prompt += `4. 使用中文\n`
    prompt += `5. 只返回描述内容，不要其他解释\n\n`
    prompt += `描述:`

    return prompt
  }

  /**
   * 过滤AI返回内容，移除思考过程和无关内容
   */
  private filterAIResponse(response: string): string {
    try {
      let filtered = response

      // 移除 <think> 标签及其内容（包括不完整的标签）
      // 使用更精确的正则表达式，确保不会误删有效内容
      filtered = filtered.replace(/<think>[\s\S]*?<\/think>/gi, '')

      // 处理不完整的 <think> 标签（没有结束标签的情况）
      // 只移除从 <think> 开始到下一个有效内容之前的部分
      filtered = filtered.replace(/<think>[\s\S]*?(?=\n\s*[^\s<])/gi, '')

      // 如果整个响应都在 <think> 标签内且没有结束标签，移除 <think> 开始标签
      if (filtered.includes('<think>') && !filtered.includes('</think>')) {
        // 查找 <think> 标签后的有效内容
        const thinkMatch = filtered.match(/<think>[\s\S]*$/gi)
        if (thinkMatch) {
          // 尝试从 <think> 内容中提取可能的标签
          const thinkContent = thinkMatch[0].replace(/<think>/gi, '')

          // 查找可能的标签模式（逗号分隔的简短词汇）
          const tagPattern = /([^。！？\n]*[,，]\s*[^。！？\n]*)/g
          const possibleTags = thinkContent.match(tagPattern)

          if (possibleTags && possibleTags.length > 0) {
            // 找到最后一个可能的标签行
            const lastTagLine = possibleTags[possibleTags.length - 1]
            // 检查是否看起来像标签（简短、逗号分隔）
            if (lastTagLine.length < 50 && lastTagLine.split(/[,，]/).length >= 2) {
              filtered = lastTagLine.trim()
            } else {
              filtered = filtered.replace(/<think>[\s\S]*/gi, '')
            }
          } else {
            filtered = filtered.replace(/<think>[\s\S]*/gi, '')
          }
        }
      }

      // 移除其他常见的思考标记
      filtered = filtered.replace(/\[思考\][\s\S]*?\[\/思考\]/gi, '')
      filtered = filtered.replace(/\[分析\][\s\S]*?\[\/分析\]/gi, '')
      filtered = filtered.replace(/\[推理\][\s\S]*?\[\/推理\]/gi, '')

      // 移除常见的思考过程文本模式（但要更保守，避免误删标签）
      const thinkingPatterns = [
        /^首先[，,]\s*.*?[。.]/gm,
        /^好的[，,]\s*.*?[。.]/gm,
        /^让我[分析思考].*?[。.]/gm,
        /^根据.*?可以看出.*?[。.]/gm,
        /^这个.*?主要是.*?[。.]/gm,
        /^从.*?来看.*?[。.]/gm,
        /^分析.*?内容.*?[。.]/gm,
        /^考虑到.*?[。.]/gm,
        /^综合.*?信息.*?[。.]/gm,
        /^用户提供的.*?[。.]/gm,
        /^但内容里.*?[。.]/gm,
        /^所以可能.*?[。.]/gm,
        /^因为.*?属于.*?[。.]/gm
      ]

      thinkingPatterns.forEach(pattern => {
        filtered = filtered.replace(pattern, '')
      })

      // 移除多余的空白字符
      filtered = filtered.replace(/\s+/g, ' ').trim()

      // 移除开头和结尾的标点符号，但保留逗号（标签分隔符）
      filtered = filtered.replace(/^[。.：:\s]+/, '')
      filtered = filtered.replace(/[。.：:\s]+$/, '')

      // 如果过滤后内容为空或只有无意义的标点符号，返回空字符串
      if (!filtered || /^[。.：:\s]*$/.test(filtered)) {
        return ''
      }

      return filtered

    } catch (error) {
      console.error('过滤AI响应失败:', error)
      return response
    }
  }

  /**
   * 从响应中解析标签
   */
  private parseTagsFromResponse(response: string): string[] {
    try {
      console.log('原始AI响应:', response)

      // 过滤AI返回内容，移除思考过程
      const cleanedResponse = this.filterAIResponse(response)
      console.log('过滤后的响应:', cleanedResponse)

      // 检查响应是否被截断
      const isResponseTruncated = response.includes('"finish_reason": "length"') ||
                                  response.includes('finish_reason":"length"')

      if (isResponseTruncated) {
        console.log('AI响应被截断，尝试从截断的响应中提取可用标签')
        // 尝试从截断的响应中提取部分标签
        const partialTags = this.extractTagsFromTruncatedResponse(response)
        if (partialTags.length > 0) {
          console.log('从截断响应中提取到标签:', partialTags)
          return partialTags
        }
      }

      // 如果过滤后内容为空，尝试从原始响应中提取标签
      if (!cleanedResponse) {
        console.log('过滤后内容为空，尝试从原始响应中提取标签')
        const extractedTags = this.extractTagsFromRawResponse(response)
        if (extractedTags.length > 0) {
          console.log('从原始响应中提取到标签:', extractedTags)
          return extractedTags
        }
        console.log('无法从原始响应中提取标签，返回空数组')
        return []
      }

      // 严格清理响应文本
      let cleanResponse = cleanedResponse.trim()

      // 移除各种可能的前缀和后缀
      cleanResponse = cleanResponse.replace(/^(标签|推荐标签|建议标签|输出标签|生成标签)[：:]\s*/i, '')
      cleanResponse = cleanResponse.replace(/^(tags|recommended tags|output tags)[：:]\s*/i, '')
      cleanResponse = cleanResponse.replace(/\s*(完成|结束|end)$/i, '')

      // 移除引号
      cleanResponse = cleanResponse.replace(/^["'`]|["'`]$/g, '')

      // 如果响应包含结构化格式，尝试解析
      if (cleanResponse.includes('现有标签:') || cleanResponse.includes('新标签:')) {
        return this.parseStructuredTagResponse(cleanResponse)
      }

      // 严格分割标签 - 主要使用逗号分隔
      let tags = cleanResponse
        .split(/[,，]/) // 主要使用逗号分隔
        .map(tag => tag.trim())
        .filter(tag => {
          // 严格过滤条件
          return tag.length > 0 &&
                 tag.length <= 15 && // 更严格的长度限制
                 !tag.includes('推荐') &&
                 !tag.includes('建议') &&
                 !tag.includes('理由') &&
                 !tag.includes('分析') &&
                 !tag.includes('因为') &&
                 !tag.includes('所以') &&
                 !tag.includes('新标签') &&
                 !tag.includes('现有标签') &&
                 !tag.includes('标签') &&
                 !/^[0-9]+[\.\)、]/.test(tag) && // 移除编号
                 !/^(第|其|这|那|一个|两个|三个|四个|五个)/.test(tag) && // 移除序数词
                 !/[。！？]/.test(tag) // 移除包含句号等的内容
        })
        .slice(0, 5) // 最多5个标签

      console.log('解析出的标签:', tags)
      return tags.length > 0 ? tags : ['未分类']
    } catch (error) {
      console.error('解析标签失败:', error)
      return ['未分类']
    }
  }

  /**
   * 从截断的响应中提取可用标签
   */
  private extractTagsFromTruncatedResponse(response: string): string[] {
    try {
      // 尝试从思考过程中提取提到的现有标签
      const existingTagsPattern = /现有标签[库中]*[有的]*[：:]\s*([^。\n]+)/g
      const matches = response.match(existingTagsPattern)

      if (matches) {
        const extractedTags: string[] = []
        matches.forEach(match => {
          // 提取标签名称
          const tagText = match.replace(/现有标签[库中]*[有的]*[：:]/, '').trim()
          const tags = tagText.split(/[、，,]/).map(tag => tag.trim()).filter(tag => tag.length > 0)
          extractedTags.push(...tags)
        })

        // 去重并限制数量
        const uniqueTags = [...new Set(extractedTags)].slice(0, 3)
        console.log('从截断响应中提取的标签:', uniqueTags)
        return uniqueTags
      }

      return []
    } catch (error) {
      console.warn('从截断响应提取标签失败:', error)
      return []
    }
  }

  /**
   * 从原始响应中提取标签（当过滤后内容为空时的备用方法）
   */
  private extractTagsFromRawResponse(response: string): string[] {
    try {
      console.log('尝试从原始响应中提取标签')

      // 首先尝试查找 <think> 标签外的内容
      let contentAfterThink = ''

      // 查找 </think> 标签后的内容
      const afterThinkMatch = response.match(/<\/think>\s*([\s\S]*?)$/i)
      if (afterThinkMatch && afterThinkMatch[1]) {
        contentAfterThink = afterThinkMatch[1].trim()
        console.log('找到 </think> 后的内容:', contentAfterThink)
      }

      // 如果没有找到 </think>，查找可能的标签模式
      if (!contentAfterThink) {
        // 查找 <think> 标签内最后出现的可能标签
        const thinkContentMatch = response.match(/<think>([\s\S]*?)$/i)
        if (thinkContentMatch && thinkContentMatch[1]) {
          const thinkContent = thinkContentMatch[1]

          // 在 think 内容中查找最后一行可能的标签
          const lines = thinkContent.split('\n').reverse()
          for (const line of lines) {
            const trimmedLine = line.trim()
            // 检查是否看起来像标签（包含逗号分隔的短词汇）
            if (trimmedLine &&
                trimmedLine.length < 100 &&
                trimmedLine.includes(',') &&
                !trimmedLine.includes('。') &&
                !trimmedLine.includes('？') &&
                !trimmedLine.includes('！') &&
                !trimmedLine.includes('分析') &&
                !trimmedLine.includes('考虑') &&
                !trimmedLine.includes('因为') &&
                !trimmedLine.includes('所以')) {
              contentAfterThink = trimmedLine
              console.log('从 <think> 内容中找到可能的标签:', contentAfterThink)
              break
            }
          }
        }
      }

      // 如果找到了内容，解析标签
      if (contentAfterThink) {
        const tags = contentAfterThink
          .split(/[,，、]/)
          .map(tag => tag.trim())
          .filter(tag => {
            return tag.length > 0 &&
                   tag.length <= 20 &&
                   !tag.includes('分析') &&
                   !tag.includes('内容') &&
                   !tag.includes('特征') &&
                   !tag.includes('这是') &&
                   !tag.includes('一个') &&
                   !tag.includes('用户') &&
                   !tag.includes('提供') &&
                   !tag.includes('要求') &&
                   !tag.includes('生成') &&
                   !/^[0-9]+[\.\)、]/.test(tag) // 移除编号
          })
          .slice(0, 5)

        if (tags.length > 0) {
          console.log('从原始响应提取的标签:', tags)
          return tags
        }
      }

      // 如果以上方法都失败，尝试其他模式
      const fallbackPatterns = [
        /标签[：:]\s*([^。\n]+)/i,
        /推荐[：:]\s*([^。\n]+)/i,
        /建议[：:]\s*([^。\n]+)/i,
        /tags[：:]\s*([^。\n]+)/i
      ]

      for (const pattern of fallbackPatterns) {
        const match = response.match(pattern)
        if (match && match[1]) {
          const tags = match[1]
            .split(/[,，、\n\r\t;；|｜]/)
            .map(tag => tag.trim())
            .filter(tag => {
              return tag.length > 0 &&
                     tag.length <= 20 &&
                     !tag.includes('分析') &&
                     !tag.includes('内容') &&
                     !tag.includes('特征')
            })
            .slice(0, 5)

          if (tags.length > 0) {
            console.log('使用备用模式提取的标签:', tags)
            return tags
          }
        }
      }

      console.log('无法从原始响应中提取有效标签')
      return []
    } catch (error) {
      console.error('从原始响应提取标签失败:', error)
      return ['未分类']
    }
  }

  /**
   * 解析结构化标签响应
   */
  private parseStructuredTagResponse(response: string): string[] {
    try {
      const tags: string[] = []

      // 提取现有标签
      const existingMatch = response.match(/现有标签[：:]\s*([^。\n\r]+)/i)
      if (existingMatch && existingMatch[1]) {
        const existingTags = existingMatch[1]
          .split(/[,，、]/)
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0 && tag.length <= 20)
        tags.push(...existingTags)
      }

      // 提取新标签
      const newMatch = response.match(/新标签[：:]\s*([^。\n\r]+)/i)
      if (newMatch && newMatch[1]) {
        const newTags = newMatch[1]
          .split(/[,，、]/)
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0 && tag.length <= 20)
        tags.push(...newTags)
      }

      // 去重
      const uniqueTags = [...new Set(tags)]
      return uniqueTags.length > 0 ? uniqueTags.slice(0, 10) : ['未分类']
    } catch (error) {
      console.error('解析结构化标签响应失败:', error)
      return ['未分类']
    }
  }

  /**
   * 从响应中解析分类
   */
  private parseCategoryFromResponse(response: string): {
    category: string
    confidence: number
    alternatives?: Array<{category: string, confidence: number}>
  } {
    try {
      // 过滤AI返回内容
      const filteredResponse = this.filterAIResponse(response)
      const lines = filteredResponse.trim().split('\n')
      let category = '默认分类'
      let alternatives: Array<{category: string, confidence: number}> = []

      for (const line of lines) {
        if (line.includes('主分类:') || line.includes('分类:')) {
          category = line.split(':')[1]?.trim() || '默认分类'
        } else if (line.includes('备选分类:')) {
          const altText = line.split(':')[1]?.trim() || ''
          alternatives = altText
            .split(/[,，]/)
            .map(alt => alt.trim())
            .filter(alt => alt.length > 0)
            .map((alt, index) => ({
              category: alt,
              confidence: 0.8 - (index * 0.1)
            }))
        }
      }

      return {
        category,
        confidence: 0.9,
        alternatives
      }
    } catch (error) {
      console.error('解析分类失败:', error)
      return {
        category: '默认分类',
        confidence: 0.5
      }
    }
  }

  /**
   * 从响应中解析描述
   */
  private parseDescriptionFromResponse(response: string): string {
    try {
      // 清理响应文本
      const cleanResponse = response.trim().replace(/^描述:?\s*/, '')
      
      // 限制长度
      return cleanResponse.length > 500 
        ? cleanResponse.substring(0, 500) + '...'
        : cleanResponse
    } catch (error) {
      console.error('解析描述失败:', error)
      return '暂无描述'
    }
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(response: string): number {
    // 简单的置信度计算逻辑
    const length = response.length
    if (length > 100) return 0.9
    if (length > 50) return 0.8
    if (length > 20) return 0.7
    return 0.6
  }

  /**
   * 提取推理过程
   */
  private extractReasoning(response: string): string | undefined {
    // 过滤AI返回内容
    const filteredResponse = this.filterAIResponse(response)
    const lines = filteredResponse.split('\n')
    for (const line of lines) {
      if (line.includes('推荐理由:') || line.includes('理由:')) {
        return line.split(':')[1]?.trim()
      }
    }
    return undefined
  }

  /**
   * 更新统计信息
   */
  private updateStats(type: 'tags' | 'category' | 'description', success: boolean, responseTime: number): void {
    this.stats.totalRequests++
    
    if (success) {
      this.stats.successfulRequests++
    } else {
      this.stats.failedRequests++
    }

    // 更新平均响应时间
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests

    // 更新错误率
    this.stats.errorRate = this.stats.failedRequests / this.stats.totalRequests

    // 更新功能统计
    switch (type) {
      case 'tags':
        this.stats.tagGenerationCount++
        break
      case 'category':
        this.stats.categoryGenerationCount++
        break
      case 'description':
        this.stats.descriptionGenerationCount++
        break
    }

    // 更新最后请求时间
    this.stats.lastRequestDate = new Date()

    // 更新每日统计
    this.updateDailyStats(success)
  }

  /**
   * 更新每日统计
   */
  private updateDailyStats(success: boolean): void {
    const today = new Date().toISOString().split('T')[0]
    let todayStats = this.stats.dailyStats.find(stat => stat.date === today)

    if (!todayStats) {
      todayStats = { date: today, requests: 0, errors: 0 }
      this.stats.dailyStats.push(todayStats)
    }

    todayStats.requests++
    if (!success) {
      todayStats.errors++
    }

    // 只保留最近30天的统计
    this.stats.dailyStats = this.stats.dailyStats.slice(-30)
  }

  /**
   * 调用AI服务并设置超时
   */
  private async callAIWithTimeout(prompt: string, request: AITagGenerationRequest, timeout: number): Promise<AITagGenerationResponse> {
    const aiResponse = await aiChatService.generateText({
      prompt,
      generationType: 'tags',
      context: {
        title: request.title,
        content: request.content,
        url: request.url
      },
      maxLength: 2000 // 大幅增加到2000，让AI有充分空间完成思考和输出
    })

    // 解析响应
    const tags = this.parseTagsFromResponse(aiResponse.content)

    return {
      tags,
      confidence: this.calculateConfidence(aiResponse.content),
      reasoning: this.extractReasoning(aiResponse.content),
      processingTime: 0 // 将在上层计算
    }
  }

  /**
   * 创建延迟的降级策略Promise
   */
  private async createDelayedFallback(request: AITagGenerationRequest, delay: number): Promise<AITagGenerationResponse> {
    // 等待指定时间
    await new Promise(resolve => setTimeout(resolve, delay))

    console.log(`AI调用超过${delay}ms，使用降级策略`)
    return await this.fallbackTagGeneration(request)
  }

  /**
   * 启动后台AI优化
   */
  private startBackgroundAIOptimization(
    prompt: string,
    request: AITagGenerationRequest,
    fallbackResult: AITagGenerationResponse,
    useCache: boolean
  ): void {
    // 创建后台优化Promise
    let backgroundPromise: Promise<AITagGenerationResponse>

    try {
      // 根据实际AI提供商设置后台优化超时时间
      this.getActualAIProvider().then(actualProvider => {
        const isLocalAI = this.isLocalAIProviderByName(actualProvider?.name || '')
        const backgroundTimeout = isLocalAI ? 180000 : 90000 // 本地AI: 3分钟，云端AI: 90秒

        console.log(`启动后台AI优化，提供商: ${actualProvider?.name || '未知'}, 超时时间: ${backgroundTimeout/1000}秒`)

        // 在后台继续AI调用（不使用Promise.race，让AI有充分时间完成）
        backgroundPromise = this.callAIWithTimeout(prompt, request, backgroundTimeout)

        // 处理后台优化结果
        this.handleBackgroundOptimizationResult(backgroundPromise, request, fallbackResult, useCache)
      }).catch(error => {
        console.warn('获取AI提供商失败，使用默认后台优化设置:', error)

        // 使用默认设置（不使用Promise.race，让AI有充分时间完成）
        backgroundPromise = this.callAIWithTimeout(prompt, request, 120000) // 默认2分钟

        // 处理后台优化结果
        this.handleBackgroundOptimizationResult(backgroundPromise, request, fallbackResult, useCache)
      })
    } catch (error) {
      console.error('启动后台AI优化失败:', error)
    }
  }

  /**
   * 处理后台优化结果
   */
  private handleBackgroundOptimizationResult(
    backgroundPromise: Promise<AITagGenerationResponse>,
    request: AITagGenerationRequest,
    fallbackResult: AITagGenerationResponse,
    useCache: boolean
  ): void {

    backgroundPromise
      .then(async (aiResult) => {
        console.log('后台AI优化完成:', aiResult.tags)
        console.log('AI结果详情:', {
          tags: aiResult.tags,
          confidence: aiResult.confidence,
          reasoning: aiResult.reasoning
        })
        console.log('降级结果详情:', {
          tags: fallbackResult.tags,
          confidence: fallbackResult.confidence,
          reasoning: fallbackResult.reasoning
        })

        // 检查AI结果是否比降级结果更好
        // 改进比较逻辑：只要AI结果有有效标签且不为空，就认为比降级结果好
        const isAIResultBetter = aiResult.tags.length > 0 &&
                                (aiResult.confidence > fallbackResult.confidence ||
                                 aiResult.tags.length > fallbackResult.tags.length ||
                                 (aiResult.tags.length > 0 && fallbackResult.tags.length === 0))

        console.log('AI结果是否更好:', isAIResultBetter)

        if (isAIResultBetter) {
          console.log('AI优化结果更好，通知前端更新:', aiResult.tags)

          // 通过Chrome消息API通知前端
          try {
            console.log('准备发送AI优化完成消息')

            // 获取所有标签页并发送消息
            chrome.tabs.query({}, (tabs) => {
              tabs.forEach(tab => {
                if (tab.id && tab.url) {
                  // 检查是否是扩展页面（options页面或popup页面）
                  if (tab.url.includes('chrome-extension://') || tab.url.includes('moz-extension://')) {
                    console.log(`发送AI优化消息到扩展页面: ${tab.url}`)
                    chrome.tabs.sendMessage(tab.id, {
                      type: 'AI_TAGS_OPTIMIZED',
                      data: {
                        originalRequest: request,
                        optimizedResult: aiResult,
                        fallbackResult: fallbackResult
                      }
                    }).catch((error) => {
                      console.log(`发送到标签页 ${tab.id} 失败:`, error.message)
                    })
                  } else {
                    // 对于普通网页，尝试发送给content script
                    chrome.tabs.sendMessage(tab.id, {
                      type: 'AI_TAGS_OPTIMIZED',
                      data: {
                        originalRequest: request,
                        optimizedResult: aiResult,
                        fallbackResult: fallbackResult
                      }
                    }).catch(() => {
                      // 忽略没有content script的标签页
                    })
                  }
                }
              })
            })

            // 同时也发送给runtime（popup可能在监听）
            chrome.runtime.sendMessage({
              type: 'AI_TAGS_OPTIMIZED',
              data: {
                originalRequest: request,
                optimizedResult: aiResult,
                fallbackResult: fallbackResult
              }
            }).catch((error) => {
              console.log('发送runtime消息失败:', error.message)
            })

            console.log('AI优化消息发送完成')
          } catch (error) {
            console.warn('通知前端AI优化结果失败:', error)
          }

          // 保存优化后的结果到缓存
          if (useCache) {
            try {
              await aiCacheService.saveTagsCache(request, aiResult)
            } catch (error) {
              console.warn('保存AI优化结果到缓存失败:', error)
            }
          }
        } else {
          console.log('AI优化结果未改善，保持降级结果')
        }
      })
      .catch((error) => {
        console.log('后台AI优化失败:', error.message)
      })
  }

  /**
   * 获取实际使用的AI提供商
   */
  private async getActualAIProvider(): Promise<any> {
    try {
      // 通过aiIntegrationService获取当前使用的提供商信息
      const providers = await aiIntegrationService.getConfiguredProviders()

      // 获取当前配置
      const config = await aiConfigService.getConfig()

      // 尝试从配置中找到当前使用的提供商
      if (config.provider) {
        // 根据配置的provider名称查找
        const provider = providers.find(p =>
          p.name.toLowerCase().includes(config.provider.toLowerCase()) ||
          p.id.toLowerCase().includes(config.provider.toLowerCase())
        )
        if (provider) {
          return provider
        }
      }

      // 如果找不到，返回第一个可用的提供商
      return providers.length > 0 ? providers[0] : null
    } catch (error) {
      console.warn('获取实际AI提供商失败:', error)
      return null
    }
  }

  /**
   * 根据提供商名称检测是否是本地AI
   */
  private isLocalAIProviderByName(providerName: string): boolean {
    const name = (providerName || '').toLowerCase()
    const localIndicators = [
      'lm studio', 'lm-studio', '本地', 'local', 'localhost', '127.0.0.1',
      'ollama', 'text-generation-webui', 'koboldai', 'oobabooga'
    ]

    const isLocal = localIndicators.some(indicator => name.includes(indicator))
    console.log('AI提供商名称检测:', { providerName, isLocal })
    return isLocal
  }

  /**
   * 检测是否是本地AI提供商（保留原方法作为备用）
   */
  private isLocalAIProvider(config: any): boolean {
    try {
      // 打印配置信息用于调试
      console.log('检测AI配置:', config)

      const providerName = (config?.provider || config?.name || '').toLowerCase()
      const apiUrl = (config?.apiUrl || config?.baseUrl || '').toLowerCase()
      const providerId = (config?.providerId || '').toLowerCase()

      console.log('检测参数:', { providerName, apiUrl, providerId })

      // 检测本地AI服务的特征
      const localIndicators = [
        'lm studio', 'lm-studio', '本地', 'local', 'localhost', '127.0.0.1',
        'ollama', 'text-generation-webui', 'koboldai', 'oobabooga'
      ]

      const isLocal = localIndicators.some(indicator =>
        providerName.includes(indicator) ||
        apiUrl.includes(indicator) ||
        providerId.includes(indicator)
      )

      console.log('本地AI检测结果:', isLocal)
      return isLocal
    } catch (error) {
      console.warn('检测本地AI提供商失败:', error)
      return false
    }
  }

  /**
   * 判断是否应该使用降级策略
   */
  private shouldUseFallback(error: any): boolean {
    const errorMessage = error.message?.toLowerCase() || ''

    // 对于以下情况使用降级策略
    const fallbackConditions = [
      'timeout', // 超时错误
      'timed out', // 超时错误
      'signal timed out', // 信号超时
      'ai标签生成超时', // AI标签生成超时
      'network error', // 网络错误
      'connection refused', // 连接被拒绝
      'service unavailable', // 服务不可用
      'internal server error', // 服务器内部错误
      'bad gateway', // 网关错误
      'gateway timeout', // 网关超时
      'ai api调用失败', // AI API调用失败
      '未找到可用的ai模型', // 模型配置问题
      'no available models', // 没有可用模型
      'fetch failed', // 网络请求失败
      'failed to fetch' // 网络请求失败
    ]

    return fallbackConditions.some(condition => errorMessage.includes(condition))
  }

  /**
   * 记录错误
   */
  private logError(error: any, operation: string, config?: AIConfig): void {
    const aiError: AIServiceError = {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || 'Unknown error',
      details: error.details,
      timestamp: new Date(),
      provider: config?.provider || 'unknown',
      operation
    }

    this.errorLog.push(aiError)

    // 只保留最近100个错误
    this.errorLog = this.errorLog.slice(-100)
  }

  /**
   * 标签生成降级策略
   * @param request 标签生成请求
   * @returns Promise<AITagGenerationResponse>
   */
  private async fallbackTagGeneration(request: AITagGenerationRequest): Promise<AITagGenerationResponse> {
    console.log('使用标签生成降级策略')

    const fallbackTags: string[] = []
    const text = `${request.title || ''} ${request.content || ''}`.toLowerCase()

    // 获取现有标签（如果请求中没有）
    let existingTags = request.existingTags
    if (!existingTags || existingTags.length === 0) {
      try {
        const tags = await tagService.getTags()
        existingTags = tags.map(tag => tag.name)
      } catch (error) {
        console.warn('获取现有标签失败:', error)
        existingTags = []
      }
    }

    // 优先从现有标签中匹配（更智能的匹配）
    if (existingTags && existingTags.length > 0) {
      console.log('从现有标签中匹配:', existingTags)

      for (const tag of existingTags) {
        const tagLower = tag.toLowerCase()

        // 精确匹配
        if (text.includes(tagLower)) {
          fallbackTags.push(tag)
          continue
        }

        // 基于URL域名匹配
        if (request.url) {
          try {
            const domain = new URL(request.url).hostname.toLowerCase()
            if (domain.includes(tagLower) || tagLower.includes(domain.split('.')[0])) {
              fallbackTags.push(tag)
              continue
            }
          } catch (error) {
            // 忽略URL解析错误
          }
        }

        if (fallbackTags.length >= 3) break
      }
    }

    // 如果现有标签匹配不足，基于URL域名生成标签
    if (request.url) {
      try {
        const domain = new URL(request.url).hostname
        const domainParts = domain.split('.')
        
        // 添加域名相关标签
        if (domainParts.includes('github')) {
          fallbackTags.push('开发', '代码')
        } else if (domainParts.includes('stackoverflow')) {
          fallbackTags.push('编程', '问答')
        } else if (domainParts.includes('youtube')) {
          fallbackTags.push('视频', '娱乐')
        } else if (domainParts.includes('wikipedia')) {
          fallbackTags.push('百科', '知识')
        } else if (domainParts.includes('news') || domainParts.includes('xinhua') || domainParts.includes('people')) {
          fallbackTags.push('新闻', '资讯')
        } else {
          fallbackTags.push('网站')
        }
      } catch (error) {
        console.warn('解析URL失败:', error)
      }
    }
    
    // 如果现有标签匹配不足，使用关键词映射
    if (fallbackTags.length < 3) {
      const keywordMap: Record<string, string[]> = {
        '软件': ['software', 'app', 'application', '应用', '软件', 'tool', '工具'],
        '开发资源': ['development', 'dev', 'programming', 'coding', '开发', '编程', 'github', 'api', 'framework'],
        'AI': ['ai', 'artificial intelligence', '人工智能', 'machine learning', 'ml', 'chatgpt', 'gpt'],
        '学习': ['education', 'learning', 'course', 'tutorial', '教程', '学习', '课程', 'mooc'],
        '设计': ['design', 'ui', 'ux', '设计', '界面'],
        '技术': ['technology', 'tech', '技术', 'innovation', 'javascript', 'python', 'java', 'react', 'vue'],
        '工具': ['tool', 'utility', '工具', 'service'],
        '资源': ['resource', 'library', '资源', '库'],
        '商业': ['business', '商业', '营销', '市场', '销售', '创业'],
        '生活': ['life', '生活', '健康', '美食', '旅游', '娱乐'],
        '科学': ['science', '科学', '研究', '创新', '发现']
      }

      for (const [tag, keywords] of Object.entries(keywordMap)) {
        if (!fallbackTags.includes(tag) && keywords.some(keyword => text.includes(keyword))) {
          fallbackTags.push(tag)
          if (fallbackTags.length >= 5) break
        }
      }
    }
    
    // 如果仍然没有找到标签，使用默认标签
    if (fallbackTags.length === 0) {
      fallbackTags.push('收藏', '资源')
    }

    // 去重并限制标签数量
    const maxTags = request.maxTags || 5
    const uniqueTags = [...new Set(fallbackTags)]
    const finalTags = uniqueTags.slice(0, maxTags)

    console.log('降级策略生成的标签:', finalTags)

    return {
      tags: finalTags,
      confidence: 0.6, // 降级策略的置信度较低
      reasoning: '使用本地规则生成标签（AI服务不可用）',
      processingTime: 50 // 本地处理很快
    }
  }

  /**
   * 分类生成降级策略
   * @param request 分类建议请求
   * @returns Promise<AICategoryResponse>
   */
  private async fallbackCategoryGeneration(request: AICategoryRequest): Promise<AICategoryResponse> {
    console.log('使用分类生成降级策略')
    
    let category = '默认分类'
    const alternatives: Array<{category: string, confidence: number}> = []
    
    // 基于URL域名判断分类
    if (request.url) {
      try {
        const domain = new URL(request.url).hostname
        
        if (domain.includes('github') || domain.includes('gitlab')) {
          category = '开发工具'
          alternatives.push(
            { category: '代码仓库', confidence: 0.8 },
            { category: '技术资源', confidence: 0.7 }
          )
        } else if (domain.includes('stackoverflow') || domain.includes('csdn')) {
          category = '技术问答'
          alternatives.push(
            { category: '编程学习', confidence: 0.8 },
            { category: '开发资源', confidence: 0.7 }
          )
        } else if (domain.includes('youtube') || domain.includes('bilibili')) {
          category = '视频资源'
          alternatives.push(
            { category: '娱乐视频', confidence: 0.8 },
            { category: '学习视频', confidence: 0.7 }
          )
        } else if (domain.includes('wikipedia') || domain.includes('baidu')) {
          category = '知识百科'
          alternatives.push(
            { category: '参考资料', confidence: 0.8 },
            { category: '学习资源', confidence: 0.7 }
          )
        }
      } catch (error) {
        console.warn('解析URL失败:', error)
      }
    }
    
    // 基于内容关键词判断分类
    const text = `${request.title || ''} ${request.content}`.toLowerCase()
    
    if (text.includes('教程') || text.includes('学习') || text.includes('课程')) {
      category = '学习资源'
    } else if (text.includes('工具') || text.includes('软件') || text.includes('应用')) {
      category = '工具软件'
    } else if (text.includes('新闻') || text.includes('资讯') || text.includes('报道')) {
      category = '新闻资讯'
    } else if (text.includes('设计') || text.includes('ui') || text.includes('界面')) {
      category = '设计资源'
    }
    
    return {
      category,
      confidence: 0.6,
      alternatives,
      reasoning: '使用本地规则生成分类（AI服务不可用）'
    }
  }

  /**
   * 描述生成降级策略
   * @param request 描述生成请求
   * @returns Promise<AIDescriptionResponse>
   */
  private async fallbackDescriptionGeneration(request: AIDescriptionRequest): Promise<AIDescriptionResponse> {
    console.log('使用描述生成降级策略')

    let description = ''
    let confidence = 0.3 // 默认较低置信度

    // 优先使用标题作为基础描述
    if (request.title && request.title.trim()) {
      description = request.title.trim()
      confidence = 0.6 // 有标题时置信度稍高
    }

    // 从内容中提取前几句话（如果有内容）
    if (request.content && request.content.trim()) {
      const sentences = request.content
        .replace(/\n+/g, ' ')
        .replace(/\s+/g, ' ')
        .split(/[。！？.!?]/)
        .filter(s => s.trim().length > 10)
        .slice(0, 3)

      if (sentences.length > 0) {
        const contentSummary = sentences.join('。').trim() + '。'
        if (description) {
          // 如果已有标题，将内容作为补充
          description = `${description}。${contentSummary}`
        } else {
          // 如果没有标题，直接使用内容摘要
          description = contentSummary
        }
        confidence = 0.7 // 有内容时置信度更高
      }
    }

    // 基于URL推断网站类型和描述
    if (request.url && (!description || description === request.title)) {
      const urlDescription = this.generateDescriptionFromUrl(request.url, request.title)
      if (urlDescription) {
        description = description ? `${description}。${urlDescription}` : urlDescription
        confidence = Math.max(confidence, 0.5)
      }
    }

    // 如果仍然没有有效描述，使用智能默认描述
    if (!description.trim()) {
      if (request.url) {
        const domain = this.extractDomainFromUrl(request.url)
        description = `来自 ${domain} 的收藏资源`
        confidence = 0.4
      } else {
        description = '这是一个收藏的资源，暂无详细描述。'
        confidence = 0.3
      }
    }

    // 限制长度并优化格式
    const maxLength = request.maxLength || 200
    if (description.length > maxLength) {
      // 智能截断，尽量在句号处截断
      const truncated = description.substring(0, maxLength - 3)
      const lastPeriod = truncated.lastIndexOf('。')
      if (lastPeriod > maxLength * 0.7) {
        description = truncated.substring(0, lastPeriod + 1)
      } else {
        description = truncated + '...'
      }
    }

    // 清理描述格式
    description = description
      .replace(/。{2,}/g, '。') // 移除多余的句号
      .replace(/\s+/g, ' ') // 规范化空格
      .trim()

    return {
      description,
      confidence,
      wordCount: description.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length // 计算有效字符数
    }
  }

  /**
   * 基于URL生成描述
   */
  private generateDescriptionFromUrl(url: string, title?: string): string {
    try {
      const urlObj = new URL(url)
      const domain = urlObj.hostname.toLowerCase()
      const path = urlObj.pathname

      // 常见网站类型识别
      const siteTypes: Record<string, string> = {
        'github.com': '开源代码仓库',
        'stackoverflow.com': '技术问答社区',
        'medium.com': '技术博客文章',
        'dev.to': '开发者社区文章',
        'youtube.com': '视频内容',
        'bilibili.com': '视频内容',
        'zhihu.com': '知识问答社区',
        'juejin.cn': '技术文章',
        'csdn.net': '技术博客',
        'segmentfault.com': '技术问答',
        'npmjs.com': 'NPM包',
        'pypi.org': 'Python包',
        'docs.': '技术文档',
        'wiki': '百科知识'
      }

      for (const [pattern, type] of Object.entries(siteTypes)) {
        if (domain.includes(pattern) || url.includes(pattern)) {
          return `${type}资源`
        }
      }

      // 基于路径推断内容类型
      if (path.includes('/blog/') || path.includes('/article/')) {
        return '博客文章'
      } else if (path.includes('/doc/') || path.includes('/docs/')) {
        return '技术文档'
      } else if (path.includes('/tutorial/')) {
        return '教程资源'
      } else if (path.includes('/api/')) {
        return 'API文档'
      }

      return ''
    } catch (error) {
      return ''
    }
  }

  /**
   * 从URL提取域名
   */
  private extractDomainFromUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace(/^www\./, '')
    } catch (error) {
      return 'unknown'
    }
  }

  /**
   * 调用AI服务并设置超时（描述生成专用）
   */
  private async callAIWithTimeoutForDescription(prompt: string, request: AIDescriptionRequest, timeout: number): Promise<AIDescriptionResponse> {
    const aiResponse = await aiChatService.generateText({
      prompt,
      generationType: 'description',
      context: {
        title: request.title,
        content: request.content,
        url: request.url
      },
      maxLength: 300
    })

    // 解析响应
    const description = this.parseDescriptionFromResponse(aiResponse.content)

    return {
      description,
      confidence: this.calculateConfidence(aiResponse.content),
      wordCount: description.split(/\s+/).length
    }
  }

  /**
   * 创建延迟的降级策略Promise（描述生成专用）
   */
  private async createDelayedFallbackForDescription(request: AIDescriptionRequest, delay: number): Promise<AIDescriptionResponse> {
    // 等待指定时间
    await new Promise(resolve => setTimeout(resolve, delay))

    console.log(`AI调用超过${delay}ms，使用降级策略`)
    return await this.fallbackDescriptionGeneration(request)
  }

  /**
   * 启动后台AI优化（描述生成专用）
   */
  private startBackgroundAIOptimizationForDescription(
    prompt: string,
    request: AIDescriptionRequest,
    fallbackResult: AIDescriptionResponse,
    useCache: boolean
  ): void {
    // 创建后台优化Promise
    let backgroundPromise: Promise<AIDescriptionResponse>

    try {
      // 根据实际AI提供商设置后台优化超时时间
      this.getActualAIProvider().then(actualProvider => {
        const isLocalAI = this.isLocalAIProviderByName(actualProvider?.name || '')
        const backgroundTimeout = isLocalAI ? 180000 : 90000 // 本地AI: 3分钟，云端AI: 90秒

        console.log(`启动后台AI优化，提供商: ${actualProvider?.name || '未知'}, 超时时间: ${backgroundTimeout/1000}秒`)

        // 在后台继续AI调用（不使用Promise.race，让AI有充分时间完成）
        backgroundPromise = this.callAIWithTimeoutForDescription(prompt, request, backgroundTimeout)

        // 处理后台优化结果
        this.handleBackgroundOptimizationResultForDescription(backgroundPromise, request, fallbackResult, useCache)
      }).catch(error => {
        console.warn('获取AI提供商失败，使用默认后台优化设置:', error)

        // 使用默认设置（不使用Promise.race，让AI有充分时间完成）
        backgroundPromise = this.callAIWithTimeoutForDescription(prompt, request, 120000) // 默认2分钟

        // 处理后台优化结果
        this.handleBackgroundOptimizationResultForDescription(backgroundPromise, request, fallbackResult, useCache)
      })
    } catch (error) {
      console.warn('启动后台AI优化失败:', error)
    }
  }

  /**
   * 处理后台优化结果（描述生成专用）
   */
  private async handleBackgroundOptimizationResultForDescription(
    backgroundPromise: Promise<AIDescriptionResponse>,
    request: AIDescriptionRequest,
    fallbackResult: AIDescriptionResponse,
    useCache: boolean
  ): Promise<void> {
    try {
      const optimizedResult = await backgroundPromise

      console.log('后台AI优化完成:', optimizedResult.description.substring(0, 100))

      // 如果优化结果明显更好，更新缓存
      if (optimizedResult.confidence > fallbackResult.confidence + 0.2) {
        console.log('后台优化结果更好，更新缓存')

        if (useCache) {
          await aiCacheService.saveDescriptionCache(request, optimizedResult)
        }
      }
    } catch (error) {
      console.warn('后台AI优化失败:', error)
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 导出单例实例
export const aiService = new AIService()