#!/usr/bin/env node

/**
 * 验证关于我们页面信息修改
 * 检查开发者信息、网站链接、交流群链接等是否正确更新
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 验证关于我们页面信息修改...\n')

// 检查aboutInfo.ts文件
const aboutInfoPath = path.join(__dirname, '../src/options/data/aboutInfo.ts')
const aboutInfoContent = fs.readFileSync(aboutInfoPath, 'utf8')

console.log('📋 检查 aboutInfo.ts 文件:')

// 检查开发者名称
if (aboutInfoContent.includes("name: 'coffeebean'")) {
  console.log('✅ 开发者名称已更新为 coffeebean')
} else {
  console.log('❌ 开发者名称未正确更新')
}

// 检查网站链接
if (aboutInfoContent.includes("website: 'https://www.obsidian.vip'")) {
  console.log('✅ 网站链接已更新为 https://www.obsidian.vip')
} else {
  console.log('❌ 网站链接未正确更新')
}

// 检查交流群链接
if (aboutInfoContent.includes("communityUrl: 'https://obsidian.vip/zh/documentation/community.html'")) {
  console.log('✅ 交流群链接已添加')
} else {
  console.log('❌ 交流群链接未正确添加')
}

// 检查邮箱是否已移除
if (!aboutInfoContent.includes('email:') || aboutInfoContent.includes('email?: string')) {
  console.log('✅ 邮箱信息已移除')
} else {
  console.log('❌ 邮箱信息未正确移除')
}

// 检查许可证信息是否已清空
if (aboutInfoContent.includes("type: ''") && aboutInfoContent.includes("text: ''")) {
  console.log('✅ 许可证信息已清空')
} else {
  console.log('❌ 许可证信息未正确清空')
}

console.log('\n📋 检查 AboutTab.tsx 组件:')

// 检查AboutTab组件
const aboutTabPath = path.join(__dirname, '../src/options/components/AboutTab.tsx')
const aboutTabContent = fs.readFileSync(aboutTabPath, 'utf8')

// 检查是否移除了Mail图标导入
if (!aboutTabContent.includes('Mail') || !aboutTabContent.includes('import { Star, Info, Globe, Calendar, Code, Shield, Package } from')) {
  console.log('✅ Mail图标导入已移除')
} else {
  console.log('❌ Mail图标导入未正确移除')
}

// 检查是否添加了交流群显示逻辑
if (aboutTabContent.includes('communityUrl') && aboutTabContent.includes('交流群:')) {
  console.log('✅ 交流群显示逻辑已添加')
} else {
  console.log('❌ 交流群显示逻辑未正确添加')
}

// 检查是否移除了许可证信息卡片
if (!aboutTabContent.includes('许可证信息卡片') && !aboutTabContent.includes('许可证信息')) {
  console.log('✅ 许可证信息卡片已移除')
} else {
  console.log('❌ 许可证信息卡片未正确移除')
}

console.log('\n📋 检查 manifestReader.ts 文件:')

// 检查manifestReader文件
const manifestReaderPath = path.join(__dirname, '../src/options/utils/manifestReader.ts')
const manifestReaderContent = fs.readFileSync(manifestReaderPath, 'utf8')

// 检查默认开发者信息
if (manifestReaderContent.includes("developer: manifest.author || 'coffeebean'")) {
  console.log('✅ manifestReader默认开发者信息已更新')
} else {
  console.log('❌ manifestReader默认开发者信息未正确更新')
}

console.log('\n📋 检查测试文件:')

// 检查测试文件
const testPath = path.join(__dirname, '../tests/AboutTab.shadcn.test.tsx')
const testContent = fs.readFileSync(testPath, 'utf8')

if (testContent.includes("name: 'coffeebean'") && testContent.includes('https://www.obsidian.vip')) {
  console.log('✅ 测试文件已更新')
} else {
  console.log('❌ 测试文件未正确更新')
}

console.log('\n🎉 关于我们页面信息修改验证完成！')
console.log('\n📝 修改摘要:')
console.log('• 开发者: Universe Bag Team → coffeebean')
console.log('• 网站: https://universebag.com → https://www.obsidian.vip')
console.log('• 邮箱: 已移除')
console.log('• 交流群: 已添加 https://obsidian.vip/zh/documentation/community.html')
console.log('• 许可证信息: 已移除')
console.log('• 版权信息: 已更新为新的开发者信息')