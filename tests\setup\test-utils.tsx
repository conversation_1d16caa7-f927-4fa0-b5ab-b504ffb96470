/**
 * 测试工具函数和配置
 */

import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { vi } from 'vitest'
import '@testing-library/jest-dom'

// 模拟 Chrome 扩展 API
export const mockChromeAPI = {
  runtime: {
    sendMessage: vi.fn().mockResolvedValue({ success: true, data: [] }),
    getManifest: vi.fn(() => ({
      name: 'Universe Bag（乾坤袋）',
      version: '1.0.0',
      description: '智能收藏管理工具，支持AI自动分类和云端同步',
      manifest_version: 3,
      permissions: ['storage', 'activeTab', 'contextMenus', 'tabs', 'scripting']
    })),
    getURL: vi.fn((path) => `chrome-extension://test/${path}`)
  },
  storage: {
    sync: {
      get: vi.fn().mockResolvedValue({}),
      set: vi.fn().mockResolvedValue(undefined),
      remove: vi.fn().mockResolvedValue(undefined),
      clear: vi.fn().mockResolvedValue(undefined)
    },
    local: {
      get: vi.fn().mockResolvedValue({}),
      set: vi.fn().mockResolvedValue(undefined),
      remove: vi.fn().mockResolvedValue(undefined),
      clear: vi.fn().mockResolvedValue(undefined)
    }
  },
  tabs: {
    query: vi.fn().mockResolvedValue([]),
    get: vi.fn().mockResolvedValue({}),
    create: vi.fn().mockResolvedValue({}),
    update: vi.fn().mockResolvedValue({})
  },
  action: {
    setBadgeText: vi.fn().mockResolvedValue(undefined),
    setBadgeBackgroundColor: vi.fn().mockResolvedValue(undefined),
    setTitle: vi.fn().mockResolvedValue(undefined),
    openPopup: vi.fn().mockResolvedValue(undefined)
  }
}

// 模拟 localStorage
export const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
}

// 模拟 matchMedia
export const mockMatchMedia = vi.fn(() => ({
  matches: false,
  media: '',
  onchange: null,
  addListener: vi.fn(), // deprecated
  removeListener: vi.fn(), // deprecated
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}))

// 设置全局模拟
export const setupGlobalMocks = () => {
  // Chrome API
  ;(global as any).chrome = mockChromeAPI
  
  // localStorage
  Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })
  
  // matchMedia
  Object.defineProperty(window, 'matchMedia', { value: mockMatchMedia })
  
  // location
  Object.defineProperty(window, 'location', {
    value: {
      hash: '',
      pathname: '/options.html',
      search: '',
      href: 'chrome-extension://test/options.html',
      origin: 'chrome-extension://test',
      protocol: 'chrome-extension:',
      host: 'test',
      hostname: 'test',
      port: '',
      assign: vi.fn(),
      replace: vi.fn(),
      reload: vi.fn()
    },
    writable: true
  })
  
  // history
  Object.defineProperty(window, 'history', {
    value: {
      replaceState: vi.fn(),
      pushState: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      go: vi.fn(),
      length: 1,
      state: null
    },
    writable: true
  })
  
  // ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }))
  
  // IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
    root: null,
    rootMargin: '',
    thresholds: []
  }))
  
  // MutationObserver
  global.MutationObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn().mockReturnValue([])
  }))
  
  // requestAnimationFrame
  global.requestAnimationFrame = vi.fn().mockImplementation(cb => setTimeout(cb, 16))
  global.cancelAnimationFrame = vi.fn().mockImplementation(id => clearTimeout(id))
  
  // getComputedStyle
  global.getComputedStyle = vi.fn().mockImplementation(() => ({
    getPropertyValue: vi.fn().mockReturnValue(''),
    setProperty: vi.fn(),
    removeProperty: vi.fn()
  }))
}

// 清理模拟
export const cleanupMocks = () => {
  vi.clearAllMocks()
  mockLocalStorage.getItem.mockReturnValue(null)
  mockMatchMedia.mockReturnValue({
    matches: false,
    media: '',
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })
  
  // 重置 DOM
  document.documentElement.classList.remove('dark')
  
  // 重置窗口尺寸
  Object.defineProperty(window, 'innerWidth', { value: 1024, writable: true })
  Object.defineProperty(window, 'innerHeight', { value: 768, writable: true })
}

// 测试提供者组件
interface TestProvidersProps {
  children: React.ReactNode
}

const TestProviders: React.FC<TestProvidersProps> = ({ children }) => {
  return <>{children}</>
}

// 自定义渲染函数
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  return render(ui, { wrapper: TestProviders, ...options })
}

// 等待函数
export const waitForElement = async (selector: string, timeout = 5000) => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    
    const checkElement = () => {
      const element = document.querySelector(selector)
      if (element) {
        resolve(element)
      } else if (Date.now() - startTime > timeout) {
        reject(new Error(`Element ${selector} not found within ${timeout}ms`))
      } else {
        setTimeout(checkElement, 100)
      }
    }
    
    checkElement()
  })
}

// 模拟用户交互延迟
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟网络请求
export const mockNetworkRequest = (response: any, delay = 100) => {
  return vi.fn().mockImplementation(() => 
    new Promise(resolve => setTimeout(() => resolve(response), delay))
  )
}

// 模拟错误
export const mockError = (message: string) => {
  return vi.fn().mockRejectedValue(new Error(message))
}

// 创建测试数据
export const createTestAboutData = () => ({
  extensionInfo: {
    name: 'Test Extension',
    version: '2.0.0',
    description: 'Test extension description',
    developer: 'Test Developer'
  },
  buildInfo: {
    buildDate: '2024-01-01',
    buildVersion: '2.0.0'
  },
  developerInfo: {
    name: 'Test Developer',
    website: 'https://test.com',
    email: '<EMAIL>'
  },
  licenseInfo: {
    type: 'MIT License',
    text: 'Test license text',
    url: 'https://opensource.org/licenses/MIT'
  }
})

export const createTestHelpContent = () => ({
  sections: [
    {
      id: 'test-section-1',
      title: '测试章节 1',
      content: '这是测试内容 1',
      category: 'guide' as const,
      keywords: ['测试', '指南']
    },
    {
      id: 'test-section-2',
      title: '测试章节 2',
      content: '这是测试内容 2',
      category: 'faq' as const,
      keywords: ['测试', '问题']
    }
  ],
  categories: [
    {
      id: 'guide',
      name: '使用指南',
      description: '详细的功能使用说明',
      icon: 'book',
      order: 1
    },
    {
      id: 'faq',
      name: '常见问题',
      description: '用户常见问题解答',
      icon: 'help-circle',
      order: 2
    }
  ]
})

// 断言辅助函数
export const expectElementToBeVisible = async (selector: string) => {
  const element = await waitForElement(selector)
  expect(element).toBeVisible()
}

export const expectElementToHaveText = async (selector: string, text: string) => {
  const element = await waitForElement(selector)
  expect(element).toHaveTextContent(text)
}

// 重新导出所有测试工具
export * from '@testing-library/react'
export { customRender as render }