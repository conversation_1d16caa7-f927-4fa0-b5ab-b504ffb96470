// 调试收藏功能修复的脚本
// 在浏览器控制台中运行此脚本来测试收藏功能

console.log('🔧 开始调试收藏功能修复...')

// 测试数据
const testData = {
  title: 'GitHub MinerU项目测试',
  url: 'https://github.com/opendatalab/MinerU',
  favIconUrl: 'https://github.com/favicon.ico',
  timestamp: new Date().toISOString()
}

// 测试快速收藏功能
async function testQuickBookmarkFix() {
  try {
    console.log('📝 测试快速收藏功能...')
    
    const response = await chrome.runtime.sendMessage({
      type: 'QUICK_BOOKMARK',
      data: testData
    })
    
    console.log('收藏响应:', response)
    
    if (response.success) {
      console.log('✅ 快速收藏成功:', response.data.bookmarkId)
      
      // 等待一秒后检查收藏状态
      setTimeout(async () => {
        await testBookmarkStatusAfterSave(testData.url)
      }, 1000)
      
      return response.data.bookmarkId
    } else {
      console.error('❌ 快速收藏失败:', response.error)
      return null
    }
  } catch (error) {
    console.error('❌ 快速收藏异常:', error)
    return null
  }
}

// 测试收藏状态检查
async function testBookmarkStatusAfterSave(url) {
  try {
    console.log('🔍 测试收藏状态检查...')
    
    const response = await chrome.runtime.sendMessage({
      type: 'CHECK_BOOKMARK_STATUS',
      data: { url }
    })
    
    console.log('状态检查响应:', response)
    
    if (response.success) {
      console.log('✅ 收藏状态检查成功:', response.data)
      
      if (response.data.isBookmarked) {
        console.log('🎉 收藏状态正确！页面已被标记为已收藏')
      } else {
        console.log('⚠️ 收藏状态异常！页面未被标记为已收藏')
      }
      
      return response.data
    } else {
      console.error('❌ 收藏状态检查失败:', response.error)
      return null
    }
  } catch (error) {
    console.error('❌ 收藏状态检查异常:', error)
    return null
  }
}

// 测试获取所有收藏
async function testGetAllBookmarks() {
  try {
    console.log('📚 测试获取所有收藏...')
    
    // 直接调用background script获取收藏列表
    const response = await chrome.runtime.sendMessage({
      type: 'GET_BOOKMARKS',
      data: {}
    })
    
    console.log('获取收藏响应:', response)
    
    if (response && response.success) {
      console.log('✅ 获取收藏成功，数量:', response.data.length)
      response.data.forEach((bookmark, index) => {
        console.log(`收藏 ${index + 1}:`, {
          id: bookmark.id,
          title: bookmark.title,
          url: bookmark.url,
          category: bookmark.category,
          createdAt: bookmark.createdAt
        })
      })
    } else {
      console.log('⚠️ 获取收藏失败或无数据:', response)
    }
  } catch (error) {
    console.error('❌ 获取收藏异常:', error)
  }
}

// 测试数据库直接访问
async function testDirectDatabaseAccess() {
  try {
    console.log('🗄️ 测试数据库直接访问...')
    
    // 打开IndexedDB
    const request = indexedDB.open('UniverseBagDB', 1)
    
    request.onsuccess = (event) => {
      const db = event.target.result
      const transaction = db.transaction(['bookmarks'], 'readonly')
      const store = transaction.objectStore('bookmarks')
      const getAllRequest = store.getAll()
      
      getAllRequest.onsuccess = () => {
        const bookmarks = getAllRequest.result
        console.log('✅ 数据库直接访问成功，收藏数量:', bookmarks.length)
        bookmarks.forEach((bookmark, index) => {
          console.log(`数据库收藏 ${index + 1}:`, {
            id: bookmark.id,
            title: bookmark.title,
            url: bookmark.url,
            category: bookmark.category,
            createdAt: bookmark.createdAt
          })
        })
      }
      
      getAllRequest.onerror = () => {
        console.error('❌ 数据库直接访问失败:', getAllRequest.error)
      }
    }
    
    request.onerror = () => {
      console.error('❌ 打开数据库失败:', request.error)
    }
  } catch (error) {
    console.error('❌ 数据库直接访问异常:', error)
  }
}

// 运行所有测试
async function runAllFixTests() {
  console.log('🚀 开始运行所有修复测试...')
  
  // 测试1: 快速收藏
  await testQuickBookmarkFix()
  
  // 等待2秒
  setTimeout(async () => {
    // 测试2: 获取所有收藏
    await testGetAllBookmarks()
    
    // 测试3: 数据库直接访问
    await testDirectDatabaseAccess()
  }, 2000)
  
  console.log('🎉 所有修复测试完成！')
}

// 如果在扩展环境中运行
if (typeof chrome !== 'undefined' && chrome.runtime) {
  runAllFixTests()
} else {
  console.log('⚠️ 请在Chrome扩展环境中运行此测试')
  console.log('1. 构建扩展: npm run build')
  console.log('2. 在Chrome中加载扩展')
  console.log('3. 在任意网页的控制台中运行此脚本')
}