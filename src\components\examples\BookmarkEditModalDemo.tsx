// BookmarkEditModal shadcn重构演示组件

import React, { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import BookmarkEditModal from '../BookmarkEditModal'

/**
 * BookmarkEditModal shadcn重构演示组件
 * 展示使用shadcn/ui组件重构后的收藏编辑模态窗口
 */
const BookmarkEditModalDemo: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  // 模拟收藏数据
  const mockBookmark = {
    id: '1',
    title: 'React官方文档',
    url: 'https://react.dev',
    description: 'React官方文档，学习React的最佳资源',
    category: '学习',
    tags: ['React', '前端', '文档', 'JavaScript']
  }

  // 处理保存操作
  const handleSave = async (updatedBookmark: any) => {
    setLoading(true)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('保存收藏:', updatedBookmark)
    alert('收藏保存成功！')
    
    setLoading(false)
    setIsModalOpen(false)
  }

  // 处理取消操作
  const handleCancel = () => {
    setIsModalOpen(false)
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            📝 BookmarkEditModal shadcn重构演示
          </CardTitle>
          <CardDescription>
            展示使用shadcn/ui组件重构后的收藏编辑模态窗口，包括Dialog、Form、Input、Select等组件的使用
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">功能特性</h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>✅ 使用shadcn Dialog组件替换自定义模态窗口</li>
                <li>✅ 使用shadcn Form组件重构编辑表单</li>
                <li>✅ 使用shadcn Input组件替换所有输入字段</li>
                <li>✅ 使用shadcn Button组件替换操作按钮</li>
                <li>✅ 使用shadcn Select组件替换分类选择器</li>
                <li>✅ 使用shadcn Badge组件显示标签</li>
                <li>✅ 移除自定义CSS样式，严格使用shadcn原生组件</li>
                <li>✅ 集成react-hook-form进行表单管理</li>
                <li>✅ 支持表单验证和错误提示</li>
                <li>✅ 支持标签的添加和删除</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">shadcn组件使用</h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li><code>Dialog</code> - 模态窗口容器</li>
                <li><code>DialogContent</code> - 模态窗口内容</li>
                <li><code>DialogHeader</code> - 模态窗口头部</li>
                <li><code>DialogTitle</code> - 模态窗口标题</li>
                <li><code>DialogDescription</code> - 模态窗口描述</li>
                <li><code>DialogFooter</code> - 模态窗口底部</li>
                <li><code>Form</code> - 表单容器</li>
                <li><code>FormField</code> - 表单字段</li>
                <li><code>FormItem</code> - 表单项</li>
                <li><code>FormLabel</code> - 表单标签</li>
                <li><code>FormControl</code> - 表单控件</li>
                <li><code>FormMessage</code> - 表单错误信息</li>
                <li><code>Input</code> - 输入框</li>
                <li><code>Textarea</code> - 文本域</li>
                <li><code>Select</code> - 选择器</li>
                <li><code>Button</code> - 按钮</li>
                <li><code>Badge</code> - 标签徽章</li>
              </ul>
            </div>
          </div>

          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold mb-2">当前收藏信息</h3>
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div><strong>标题:</strong> {mockBookmark.title}</div>
              <div><strong>网址:</strong> {mockBookmark.url}</div>
              <div><strong>描述:</strong> {mockBookmark.description}</div>
              <div><strong>分类:</strong> {mockBookmark.category}</div>
              <div><strong>标签:</strong> {mockBookmark.tags.join(', ')}</div>
            </div>
          </div>

          <div className="flex gap-4">
            <Button 
              onClick={() => setIsModalOpen(true)}
              className="flex items-center gap-2"
            >
              📝 打开编辑模态窗口
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* BookmarkEditModal组件 */}
      <BookmarkEditModal
        bookmark={mockBookmark}
        isOpen={isModalOpen}
        onSave={handleSave}
        onCancel={handleCancel}
        loading={loading}
      />
    </div>
  )
}

export default BookmarkEditModalDemo