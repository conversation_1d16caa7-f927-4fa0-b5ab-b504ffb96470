// UI修复功能集成测试

import { jest } from '@jest/globals'

// Mock Chrome APIs
global.chrome = {
  tabs: {
    query: jest.fn(),
    get: jest.fn(),
    onActivated: {
      addListener: jest.fn()
    },
    onUpdated: {
      addListener: jest.fn()
    }
  },
  windows: {
    onFocusChanged: {
      addListener: jest.fn()
    },
    WINDOW_ID_NONE: -1
  },
  action: {
    setBadgeText: jest.fn(),
    setBadgeBackgroundColor: jest.fn(),
    setBadgeTextColor: jest.fn(),
    setTitle: jest.fn(),
    getBadgeText: jest.fn()
  },
  runtime: {
    sendMessage: jest.fn()
  }
}

describe('UI修复功能集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('插件图标状态显示集成测试', () => {
    test('应该在标签页激活时正确更新图标状态', async () => {
      // 模拟标签页数据
      const mockTab = {
        id: 123,
        url: 'https://example.com',
        title: '测试页面'
      }

      // 模拟收藏状态检查响应
      chrome.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: { isBookmarked: true, bookmarkId: 'test-bookmark-id' }
      })

      chrome.tabs.get.mockResolvedValue(mockTab)

      // 动态导入标签页状态管理器
      const { tabStatusManager } = await import('../../src/services/tabStatusManager.js')

      // 执行标签页激活处理
      await tabStatusManager.onTabActivated(mockTab.id)

      // 验证Chrome API调用
      expect(chrome.tabs.get).toHaveBeenCalledWith(mockTab.id)
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'CHECK_BOOKMARK_STATUS',
        data: { url: mockTab.url }
      })
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: mockTab.id,
        text: '✓'
      })
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({
        tabId: mockTab.id,
        color: '#10b981'
      })
    })

    test('应该在页面加载完成时更新图标状态', async () => {
      const mockTab = {
        id: 456,
        url: 'https://test.com',
        title: '另一个测试页面'
      }

      const changeInfo = { status: 'complete' }

      chrome.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: { isBookmarked: false }
      })

      chrome.tabs.get.mockResolvedValue(mockTab)

      const { tabStatusManager } = await import('../../src/services/tabStatusManager.js')

      // 执行标签页更新处理
      await tabStatusManager.onTabUpdated(mockTab.id, changeInfo)

      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 350))

      // 验证API调用
      expect(chrome.tabs.get).toHaveBeenCalledWith(mockTab.id)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: mockTab.id,
        text: ''
      })
    })

    test('应该处理收藏状态变化并更新所有相关标签页', async () => {
      const url = 'https://example.com'
      const mockTabs = [
        { id: 123, url },
        { id: 456, url }
      ]

      chrome.tabs.query.mockResolvedValue(mockTabs)

      const { tabStatusManager } = await import('../../src/services/tabStatusManager.js')

      // 处理收藏状态变化
      await tabStatusManager.handleBookmarkStatusChange(url, true)

      // 验证所有相关标签页都被更新
      expect(chrome.tabs.query).toHaveBeenCalledWith({ url })
      expect(chrome.action.setBadgeText).toHaveBeenCalledTimes(2)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 123,
        text: '✓'
      })
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 456,
        text: '✓'
      })
    })
  })

  describe('收藏管理界面集成测试', () => {
    test('应该正确处理收藏列表获取和显示', async () => {
      const mockBookmarks = [
        {
          id: '1',
          title: '这是一个非常长的标题，用来测试截断功能是否正常工作',
          url: 'https://example.com',
          description: '测试描述',
          category: '测试分类',
          tags: ['测试', '标签']
        },
        {
          id: '2',
          title: '短标题',
          url: 'https://test.com',
          description: '另一个测试描述',
          category: '默认分类',
          tags: []
        }
      ]

      chrome.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: mockBookmarks
      })

      // 模拟获取收藏列表
      const response = await chrome.runtime.sendMessage({
        type: 'GET_BOOKMARKS',
        data: {}
      })

      expect(response.success).toBe(true)
      expect(response.data).toHaveLength(2)
      expect(response.data[0].title.length).toBeGreaterThan(50) // 验证有长标题
    })

    test('应该正确处理收藏编辑操作', async () => {
      const mockBookmark = {
        id: 'test-bookmark-id',
        title: '原始标题',
        url: 'https://example.com',
        description: '原始描述',
        category: '原始分类',
        tags: ['原始标签']
      }

      const updatedData = {
        id: mockBookmark.id,
        updates: {
          title: '更新后的标题',
          url: 'https://updated-example.com',
          description: '更新后的描述',
          category: '更新后的分类',
          tags: ['更新后的标签'],
          updatedAt: new Date().toISOString()
        }
      }

      chrome.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: { bookmarkId: mockBookmark.id }
      })

      // 模拟编辑操作
      const response = await chrome.runtime.sendMessage({
        type: 'UPDATE_BOOKMARK',
        data: updatedData
      })

      expect(response.success).toBe(true)
      expect(response.data.bookmarkId).toBe(mockBookmark.id)
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'UPDATE_BOOKMARK',
        data: updatedData
      })
    })
  })

  describe('端到端用户流程测试', () => {
    test('完整的收藏和编辑流程', async () => {
      // 1. 模拟页面收藏
      const mockTab = {
        id: 123,
        url: 'https://example.com',
        title: '测试页面'
      }

      chrome.runtime.sendMessage
        .mockResolvedValueOnce({
          success: true,
          data: { bookmarkId: 'new-bookmark-id' }
        })
        .mockResolvedValueOnce({
          success: true,
          data: { isBookmarked: true, bookmarkId: 'new-bookmark-id' }
        })
        .mockResolvedValueOnce({
          success: true,
          data: { bookmarkId: 'new-bookmark-id' }
        })

      chrome.tabs.get.mockResolvedValue(mockTab)

      // 执行收藏操作
      const bookmarkResponse = await chrome.runtime.sendMessage({
        type: 'QUICK_BOOKMARK',
        data: {
          title: mockTab.title,
          url: mockTab.url,
          timestamp: new Date().toISOString()
        }
      })

      expect(bookmarkResponse.success).toBe(true)

      // 2. 验证图标状态更新
      const { tabStatusManager } = await import('../../src/services/tabStatusManager.js')
      await tabStatusManager.checkAndUpdateIconStatus(mockTab.id, mockTab.url)

      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: mockTab.id,
        text: '✓'
      })

      // 3. 模拟编辑操作
      const editResponse = await chrome.runtime.sendMessage({
        type: 'UPDATE_BOOKMARK',
        data: {
          id: 'new-bookmark-id',
          updates: {
            title: '编辑后的标题',
            description: '添加的描述'
          }
        }
      })

      expect(editResponse.success).toBe(true)
    })

    test('错误处理流程', async () => {
      // 模拟网络错误
      chrome.runtime.sendMessage.mockRejectedValue(new Error('网络连接失败'))

      const { tabStatusManager } = await import('../../src/services/tabStatusManager.js')

      // 应该优雅处理错误，不抛出异常
      await expect(
        tabStatusManager.checkAndUpdateIconStatus(123, 'https://example.com')
      ).resolves.toBeUndefined()

      // 验证错误时清除图标状态
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 123,
        text: ''
      })
    })
  })

  describe('性能测试', () => {
    test('批量标签页状态更新性能', async () => {
      const mockTabs = Array.from({ length: 50 }, (_, i) => ({
        id: i + 1,
        url: `https://example${i}.com`,
        title: `测试页面 ${i}`
      }))

      chrome.tabs.query.mockResolvedValue(mockTabs)
      chrome.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: { isBookmarked: false }
      })

      const { tabStatusManager } = await import('../../src/services/tabStatusManager.js')

      const startTime = performance.now()
      await tabStatusManager.updateAllTabsStatus()
      const endTime = performance.now()

      const duration = endTime - startTime

      // 批量更新应该在合理时间内完成（小于5秒）
      expect(duration).toBeLessThan(5000)

      // 验证所有有效标签页都被处理
      expect(chrome.runtime.sendMessage).toHaveBeenCalledTimes(50)
    })

    test('防抖机制测试', async () => {
      const mockTab = {
        id: 123,
        url: 'https://example.com'
      }

      chrome.tabs.get.mockResolvedValue(mockTab)
      chrome.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: { isBookmarked: false }
      })

      const { tabStatusManager } = await import('../../src/services/tabStatusManager.js')

      // 快速连续调用多次
      const promises = []
      for (let i = 0; i < 10; i++) {
        promises.push(tabStatusManager.onTabUpdated(mockTab.id, { status: 'complete' }))
      }

      await Promise.all(promises)

      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 350))

      // 由于防抖机制，实际的状态检测应该只执行一次
      expect(chrome.runtime.sendMessage).toHaveBeenCalledTimes(1)
    })
  })
})