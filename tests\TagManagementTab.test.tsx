// TagManagementTab 组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import TagManagementTab from '../src/components/TagManagementTab'
import { tagService } from '../src/services/tagService'
import type { TagWithStats } from '../src/components/TagList'

// Mock tagService
vi.mock('../src/services/tagService', () => ({
  tagService: {
    syncTagsFromBookmarks: vi.fn(),
    getAllTagsWithStats: vi.fn(),
    createTag: vi.fn(),
    updateTag: vi.fn(),
    deleteTag: vi.fn()
  }
}))

// Mock 子组件
vi.mock('../src/components/TagList', () => ({
  default: ({ tags, onTagEdit, onTagDelete, onCreateTag, loading, searchQuery, onSearchChange, sortBy, onSortChange }: any) => (
    <div data-testid="tag-list">
      <div data-testid="tag-count">{tags.length}</div>
      <div data-testid="loading">{loading ? 'loading' : 'loaded'}</div>
      <div data-testid="search-query">{searchQuery}</div>
      <div data-testid="sort-by">{sortBy}</div>
      {tags.map((tag: TagWithStats) => (
        <div key={tag.id} data-testid={`tag-${tag.id}`}>
          <span>{tag.name} ({tag.usageCount})</span>
          <button onClick={() => onTagEdit(tag)} data-testid={`edit-${tag.id}`}>编辑</button>
          <button onClick={() => onTagDelete(tag)} data-testid={`delete-${tag.id}`}>删除</button>
        </div>
      ))}
      <button onClick={onCreateTag} data-testid="create-tag-button">创建标签</button>
      {onSearchChange && (
        <input 
          data-testid="search-input"
          onChange={(e) => onSearchChange(e.target.value)}
          value={searchQuery}
        />
      )}
      {onSortChange && (
        <select 
          data-testid="sort-select"
          onChange={(e) => onSortChange(e.target.value)}
          value={sortBy}
        >
          <option value="name-asc">名称升序</option>
          <option value="usage-desc">使用次数降序</option>
        </select>
      )}
    </div>
  )
}))

vi.mock('../src/components/TagModal', () => ({
  default: ({ isOpen, type, tag, onSave, onDelete, onClose, loading }: any) => (
    isOpen ? (
      <div data-testid="tag-modal">
        <div data-testid="modal-type">{type}</div>
        <div data-testid="modal-tag">{tag?.name || 'new'}</div>
        <div data-testid="modal-loading">{loading ? 'loading' : 'ready'}</div>
        {onSave && (
          <button 
            onClick={() => onSave({ name: 'Test Tag', color: '#FF0000' })}
            data-testid="modal-save"
          >
            保存
          </button>
        )}
        {onDelete && (
          <button onClick={onDelete} data-testid="modal-delete">删除</button>
        )}
        <button onClick={onClose} data-testid="modal-close">关闭</button>
      </div>
    ) : null
  )
}))

describe('TagManagementTab', () => {
  const mockTags: TagWithStats[] = [
    {
      id: 'tag1',
      name: '技术',
      color: '#3B82F6',
      usageCount: 5,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: 'tag2',
      name: '学习',
      color: '#10B981',
      usageCount: 3,
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02')
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    // 默认成功的mock实现
    vi.mocked(tagService.syncTagsFromBookmarks).mockResolvedValue([])
    vi.mocked(tagService.getAllTagsWithStats).mockResolvedValue(mockTags)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('基础渲染', () => {
    it('应该正确渲染组件结构', async () => {
      render(<TagManagementTab />)

      // 检查标题和描述
      expect(screen.getByText('标签管理')).toBeInTheDocument()
      expect(screen.getByText('管理您的书签标签，更好地分类和查找内容')).toBeInTheDocument()

      // 检查按钮
      expect(screen.getByText('刷新')).toBeInTheDocument()
      expect(screen.getByText('新建标签')).toBeInTheDocument()

      // 等待数据加载完成
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })
    })

    it('应该在组件挂载时加载标签数据', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(1)
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(1)
      })

      // 检查标签数据是否传递给TagList
      expect(screen.getByTestId('tag-count')).toHaveTextContent('2')
    })

    it('应该正确应用自定义className', () => {
      const { container } = render(<TagManagementTab className="custom-class" />)
      expect(container.firstChild).toHaveClass('custom-class')
    })
  })

  describe('数据加载', () => {
    it('应该显示加载状态', () => {
      render(<TagManagementTab />)
      
      // 初始状态应该是加载中
      expect(screen.getByTestId('loading')).toHaveTextContent('loading')
    })

    it('应该处理加载错误', async () => {
      const errorMessage = '网络错误'
      vi.mocked(tagService.getAllTagsWithStats).mockRejectedValue(new Error(errorMessage))

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('加载失败')).toBeInTheDocument()
        expect(screen.getByText(errorMessage)).toBeInTheDocument()
      })

      // 检查重试按钮
      const retryButton = screen.getByText('重试')
      expect(retryButton).toBeInTheDocument()
    })

    it('应该支持重试加载', async () => {
      // 第一次失败
      vi.mocked(tagService.getAllTagsWithStats)
        .mockRejectedValueOnce(new Error('网络错误'))
        .mockResolvedValueOnce(mockTags)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('加载失败')).toBeInTheDocument()
      })

      // 点击重试
      const retryButton = screen.getByText('重试')
      fireEvent.click(retryButton)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
        expect(screen.getByTestId('tag-count')).toHaveTextContent('2')
      })
    })
  })

  describe('标签操作', () => {
    it('应该支持创建新标签', async () => {
      const newTag = {
        id: 'tag3',
        name: 'Test Tag',
        color: '#FF0000',
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      vi.mocked(tagService.createTag).mockResolvedValue(newTag)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 点击新建标签按钮
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      // 检查模态窗口是否打开
      expect(screen.getByTestId('tag-modal')).toBeInTheDocument()
      expect(screen.getByTestId('modal-type')).toHaveTextContent('create')

      // 模拟保存操作
      const saveButton = screen.getByTestId('modal-save')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(tagService.createTag).toHaveBeenCalledWith({
          name: 'Test Tag',
          color: '#FF0000'
        })
      })
    })

    it('应该支持编辑标签', async () => {
      const updatedTag = { ...mockTags[0], name: 'Updated Tag' }
      vi.mocked(tagService.updateTag).mockResolvedValue(updatedTag)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 点击编辑按钮
      const editButton = screen.getByTestId('edit-tag1')
      fireEvent.click(editButton)

      // 检查模态窗口是否打开
      expect(screen.getByTestId('tag-modal')).toBeInTheDocument()
      expect(screen.getByTestId('modal-type')).toHaveTextContent('edit')
      expect(screen.getByTestId('modal-tag')).toHaveTextContent('技术')

      // 模拟保存操作
      const saveButton = screen.getByTestId('modal-save')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(tagService.updateTag).toHaveBeenCalledWith('tag1', {
          name: 'Test Tag',
          color: '#FF0000'
        })
      })
    })

    it('应该支持删除标签', async () => {
      vi.mocked(tagService.deleteTag).mockResolvedValue()

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 点击删除按钮
      const deleteButton = screen.getByTestId('delete-tag1')
      fireEvent.click(deleteButton)

      // 检查模态窗口是否打开
      expect(screen.getByTestId('tag-modal')).toBeInTheDocument()
      expect(screen.getByTestId('modal-type')).toHaveTextContent('delete')
      expect(screen.getByTestId('modal-tag')).toHaveTextContent('技术')

      // 模拟删除操作
      const confirmDeleteButton = screen.getByTestId('modal-delete')
      fireEvent.click(confirmDeleteButton)

      await waitFor(() => {
        expect(tagService.deleteTag).toHaveBeenCalledWith('tag1')
      })
    })

    it('应该处理操作错误', async () => {
      vi.mocked(tagService.createTag).mockRejectedValue(new Error('创建失败'))
      
      // Mock window.alert
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 点击新建标签按钮
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      // 模拟保存操作
      const saveButton = screen.getByTestId('modal-save')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(alertSpy).toHaveBeenCalledWith('保存标签失败: 创建失败')
      })

      alertSpy.mockRestore()
    })
  })

  describe('搜索和排序', () => {
    it('应该支持搜索功能', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 模拟搜索输入
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: '技术' } })

      // 检查搜索查询是否更新
      expect(screen.getByTestId('search-query')).toHaveTextContent('技术')
    })

    it('应该支持排序功能', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 模拟排序选择
      const sortSelect = screen.getByTestId('sort-select')
      fireEvent.change(sortSelect, { target: { value: 'usage-desc' } })

      // 检查排序选项是否更新
      expect(screen.getByTestId('sort-by')).toHaveTextContent('usage-desc')
    })
  })

  describe('模态窗口管理', () => {
    it('应该支持关闭模态窗口', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 打开模态窗口
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      expect(screen.getByTestId('tag-modal')).toBeInTheDocument()

      // 关闭模态窗口
      const closeButton = screen.getByTestId('modal-close')
      fireEvent.click(closeButton)

      expect(screen.queryByTestId('tag-modal')).not.toBeInTheDocument()
    })

    it('应该在操作进行中时禁止关闭模态窗口', async () => {
      // 让创建操作挂起
      vi.mocked(tagService.createTag).mockImplementation(() => new Promise(() => {}))

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 打开模态窗口
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      // 开始保存操作
      const saveButton = screen.getByTestId('modal-save')
      fireEvent.click(saveButton)

      // 检查模态窗口显示加载状态
      expect(screen.getByTestId('modal-loading')).toHaveTextContent('loading')

      // 尝试关闭模态窗口（应该无效）
      const closeButton = screen.getByTestId('modal-close')
      fireEvent.click(closeButton)

      // 模态窗口应该仍然存在
      expect(screen.getByTestId('tag-modal')).toBeInTheDocument()
    })
  })

  describe('刷新功能', () => {
    it('应该支持手动刷新', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 清除之前的调用记录
      vi.clearAllMocks()

      // 点击刷新按钮
      const refreshButton = screen.getByText('刷新')
      fireEvent.click(refreshButton)

      // 检查是否重新加载数据
      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(1)
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(1)
      })
    })

    it('应该在加载时禁用刷新按钮', () => {
      render(<TagManagementTab />)

      const refreshButton = screen.getByText('刷新')
      expect(refreshButton).toBeDisabled()
    })
  })

  describe('数据同步', () => {
    it('应该在加载时同步书签标签', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(1)
      })

      // 同步应该在获取统计信息之前执行
      expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledBefore(
        tagService.getAllTagsWithStats as any
      )
    })

    it('应该在操作完成后重新加载数据', async () => {
      const newTag = {
        id: 'tag3',
        name: 'Test Tag',
        color: '#FF0000',
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      vi.mocked(tagService.createTag).mockResolvedValue(newTag)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
      })

      // 清除初始加载的调用记录
      vi.clearAllMocks()

      // 创建新标签
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      const saveButton = screen.getByTestId('modal-save')
      fireEvent.click(saveButton)

      // 等待操作完成并重新加载数据
      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(1)
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(1)
      })
    })
  })
})