# Universe Bag 导入导出功能用户指南

## 📖 目录

1. [功能概述](#功能概述)
2. [导出数据](#导出数据)
3. [导入数据](#导入数据)
4. [冲突处理](#冲突处理)
5. [常见问题](#常见问题)
6. [故障排除](#故障排除)

## 🎯 功能概述

Universe Bag的导入导出功能让您可以轻松地备份、迁移和共享您的收藏数据。主要功能包括：

### ✨ 核心特性

- **多类型导出**: 支持导出全部数据、仅收藏夹、仅分类或仅标签
- **多种格式**: 收藏夹支持JSON、CSV、HTML格式，其他数据支持JSON格式
- **智能冲突检测**: 自动识别重复和相似的数据项
- **灵活冲突解决**: 提供多种解决方案，包括智能合并
- **大数据支持**: 优化的性能，支持处理大量数据
- **安全验证**: 完整的数据验证和安全检查

### 🎨 界面预览

导入导出功能位于扩展的选项页面中，提供直观的用户界面：

- **导出区域**: 选择导出类型和格式，配置导出选项
- **导入区域**: 选择文件，配置导入选项，查看导入进度
- **冲突解决**: 当检测到冲突时，会显示专门的冲突解决对话框

## 📤 导出数据

### 1. 全部数据导出

导出包含收藏夹、分类和标签的完整数据包。

**操作步骤:**

1. 打开Universe Bag扩展选项页面
2. 切换到"导入导出"标签页
3. 在导出区域选择"全部数据"
4. 配置导出选项：
   - ✅ **包含收藏夹**: 导出所有收藏的网站
   - ✅ **包含分类**: 导出分类结构和层级关系
   - ✅ **包含标签**: 导出标签和使用统计
   - ✅ **包含元数据**: 导出创建时间、统计信息等
5. 可选择日期范围来限制导出的数据
6. 点击"导出"按钮
7. 选择保存位置，文件将以JSON格式保存

**导出文件结构:**
```json
{
  "version": "1.0",
  "exportDate": "2025-08-01T10:30:00Z",
  "exportType": "all",
  "metadata": {
    "totalBookmarks": 150,
    "totalCategories": 12,
    "totalTags": 25
  },
  "bookmarks": [...],
  "categories": [...],
  "tags": [...]
}
```

### 2. 收藏夹导出

仅导出收藏的网站数据，支持多种格式。

**支持的格式:**

#### JSON格式
- 完整的数据结构，包含所有字段
- 适合程序处理和数据迁移
- 保留完整的元数据信息

#### CSV格式
- 表格形式，适合在Excel中查看
- 包含标题、URL、分类、标签、描述等主要字段
- 便于数据分析和统计

#### HTML格式
- 网页书签格式，兼容浏览器导入
- 保留链接结构和分类层级
- 可直接在浏览器中查看

**操作步骤:**
1. 选择"收藏夹"导出类型
2. 选择导出格式（JSON/CSV/HTML）
3. 配置筛选条件（可选）：
   - 按分类筛选
   - 按标签筛选
   - 按日期范围筛选
4. 点击"导出"按钮

### 3. 分类数据导出

导出分类结构和相关统计信息。

**包含内容:**
- 分类名称和描述
- 层级关系（父子分类）
- 使用统计（包含的收藏数量）
- 创建和修改时间

**操作步骤:**
1. 选择"分类"导出类型
2. 配置导出选项：
   - ✅ **包含层级关系**: 导出完整的分类树结构
   - ✅ **包含统计信息**: 导出每个分类的使用统计
3. 可选择特定分类进行导出
4. 点击"导出"按钮

### 4. 标签数据导出

导出标签信息和使用统计。

**包含内容:**
- 标签名称和描述
- 使用统计（被多少收藏使用）
- 相关收藏列表（可选）
- 创建时间

**操作步骤:**
1. 选择"标签"导出类型
2. 配置导出选项：
   - ✅ **包含使用统计**: 导出标签的使用频率
   - ✅ **包含相关收藏**: 导出使用该标签的收藏列表
3. 可选择特定标签进行导出
4. 点击"导出"按钮

## 📥 导入数据

### 1. 准备导入文件

**支持的文件格式:**
- JSON文件（推荐）
- CSV文件（仅收藏夹数据）
- HTML文件（浏览器书签格式）

**文件大小限制:**
- 最大文件大小：100MB
- 建议单次导入不超过10,000条记录

### 2. 导入操作步骤

1. 在导入区域点击"选择文件"按钮
2. 选择要导入的文件
3. 系统会自动验证文件格式和内容
4. 配置导入选项：
   - **冲突处理方式**: 
     - 自动处理：使用系统推荐的解决方案
     - 提示处理：手动选择如何处理每个冲突
     - 跳过冲突：忽略有冲突的数据项
   - **数据验证**: 启用完整的数据验证（推荐）
   - **批处理大小**: 设置每批处理的数据量（默认100）
5. 点击"开始导入"按钮
6. 查看导入进度和结果

### 3. 导入进度监控

导入过程中会显示详细的进度信息：

- **总体进度**: 显示导入的百分比
- **当前阶段**: 显示正在执行的操作（验证、检测冲突、导入等）
- **处理统计**: 显示已处理的数据量
- **错误信息**: 显示遇到的错误和警告

### 4. 导入结果

导入完成后会显示详细的结果报告：

```
导入完成！

成功导入:
- 收藏夹: 145 条
- 分类: 8 条  
- 标签: 20 条

处理的冲突: 5 个
跳过的无效数据: 2 条
总耗时: 2.3 秒
```

## ⚡ 冲突处理

当导入数据与现有数据存在冲突时，系统会自动检测并提供解决方案。

### 1. 冲突类型

#### URL重复冲突
- **描述**: 导入的收藏与现有收藏具有相同的URL
- **检测**: 精确URL匹配
- **影响**: 可能导致重复的收藏项

#### 内容相似冲突
- **描述**: 导入的收藏与现有收藏内容高度相似
- **检测**: 基于标题、描述的相似度算法
- **阈值**: 相似度超过80%时触发
- **影响**: 可能是同一网站的不同页面

#### 名称冲突
- **描述**: 分类或标签名称重复
- **检测**: 名称完全匹配
- **影响**: 可能导致分类/标签结构混乱

#### 数据不匹配
- **描述**: 同一项目的不同字段值不一致
- **检测**: 字段级别的差异比较
- **影响**: 数据完整性问题

### 2. 冲突解决方案

#### 保留现有 (Keep Existing)
- **说明**: 保持当前系统中的数据不变
- **适用**: 当现有数据更准确或更新时
- **结果**: 导入的冲突项被忽略

#### 使用导入 (Use Imported)
- **说明**: 用导入的数据替换现有数据
- **适用**: 当导入数据更准确或更新时
- **结果**: 现有的冲突项被覆盖

#### 智能合并 (Smart Merge)
- **说明**: 自动合并两个数据源的最佳内容
- **算法**: 
  - 选择更完整的字段值
  - 合并标签和分类信息
  - 保留最新的时间戳
- **适用**: 当两个数据源都有价值时
- **结果**: 创建包含最佳信息的合并项

#### 手动编辑 (Manual Edit)
- **说明**: 用户自定义编辑合并结果
- **功能**: 
  - 字段级别的选择
  - 自由文本编辑
  - 实时预览
- **适用**: 需要精确控制合并结果时
- **结果**: 按用户指定创建数据项

### 3. 冲突解决界面

#### 冲突列表
- 显示所有检测到的冲突项
- 按冲突类型和严重程度排序
- 支持筛选和搜索功能

#### 数据对比
- 并排显示现有数据和导入数据
- 高亮显示差异字段
- 显示相似度分数

#### 批量操作
- **全部保留现有**: 对所有冲突应用"保留现有"
- **全部使用导入**: 对所有冲突应用"使用导入"
- **智能批量处理**: 根据冲突类型自动选择最佳方案
- **按类型处理**: 对同类型冲突应用相同解决方案

#### 预览功能
- 显示解决方案的预期结果
- 支持在应用前预览合并效果
- 提供撤销和重新选择的机会

### 4. 冲突处理最佳实践

#### 导入前准备
1. **备份现有数据**: 在导入前先导出当前数据作为备份
2. **清理导入数据**: 移除明显的重复或无效数据
3. **分批导入**: 对于大量数据，建议分批导入以便更好地控制

#### 冲突处理策略
1. **URL重复**: 通常选择"保留现有"，除非导入数据更新
2. **内容相似**: 建议使用"智能合并"获得最完整的信息
3. **名称冲突**: 检查是否真的是同一分类/标签，考虑重命名
4. **数据不匹配**: 选择更准确或更新的数据

#### 质量控制
1. **验证合并结果**: 导入后检查关键数据的正确性
2. **测试功能**: 确保导入后的数据能正常使用
3. **清理重复**: 如有必要，手动清理剩余的重复项

## ❓ 常见问题

### Q1: 导出的文件可以在其他浏览器中使用吗？

**A**: 部分可以。HTML格式的收藏夹导出文件可以直接导入到大多数浏览器中。JSON格式的文件需要相应的导入工具支持。CSV格式主要用于数据分析，不能直接导入浏览器。

### Q2: 导入大文件时浏览器卡顿怎么办？

**A**: 这是正常现象。系统会自动使用分批处理和后台处理来优化性能。建议：
- 关闭其他不必要的标签页
- 等待处理完成，不要中断操作
- 如果文件过大（>50MB），考虑分割成多个小文件

### Q3: 为什么有些数据没有被导入？

**A**: 可能的原因：
- 数据格式不正确，未通过验证
- 存在冲突且选择了"跳过冲突"
- 数据被安全检查拦截
- 检查导入结果报告中的详细信息

### Q4: 如何处理大量的冲突项？

**A**: 建议策略：
1. 使用批量操作功能
2. 按冲突类型分别处理
3. 对于相似度高的项目，使用"智能合并"
4. 对于明确的重复项，选择"保留现有"

### Q5: 导入后发现数据有问题，如何恢复？

**A**: 
1. 如果有备份，可以清空当前数据后重新导入备份
2. 使用浏览器的同步功能恢复（如果启用了同步）
3. 手动删除有问题的数据项
4. 建议在重要操作前总是先备份

### Q6: 支持从其他收藏夹管理工具导入吗？

**A**: 目前支持：
- 浏览器原生书签（HTML格式）
- 标准CSV格式的收藏数据
- 其他Universe Bag实例的导出文件
- 如需支持其他工具，可能需要先转换为支持的格式

## 🔧 故障排除

### 导出问题

#### 导出失败或文件损坏
**症状**: 导出过程中断或生成的文件无法打开

**解决方案**:
1. 检查浏览器存储空间是否充足
2. 尝试导出较小的数据集
3. 更换导出格式
4. 重启浏览器后重试

#### 导出文件过大
**症状**: 导出的文件超过预期大小

**解决方案**:
1. 使用日期范围限制导出数据
2. 分别导出不同类型的数据
3. 清理不需要的历史数据
4. 选择不包含元数据的选项

### 导入问题

#### 文件格式不支持
**症状**: 提示"不支持的文件格式"

**解决方案**:
1. 确认文件扩展名正确（.json, .csv, .html）
2. 检查文件内容格式是否符合规范
3. 尝试用文本编辑器打开文件检查编码
4. 重新导出原始数据

#### 导入进度卡住
**症状**: 导入进度长时间不变

**解决方案**:
1. 等待更长时间（大文件需要更多时间）
2. 检查浏览器控制台是否有错误信息
3. 刷新页面重新开始导入
4. 尝试分批导入较小的文件

#### 内存不足错误
**症状**: 提示内存不足或浏览器崩溃

**解决方案**:
1. 关闭其他标签页和应用程序
2. 重启浏览器
3. 减小批处理大小
4. 分割大文件为多个小文件

### 冲突处理问题

#### 冲突检测过于敏感
**症状**: 检测到过多不必要的冲突

**解决方案**:
1. 调整相似度阈值设置
2. 使用"智能批量处理"功能
3. 手动审查冲突项的合理性
4. 考虑预处理导入数据

#### 合并结果不理想
**症状**: 智能合并的结果不符合预期

**解决方案**:
1. 使用"手动编辑"功能精确控制
2. 选择"保留现有"或"使用导入"
3. 导入后手动调整数据
4. 提供反馈以改进合并算法

### 性能问题

#### 处理速度慢
**症状**: 导入导出操作耗时过长

**解决方案**:
1. 确保有足够的系统资源
2. 关闭不必要的后台程序
3. 使用较小的批处理大小
4. 考虑在系统负载较低时操作

#### 界面响应慢
**症状**: 操作界面反应迟缓

**解决方案**:
1. 刷新页面重新加载
2. 清理浏览器缓存
3. 检查是否有其他扩展冲突
4. 重启浏览器

### 数据完整性问题

#### 导入后数据丢失
**症状**: 部分数据没有出现在导入结果中

**解决方案**:
1. 检查导入结果报告
2. 查看是否被冲突处理跳过
3. 验证原始文件的完整性
4. 检查数据验证错误信息

#### 数据关联错误
**症状**: 收藏的分类或标签关联不正确

**解决方案**:
1. 检查分类和标签是否正确导入
2. 验证数据文件中的关联关系
3. 手动修正错误的关联
4. 重新导入相关数据

## 📞 获取帮助

如果您遇到本指南未涵盖的问题，可以通过以下方式获取帮助：

1. **查看错误日志**: 浏览器开发者工具的控制台
2. **检查系统状态**: 扩展的状态页面
3. **社区支持**: 访问项目的GitHub页面
4. **反馈问题**: 通过扩展的反馈功能报告问题

---

**提示**: 建议在进行重要的导入导出操作前，先用小量数据进行测试，确保流程正常工作。

祝您使用愉快！ 🎉