// AI生成功能测试页面

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Sparkles, 
  FileText, 
  Tag, 
  BookOpen, 
  StickyNote, 
  Save,
  RefreshCw,
  Trash2
} from 'lucide-react'

import AITextGenerator from '../AITextGenerator'

interface TestBookmark {
  id: string
  title: string
  url: string
  description: string
  category: string
  tags: string[]
  notes: string
}

/**
 * AI生成功能测试页面
 * 用于测试和演示AI文本生成组件的各种功能
 */
const AIGeneratorTest: React.FC = () => {
  // 测试用的收藏数据
  const [testBookmarks, setTestBookmarks] = useState<TestBookmark[]>([
    {
      id: '1',
      title: 'React官方文档',
      url: 'https://react.dev',
      description: '',
      category: '开发工具',
      tags: ['React', '前端', '文档'],
      notes: ''
    },
    {
      id: '2',
      title: 'GitHub',
      url: 'https://github.com',
      description: '',
      category: '开发工具',
      tags: ['Git', '代码托管', '开源'],
      notes: ''
    },
    {
      id: '3',
      title: 'MDN Web Docs',
      url: 'https://developer.mozilla.org',
      description: '',
      category: '学习资源',
      tags: ['Web', 'JavaScript', 'CSS', 'HTML'],
      notes: ''
    }
  ])

  const [currentBookmark, setCurrentBookmark] = useState<TestBookmark>(testBookmarks[0])
  const [generationHistory, setGenerationHistory] = useState<Array<{
    id: string
    type: string
    content: string
    timestamp: string
    accepted: boolean
  }>>([])

  /**
   * 更新当前收藏的字段
   */
  const updateBookmarkField = (field: keyof TestBookmark, value: string) => {
    setCurrentBookmark(prev => ({
      ...prev,
      [field]: value
    }))
  }

  /**
   * 保存当前收藏
   */
  const saveCurrentBookmark = () => {
    setTestBookmarks(prev => 
      prev.map(bookmark => 
        bookmark.id === currentBookmark.id ? currentBookmark : bookmark
      )
    )
    
    // 添加到历史记录
    const historyEntry = {
      id: Date.now().toString(),
      type: 'save',
      content: `保存收藏: ${currentBookmark.title}`,
      timestamp: new Date().toLocaleString(),
      accepted: true
    }
    
    setGenerationHistory(prev => [historyEntry, ...prev].slice(0, 20))
  }

  /**
   * 重置当前收藏
   */
  const resetCurrentBookmark = () => {
    const original = testBookmarks.find(b => b.id === currentBookmark.id)
    if (original) {
      setCurrentBookmark({ ...original })
    }
  }

  /**
   * 清空所有字段
   */
  const clearAllFields = () => {
    setCurrentBookmark(prev => ({
      ...prev,
      description: '',
      notes: ''
    }))
  }

  /**
   * 记录生成历史
   */
  const recordGeneration = (type: string, content: string, accepted: boolean = false) => {
    const historyEntry = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date().toLocaleString(),
      accepted
    }
    
    setGenerationHistory(prev => [historyEntry, ...prev].slice(0, 20))
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold flex items-center mb-2">
          <Sparkles className="w-8 h-8 mr-3 text-primary" />
          AI生成功能测试
        </h1>
        <p className="text-muted-foreground">
          测试和演示AI文本生成组件的各种功能，包括描述生成、笔记生成等
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：测试收藏选择 */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                测试收藏
              </CardTitle>
              <CardDescription>
                选择一个收藏进行AI生成测试
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {testBookmarks.map((bookmark) => (
                <div
                  key={bookmark.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    currentBookmark.id === bookmark.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:bg-accent'
                  }`}
                  onClick={() => setCurrentBookmark({ ...bookmark })}
                >
                  <div className="font-medium text-sm">{bookmark.title}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {bookmark.url}
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {bookmark.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
              
              <Separator />
              
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetCurrentBookmark}
                  className="flex-1"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  重置
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearAllFields}
                  className="flex-1"
                >
                  <Trash2 className="w-3 h-3 mr-1" />
                  清空
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 生成历史 */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-sm">生成历史</CardTitle>
            </CardHeader>
            <CardContent className="max-h-60 overflow-y-auto">
              {generationHistory.length === 0 ? (
                <div className="text-sm text-muted-foreground text-center py-4">
                  暂无生成历史
                </div>
              ) : (
                <div className="space-y-2">
                  {generationHistory.map((entry) => (
                    <div
                      key={entry.id}
                      className="text-xs p-2 rounded bg-muted/50"
                    >
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {entry.type}
                        </Badge>
                        <span className="text-muted-foreground">
                          {entry.timestamp}
                        </span>
                      </div>
                      <div className="mt-1 text-foreground">
                        {entry.content.length > 50
                          ? entry.content.substring(0, 50) + '...'
                          : entry.content}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：AI生成测试区域 */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Sparkles className="w-5 h-5 mr-2" />
                  AI生成测试区域
                </div>
                <Button
                  onClick={saveCurrentBookmark}
                  size="sm"
                  className="ml-2"
                >
                  <Save className="w-3 h-3 mr-1" />
                  保存
                </Button>
              </CardTitle>
              <CardDescription>
                测试不同类型的AI文本生成功能
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">基础信息</TabsTrigger>
                  <TabsTrigger value="description">描述生成</TabsTrigger>
                  <TabsTrigger value="notes">笔记生成</TabsTrigger>
                </TabsList>

                {/* 基础信息标签页 */}
                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <Label htmlFor="title">标题</Label>
                      <Input
                        id="title"
                        value={currentBookmark.title}
                        onChange={(e) => updateBookmarkField('title', e.target.value)}
                        placeholder="收藏标题"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="url">网址</Label>
                      <Input
                        id="url"
                        value={currentBookmark.url}
                        onChange={(e) => updateBookmarkField('url', e.target.value)}
                        placeholder="https://example.com"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="category">分类</Label>
                      <Input
                        id="category"
                        value={currentBookmark.category}
                        onChange={(e) => updateBookmarkField('category', e.target.value)}
                        placeholder="收藏分类"
                      />
                    </div>
                    
                    <div>
                      <Label>标签</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {currentBookmark.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* 描述生成标签页 */}
                <TabsContent value="description" className="space-y-4">
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      基于标题、网址、分类和标签信息，AI将为您生成合适的描述内容。
                    </div>
                    
                    <AITextGenerator
                      label={
                        <div className="flex items-center">
                          <FileText className="w-4 h-4 mr-1" />
                          收藏描述
                        </div>
                      }
                      placeholder="AI将根据收藏信息生成描述..."
                      value={currentBookmark.description}
                      onChange={(value) => {
                        updateBookmarkField('description', value)
                        recordGeneration('description', value, true)
                      }}
                      context={{
                        title: currentBookmark.title,
                        url: currentBookmark.url,
                        category: currentBookmark.category,
                        tags: currentBookmark.tags
                      }}
                      generationType="description"
                      maxRows={4}
                      showSuggestions={true}
                    />
                    
                    <div className="text-xs text-muted-foreground">
                      💡 提示：确保标题和网址信息准确，这将帮助AI生成更精确的描述
                    </div>
                  </div>
                </TabsContent>

                {/* 笔记生成标签页 */}
                <TabsContent value="notes" className="space-y-4">
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      AI将基于收藏的所有信息，为您生成有用的笔记和使用建议。
                    </div>
                    
                    <AITextGenerator
                      label={
                        <div className="flex items-center">
                          <StickyNote className="w-4 h-4 mr-1" />
                          个人笔记
                        </div>
                      }
                      placeholder="AI将生成有用的笔记和想法..."
                      value={currentBookmark.notes}
                      onChange={(value) => {
                        updateBookmarkField('notes', value)
                        recordGeneration('notes', value, true)
                      }}
                      context={{
                        title: currentBookmark.title,
                        url: currentBookmark.url,
                        description: currentBookmark.description,
                        category: currentBookmark.category,
                        tags: currentBookmark.tags
                      }}
                      generationType="notes"
                      maxRows={5}
                      showSuggestions={true}
                    />
                    
                    <div className="text-xs text-muted-foreground">
                      💡 提示：笔记生成会考虑描述内容，建议先生成描述再生成笔记
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default AIGeneratorTest