// 标签页状态管理器 - 专门处理标签页收藏状态的检测和更新

import { bookmarkStatusService } from './bookmarkStatusService'

/**
 * 标签页状态管理器类
 * 负责管理标签页的收藏状态显示和更新
 */
export class TabStatusManager {
  private updateQueue = new Map<number, number>()
  private readonly DEBOUNCE_DELAY = 300 // 防抖延迟300ms
  private iconUpdateQueue = new Map<number, number>()
  private readonly ICON_UPDATE_DEBOUNCE = 200 // 图标更新防抖延迟200ms

  /**
   * 处理标签页激活事件
   * @param tabId 标签页ID
   */
  async onTabActivated(tabId: number): Promise<void> {
    try {
      console.log(`标签页激活: ${tabId}`)
      
      // 获取标签页信息
      const tab = await chrome.tabs.get(tabId)
      if (!tab.url) {
        console.log(`标签页 ${tabId} 没有URL，跳过状态检测`)
        return
      }

      // 检测并更新图标状态
      await this.checkAndUpdateIconStatus(tabId, tab.url)
    } catch (error) {
      console.error(`处理标签页激活事件失败 (${tabId}):`, error)
    }
  }

  /**
   * 处理标签页更新事件
   * @param tabId 标签页ID
   * @param changeInfo 变更信息
   */
  async onTabUpdated(tabId: number, changeInfo: chrome.tabs.TabChangeInfo): Promise<void> {
    try {
      // 只在页面加载完成时处理
      if (changeInfo.status !== 'complete') {
        return
      }

      console.log(`标签页更新完成: ${tabId}`)
      
      // 获取标签页信息
      const tab = await chrome.tabs.get(tabId)
      if (!tab.url) {
        console.log(`标签页 ${tabId} 没有URL，跳过状态检测`)
        return
      }

      // 使用防抖机制避免频繁更新
      await this.debouncedUpdateIconStatus(tabId, tab.url)
    } catch (error) {
      console.error(`处理标签页更新事件失败 (${tabId}):`, error)
    }
  }

  /**
   * 检测并更新图标状态
   * @param tabId 标签页ID
   * @param url 页面URL
   */
  async checkAndUpdateIconStatus(tabId: number, url: string): Promise<void> {
    try {
      console.log(`检测收藏状态: Tab ${tabId}, URL: ${url}`)
      
      // 检查收藏状态
      const status = await bookmarkStatusService.checkBookmarkStatus(url, true)
      
      console.log(`收藏状态检测结果: Tab ${tabId}, 已收藏: ${status.isBookmarked}`)
      
      // 更新图标状态
      await this.updateIconStatus(tabId, status.isBookmarked)
      
      // 更新缓存
      bookmarkStatusService.updateStatusCache(url, status.isBookmarked, status.bookmarkId)
    } catch (error) {
      console.error(`检测并更新图标状态失败 (Tab ${tabId}):`, error)
      
      // 发生错误时清除图标状态
      try {
        await this.updateIconStatus(tabId, false)
      } catch (iconError) {
        console.error(`清除图标状态失败:`, iconError)
      }
    }
  }

  /**
   * 防抖更新图标状态
   * @param tabId 标签页ID
   * @param url 页面URL
   */
  private async debouncedUpdateIconStatus(tabId: number, url: string): Promise<void> {
    // 清除之前的定时器
    const existingTimer = this.updateQueue.get(tabId)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 设置新的定时器
    const timer = setTimeout(async () => {
      await this.checkAndUpdateIconStatus(tabId, url)
      this.updateQueue.delete(tabId)
    }, this.DEBOUNCE_DELAY)

    this.updateQueue.set(tabId, timer)
  }

  /**
   * 更新插件图标状态（带防抖机制）
   * @param tabId 标签页ID
   * @param isBookmarked 是否已收藏
   */
  private async updateIconStatus(tabId: number, isBookmarked: boolean): Promise<void> {
    // 清除之前的定时器
    const existingTimer = this.iconUpdateQueue.get(tabId)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 设置新的防抖定时器
    const timer = setTimeout(async () => {
      await this.performIconUpdate(tabId, isBookmarked)
      this.iconUpdateQueue.delete(tabId)
    }, this.ICON_UPDATE_DEBOUNCE)

    this.iconUpdateQueue.set(tabId, timer)
  }

  /**
   * 执行实际的图标状态更新
   * @param tabId 标签页ID
   * @param isBookmarked 是否已收藏
   */
  private async performIconUpdate(tabId: number, isBookmarked: boolean): Promise<void> {
    try {
      // 验证标签页是否仍然存在
      try {
        await chrome.tabs.get(tabId)
      } catch (tabError) {
        console.log(`标签页 ${tabId} 不存在，跳过图标更新`)
        return
      }

      // 获取当前图标状态，避免重复更新
      const currentBadgeText = await chrome.action.getBadgeText({ tabId })
      const expectedBadgeText = isBookmarked ? '✓' : ''
      
      if (currentBadgeText === expectedBadgeText) {
        console.log(`标签页 ${tabId} 图标状态已是最新，跳过更新`)
        return
      }

      if (isBookmarked) {
        // 设置已收藏状态的徽章 - 使用批量操作提高性能
        await Promise.all([
          chrome.action.setBadgeText({
            tabId: tabId,
            text: '✓'
          }),
          chrome.action.setBadgeBackgroundColor({
            tabId: tabId,
            color: '#10b981' // 绿色
          }),
          chrome.action.setBadgeTextColor({
            tabId: tabId,
            color: '#ffffff' // 白色文字
          }),
          chrome.action.setTitle({
            tabId: tabId,
            title: 'Universe Bag - 当前页面已收藏 ✓'
          })
        ])
      } else {
        // 清除徽章 - 使用批量操作
        await Promise.all([
          chrome.action.setBadgeText({
            tabId: tabId,
            text: ''
          }),
          chrome.action.setTitle({
            tabId: tabId,
            title: 'Universe Bag - 智能收藏助手'
          })
        ])
      }
      
      console.log(`图标状态已更新 - Tab ${tabId}: ${isBookmarked ? '已收藏 ✓' : '未收藏'}`)
    } catch (error) {
      console.error(`更新图标状态失败 (Tab ${tabId}):`, error)
      
      // 降级处理：尝试只更新徽章文字
      try {
        await chrome.action.setBadgeText({
          tabId: tabId,
          text: isBookmarked ? '✓' : ''
        })
        console.log(`图标状态降级更新成功 - Tab ${tabId}`)
      } catch (fallbackError) {
        console.error(`图标状态降级更新也失败 (Tab ${tabId}):`, fallbackError)
        // 不再抛出错误，避免影响其他功能
      }
    }
  }

  /**
   * 批量更新所有标签页的状态
   */
  async updateAllTabsStatus(): Promise<void> {
    try {
      console.log('开始批量更新所有标签页状态')
      
      // 获取所有标签页
      const tabs = await chrome.tabs.query({})
      
      // 过滤有效的标签页
      const validTabs = tabs.filter(tab => tab.id && tab.url && !tab.url.startsWith('chrome://'))
      
      console.log(`找到 ${validTabs.length} 个有效标签页`)
      
      // 批量更新状态
      const updatePromises = validTabs.map(tab => 
        this.checkAndUpdateIconStatus(tab.id!, tab.url!)
      )
      
      await Promise.allSettled(updatePromises)
      
      console.log('批量更新标签页状态完成')
    } catch (error) {
      console.error('批量更新标签页状态失败:', error)
    }
  }

  /**
   * 处理收藏状态变化
   * @param url 收藏的URL
   * @param isBookmarked 是否已收藏
   */
  async handleBookmarkStatusChange(url: string, isBookmarked: boolean): Promise<void> {
    try {
      console.log(`处理收藏状态变化: ${url}, 已收藏: ${isBookmarked}`)
      
      // 查找所有匹配的标签页
      const tabs = await chrome.tabs.query({ url })
      
      // 更新所有匹配标签页的图标状态
      const updatePromises = tabs
        .filter(tab => tab.id)
        .map(tab => this.updateIconStatus(tab.id!, isBookmarked))
      
      await Promise.allSettled(updatePromises)
      
      console.log(`已更新 ${tabs.length} 个标签页的图标状态`)
    } catch (error) {
      console.error('处理收藏状态变化失败:', error)
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 清除所有待处理的定时器
    for (const timer of this.updateQueue.values()) {
      clearTimeout(timer)
    }
    this.updateQueue.clear()
    
    // 清除图标更新定时器
    for (const timer of this.iconUpdateQueue.values()) {
      clearTimeout(timer)
    }
    this.iconUpdateQueue.clear()
    
    console.log('标签页状态管理器资源已清理')
  }

  /**
   * 获取管理器状态信息
   */
  getStatus(): {
    pendingUpdates: number
    pendingIconUpdates: number
    debounceDelay: number
    iconUpdateDebounce: number
  } {
    return {
      pendingUpdates: this.updateQueue.size,
      pendingIconUpdates: this.iconUpdateQueue.size,
      debounceDelay: this.DEBOUNCE_DELAY,
      iconUpdateDebounce: this.ICON_UPDATE_DEBOUNCE
    }
  }
}

// 导出单例实例
export const tabStatusManager = new TabStatusManager()