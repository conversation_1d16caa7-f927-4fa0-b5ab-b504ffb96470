// Background Service Worker 主入口文件

import { messageHandler } from './messageHandler'
import { MessageSender } from '../utils/messaging'
import { indexedDBService } from '../utils/indexedDB'
import { bookmarkStatusService } from '../services/bookmarkStatusService'
import { tabStatusManager } from '../services/tabStatusManager'

console.log('Universe Bag Background Service Worker 已启动')

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('扩展已安装，原因:', details.reason)
  
  try {
    // 初始化数据库
    await indexedDBService.init()
    
    // 初始化存储
    await initializeStorage()
    
    // 启动状态监听器
    bookmarkStatusService.startTabUpdateListener()
    bookmarkStatusService.startBookmarkChangeListener()
    bookmarkStatusService.startPeriodicCleanup()
    
    // 启动标签页状态管理器
    initializeTabStatusManager()
    
    console.log('扩展初始化完成')
  } catch (error) {
    console.error('扩展初始化失败:', error)
  }
})

// 右键菜单已在messageHandler中初始化，这里不需要重复创建

// 右键菜单点击处理已在messageHandler中实现

// 初始化存储
async function initializeStorage() {
  try {
    const result = await chrome.storage.local.get(['initialized'])
    
    if (!result.initialized) {
      // 设置默认配置
      await chrome.storage.local.set({
        initialized: true,
        settings: {
          theme: 'light',
          language: 'zh-CN',
          autoTagging: true,
          floatingWidget: false,
          syncEnabled: false
        },
        categories: [
          {
            id: 'default',
            name: '默认分类',
            description: '未分类的收藏内容',
            color: '#3b82f6',
            createdAt: new Date().toISOString()
          }
        ],
        tags: [],
        bookmarks: []
      })
      
      console.log('存储初始化完成')
    }
  } catch (error) {
    console.error('存储初始化失败:', error)
  }
}

// 初始化标签页状态管理器
function initializeTabStatusManager() {
  try {
    // 监听标签页激活事件
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      await tabStatusManager.onTabActivated(activeInfo.tabId)
    })

    // 监听标签页更新事件
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      await tabStatusManager.onTabUpdated(tabId, changeInfo)
    })

    // 监听窗口焦点变化
    chrome.windows.onFocusChanged.addListener(async (windowId) => {
      if (windowId === chrome.windows.WINDOW_ID_NONE) return

      try {
        const tabs = await chrome.tabs.query({ windowId, active: true })
        if (tabs.length > 0 && tabs[0].id && tabs[0].url) {
          await tabStatusManager.checkAndUpdateIconStatus(tabs[0].id, tabs[0].url)
        }
      } catch (error) {
        console.error('处理窗口焦点变化失败:', error)
      }
    })

    // 初始化时更新所有标签页状态
    setTimeout(async () => {
      await tabStatusManager.updateAllTabsStatus()
    }, 1000) // 延迟1秒确保扩展完全加载

    console.log('标签页状态管理器已初始化')
  } catch (error) {
    console.error('初始化标签页状态管理器失败:', error)
  }
}

// 处理来自其他脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到消息:', message, '来自:', sender)
  
  // 使用新的消息处理器
  messageHandler.handleMessage(message, sender)
    .then(response => sendResponse(response))
    .catch(error => {
      console.error('处理消息失败:', error)
      sendResponse({ 
        success: false,
        error: error.message || '未知错误',
        requestId: message.requestId
      })
    })
  
  // 返回true表示异步响应
  return true
})