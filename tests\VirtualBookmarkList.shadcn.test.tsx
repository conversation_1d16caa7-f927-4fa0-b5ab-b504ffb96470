/**
 * VirtualBookmarkList shadcn组件集成测试
 * 测试shadcn组件的正确使用和集成
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import VirtualBookmarkList from '../src/components/VirtualBookmarkList'

// Mock虚拟滚动hooks
vi.mock('../src/utils/virtualScroll', () => ({
  useVirtualScroll: vi.fn(() => ({
    containerRef: { current: null },
    virtualState: {
      totalHeight: 1000,
      offsetY: 0,
      visibleItems: mockBookmarks,
      startIndex: 0
    },
    handleScroll: vi.fn(),
    updateItemHeight: vi.fn()
  })),
  useItemHeight: vi.fn(() => ({
    itemRef: { current: null }
  }))
}))

// Mock子组件
vi.mock('../src/components/BookmarkRow', () => ({
  default: ({ bookmark, onEdit, onDelete, onClick }: any) => (
    <div data-testid="bookmark-row">
      <span>{bookmark.title}</span>
      <button onClick={() => onEdit(bookmark)}>编辑</button>
      <button onClick={() => onDelete(bookmark)}>删除</button>
      <button onClick={() => onClick(bookmark)}>点击</button>
    </div>
  )
}))

vi.mock('../src/components/BookmarkCompact', () => ({
  default: ({ bookmark, onEdit, onDelete, onClick }: any) => (
    <div data-testid="bookmark-compact">
      <span>{bookmark.title}</span>
      <button onClick={() => onEdit(bookmark)}>编辑</button>
      <button onClick={() => onDelete(bookmark)}>删除</button>
      <button onClick={() => onClick(bookmark)}>点击</button>
    </div>
  )
}))

vi.mock('../src/components/TruncatedTitle', () => ({
  default: ({ title, className }: any) => (
    <span className={className}>{title}</span>
  )
}))

const mockBookmarks = [
  {
    id: '1',
    title: '测试收藏1',
    url: 'https://example.com',
    description: '这是一个测试收藏',
    content: '收藏内容',
    category: '测试分类',
    tags: ['标签1', '标签2'],
    createdAt: new Date('2024-01-01').toISOString(),
    favicon: 'https://example.com/favicon.ico'
  },
  {
    id: '2',
    title: '测试收藏2',
    url: 'https://example2.com',
    description: '这是另一个测试收藏',
    category: '测试分类2',
    tags: ['标签3'],
    createdAt: new Date('2024-01-02').toISOString()
  }
]

const defaultProps = {
  bookmarks: mockBookmarks,
  viewMode: 'card' as const,
  containerHeight: 400,
  onEdit: vi.fn(),
  onDelete: vi.fn(),
  onClick: vi.fn()
}

describe('VirtualBookmarkList shadcn集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('shadcn组件使用验证', () => {
    it('应该在card视图中使用shadcn Button组件', () => {
      render(<VirtualBookmarkList {...defaultProps} />)
      
      // 验证shadcn Button组件的存在
      const editButtons = screen.getAllByTitle('编辑收藏')
      const deleteButtons = screen.getAllByTitle('删除收藏')
      
      expect(editButtons).toHaveLength(2)
      expect(deleteButtons).toHaveLength(2)
      
      // 验证Button组件的shadcn类名
      editButtons.forEach(button => {
        expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
      })
      
      deleteButtons.forEach(button => {
        expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
      })
    })

    it('应该使用shadcn Badge组件显示标签', () => {
      render(<VirtualBookmarkList {...defaultProps} />)
      
      // 验证Badge组件的存在
      const badge1 = screen.getByText('标签1')
      const badge2 = screen.getByText('标签2')
      const badge3 = screen.getByText('标签3')
      
      // 验证Badge组件的shadcn类名
      expect(badge1).toHaveClass('inline-flex', 'items-center', 'rounded-full', 'border')
      expect(badge2).toHaveClass('inline-flex', 'items-center', 'rounded-full', 'border')
      expect(badge3).toHaveClass('inline-flex', 'items-center', 'rounded-full', 'border')
    })

    it('应该使用shadcn颜色系统', () => {
      render(<VirtualBookmarkList {...defaultProps} />)
      
      // 验证使用shadcn颜色类名
      const containers = screen.getAllByRole('generic').filter(el => 
        el.className.includes('border-border') || el.className.includes('text-foreground')
      )
      
      expect(containers.length).toBeGreaterThan(0)
    })

    it('应该使用cn工具函数合并类名', () => {
      render(<VirtualBookmarkList {...defaultProps} />)
      
      // 验证容器使用了正确的类名组合
      const container = document.querySelector('.w-full.transition-all')
      expect(container).toBeTruthy()
      expect(container?.className).toContain('w-full')
      expect(container?.className).toContain('transition-all')
    })
  })

  describe('高亮状态shadcn样式', () => {
    it('应该为高亮项目使用shadcn主题颜色', () => {
      render(
        <VirtualBookmarkList 
          {...defaultProps} 
          highlightBookmarkId="1"
        />
      )
      
      // 查找高亮的收藏项容器
      const highlightedContainer = document.querySelector('.border-primary')
      expect(highlightedContainer).toBeTruthy()
      expect(highlightedContainer).toHaveClass('border-primary', 'bg-primary/5')
    })

    it('应该为非高亮项目使用默认shadcn边框颜色', () => {
      render(<VirtualBookmarkList {...defaultProps} />)
      
      // 查找非高亮的收藏项容器
      const normalContainer = document.querySelector('.border-border')
      expect(normalContainer).toBeTruthy()
      expect(normalContainer).toHaveClass('border-border')
    })
  })

  describe('不同视图模式', () => {
    it('应该在row视图中使用BookmarkRow组件', () => {
      render(
        <VirtualBookmarkList 
          {...defaultProps} 
          viewMode="row"
        />
      )
      
      expect(screen.getAllByTestId('bookmark-row')).toHaveLength(2)
    })

    it('应该在compact视图中使用BookmarkCompact组件', () => {
      render(
        <VirtualBookmarkList 
          {...defaultProps} 
          viewMode="compact"
        />
      )
      
      expect(screen.getAllByTestId('bookmark-compact')).toHaveLength(2)
    })

    it('应该在card视图中使用shadcn组件', () => {
      render(
        <VirtualBookmarkList 
          {...defaultProps} 
          viewMode="card"
        />
      )
      
      // 验证card视图使用了shadcn组件
      expect(screen.getAllByTitle('编辑收藏')).toHaveLength(2)
      expect(screen.getAllByTitle('删除收藏')).toHaveLength(2)
      expect(screen.getByText('标签1')).toBeInTheDocument()
    })
  })

  describe('交互行为测试', () => {
    it('应该正确处理编辑按钮点击', () => {
      const onEdit = vi.fn()
      render(
        <VirtualBookmarkList 
          {...defaultProps} 
          onEdit={onEdit}
        />
      )
      
      const editButton = screen.getAllByTitle('编辑收藏')[0]
      fireEvent.click(editButton)
      
      expect(onEdit).toHaveBeenCalledWith(mockBookmarks[0])
    })

    it('应该正确处理删除按钮点击', () => {
      const onDelete = vi.fn()
      render(
        <VirtualBookmarkList 
          {...defaultProps} 
          onDelete={onDelete}
        />
      )
      
      const deleteButton = screen.getAllByTitle('删除收藏')[0]
      fireEvent.click(deleteButton)
      
      expect(onDelete).toHaveBeenCalledWith(mockBookmarks[0])
    })

    it('应该正确处理收藏项点击', () => {
      const onClick = vi.fn()
      render(
        <VirtualBookmarkList 
          {...defaultProps} 
          onClick={onClick}
        />
      )
      
      const bookmarkItem = screen.getByText('测试收藏1').closest('div')
      fireEvent.click(bookmarkItem!)
      
      expect(onClick).toHaveBeenCalledWith(mockBookmarks[0])
    })

    it('应该阻止按钮点击事件冒泡', () => {
      const onClick = vi.fn()
      const onEdit = vi.fn()
      
      render(
        <VirtualBookmarkList 
          {...defaultProps} 
          onClick={onClick}
          onEdit={onEdit}
        />
      )
      
      const editButton = screen.getAllByTitle('编辑收藏')[0]
      fireEvent.click(editButton)
      
      expect(onEdit).toHaveBeenCalledWith(mockBookmarks[0])
      expect(onClick).not.toHaveBeenCalled()
    })
  })

  describe('shadcn主题一致性', () => {
    it('应该使用shadcn文本颜色类', () => {
      render(<VirtualBookmarkList {...defaultProps} />)
      
      // 验证使用了shadcn文本颜色
      const foregroundElements = document.querySelectorAll('.text-foreground')
      expect(foregroundElements.length).toBeGreaterThan(0)
      
      // 验证标题元素使用了正确的颜色类
      const titleContainers = document.querySelectorAll('.text-lg.font-medium.text-foreground')
      expect(titleContainers.length).toBeGreaterThanOrEqual(2)
    })

    it('应该使用shadcn muted颜色用于次要文本', () => {
      render(<VirtualBookmarkList {...defaultProps} />)
      
      // 验证描述文本使用了muted颜色
      const descriptionElement = screen.getByText('这是一个测试收藏')
      expect(descriptionElement).toHaveClass('text-muted-foreground')
    })

    it('应该使用shadcn primary颜色用于链接', () => {
      render(<VirtualBookmarkList {...defaultProps} />)
      
      // 验证URL链接使用了primary颜色
      const linkElement = screen.getByText('https://example.com')
      expect(linkElement).toHaveClass('text-primary')
    })
  })

  describe('响应式布局', () => {
    it('应该为不同视图模式使用正确的网格类', () => {
      const { rerender } = render(
        <VirtualBookmarkList 
          {...defaultProps} 
          viewMode="compact"
        />
      )
      
      // compact视图应该使用网格布局
      let container = document.querySelector('.grid')
      expect(container).toHaveClass('grid-cols-1', 'sm:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4')
      
      // card视图应该使用简单网格
      rerender(
        <VirtualBookmarkList 
          {...defaultProps} 
          viewMode="card"
        />
      )
      
      container = document.querySelector('.grid')
      expect(container).toHaveClass('grid', 'gap-4')
    })
  })
})