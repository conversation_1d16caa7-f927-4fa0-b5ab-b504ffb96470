// TextUtils 工具类单元测试

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { TextUtils } from '../src/utils/textUtils'

describe('TextUtils', () => {
  // 模拟DOM环境
  beforeEach(() => {
    // 模拟canvas和context
    const mockCanvas = {
      getContext: vi.fn(() => ({
        font: '',
        measureText: vi.fn(() => ({
          width: 100
        }))
      }))
    }
    
    global.document = {
      createElement: vi.fn(() => mockCanvas)
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('smartTruncate', () => {
    it('应该在文本长度小于最大长度时返回原文本', () => {
      const text = '短文本'
      const result = TextUtils.smartTruncate(text, 10)
      
      expect(result.text).toBe(text)
      expect(result.isTruncated).toBe(false)
      expect(result.originalLength).toBe(3)
      expect(result.truncatedLength).toBe(3)
    })

    it('应该正确截断末尾文本', () => {
      const text = '这是一个很长的文本内容'
      const result = TextUtils.smartTruncate(text, 10)
      
      expect(result.text).toBe('这是一个很长的...')
      expect(result.isTruncated).toBe(true)
      expect(result.originalLength).toBe(11)
    })

    it('应该正确截断开头文本', () => {
      const text = '这是一个很长的文本内容'
      const result = TextUtils.smartTruncate(text, 10, { position: 'start' })
      
      expect(result.text).toContain('...')
      expect(result.text.startsWith('...')).toBe(true)
      expect(result.isTruncated).toBe(true)
    })

    it('应该正确截断中间文本', () => {
      const text = '这是一个很长的文本内容'
      const result = TextUtils.smartTruncate(text, 10, { position: 'middle' })
      
      expect(result.text).toContain('...')
      expect(result.isTruncated).toBe(true)
    })

    it('应该支持自定义省略号', () => {
      const text = '这是一个很长的文本内容'
      const result = TextUtils.smartTruncate(text, 10, { ellipsis: '---' })
      
      expect(result.text).toContain('---')
      expect(result.text).toBe('这是一个很长的---')
    })

    it('应该支持按单词边界截断', () => {
      const text = 'This is a very long text content'
      const result = TextUtils.smartTruncate(text, 15, { 
        wordBoundary: true,
        position: 'end'
      })
      
      expect(result.text).not.toMatch(/\w\.\.\.\w/) // 不应该在单词中间截断
      expect(result.isTruncated).toBe(true)
    })

    it('应该处理空文本', () => {
      const result = TextUtils.smartTruncate('', 10)
      
      expect(result.text).toBe('')
      expect(result.isTruncated).toBe(false)
      expect(result.originalLength).toBe(0)
    })

    it('应该处理null和undefined', () => {
      const resultNull = TextUtils.smartTruncate(null, 10)
      const resultUndefined = TextUtils.smartTruncate(undefined, 10)
      
      expect(resultNull.text).toBe(null)
      expect(resultUndefined.text).toBe(undefined)
      expect(resultNull.isTruncated).toBe(false)
      expect(resultUndefined.isTruncated).toBe(false)
    })
  })

  describe('truncateByWidth', () => {
    it('应该基于容器宽度截断文本', () => {
      const text = '这是一个需要基于宽度截断的长文本'
      const result = TextUtils.truncateByWidth(text, 200, 14)
      
      expect(typeof result).toBe('string')
      // 由于使用了模拟的measureText，这里主要测试函数不会报错
    })

    it('应该处理容器宽度为0的情况', () => {
      const text = '测试文本'
      const result = TextUtils.truncateByWidth(text, 0)
      
      expect(result).toBe(text)
    })

    it('应该处理空文本', () => {
      const result = TextUtils.truncateByWidth('', 200)
      
      expect(result).toBe('')
    })
  })

  describe('clampLines', () => {
    it('应该限制文本行数', () => {
      const text = '第一行\n第二行\n第三行\n第四行'
      const config = { maxLines: 2, lineHeight: 20 }
      const result = TextUtils.clampLines(text, config)
      
      const lines = result.split('\n')
      expect(lines.length).toBeLessThanOrEqual(2)
    })

    it('应该处理单行文本', () => {
      const text = '这是单行文本'
      const config = { maxLines: 3, lineHeight: 20 }
      const result = TextUtils.clampLines(text, config)
      
      expect(result).toBe(text)
    })

    it('应该处理空文本', () => {
      const config = { maxLines: 2, lineHeight: 20 }
      const result = TextUtils.clampLines('', config)
      
      expect(result).toBe('')
    })

    it('应该截断过长的最后一行', () => {
      // 创建一个超过50个字符的长行
      const longLine = '这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的第二行内容'
      const text = `第一行\n${longLine}`
      const config = { maxLines: 2, lineHeight: 20 }
      
      console.log('longLine length:', longLine.length)
      
      const result = TextUtils.clampLines(text, config)
      
      // 检查第二行是否被截断了
      const lines = result.split('\n')
      expect(lines.length).toBeLessThanOrEqual(2)
      
      // 由于长度超过50，应该被截断
      if (longLine.length > 50) {
        expect(lines[1]).toContain('...')
        expect(lines[1].length).toBeLessThan(longLine.length)
      }
    })
  })

  describe('beautifyUrl', () => {
    it('应该美化标准URL', () => {
      const url = 'https://www.example.com/path/to/page'
      const result = TextUtils.beautifyUrl(url)
      
      expect(result).toBe('example.com/path/to/page')
    })

    it('应该移除www前缀', () => {
      const url = 'https://www.example.com'
      const result = TextUtils.beautifyUrl(url, { removeWww: true })
      
      expect(result).toBe('example.com')
    })

    it('应该保留协议', () => {
      const url = 'https://example.com'
      const result = TextUtils.beautifyUrl(url, { removeProtocol: false })
      
      expect(result).toBe('https://example.com')
    })

    it('应该隐藏路径', () => {
      const url = 'https://example.com/path/to/page'
      const result = TextUtils.beautifyUrl(url, { showPath: false })
      
      expect(result).toBe('example.com')
    })

    it('应该处理查询参数', () => {
      const url = 'https://example.com?param1=value1&param2=value2'
      const result = TextUtils.beautifyUrl(url)
      
      expect(result).toContain('?...2个参数')
    })

    it('应该处理端口号', () => {
      const url = 'https://example.com:8080/path'
      const result = TextUtils.beautifyUrl(url)
      
      expect(result).toBe('example.com:8080/path')
    })

    it('应该处理无效URL', () => {
      const invalidUrl = 'not-a-valid-url'
      const result = TextUtils.beautifyUrl(invalidUrl)
      
      expect(result).toBe(invalidUrl) // 应该返回原始URL
    })

    it('应该应用长度限制', () => {
      const url = 'https://www.example.com/very/long/path/to/some/page'
      const result = TextUtils.beautifyUrl(url, { maxLength: 20 })
      
      expect(result.length).toBeLessThanOrEqual(20)
      expect(result).toContain('...')
    })
  })

  describe('measureText', () => {
    it('应该返回文本测量结果', () => {
      const text = '测试文本'
      const result = TextUtils.measureText(text, 14)
      
      expect(result).toHaveProperty('width')
      expect(result).toHaveProperty('height')
      expect(result).toHaveProperty('lines')
      expect(typeof result.width).toBe('number')
      expect(typeof result.height).toBe('number')
      expect(typeof result.lines).toBe('number')
    })

    it('应该处理不同字体大小', () => {
      const text = '测试文本'
      const result12 = TextUtils.measureText(text, 12)
      const result16 = TextUtils.measureText(text, 16)
      
      expect(result16.height).toBeGreaterThan(result12.height)
    })
  })

  describe('needsTruncation', () => {
    it('应该正确判断是否需要截断', () => {
      expect(TextUtils.needsTruncation('短文本', 10)).toBe(false)
      expect(TextUtils.needsTruncation('这是一个很长的文本', 5)).toBe(true)
      expect(TextUtils.needsTruncation('', 10)).toBe(false)
      expect(TextUtils.needsTruncation(null, 10)).toBe(false)
    })
  })

  describe('getTextSummary', () => {
    it('应该生成文本摘要', () => {
      const text = '这是第一句话。这是第二句话！这是第三句话？'
      const result = TextUtils.getTextSummary(text, 20)
      
      expect(result.length).toBeLessThanOrEqual(20)
      expect(typeof result).toBe('string')
    })

    it('应该在句号处截断', () => {
      const text = '这是第一句话。这是第二句话。'
      const result = TextUtils.getTextSummary(text, 8)
      
      expect(result).toBe('这是第一句话.')
    })

    it('应该处理没有句号的文本', () => {
      const text = '这是一段没有句号的长文本内容'
      const result = TextUtils.getTextSummary(text, 10)
      
      expect(result.length).toBeLessThanOrEqual(10)
      expect(result).toContain('...')
    })

    it('应该清理多余空白字符', () => {
      const text = '这是   一段   有多余   空格的   文本'
      const result = TextUtils.getTextSummary(text, 50)
      
      expect(result).toBe('这是 一段 有多余 空格的 文本')
    })

    it('应该处理空文本', () => {
      const result = TextUtils.getTextSummary('', 10)
      
      expect(result).toBe('')
    })
  })
})