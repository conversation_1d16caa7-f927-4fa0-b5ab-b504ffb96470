// 错误处理工具

import React from 'react'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  STORAGE = 'STORAGE',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  CHROME_API = 'CHROME_API',
  UI = 'UI',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 自定义错误类
 */
export class ExtensionError extends Error {
  public readonly type: ErrorType
  public readonly code: string
  public readonly timestamp: Date
  public readonly context?: any

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    code: string = 'UNKNOWN_ERROR',
    context?: any
  ) {
    super(message)
    this.name = 'ExtensionError'
    this.type = type
    this.code = code
    this.timestamp = new Date()
    this.context = context

    // 确保堆栈跟踪正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ExtensionError)
    }
  }

  /**
   * 转换为JSON格式
   */
  toJSON(): object {
    return {
      name: this.name,
      message: this.message,
      type: this.type,
      code: this.code,
      timestamp: this.timestamp.toISOString(),
      context: this.context,
      stack: this.stack
    }
  }
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  private static instance: ErrorHandler
  private errorLog: ExtensionError[] = []
  private maxLogSize: number = 100

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 处理错误
   * @param error 错误对象
   * @param context 错误上下文
   */
  handleError(error: Error | ExtensionError | string, context?: any): ExtensionError {
    let extensionError: ExtensionError

    if (error instanceof ExtensionError) {
      extensionError = error
    } else if (error instanceof Error) {
      extensionError = this.convertToExtensionError(error, context)
    } else {
      extensionError = new ExtensionError(
        String(error),
        ErrorType.UNKNOWN,
        'STRING_ERROR',
        context
      )
    }

    // 记录错误
    this.logError(extensionError)

    // 根据错误类型进行不同处理
    this.processError(extensionError)

    return extensionError
  }

  /**
   * 将普通错误转换为扩展错误
   */
  private convertToExtensionError(error: Error, context?: any): ExtensionError {
    let type = ErrorType.UNKNOWN
    let code = 'GENERIC_ERROR'

    // 根据错误消息判断类型
    const message = error.message.toLowerCase()
    
    if (message.includes('network') || message.includes('fetch')) {
      type = ErrorType.NETWORK
      code = 'NETWORK_ERROR'
    } else if (message.includes('storage') || message.includes('indexeddb')) {
      type = ErrorType.STORAGE
      code = 'STORAGE_ERROR'
    } else if (message.includes('validation') || message.includes('invalid')) {
      type = ErrorType.VALIDATION
      code = 'VALIDATION_ERROR'
    } else if (message.includes('permission') || message.includes('denied')) {
      type = ErrorType.PERMISSION
      code = 'PERMISSION_ERROR'
    } else if (message.includes('chrome') || message.includes('extension')) {
      type = ErrorType.CHROME_API
      code = 'CHROME_API_ERROR'
    }

    return new ExtensionError(error.message, type, code, {
      originalError: error.name,
      stack: error.stack,
      ...context
    })
  }

  /**
   * 记录错误到日志
   */
  private logError(error: ExtensionError): void {
    // 添加到错误日志
    this.errorLog.push(error)

    // 保持日志大小限制
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift()
    }

    // 控制台输出
    console.error(`[${error.type}] ${error.code}: ${error.message}`, error.context)

    // 在开发环境中输出详细信息
    if (process.env.NODE_ENV === 'development') {
      console.error('错误详情:', error.toJSON())
    }
  }

  /**
   * 处理不同类型的错误
   */
  private processError(error: ExtensionError): void {
    switch (error.type) {
      case ErrorType.NETWORK:
        this.handleNetworkError(error)
        break
      case ErrorType.STORAGE:
        this.handleStorageError(error)
        break
      case ErrorType.VALIDATION:
        this.handleValidationError(error)
        break
      case ErrorType.PERMISSION:
        this.handlePermissionError(error)
        break
      case ErrorType.CHROME_API:
        this.handleChromeApiError(error)
        break
      case ErrorType.UI:
        this.handleUIError(error)
        break
      default:
        this.handleUnknownError(error)
    }
  }

  /**
   * 处理网络错误
   */
  private handleNetworkError(error: ExtensionError): void {
    // 可以实现重试逻辑
    console.log('🌐 网络错误处理:', error.message)
  }

  /**
   * 处理存储错误
   */
  private handleStorageError(error: ExtensionError): void {
    // 可以尝试使用备用存储
    console.log('💾 存储错误处理:', error.message)
  }

  /**
   * 处理验证错误
   */
  private handleValidationError(error: ExtensionError): void {
    // 验证错误通常需要用户修正
    console.log('✅ 验证错误处理:', error.message)
  }

  /**
   * 处理权限错误
   */
  private handlePermissionError(error: ExtensionError): void {
    // 权限错误需要引导用户授权
    console.log('🔐 权限错误处理:', error.message)
  }

  /**
   * 处理Chrome API错误
   */
  private handleChromeApiError(error: ExtensionError): void {
    // Chrome API错误可能需要降级处理
    console.log('🔧 Chrome API错误处理:', error.message)
  }

  /**
   * 处理UI错误
   */
  private handleUIError(error: ExtensionError): void {
    // UI错误通常需要用户反馈
    console.log('🎨 UI错误处理:', error.message)
  }

  /**
   * 处理未知错误
   */
  private handleUnknownError(error: ExtensionError): void {
    console.log('❓ 未知错误处理:', error.message)
  }

  /**
   * 获取错误日志
   */
  getErrorLog(): ExtensionError[] {
    return [...this.errorLog]
  }

  /**
   * 清除错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number
    byType: Record<ErrorType, number>
    recent: ExtensionError[]
  } {
    const byType = {} as Record<ErrorType, number>
    
    // 初始化计数器
    Object.values(ErrorType).forEach(type => {
      byType[type] = 0
    })

    // 统计各类型错误数量
    this.errorLog.forEach(error => {
      byType[error.type]++
    })

    // 获取最近的错误（最多10个）
    const recent = this.errorLog.slice(-10)

    return {
      total: this.errorLog.length,
      byType,
      recent
    }
  }

  /**
   * 导出错误日志
   */
  exportErrorLog(): string {
    return JSON.stringify(this.errorLog.map(error => error.toJSON()), null, 2)
  }
}

/**
 * React错误边界组件
 */
export class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误到错误处理器
    const errorHandler = ErrorHandler.getInstance()
    errorHandler.handleError(error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: true
    })
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback
      return <FallbackComponent error={this.state.error!} />
    }

    return this.props.children
  }
}

/**
 * 默认错误回退组件
 */
const DefaultErrorFallback: React.FC<{ error: Error }> = ({ error }) => (
  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
    <h3 className="text-lg font-semibold text-red-800 mb-2">出现了错误</h3>
    <p className="text-red-600 mb-2">{error.message}</p>
    <button
      onClick={() => window.location.reload()}
      className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
    >
      重新加载
    </button>
  </div>
)

/**
 * 异步错误处理装饰器
 */
export function handleAsyncErrors<T extends (...args: any[]) => Promise<any>>(
  target: any,
  propertyName: string,
  descriptor: TypedPropertyDescriptor<T>
): TypedPropertyDescriptor<T> {
  const method = descriptor.value!

  descriptor.value = (async function (this: any, ...args: any[]) {
    try {
      return await method.apply(this, args)
    } catch (error) {
      const errorHandler = ErrorHandler.getInstance()
      throw errorHandler.handleError(error as Error, {
        method: propertyName,
        args: args.length
      })
    }
  }) as T

  return descriptor
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()

// 全局错误处理
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    errorHandler.handleError(event.error, {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    })
  })

  window.addEventListener('unhandledrejection', (event) => {
    errorHandler.handleError(event.reason, {
      type: 'unhandledPromiseRejection'
    })
  })
}