// 智能分类服务 - 基于内容的自动分类功能

import { Bookmark, Category, CategoryInput, CategoryUpdate } from '../types'
import { indexedDBService } from '../utils/indexedDB'
import { aiService } from './aiService'

/**
 * 分类统计信息接口
 */
export interface CategoryWithStats extends Category {
  bookmarkCount: number
}

/**
 * 智能分类服务类
 * 提供基于内容的自动分类和分类建议功能，以及分类管理功能
 */
export class CategoryService {
  
  /**
   * 基于内容自动分类
   * @param content 内容文本
   * @param title 标题
   * @param url URL地址
   * @returns Promise<string> 建议的分类名称
   */
  async suggestCategory(content: string, title?: string, url?: string): Promise<string> {
    try {
      // 首先尝试使用AI生成分类
      const aiResult = await aiService.generateCategory({
        content,
        title,
        url
      })
      
      if (aiResult.category && aiResult.confidence > 0.6) {
        return aiResult.category
      }
      
      // 如果AI结果不够可信，使用基于规则的分类
      return await this.ruleBasedCategorization(content, title, url)
    } catch (error) {
      console.error('智能分类失败，使用规则分类:', error)
      return await this.ruleBasedCategorization(content, title, url)
    }
  }

  /**
   * 基于规则的分类
   * @param content 内容文本
   * @param title 标题
   * @param url URL地址
   * @returns Promise<string> 分类名称
   */
  private async ruleBasedCategorization(content: string, title?: string, url?: string): Promise<string> {
    const text = `${title || ''} ${content || ''} ${url || ''}`.toLowerCase()
    
    // 技术相关关键词
    const techKeywords = [
      'javascript', 'python', 'java', 'react', 'vue', 'angular', 'node',
      'api', 'database', 'sql', 'html', 'css', 'programming', 'code',
      'github', 'stackoverflow', 'developer', 'tutorial', 'documentation'
    ]
    
    // 学习相关关键词
    const learningKeywords = [
      'course', 'tutorial', 'learn', 'education', 'study', 'training',
      'guide', 'how to', 'lesson', 'class', 'university', 'school'
    ]
    
    // 新闻相关关键词
    const newsKeywords = [
      'news', 'article', 'report', 'breaking', 'update', 'latest',
      'today', 'yesterday', 'current', 'events'
    ]
    
    // 工具相关关键词
    const toolKeywords = [
      'tool', 'software', 'app', 'application', 'utility', 'service',
      'platform', 'extension', 'plugin', 'download'
    ]
    
    // 娱乐相关关键词
    const entertainmentKeywords = [
      'video', 'movie', 'music', 'game', 'entertainment', 'fun',
      'youtube', 'netflix', 'spotify', 'steam', 'play'
    ]
    
    // 检查关键词匹配
    if (this.containsKeywords(text, techKeywords)) {
      return '技术'
    } else if (this.containsKeywords(text, learningKeywords)) {
      return '学习'
    } else if (this.containsKeywords(text, newsKeywords)) {
      return '新闻'
    } else if (this.containsKeywords(text, toolKeywords)) {
      return '工具'
    } else if (this.containsKeywords(text, entertainmentKeywords)) {
      return '娱乐'
    }
    
    // 基于URL域名分类
    if (url) {
      const domain = this.extractDomain(url)
      const domainCategory = this.categorizeDomain(domain)
      if (domainCategory) {
        return domainCategory
      }
    }
    
    // 默认分类
    return '默认分类'
  }

  /**
   * 检查文本是否包含关键词
   * @param text 文本
   * @param keywords 关键词数组
   * @returns 是否包含关键词
   */
  private containsKeywords(text: string, keywords: string[]): boolean {
    return keywords.some(keyword => text.includes(keyword))
  }

  /**
   * 从URL提取域名
   * @param url URL地址
   * @returns 域名
   */
  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace('www.', '')
    } catch {
      return ''
    }
  }

  /**
   * 基于域名分类
   * @param domain 域名
   * @returns 分类名称或null
   */
  private categorizeDomain(domain: string): string | null {
    const domainCategories: Record<string, string> = {
      // 技术网站
      'github.com': '技术',
      'stackoverflow.com': '技术',
      'developer.mozilla.org': '技术',
      'npmjs.com': '技术',
      'codepen.io': '技术',
      
      // 学习网站
      'coursera.org': '学习',
      'udemy.com': '学习',
      'edx.org': '学习',
      'khanacademy.org': '学习',
      'codecademy.com': '学习',
      
      // 新闻网站
      'cnn.com': '新闻',
      'bbc.com': '新闻',
      'reuters.com': '新闻',
      'techcrunch.com': '新闻',
      
      // 工具网站
      'figma.com': '工具',
      'notion.so': '工具',
      'trello.com': '工具',
      'slack.com': '工具',
      
      // 娱乐网站
      'youtube.com': '娱乐',
      'netflix.com': '娱乐',
      'spotify.com': '娱乐',
      'twitch.tv': '娱乐'
    }
    
    return domainCategories[domain] || null
  }

  /**
   * 获取相似内容的收藏
   * @param bookmark 当前收藏
   * @param threshold 相似度阈值
   * @returns Promise<Bookmark[]> 相似的收藏
   */
  async findSimilarBookmarks(bookmark: Bookmark, threshold: number = 0.7): Promise<Bookmark[]> {
    try {
      const allBookmarks = await indexedDBService.getBookmarks()
      const similarBookmarks: Bookmark[] = []
      
      for (const existingBookmark of allBookmarks) {
        if (existingBookmark.id === bookmark.id) continue
        
        const similarity = this.calculateContentSimilarity(bookmark, existingBookmark)
        if (similarity >= threshold) {
          similarBookmarks.push(existingBookmark)
        }
      }
      
      return similarBookmarks.sort((a, b) => {
        const simA = this.calculateContentSimilarity(bookmark, a)
        const simB = this.calculateContentSimilarity(bookmark, b)
        return simB - simA
      })
    } catch (error) {
      console.error('查找相似收藏失败:', error)
      return []
    }
  }

  /**
   * 计算内容相似度
   * @param bookmark1 收藏1
   * @param bookmark2 收藏2
   * @returns 相似度（0-1）
   */
  private calculateContentSimilarity(bookmark1: Bookmark, bookmark2: Bookmark): number {
    let similarity = 0
    let factors = 0
    
    // 标题相似度
    if (bookmark1.title && bookmark2.title) {
      factors++
      similarity += this.calculateTextSimilarity(bookmark1.title, bookmark2.title)
    }
    
    // 内容相似度
    if (bookmark1.content && bookmark2.content) {
      factors++
      similarity += this.calculateTextSimilarity(bookmark1.content, bookmark2.content)
    }
    
    // 标签相似度
    if (bookmark1.tags.length > 0 && bookmark2.tags.length > 0) {
      factors++
      const commonTags = bookmark1.tags.filter(tag => bookmark2.tags.includes(tag))
      const totalTags = new Set([...bookmark1.tags, ...bookmark2.tags]).size
      similarity += commonTags.length / totalTags
    }
    
    // 分类相似度
    if (bookmark1.category && bookmark2.category) {
      factors++
      similarity += bookmark1.category === bookmark2.category ? 1 : 0
    }
    
    return factors > 0 ? similarity / factors : 0
  }

  /**
   * 计算文本相似度
   * @param text1 文本1
   * @param text2 文本2
   * @returns 相似度（0-1）
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    if (!text1 || !text2) return 0
    if (text1 === text2) return 1
    
    // 简单的词汇重叠相似度
    const words1 = text1.toLowerCase().split(/\s+/)
    const words2 = text2.toLowerCase().split(/\s+/)
    
    const commonWords = words1.filter(word => words2.includes(word))
    const totalWords = new Set([...words1, ...words2]).size
    
    return commonWords.length / totalWords
  }

  /**
   * 建议将收藏移动到现有分类
   * @param bookmark 收藏对象
   * @returns Promise<string[]> 建议的分类列表
   */
  async suggestExistingCategories(bookmark: Bookmark): Promise<string[]> {
    try {
      // 获取所有现有分类
      const categories = await indexedDBService.getCategories()
      const categoryNames = categories.map(cat => cat.name)
      
      // 查找相似的收藏
      const similarBookmarks = await this.findSimilarBookmarks(bookmark, 0.6)
      
      // 统计相似收藏的分类
      const categoryCount: Record<string, number> = {}
      similarBookmarks.forEach(similar => {
        if (categoryNames.includes(similar.category)) {
          categoryCount[similar.category] = (categoryCount[similar.category] || 0) + 1
        }
      })
      
      // 按使用频率排序
      return Object.entries(categoryCount)
        .sort(([, a], [, b]) => b - a)
        .map(([category]) => category)
        .slice(0, 3) // 返回前3个建议
    } catch (error) {
      console.error('建议现有分类失败:', error)
      return []
    }
  }

  /**
   * 自动整理收藏分类
   * @param bookmarks 要整理的收藏数组（可选，默认整理所有）
   * @returns Promise<{moved: number, suggestions: Array<{bookmarkId: string, suggestedCategory: string}>}>
   */
  async autoOrganizeBookmarks(bookmarks?: Bookmark[]): Promise<{
    moved: number
    suggestions: Array<{bookmarkId: string, suggestedCategory: string}>
  }> {
    try {
      const targetBookmarks = bookmarks || await indexedDBService.getBookmarks()
      let movedCount = 0
      const suggestions: Array<{bookmarkId: string, suggestedCategory: string}> = []
      
      for (const bookmark of targetBookmarks) {
        // 跳过已经有明确分类的收藏（非默认分类）
        if (bookmark.category !== '默认分类' && bookmark.category !== '未分类') {
          continue
        }
        
        // 生成分类建议
        const suggestedCategory = await this.suggestCategory(
          bookmark.content || '',
          bookmark.title,
          bookmark.url
        )
        
        // 如果建议的分类与当前分类不同
        if (suggestedCategory !== bookmark.category) {
          suggestions.push({
            bookmarkId: bookmark.id,
            suggestedCategory
          })
          
          // 自动移动到建议的分类（可选）
          // 这里可以根据需要决定是否自动移动
          // await indexedDBService.updateBookmark(bookmark.id, { category: suggestedCategory })
          // movedCount++
        }
      }
      
      return { moved: movedCount, suggestions }
    } catch (error) {
      console.error('自动整理收藏失败:', error)
      return { moved: 0, suggestions: [] }
    }
  }

  // ==================== 分类管理功能 ====================

  /**
   * 获取所有分类
   * @returns Promise<Category[]> 分类列表
   */
  async getAllCategories(): Promise<Category[]> {
    try {
      return await indexedDBService.getCategories()
    } catch (error) {
      console.error('获取分类列表失败:', error)
      throw new Error('获取分类列表失败')
    }
  }

  /**
   * 获取所有分类及其统计信息
   * @returns Promise<CategoryWithStats[]> 包含统计信息的分类列表
   */
  async getAllCategoriesWithStats(): Promise<CategoryWithStats[]> {
    try {
      const [categories, bookmarks] = await Promise.all([
        indexedDBService.getCategories(),
        indexedDBService.getBookmarks()
      ])

      // 计算每个分类的书签数量
      const categoryStats = new Map<string, number>()
      
      // 统计现有分类的书签数量
      bookmarks.forEach(bookmark => {
        const categoryName = bookmark.category || '默认分类'
        categoryStats.set(categoryName, (categoryStats.get(categoryName) || 0) + 1)
      })

      // 为每个分类添加统计信息
      const categoriesWithStats: CategoryWithStats[] = categories.map(category => ({
        ...category,
        bookmarkCount: categoryStats.get(category.name) || 0
      }))

      return categoriesWithStats
    } catch (error) {
      console.error('获取分类统计信息失败:', error)
      throw new Error('获取分类统计信息失败')
    }
  }

  /**
   * 从现有书签中同步分类
   * @returns Promise<Category[]> 同步后的分类列表
   */
  async syncCategoriesFromBookmarks(): Promise<Category[]> {
    try {
      const [bookmarks, existingCategories] = await Promise.all([
        indexedDBService.getBookmarks(),
        indexedDBService.getCategories()
      ])

      // 获取现有分类名称集合
      const existingCategoryNames = new Set(existingCategories.map(cat => cat.name))
      
      // 从书签中提取所有分类名称
      const bookmarkCategories = new Set<string>()
      bookmarks.forEach(bookmark => {
        if (bookmark.category && bookmark.category.trim()) {
          bookmarkCategories.add(bookmark.category.trim())
        }
      })

      // 创建新发现的分类
      const newCategories: Category[] = []
      const now = new Date()

      for (const categoryName of bookmarkCategories) {
        if (!existingCategoryNames.has(categoryName)) {
          const newCategory: Category = {
            id: this.generateCategoryId(),
            name: categoryName,
            description: `从书签自动提取的分类`,
            color: this.generateCategoryColor(categoryName),
            createdAt: now,
            updatedAt: now,
            bookmarkCount: 0 // 将在后续计算中更新
          }
          
          await indexedDBService.saveCategory(newCategory)
          newCategories.push(newCategory)
        }
      }

      // 返回所有分类（包括新创建的）
      const allCategories = await indexedDBService.getCategories()
      console.log(`同步完成，新增 ${newCategories.length} 个分类`)
      
      return allCategories
    } catch (error) {
      console.error('同步分类失败:', error)
      throw new Error('同步分类失败')
    }
  }

  /**
   * 创建新分类
   * @param categoryData 分类数据
   * @returns Promise<Category> 创建的分类
   */
  async createCategory(categoryData: CategoryInput): Promise<Category> {
    try {
      // 验证分类名称唯一性
      const isUnique = await this.validateCategoryName(categoryData.name)
      if (!isUnique) {
        throw new Error(`分类名称 "${categoryData.name}" 已存在`)
      }

      const now = new Date()
      const newCategory: Category = {
        id: this.generateCategoryId(),
        name: categoryData.name.trim(),
        description: categoryData.description?.trim() || '',
        color: categoryData.color || this.generateCategoryColor(categoryData.name),
        parentId: categoryData.parentId || undefined,
        createdAt: now,
        updatedAt: now,
        bookmarkCount: 0
      }

      await indexedDBService.saveCategory(newCategory)
      console.log(`创建分类成功: ${newCategory.name}`)
      
      return newCategory
    } catch (error) {
      console.error('创建分类失败:', error)
      throw error
    }
  }

  /**
   * 更新分类
   * @param categoryId 分类ID
   * @param updates 更新数据
   * @returns Promise<Category> 更新后的分类
   */
  async updateCategory(categoryId: string, updates: CategoryUpdate): Promise<Category> {
    try {
      const existingCategory = await indexedDBService.getCategory(categoryId)
      if (!existingCategory) {
        throw new Error(`分类不存在: ${categoryId}`)
      }

      // 如果更新名称，验证唯一性
      if (updates.name && updates.name !== existingCategory.name) {
        const isUnique = await this.validateCategoryName(updates.name, categoryId)
        if (!isUnique) {
          throw new Error(`分类名称 "${updates.name}" 已存在`)
        }
      }

      const updatedCategory: Category = {
        ...existingCategory,
        ...updates,
        id: categoryId, // 确保ID不被修改
        updatedAt: new Date()
      }

      // 如果名称发生变化，需要更新相关书签的分类
      if (updates.name && updates.name !== existingCategory.name) {
        await this.updateBookmarkCategories(existingCategory.name, updates.name)
      }

      await indexedDBService.saveCategory(updatedCategory)
      console.log(`更新分类成功: ${updatedCategory.name}`)
      
      return updatedCategory
    } catch (error) {
      console.error('更新分类失败:', error)
      throw error
    }
  }

  /**
   * 删除分类
   * @param categoryId 分类ID
   * @returns Promise<void>
   */
  async deleteCategory(categoryId: string): Promise<void> {
    try {
      const category = await indexedDBService.getCategory(categoryId)
      if (!category) {
        throw new Error(`分类不存在: ${categoryId}`)
      }

      // 获取使用该分类的书签数量
      const bookmarkCount = await this.getCategoryBookmarkCount(categoryId)
      
      if (bookmarkCount > 0) {
        // 将相关书签移动到默认分类
        await this.updateBookmarkCategories(category.name, '默认分类')
        console.log(`已将 ${bookmarkCount} 个书签移动到默认分类`)
      }

      // 删除分类
      await indexedDBService.deleteCategory(categoryId)
      console.log(`删除分类成功: ${category.name}`)
    } catch (error) {
      console.error('删除分类失败:', error)
      throw error
    }
  }

  /**
   * 验证分类名称唯一性
   * @param name 分类名称
   * @param excludeId 排除的分类ID（用于更新时的验证）
   * @returns Promise<boolean> 是否唯一
   */
  async validateCategoryName(name: string, excludeId?: string): Promise<boolean> {
    try {
      const categories = await indexedDBService.getCategories()
      const trimmedName = name.trim().toLowerCase()
      
      return !categories.some(category => 
        category.name.toLowerCase() === trimmedName && 
        category.id !== excludeId
      )
    } catch (error) {
      console.error('验证分类名称失败:', error)
      return false
    }
  }

  /**
   * 根据名称获取分类
   * @param name 分类名称
   * @returns Promise<Category | null> 分类对象或null
   */
  async getCategoryByName(name: string): Promise<Category | null> {
    try {
      const categories = await indexedDBService.getCategories()
      return categories.find(category => 
        category.name.toLowerCase() === name.toLowerCase()
      ) || null
    } catch (error) {
      console.error('根据名称获取分类失败:', error)
      return null
    }
  }

  /**
   * 获取所有分类
   * @returns Promise<Category[]> 分类列表
   */
  async getCategories(): Promise<Category[]> {
    try {
      return await indexedDBService.getCategories()
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return []
    }
  }

  /**
   * 获取分类的书签数量
   * @param categoryId 分类ID
   * @returns Promise<number> 书签数量
   */
  async getCategoryBookmarkCount(categoryId: string): Promise<number> {
    try {
      const category = await indexedDBService.getCategory(categoryId)
      if (!category) {
        return 0
      }

      const bookmarks = await indexedDBService.getBookmarks()
      return bookmarks.filter(bookmark => bookmark.category === category.name).length
    } catch (error) {
      console.error('获取分类书签数量失败:', error)
      return 0
    }
  }

  /**
   * 根据分类名称获取书签数量
   * @param categoryName 分类名称
   * @returns Promise<number> 书签数量
   */
  async getCategoryBookmarkCountByName(categoryName: string): Promise<number> {
    try {
      const bookmarks = await indexedDBService.getBookmarks()
      return bookmarks.filter(bookmark => bookmark.category === categoryName).length
    } catch (error) {
      console.error('获取分类书签数量失败:', error)
      return 0
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 生成分类ID
   * @returns string 唯一的分类ID
   */
  private generateCategoryId(): string {
    return `category_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成分类颜色
   * @param name 分类名称
   * @returns string 颜色值
   */
  private generateCategoryColor(name: string): string {
    // 基于分类名称生成一致的颜色
    const colors = [
      '#3B82F6', // 蓝色
      '#10B981', // 绿色
      '#F59E0B', // 黄色
      '#EF4444', // 红色
      '#8B5CF6', // 紫色
      '#06B6D4', // 青色
      '#F97316', // 橙色
      '#84CC16', // 青绿色
      '#EC4899', // 粉色
      '#6B7280'  // 灰色
    ]
    
    // 使用名称的哈希值选择颜色
    let hash = 0
    for (let i = 0; i < name.length; i++) {
      hash = ((hash << 5) - hash + name.charCodeAt(i)) & 0xffffffff
    }
    
    return colors[Math.abs(hash) % colors.length]
  }

  /**
   * 更新书签的分类名称
   * @param oldCategoryName 旧分类名称
   * @param newCategoryName 新分类名称
   * @returns Promise<void>
   */
  private async updateBookmarkCategories(oldCategoryName: string, newCategoryName: string): Promise<void> {
    try {
      const bookmarks = await indexedDBService.getBookmarks()
      const bookmarksToUpdate = bookmarks.filter(bookmark => bookmark.category === oldCategoryName)
      
      for (const bookmark of bookmarksToUpdate) {
        await indexedDBService.updateBookmark(bookmark.id, { 
          category: newCategoryName 
        })
      }
      
      console.log(`已更新 ${bookmarksToUpdate.length} 个书签的分类`)
    } catch (error) {
      console.error('更新书签分类失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export const categoryService = new CategoryService()