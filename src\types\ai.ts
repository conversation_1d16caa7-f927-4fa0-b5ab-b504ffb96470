// AI服务相关类型定义

/**
 * AI服务提供商类型
 */
export type AIProvider = 'openai' | 'azure-openai' | 'claude' | 'gemini' | 'local' | 'custom' | 'ollama' | 'lm-studio' | 'openrouter' | 'xinference' | 'deepseek' | 'zhipu' | 'qwen' | 'bailian' | 'hunyuan' | 'volcengine' | 'together' | 'grok'

/**
 * 扩展的AI提供商配置接口
 */
export interface AIProviderConfig {
  id: string
  name: string
  type: AIProvider
  baseUrl: string
  apiKey?: string
  headers?: Record<string, string>
  timeout?: number
  enabled: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * AI提供商信息接口
 */
export interface AIProviderInfo {
  id: string
  name: string
  description: string
  icon: string
  type: AIProvider
  defaultBaseUrl: string
  requiresApiKey: boolean
  supportedFeatures: string[]
  documentationUrl?: string
}

/**
 * 扩展的AI模型接口
 */
export interface AIModel {
  id: string
  name: string
  displayName: string
  description?: string
  size?: string
  parameters?: string
  tags?: string[]
  capabilities?: string[]
  providerId: string
  isRecommended?: boolean
  isPopular?: boolean
}

/**
 * AI连接结果接口
 */
export interface AIConnectionResult {
  providerId: string
  success: boolean
  responseTime?: number
  error?: string
  modelCount?: number
  testedAt: Date
}

/**
 * 模型筛选器接口
 */
export interface ModelFilter {
  size?: string[]
  type?: string[]
  capabilities?: string[]
  tags?: string[]
  isRecommended?: boolean
  isPopular?: boolean
}

/**
 * AI配置接口
 */
export interface AIConfig {
  // 基础配置
  provider: AIProvider
  baseUrl?: string
  apiKey?: string
  model: string
  
  // 功能开关
  autoTagging: boolean
  autoCategories: boolean
  autoDescription: boolean
  
  // 高级配置
  temperature?: number
  maxTokens?: number
  timeout?: number
  
  // 本地模型配置
  localModelPath?: string
  localModelType?: 'ollama' | 'llamacpp' | 'other'
  
  // 自定义配置
  customHeaders?: Record<string, string>
  customParams?: Record<string, any>
  
  // 状态信息
  isConnected: boolean
  lastTestDate?: Date
  availableModels?: string[]
  
  // 创建和更新时间
  createdAt: Date
  updatedAt: Date
}

/**
 * AI服务提供商预设配置
 */
export interface AIProviderPreset {
  id: AIProvider
  name: string
  description: string
  baseUrl: string
  defaultModel: string
  supportedModels: string[]
  requiresApiKey: boolean
  documentationUrl?: string
  icon?: string
}

/**
 * AI连接测试结果
 */
export interface AIConnectionTestResult {
  success: boolean
  responseTime?: number
  availableModels?: string[]
  error?: string
  errorCode?: string
  testedAt: Date
}

/**
 * AI标签生成请求
 */
export interface AITagGenerationRequest {
  content: string
  title?: string
  url?: string
  existingTags?: string[]
  maxTags?: number
  language?: string
}

/**
 * AI标签生成响应
 */
export interface AITagGenerationResponse {
  tags: string[]
  confidence: number
  reasoning?: string
  processingTime: number
}

/**
 * AI分类建议请求
 */
export interface AICategoryRequest {
  content: string
  title?: string
  url?: string
  existingCategories?: string[]
  maxSuggestions?: number
}

/**
 * AI分类建议响应
 */
export interface AICategoryResponse {
  category: string
  confidence: number
  alternatives?: Array<{
    category: string
    confidence: number
  }>
  reasoning?: string
}

/**
 * AI描述生成请求
 */
export interface AIDescriptionRequest {
  content: string
  title?: string
  url?: string
  maxLength?: number
  style?: 'brief' | 'detailed' | 'summary'
}

/**
 * AI描述生成响应
 */
export interface AIDescriptionResponse {
  description: string
  confidence: number
  wordCount: number
}

/**
 * AI服务统计信息
 */
export interface AIServiceStats {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  lastRequestDate?: Date
  errorRate: number
  
  // 按功能分类的统计
  tagGenerationCount: number
  categoryGenerationCount: number
  descriptionGenerationCount: number
  
  // 按时间段的统计
  dailyStats: Array<{
    date: string
    requests: number
    errors: number
  }>
}

/**
 * AI服务错误类型
 */
export interface AIServiceError {
  code: string
  message: string
  details?: any
  timestamp: Date
  provider: AIProvider
  operation: string
}

/**
 * AI提示词模板
 */
export interface AIPromptTemplate {
  id: string
  name: string
  description: string
  template: string
  variables: string[]
  category: 'tagging' | 'categorization' | 'description' | 'custom'
  language: string
  createdAt: Date
  updatedAt: Date
}

/**
 * AI批处理请求
 */
export interface AIBatchRequest {
  id: string
  items: Array<{
    id: string
    type: 'tags' | 'category' | 'description'
    data: AITagGenerationRequest | AICategoryRequest | AIDescriptionRequest
  }>
  priority: 'low' | 'normal' | 'high'
  createdAt: Date
}

/**
 * AI批处理响应
 */
export interface AIBatchResponse {
  id: string
  results: Array<{
    id: string
    success: boolean
    data?: AITagGenerationResponse | AICategoryResponse | AIDescriptionResponse
    error?: string
  }>
  completedAt: Date
  totalProcessingTime: number
}