// 错误反馈服务 - 提供用户友好的错误消息和解决建议

import { ValidationError } from '../types'
import { ErrorType, ErrorSeverity, ErrorInfo } from './ErrorRecoveryService'

/**
 * 错误码枚举
 */
export enum ErrorCode {
  // 文件相关错误
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  FILE_TYPE_INVALID = 'FILE_TYPE_INVALID',
  FILE_CORRUPTED = 'FILE_CORRUPTED',
  FILE_EMPTY = 'FILE_EMPTY',
  
  // 数据验证错误
  DATA_INVALID_FORMAT = 'DATA_INVALID_FORMAT',
  DATA_MISSING_REQUIRED = 'DATA_MISSING_REQUIRED',
  DATA_TYPE_MISMATCH = 'DATA_TYPE_MISMATCH',
  DATA_OUT_OF_RANGE = 'DATA_OUT_OF_RANGE',
  
  // 导入导出错误
  IMPORT_PARSE_FAILED = 'IMPORT_PARSE_FAILED',
  IMPORT_VALIDATION_FAILED = 'IMPORT_VALIDATION_FAILED',
  EXPORT_GENERATION_FAILED = 'EXPORT_GENERATION_FAILED',
  EXPORT_DOWNLOAD_FAILED = 'EXPORT_DOWNLOAD_FAILED',
  
  // 冲突处理错误
  CONFLICT_DETECTION_FAILED = 'CONFLICT_DETECTION_FAILED',
  CONFLICT_RESOLUTION_FAILED = 'CONFLICT_RESOLUTION_FAILED',
  CONFLICT_MERGE_FAILED = 'CONFLICT_MERGE_FAILED',
  
  // 存储错误
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',
  STORAGE_ACCESS_DENIED = 'STORAGE_ACCESS_DENIED',
  STORAGE_CORRUPTION = 'STORAGE_CORRUPTION',
  
  // 网络错误
  NETWORK_TIMEOUT = 'NETWORK_TIMEOUT',
  NETWORK_CONNECTION_FAILED = 'NETWORK_CONNECTION_FAILED',
  NETWORK_PERMISSION_DENIED = 'NETWORK_PERMISSION_DENIED',
  
  // 权限错误
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  PERMISSION_INSUFFICIENT = 'PERMISSION_INSUFFICIENT',
  
  // 系统错误
  SYSTEM_RESOURCE_EXHAUSTED = 'SYSTEM_RESOURCE_EXHAUSTED',
  SYSTEM_OPERATION_TIMEOUT = 'SYSTEM_OPERATION_TIMEOUT',
  SYSTEM_UNKNOWN_ERROR = 'SYSTEM_UNKNOWN_ERROR'
}

/**
 * 用户友好的错误消息
 */
export interface UserFriendlyError {
  code: ErrorCode
  title: string
  message: string
  description: string
  suggestions: string[]
  actions: ErrorAction[]
  severity: ErrorSeverity
  recoverable: boolean
  helpUrl?: string
}

/**
 * 错误处理动作
 */
export interface ErrorAction {
  label: string
  action: string
  primary?: boolean
  dangerous?: boolean
}

/**
 * 错误统计信息
 */
export interface ErrorStatistics {
  total: number
  byCode: Record<ErrorCode, number>
  bySeverity: Record<ErrorSeverity, number>
  byType: Record<ErrorType, number>
  recentCount: number
  topErrors: Array<{
    code: ErrorCode
    count: number
    lastOccurred: Date
  }>
}

/**
 * 错误反馈服务类
 * 提供用户友好的错误消息、解决建议和错误统计功能
 */
export class ErrorFeedbackService {
  private errorHistory: Array<{
    error: UserFriendlyError
    timestamp: Date
    context?: any
  }> = []

  private readonly maxHistorySize = 500

  // 错误消息模板
  private readonly errorMessages: Record<ErrorCode, Omit<UserFriendlyError, 'code'>> = {
    // 文件相关错误
    [ErrorCode.FILE_TOO_LARGE]: {
      title: '文件过大',
      message: '选择的文件超过了大小限制',
      description: '为了确保良好的性能和用户体验，我们限制了上传文件的大小。请选择更小的文件或将数据分批导入。',
      suggestions: [
        '选择小于50MB的文件',
        '将大文件分割成多个小文件',
        '删除不必要的数据后重新导出',
        '使用压缩工具减小文件大小'
      ],
      actions: [
        { label: '选择其他文件', action: 'select_file', primary: true },
        { label: '了解文件大小限制', action: 'show_help' }
      ],
      severity: ErrorSeverity.MEDIUM,
      recoverable: true,
      helpUrl: '/help/file-size-limits'
    },

    [ErrorCode.FILE_TYPE_INVALID]: {
      title: '文件类型不支持',
      message: '选择的文件类型不被支持',
      description: '目前只支持JSON、CSV、HTML等格式的文件。请确保您选择的是正确的文件格式。',
      suggestions: [
        '选择JSON格式的文件（推荐）',
        '选择CSV或HTML格式的文件',
        '检查文件扩展名是否正确',
        '重新导出数据为支持的格式'
      ],
      actions: [
        { label: '选择其他文件', action: 'select_file', primary: true },
        { label: '查看支持的格式', action: 'show_formats' }
      ],
      severity: ErrorSeverity.MEDIUM,
      recoverable: true,
      helpUrl: '/help/supported-formats'
    },

    [ErrorCode.FILE_CORRUPTED]: {
      title: '文件已损坏',
      message: '文件内容已损坏或不完整',
      description: '文件可能在传输或存储过程中损坏，无法正常读取。请尝试重新获取原始文件。',
      suggestions: [
        '重新下载或获取原始文件',
        '检查文件是否完整',
        '尝试使用其他工具打开文件验证',
        '联系数据提供方重新导出'
      ],
      actions: [
        { label: '选择其他文件', action: 'select_file', primary: true },
        { label: '报告问题', action: 'report_issue' }
      ],
      severity: ErrorSeverity.HIGH,
      recoverable: true
    },

    [ErrorCode.FILE_EMPTY]: {
      title: '文件为空',
      message: '选择的文件没有内容',
      description: '文件大小为0或不包含任何有效数据。请确保选择了正确的文件。',
      suggestions: [
        '检查是否选择了正确的文件',
        '确认文件包含有效数据',
        '重新导出数据',
        '选择其他文件'
      ],
      actions: [
        { label: '选择其他文件', action: 'select_file', primary: true }
      ],
      severity: ErrorSeverity.MEDIUM,
      recoverable: true
    },

    // 数据验证错误
    [ErrorCode.DATA_INVALID_FORMAT]: {
      title: '数据格式错误',
      message: '文件中的数据格式不正确',
      description: '文件内容不符合预期的数据结构。这可能是因为文件格式不正确或数据已损坏。',
      suggestions: [
        '检查文件是否为有效的JSON格式',
        '验证数据结构是否完整',
        '使用JSON验证工具检查语法',
        '重新导出数据'
      ],
      actions: [
        { label: '查看详细错误', action: 'show_details', primary: true },
        { label: '选择其他文件', action: 'select_file' },
        { label: '格式帮助', action: 'show_format_help' }
      ],
      severity: ErrorSeverity.HIGH,
      recoverable: true,
      helpUrl: '/help/data-format'
    },

    [ErrorCode.DATA_MISSING_REQUIRED]: {
      title: '缺少必需数据',
      message: '文件中缺少必需的字段或数据',
      description: '导入的数据缺少一些必需的字段，无法正常处理。请确保数据完整。',
      suggestions: [
        '检查数据是否包含所有必需字段',
        '补充缺失的数据',
        '使用完整的数据模板',
        '联系数据提供方'
      ],
      actions: [
        { label: '查看缺失字段', action: 'show_missing_fields', primary: true },
        { label: '下载数据模板', action: 'download_template' }
      ],
      severity: ErrorSeverity.HIGH,
      recoverable: true
    },

    [ErrorCode.DATA_TYPE_MISMATCH]: {
      title: '数据类型不匹配',
      message: '数据字段的类型不正确',
      description: '某些字段的数据类型与预期不符。例如，日期字段包含了非日期格式的数据。',
      suggestions: [
        '检查数据类型是否正确',
        '转换数据为正确的格式',
        '使用数据验证工具',
        '参考数据格式说明'
      ],
      actions: [
        { label: '查看类型错误', action: 'show_type_errors', primary: true },
        { label: '格式说明', action: 'show_format_guide' }
      ],
      severity: ErrorSeverity.MEDIUM,
      recoverable: true
    },

    // 导入导出错误
    [ErrorCode.IMPORT_PARSE_FAILED]: {
      title: '导入解析失败',
      message: '无法解析导入的文件',
      description: '系统无法正确解析您选择的文件。这可能是由于文件格式问题或内容损坏导致的。',
      suggestions: [
        '检查文件格式是否正确',
        '尝试重新保存文件',
        '使用UTF-8编码保存文件',
        '移除文件中的特殊字符'
      ],
      actions: [
        { label: '重新选择文件', action: 'select_file', primary: true },
        { label: '查看解析错误', action: 'show_parse_error' }
      ],
      severity: ErrorSeverity.HIGH,
      recoverable: true
    },

    [ErrorCode.CONFLICT_DETECTION_FAILED]: {
      title: '冲突检测失败',
      message: '无法检测数据冲突',
      description: '系统在检测导入数据与现有数据的冲突时遇到了问题。这可能会影响数据的准确性。',
      suggestions: [
        '重新尝试导入',
        '检查数据完整性',
        '分批导入数据',
        '联系技术支持'
      ],
      actions: [
        { label: '重新尝试', action: 'retry_import', primary: true },
        { label: '跳过冲突检测', action: 'skip_conflict_check' },
        { label: '报告问题', action: 'report_issue' }
      ],
      severity: ErrorSeverity.MEDIUM,
      recoverable: true
    },

    // 存储错误
    [ErrorCode.STORAGE_QUOTA_EXCEEDED]: {
      title: '存储空间不足',
      message: '没有足够的存储空间完成操作',
      description: '您的存储空间已满，无法保存更多数据。请清理一些空间后重试。',
      suggestions: [
        '删除不需要的收藏或数据',
        '清理浏览器缓存',
        '导出并备份重要数据',
        '联系管理员增加存储配额'
      ],
      actions: [
        { label: '清理存储空间', action: 'clean_storage', primary: true },
        { label: '查看存储使用情况', action: 'show_storage_usage' }
      ],
      severity: ErrorSeverity.HIGH,
      recoverable: true
    },

    // 网络错误
    [ErrorCode.NETWORK_TIMEOUT]: {
      title: '网络超时',
      message: '操作超时，请检查网络连接',
      description: '网络连接缓慢或不稳定，导致操作超时。请检查您的网络连接后重试。',
      suggestions: [
        '检查网络连接',
        '稍后重试',
        '尝试分批处理数据',
        '使用更稳定的网络环境'
      ],
      actions: [
        { label: '重新尝试', action: 'retry_operation', primary: true },
        { label: '检查网络状态', action: 'check_network' }
      ],
      severity: ErrorSeverity.MEDIUM,
      recoverable: true
    },

    // 权限错误
    [ErrorCode.PERMISSION_DENIED]: {
      title: '权限不足',
      message: '没有执行此操作的权限',
      description: '您当前的权限不足以执行此操作。请联系管理员或检查您的账户权限。',
      suggestions: [
        '联系管理员获取权限',
        '检查账户状态',
        '尝试重新登录',
        '使用有权限的账户'
      ],
      actions: [
        { label: '联系管理员', action: 'contact_admin', primary: true },
        { label: '重新登录', action: 'relogin' }
      ],
      severity: ErrorSeverity.HIGH,
      recoverable: false
    },

    // 系统错误
    [ErrorCode.SYSTEM_UNKNOWN_ERROR]: {
      title: '未知错误',
      message: '发生了未知错误',
      description: '系统遇到了一个未预期的错误。我们已经记录了这个问题，请稍后重试。',
      suggestions: [
        '稍后重试',
        '刷新页面',
        '重启应用',
        '联系技术支持'
      ],
      actions: [
        { label: '重新尝试', action: 'retry_operation', primary: true },
        { label: '刷新页面', action: 'refresh_page' },
        { label: '报告问题', action: 'report_issue' }
      ],
      severity: ErrorSeverity.MEDIUM,
      recoverable: true
    }
  }

  /**
   * 创建用户友好的错误消息
   * @param error 原始错误
   * @param context 错误上下文
   * @returns UserFriendlyError
   */
  createUserFriendlyError(error: Error | ErrorInfo, context?: any): UserFriendlyError {
    const errorCode = this.determineErrorCode(error)
    const template = this.errorMessages[errorCode]
    
    if (!template) {
      return this.createGenericError(error, context)
    }

    const userFriendlyError: UserFriendlyError = {
      code: errorCode,
      ...template
    }

    // 记录错误历史
    this.recordError(userFriendlyError, context)

    return userFriendlyError
  }

  /**
   * 批量创建错误消息
   * @param errors 错误列表
   * @param context 错误上下文
   * @returns UserFriendlyError[]
   */
  createBatchErrors(errors: (Error | ErrorInfo)[], context?: any): UserFriendlyError[] {
    return errors.map(error => this.createUserFriendlyError(error, context))
  }

  /**
   * 从验证错误创建用户友好错误
   * @param validationErrors 验证错误列表
   * @returns UserFriendlyError
   */
  createValidationError(validationErrors: ValidationError[]): UserFriendlyError {
    const errorCode = ErrorCode.DATA_INVALID_FORMAT
    const template = this.errorMessages[errorCode]
    
    const detailedMessage = validationErrors.length === 1 
      ? validationErrors[0].message
      : `发现 ${validationErrors.length} 个验证错误`

    const userFriendlyError: UserFriendlyError = {
      code: errorCode,
      ...template,
      message: detailedMessage,
      description: `${template.description}\n\n详细错误:\n${validationErrors.map(e => `• ${e.field}: ${e.message}`).join('\n')}`
    }

    this.recordError(userFriendlyError, { validationErrors })

    return userFriendlyError
  }

  /**
   * 获取错误统计信息
   * @returns ErrorStatistics
   */
  getErrorStatistics(): ErrorStatistics {
    const now = Date.now()
    const oneDayAgo = now - 24 * 60 * 60 * 1000

    const stats: ErrorStatistics = {
      total: this.errorHistory.length,
      byCode: {} as Record<ErrorCode, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
      byType: {} as Record<ErrorType, number>,
      recentCount: 0,
      topErrors: []
    }

    // 统计各种错误
    this.errorHistory.forEach(({ error, timestamp }) => {
      // 按错误码统计
      stats.byCode[error.code] = (stats.byCode[error.code] || 0) + 1
      
      // 按严重级别统计
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1
      
      // 统计最近24小时的错误
      if (timestamp.getTime() > oneDayAgo) {
        stats.recentCount++
      }
    })

    // 生成热门错误列表
    stats.topErrors = Object.entries(stats.byCode)
      .map(([code, count]) => ({
        code: code as ErrorCode,
        count,
        lastOccurred: this.getLastOccurrence(code as ErrorCode)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    return stats
  }

  /**
   * 获取错误历史
   * @param limit 限制数量
   * @param severity 过滤严重级别
   * @returns 错误历史记录
   */
  getErrorHistory(limit?: number, severity?: ErrorSeverity) {
    let history = [...this.errorHistory]
    
    if (severity) {
      history = history.filter(({ error }) => error.severity === severity)
    }
    
    history.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    
    if (limit) {
      history = history.slice(0, limit)
    }
    
    return history
  }

  /**
   * 清理错误历史
   * @param olderThan 清理指定时间之前的记录
   */
  clearErrorHistory(olderThan?: Date): void {
    if (olderThan) {
      this.errorHistory = this.errorHistory.filter(
        ({ timestamp }) => timestamp > olderThan
      )
    } else {
      this.errorHistory = []
    }
  }

  /**
   * 生成错误报告
   * @returns 错误报告字符串
   */
  generateErrorReport(): string {
    const stats = this.getErrorStatistics()
    const recentErrors = this.getErrorHistory(5)
    
    let report = `错误反馈报告 - ${new Date().toLocaleString()}\n`
    report += `==========================================\n\n`
    
    report += `总体统计:\n`
    report += `- 总错误数: ${stats.total}\n`
    report += `- 最近24小时: ${stats.recentCount}\n\n`
    
    report += `按严重级别分布:\n`
    Object.entries(stats.bySeverity).forEach(([severity, count]) => {
      report += `- ${severity}: ${count}\n`
    })
    
    report += `\n热门错误 (前5名):\n`
    stats.topErrors.slice(0, 5).forEach((error, index) => {
      report += `${index + 1}. ${error.code}: ${error.count}次\n`
      report += `   最后发生: ${error.lastOccurred.toLocaleString()}\n`
    })
    
    if (recentErrors.length > 0) {
      report += `\n最近错误:\n`
      recentErrors.forEach(({ error, timestamp }, index) => {
        report += `${index + 1}. [${error.severity}] ${error.title}\n`
        report += `   时间: ${timestamp.toLocaleString()}\n`
        report += `   消息: ${error.message}\n\n`
      })
    }
    
    return report
  }

  // ==================== 私有方法 ====================

  /**
   * 确定错误码
   */
  private determineErrorCode(error: Error | ErrorInfo): ErrorCode {
    const message = error.message.toLowerCase()
    
    // 文件相关错误
    if (message.includes('file') && message.includes('large')) {
      return ErrorCode.FILE_TOO_LARGE
    }
    if (message.includes('file') && message.includes('type')) {
      return ErrorCode.FILE_TYPE_INVALID
    }
    if (message.includes('corrupted') || message.includes('damaged')) {
      return ErrorCode.FILE_CORRUPTED
    }
    if (message.includes('empty')) {
      return ErrorCode.FILE_EMPTY
    }
    
    // 数据验证错误
    if (message.includes('format') || message.includes('invalid')) {
      return ErrorCode.DATA_INVALID_FORMAT
    }
    if (message.includes('required') || message.includes('missing')) {
      return ErrorCode.DATA_MISSING_REQUIRED
    }
    if (message.includes('type') && message.includes('mismatch')) {
      return ErrorCode.DATA_TYPE_MISMATCH
    }
    
    // 导入导出错误
    if (message.includes('parse') || message.includes('parsing')) {
      return ErrorCode.IMPORT_PARSE_FAILED
    }
    if (message.includes('conflict')) {
      return ErrorCode.CONFLICT_DETECTION_FAILED
    }
    
    // 存储错误
    if (message.includes('quota') || message.includes('storage')) {
      return ErrorCode.STORAGE_QUOTA_EXCEEDED
    }
    
    // 网络错误
    if (message.includes('timeout') || message.includes('time out')) {
      return ErrorCode.NETWORK_TIMEOUT
    }
    
    // 权限错误
    if (message.includes('permission') || message.includes('denied')) {
      return ErrorCode.PERMISSION_DENIED
    }
    
    return ErrorCode.SYSTEM_UNKNOWN_ERROR
  }

  /**
   * 创建通用错误
   */
  private createGenericError(error: Error | ErrorInfo, context?: any): UserFriendlyError {
    return {
      code: ErrorCode.SYSTEM_UNKNOWN_ERROR,
      title: '操作失败',
      message: error.message || '发生了未知错误',
      description: '系统遇到了一个问题，请稍后重试。如果问题持续存在，请联系技术支持。',
      suggestions: [
        '稍后重试',
        '刷新页面',
        '检查网络连接',
        '联系技术支持'
      ],
      actions: [
        { label: '重新尝试', action: 'retry_operation', primary: true },
        { label: '报告问题', action: 'report_issue' }
      ],
      severity: ErrorSeverity.MEDIUM,
      recoverable: true
    }
  }

  /**
   * 记录错误
   */
  private recordError(error: UserFriendlyError, context?: any): void {
    this.errorHistory.push({
      error,
      timestamp: new Date(),
      context
    })

    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize)
    }
  }

  /**
   * 获取错误最后发生时间
   */
  private getLastOccurrence(code: ErrorCode): Date {
    const lastError = this.errorHistory
      .filter(({ error }) => error.code === code)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0]
    
    return lastError ? lastError.timestamp : new Date(0)
  }
}

// 导出单例实例
export const errorFeedbackService = new ErrorFeedbackService()