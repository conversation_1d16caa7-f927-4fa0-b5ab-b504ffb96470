// HelpCenterTab shadcn重构测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import HelpCenterTab from '../src/options/components/HelpCenterTab'

// Mock HelpSearchBox组件
vi.mock('../src/options/components/HelpSearchBox', () => ({
  default: ({ onSearchResults, onQueryChange, className }: any) => (
    <div className={className} data-testid="help-search-box">
      <input
        placeholder="搜索帮助内容..."
        onChange={(e) => {
          onQueryChange(e.target.value)
          if (e.target.value) {
            onSearchResults([{
              section: {
                id: 'search-result-1',
                title: '搜索结果',
                content: '这是搜索结果内容',
                category: 'guide',
                keywords: ['搜索', '结果']
              },
              score: 1.0,
              matches: []
            }])
          } else {
            onSearchResults([])
          }
        }}
      />
    </div>
  )
}))

// Mock helpContent data
vi.mock('../src/options/data/helpContent', () => ({
  defaultHelpContent: {
    sections: [
      {
        id: 'getting-started',
        title: '快速开始',
        content: '# 欢迎使用\n这是一个帮助指南。\n\n## 基本功能\n- 功能1\n- 功能2',
        category: 'guide',
        keywords: ['开始', '指南']
      },
      {
        id: 'faq-1',
        title: '常见问题',
        content: '**问题1**\n答案1\n\n**问题2**\n答案2',
        category: 'faq',
        keywords: ['问题', 'FAQ']
      }
    ],
    categories: [
      {
        id: 'guide',
        name: '使用指南',
        description: '基础使用指南',
        icon: 'book',
        order: 1
      },
      {
        id: 'faq',
        name: '常见问题',
        description: '常见问题解答',
        icon: 'help-circle',
        order: 2
      }
    ]
  }
}))

describe('HelpCenterTab shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // 清除URL hash
    window.location.hash = ''
  })

  it('应该使用shadcn Card组件显示加载状态', () => {
    // Mock一个永不resolve的Promise来保持loading状态
    const { defaultHelpContent } = require('../src/options/data/helpContent')
    vi.doMock('../src/options/data/helpContent', () => ({
      defaultHelpContent: new Promise(() => {})
    }))
    
    render(<HelpCenterTab />)
    
    expect(screen.getByText('加载帮助内容中...')).toBeInTheDocument()
  })

  it('应该使用shadcn CardTitle渲染页面标题', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      expect(screen.getByText('帮助中心')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Button组件渲染操作按钮', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: '展开全部' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '折叠全部' })).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Button组件渲染分类筛选按钮', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /全部/ })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /使用指南/ })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /常见问题/ })).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Card组件渲染快速导航', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      expect(screen.getByText('快速导航')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '快速开始' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '常见问题' })).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Card组件渲染帮助内容', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      expect(screen.getByText('快速开始')).toBeInTheDocument()
      expect(screen.getByText('常见问题')).toBeInTheDocument()
      expect(screen.getByText('使用指南')).toBeInTheDocument()
    })
  })

  it('应该正确处理内容展开和折叠', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const sectionHeader = screen.getByText('快速开始').closest('[role="button"]') || 
                           screen.getByText('快速开始').closest('div')?.querySelector('[role="button"]')
      
      if (sectionHeader) {
        fireEvent.click(sectionHeader)
      }
    })
    
    await waitFor(() => {
      expect(screen.getByText('欢迎使用')).toBeInTheDocument()
      expect(screen.getByText('基本功能')).toBeInTheDocument()
    })
  })

  it('应该正确处理搜索功能', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('搜索帮助内容...')
      fireEvent.change(searchInput, { target: { value: '搜索关键词' } })
    })
    
    await waitFor(() => {
      expect(screen.getByText(/找到.*1.*个相关结果/)).toBeInTheDocument()
      expect(screen.getByText('搜索结果')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Badge组件显示搜索结果统计', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('搜索帮助内容...')
      fireEvent.change(searchInput, { target: { value: '搜索' } })
    })
    
    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument() // Badge显示结果数量
      expect(screen.getByText('"搜索"')).toBeInTheDocument() // Badge显示搜索关键词
    })
  })

  it('应该正确处理分类筛选', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const guideButton = screen.getByRole('button', { name: /使用指南/ })
      fireEvent.click(guideButton)
    })
    
    // 应该只显示使用指南分类的内容
    await waitFor(() => {
      expect(screen.getByText('快速开始')).toBeInTheDocument()
      expect(screen.queryByText('常见问题')).not.toBeInTheDocument()
    })
  })

  it('应该正确处理展开全部操作', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const expandAllButton = screen.getByRole('button', { name: '展开全部' })
      fireEvent.click(expandAllButton)
    })
    
    await waitFor(() => {
      expect(screen.getByText('欢迎使用')).toBeInTheDocument()
      expect(screen.getByText('问题1')).toBeInTheDocument()
    })
  })

  it('应该正确处理折叠全部操作', async () => {
    render(<HelpCenterTab />)
    
    // 先展开全部
    await waitFor(() => {
      const expandAllButton = screen.getByRole('button', { name: '展开全部' })
      fireEvent.click(expandAllButton)
    })
    
    await waitFor(() => {
      expect(screen.getByText('欢迎使用')).toBeInTheDocument()
    })
    
    // 再折叠全部
    const collapseAllButton = screen.getByRole('button', { name: '折叠全部' })
    fireEvent.click(collapseAllButton)
    
    await waitFor(() => {
      expect(screen.queryByText('欢迎使用')).not.toBeInTheDocument()
    })
  })

  it('应该使用shadcn Card组件显示空状态', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('搜索帮助内容...')
      fireEvent.change(searchInput, { target: { value: '不存在的内容' } })
    })
    
    // 清空搜索结果
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('搜索帮助内容...')
      fireEvent.change(searchInput, { target: { value: '不存在' } })
    })
    
    // Mock搜索返回空结果
    const searchBox = screen.getByTestId('help-search-box')
    const input = searchBox.querySelector('input')
    if (input) {
      // 模拟搜索无结果
      Object.defineProperty(input, 'value', { value: '不存在', writable: true })
      fireEvent.change(input, { target: { value: '不存在' } })
    }
  })

  it('应该使用shadcn Alert组件显示联系信息', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      expect(screen.getByText('需要更多帮助？')).toBeInTheDocument()
      expect(screen.getByText('如果您没有找到需要的信息，欢迎联系我们的支持团队。')).toBeInTheDocument()
      expect(screen.getByRole('link', { name: '发送邮件' })).toBeInTheDocument()
      expect(screen.getByRole('link', { name: '访问官网' })).toBeInTheDocument()
    })
  })

  it('应该正确处理快速导航跳转', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const quickNavButton = screen.getByRole('button', { name: '快速开始' })
      fireEvent.click(quickNavButton)
    })
    
    // 应该展开对应的内容
    await waitFor(() => {
      expect(screen.getByText('欢迎使用')).toBeInTheDocument()
    })
  })

  it('应该正确处理URL锚点', async () => {
    // 设置URL hash
    window.location.hash = '#getting-started'
    
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      expect(screen.getByText('欢迎使用')).toBeInTheDocument()
    })
  })

  it('应该正确渲染内容格式', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const expandAllButton = screen.getByRole('button', { name: '展开全部' })
      fireEvent.click(expandAllButton)
    })
    
    await waitFor(() => {
      // 检查标题渲染
      expect(screen.getByText('欢迎使用')).toBeInTheDocument()
      expect(screen.getByText('基本功能')).toBeInTheDocument()
      
      // 检查列表渲染
      expect(screen.getByText('功能1')).toBeInTheDocument()
      expect(screen.getByText('功能2')).toBeInTheDocument()
      
      // 检查粗体文本渲染
      expect(screen.getByText('问题1')).toBeInTheDocument()
      expect(screen.getByText('问题2')).toBeInTheDocument()
    })
  })

  it('应该正确处理外部链接', async () => {
    render(<HelpCenterTab />)
    
    await waitFor(() => {
      const emailLink = screen.getByRole('link', { name: '发送邮件' })
      expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>')
      
      const websiteLink = screen.getByRole('link', { name: '访问官网' })
      expect(websiteLink).toHaveAttribute('href', 'https://universebag.com')
      expect(websiteLink).toHaveAttribute('target', '_blank')
      expect(websiteLink).toHaveAttribute('rel', 'noopener noreferrer')
    })
  })

  it('应该正确处理清除搜索操作', async () => {
    render(<HelpCenterTab />)
    
    // 先进行搜索
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('搜索帮助内容...')
      fireEvent.change(searchInput, { target: { value: '搜索' } })
    })
    
    await waitFor(() => {
      expect(screen.getByText(/找到.*个相关结果/)).toBeInTheDocument()
    })
    
    // 清除搜索
    const clearButton = screen.getByRole('button', { name: '清除搜索' })
    fireEvent.click(clearButton)
    
    await waitFor(() => {
      expect(screen.queryByText(/找到.*个相关结果/)).not.toBeInTheDocument()
      expect(screen.getByRole('button', { name: /全部/ })).toBeInTheDocument()
    })
  })
})