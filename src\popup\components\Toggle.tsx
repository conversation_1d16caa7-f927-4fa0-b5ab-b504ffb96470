import React from 'react'

interface ToggleProps {
  checked: boolean
  onChange: (checked: boolean) => void
  disabled?: boolean
  size?: 'sm' | 'md'
}

const Toggle: React.FC<ToggleProps> = ({ 
  checked, 
  onChange, 
  disabled = false,
  size = 'md'
}) => {
  const sizeClasses = {
    sm: {
      container: 'w-8 h-4',
      thumb: 'w-3 h-3',
      translate: 'translate-x-4'
    },
    md: {
      container: 'w-10 h-5',
      thumb: 'w-4 h-4',
      translate: 'translate-x-5'
    }
  }

  const classes = sizeClasses[size]

  return (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      disabled={disabled}
      onClick={() => !disabled && onChange(!checked)}
      className={`
        relative inline-flex items-center ${classes.container} rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
        ${checked 
          ? 'bg-primary' 
          : 'bg-muted'
        }
        ${disabled 
          ? 'opacity-50 cursor-not-allowed' 
          : 'cursor-pointer'
        }
      `}
    >
      <span
        className={`
          ${classes.thumb} inline-block rounded-full bg-background shadow-sm transform transition-transform duration-200 ease-in-out
          ${checked ? classes.translate : 'translate-x-0.5'}
        `}
      />
    </button>
  )
}

export default Toggle