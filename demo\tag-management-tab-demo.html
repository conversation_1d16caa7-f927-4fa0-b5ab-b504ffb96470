<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签管理主页面演示</title>
    <script type="module" src="/src/examples/TagManagementTabDemo.tsx"></script>
</head>
<body>
    <div id="root"></div>
    <script type="module">
        import React from 'react'
        import { createRoot } from 'react-dom/client'
        import TagManagementTabDemo from '/src/examples/TagManagementTabDemo.tsx'
        
        const root = createRoot(document.getElementById('root'))
        root.render(React.createElement(TagManagementTabDemo))
    </script>
</body>
</html>