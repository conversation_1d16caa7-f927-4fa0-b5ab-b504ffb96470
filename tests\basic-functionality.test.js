// 基础功能测试

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

describe('Universe Bag 基础功能测试', () => {
  
  test('项目结构完整性检查', () => {
    const requiredFiles = [
      'package.json',
      'manifest.json',
      'vite.config.ts',
      'tsconfig.json',
      'tailwind.config.js',
      'src/background/index.ts',
      'src/content/index.ts',
      'src/popup/PopupApp.tsx',
      'src/options/OptionsApp.tsx',
      'src/types/index.ts'
    ]

    requiredFiles.forEach(file => {
      expect(fs.existsSync(file)).toBe(true)
    })
  })

  test('TypeScript编译检查', () => {
    try {
      execSync('npm run type-check', { stdio: 'pipe' })
    } catch (error) {
      fail(`TypeScript编译失败: ${error.stdout}`)
    }
  })

  test('项目构建检查', () => {
    try {
      // 清理之前的构建
      if (fs.existsSync('dist')) {
        fs.rmSync('dist', { recursive: true, force: true })
      }
      
      // 执行构建
      execSync('npm run build', { stdio: 'pipe' })
      
      // 检查构建产物
      const buildFiles = [
        'dist/manifest.json',
        'dist/src/background/index.js',
        'dist/src/content/index.js',
        'dist/src/popup/index.html',
        'dist/src/options/index.html'
      ]

      buildFiles.forEach(file => {
        expect(fs.existsSync(file)).toBe(true)
      })
    } catch (error) {
      fail(`项目构建失败: ${error.stdout}`)
    }
  })

  test('Manifest文件验证', () => {
    const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'))
    
    expect(manifest.manifest_version).toBe(3)
    expect(manifest.name).toBe('Universe Bag（乾坤袋）')
    expect(manifest.version).toBe('1.0.0')
    expect(manifest.permissions).toContain('storage')
    expect(manifest.permissions).toContain('contextMenus')
    expect(manifest.background.service_worker).toBe('src/background/index.js')
  })

  test('Package.json配置验证', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    
    expect(pkg.name).toBe('universe-bag')
    expect(pkg.version).toBe('1.0.0')
    expect(pkg.dependencies).toHaveProperty('react')
    expect(pkg.dependencies).toHaveProperty('react-dom')
    expect(pkg.devDependencies).toHaveProperty('typescript')
    expect(pkg.devDependencies).toHaveProperty('vite')
  })

  test('图标文件存在性检查', () => {
    const iconSizes = ['16', '32', '48', '128']
    
    iconSizes.forEach(size => {
      const iconPath = `public/icons/icon-${size}.png`
      expect(fs.existsSync(iconPath)).toBe(true)
    })
  })

  test('样式文件完整性检查', () => {
    const styleFiles = [
      'src/styles/globals.css',
      'src/content/style.css',
      'tailwind.config.js',
      'postcss.config.js'
    ]

    styleFiles.forEach(file => {
      expect(fs.existsSync(file)).toBe(true)
    })
  })

  test('核心类型定义检查', () => {
    const typesContent = fs.readFileSync('src/types/index.ts', 'utf8')
    
    // 检查核心接口是否定义
    expect(typesContent).toContain('interface Bookmark')
    expect(typesContent).toContain('interface Category')
    expect(typesContent).toContain('interface Tag')
    expect(typesContent).toContain('interface AIConfig')
    expect(typesContent).toContain('interface SyncConfig')
  })

  test('构建脚本功能检查', () => {
    const scriptFiles = [
      'scripts/post-build.js',
      'scripts/test-build.js',
      'scripts/test-build.test.js'
    ]

    scriptFiles.forEach(file => {
      expect(fs.existsSync(file)).toBe(true)
    })

    // 测试构建后处理脚本
    try {
      execSync('node scripts/test-build.test.js', { stdio: 'pipe' })
    } catch (error) {
      fail(`构建脚本测试失败: ${error.stdout}`)
    }
  })

})

console.log('🧪 运行基础功能测试...')