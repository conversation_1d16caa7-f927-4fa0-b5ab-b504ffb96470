// 文本处理工具类 - 提供智能文本截断、基于容器宽度的截断、多行文本处理等功能

import type { 
  TruncateOptions, 
  TruncateResult, 
  TextMeasurement, 
  LineClampConfig,
  UrlBeautifyOptions 
} from '../types/layout'

/**
 * 文本处理工具类
 * 提供各种文本处理和截断功能
 */
export class TextUtils {
  // 默认截断选项
  private static readonly DEFAULT_TRUNCATE_OPTIONS: TruncateOptions = {
    position: 'end',
    ellipsis: '...',
    wordBoundary: false,
    preserveHtml: false
  }

  // 默认URL美化选项
  private static readonly DEFAULT_URL_OPTIONS: UrlBeautifyOptions = {
    removeProtocol: true,
    removeWww: true,
    removeTrailingSlash: true,
    showPath: true
  }

  /**
   * 智能文本截断
   * @param text 要截断的文本
   * @param maxLength 最大长度
   * @param options 截断选项
   * @returns 截断结果
   */
  static smartTruncate(
    text: string, 
    maxLength: number, 
    options: Partial<TruncateOptions> = {}
  ): TruncateResult {
    const opts = { ...this.DEFAULT_TRUNCATE_OPTIONS, ...options }
    
    if (!text || text.length <= maxLength) {
      return {
        text,
        isTruncated: false,
        originalLength: text?.length || 0,
        truncatedLength: text?.length || 0
      }
    }

    let truncatedText: string

    switch (opts.position) {
      case 'start':
        truncatedText = this.truncateStart(text, maxLength, opts)
        break
      case 'middle':
        truncatedText = this.truncateMiddle(text, maxLength, opts)
        break
      case 'end':
      default:
        truncatedText = this.truncateEnd(text, maxLength, opts)
        break
    }

    return {
      text: truncatedText,
      isTruncated: true,
      originalLength: text.length,
      truncatedLength: truncatedText.length
    }
  }

  /**
   * 基于容器宽度的文本截断
   * @param text 要截断的文本
   * @param containerWidth 容器宽度（像素）
   * @param fontSize 字体大小（像素）
   * @param fontFamily 字体族
   * @returns 截断后的文本
   */
  static truncateByWidth(
    text: string, 
    containerWidth: number, 
    fontSize: number = 14,
    fontFamily: string = 'system-ui, -apple-system, sans-serif'
  ): string {
    if (!text || containerWidth <= 0) return text

    // 创建临时元素测量文本宽度
    const measurement = this.measureText(text, fontSize, fontFamily)
    
    if (measurement.width <= containerWidth) {
      return text
    }

    // 二分查找最佳截断长度
    let left = 0
    let right = text.length
    let bestLength = 0

    while (left <= right) {
      const mid = Math.floor((left + right) / 2)
      const testText = text.substring(0, mid) + '...'
      const testWidth = this.measureText(testText, fontSize, fontFamily).width

      if (testWidth <= containerWidth) {
        bestLength = mid
        left = mid + 1
      } else {
        right = mid - 1
      }
    }

    return bestLength > 0 ? text.substring(0, bestLength) + '...' : '...'
  }

  /**
   * 多行文本处理（行数限制）
   * @param text 要处理的文本
   * @param config 行限制配置
   * @returns 处理后的文本
   */
  static clampLines(
    text: string, 
    config: LineClampConfig
  ): string {
    if (!text) return text

    const lines = text.split('\n')
    
    if (lines.length <= config.maxLines) {
      // 即使行数不超过限制，也要检查最后一行是否过长
      const lastLine = lines[lines.length - 1]
      if (lastLine && lastLine.length > 50) {
        const modifiedLines = [...lines]
        modifiedLines[modifiedLines.length - 1] = lastLine.substring(0, 47) + '...'
        return modifiedLines.join('\n')
      }
      return text
    }

    const clampedLines = lines.slice(0, config.maxLines)
    const lastLine = clampedLines[clampedLines.length - 1]
    
    // 如果最后一行太长，也需要截断
    if (lastLine && lastLine.length > 50) { // 假设每行最多50个字符
      clampedLines[clampedLines.length - 1] = lastLine.substring(0, 47) + '...'
    }

    return clampedLines.join('\n')
  }

  /**
   * URL美化显示
   * @param url 要美化的URL
   * @param options 美化选项
   * @returns 美化后的URL
   */
  static beautifyUrl(url: string, options: Partial<UrlBeautifyOptions> = {}): string {
    if (!url) return url

    const opts = { ...this.DEFAULT_URL_OPTIONS, ...options }
    let beautified = url

    try {
      const urlObj = new URL(url)
      let result = ''

      // 添加协议（如果不移除的话）
      if (!opts.removeProtocol) {
        result += urlObj.protocol + '//'
      }

      // 添加主机名
      let hostname = urlObj.hostname
      if (opts.removeWww && hostname.startsWith('www.')) {
        hostname = hostname.substring(4)
      }
      result += hostname

      // 添加端口（如果不是默认端口）
      if (urlObj.port && 
          !((urlObj.protocol === 'http:' && urlObj.port === '80') ||
            (urlObj.protocol === 'https:' && urlObj.port === '443'))) {
        result += ':' + urlObj.port
      }

      // 添加路径
      if (opts.showPath && urlObj.pathname !== '/') {
        let pathname = urlObj.pathname
        if (opts.removeTrailingSlash && pathname.endsWith('/')) {
          pathname = pathname.slice(0, -1)
        }
        result += pathname
      }

      // 添加查询参数（简化显示）
      if (urlObj.search) {
        const params = new URLSearchParams(urlObj.search)
        const paramCount = Array.from(params).length
        if (paramCount > 0) {
          result += `?...${paramCount}个参数`
        }
      }

      beautified = result

    } catch (error) {
      // 如果URL解析失败，返回原始URL
      console.warn('URL解析失败:', error)
      return url
    }

    // 应用长度限制
    if (opts.maxLength && beautified.length > opts.maxLength) {
      return this.smartTruncate(beautified, opts.maxLength).text
    }

    return beautified
  }

  /**
   * 测量文本尺寸
   * @param text 要测量的文本
   * @param fontSize 字体大小
   * @param fontFamily 字体族
   * @returns 文本测量结果
   */
  static measureText(
    text: string, 
    fontSize: number = 14, 
    fontFamily: string = 'system-ui, -apple-system, sans-serif'
  ): TextMeasurement {
    // 创建临时canvas元素进行测量
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    
    if (!context) {
      // 降级方案：估算文本宽度
      return {
        width: text.length * fontSize * 0.6, // 粗略估算
        height: fontSize * 1.2,
        lines: 1
      }
    }

    context.font = `${fontSize}px ${fontFamily}`
    const metrics = context.measureText(text)
    
    return {
      width: metrics.width,
      height: fontSize * 1.2, // 行高通常是字体大小的1.2倍
      lines: Math.ceil(metrics.width / 300) // 假设每行300px
    }
  }

  /**
   * 检查文本是否需要截断
   * @param text 文本
   * @param maxLength 最大长度
   * @returns 是否需要截断
   */
  static needsTruncation(text: string, maxLength: number): boolean {
    return Boolean(text && text.length > maxLength)
  }

  /**
   * 获取文本摘要
   * @param text 原始文本
   * @param maxLength 最大长度
   * @returns 文本摘要
   */
  static getTextSummary(text: string, maxLength: number = 100): string {
    if (!text) return ''
    
    // 移除多余的空白字符
    const cleaned = text.replace(/\s+/g, ' ').trim()
    
    if (cleaned.length <= maxLength) {
      return cleaned
    }

    // 尝试在句号处截断
    const sentences = cleaned.split(/[.!?。！？]+/).filter(s => s.trim())
    let summary = ''
    
    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i].trim()
      if (!sentence) continue
      
      const testSummary = summary ? summary + sentence + '.' : sentence + '.'
      if (testSummary.length <= maxLength) {
        summary = testSummary
      } else {
        break
      }
    }

    // 如果没有找到合适的句子边界，使用智能截断
    if (!summary) {
      return this.smartTruncate(cleaned, maxLength, { wordBoundary: true }).text
    }

    return summary
  }

  // 私有方法：从开头截断
  private static truncateStart(text: string, maxLength: number, options: TruncateOptions): string {
    const ellipsisLength = options.ellipsis.length
    const availableLength = maxLength - ellipsisLength
    
    if (availableLength <= 0) return options.ellipsis
    
    let truncated = text.slice(-availableLength)
    
    if (options.wordBoundary) {
      const firstSpaceIndex = truncated.indexOf(' ')
      if (firstSpaceIndex > 0) {
        truncated = truncated.slice(firstSpaceIndex + 1)
      }
    }
    
    return options.ellipsis + truncated
  }

  // 私有方法：从中间截断
  private static truncateMiddle(text: string, maxLength: number, options: TruncateOptions): string {
    const ellipsisLength = options.ellipsis.length
    const availableLength = maxLength - ellipsisLength
    
    if (availableLength <= 0) return options.ellipsis
    
    const halfLength = Math.floor(availableLength / 2)
    const startPart = text.slice(0, halfLength)
    const endPart = text.slice(-halfLength)
    
    return startPart + options.ellipsis + endPart
  }

  // 私有方法：从末尾截断
  private static truncateEnd(text: string, maxLength: number, options: TruncateOptions): string {
    const ellipsisLength = options.ellipsis.length
    const availableLength = maxLength - ellipsisLength
    
    if (availableLength <= 0) return options.ellipsis
    
    let truncated = text.slice(0, availableLength)
    
    if (options.wordBoundary) {
      const lastSpaceIndex = truncated.lastIndexOf(' ')
      if (lastSpaceIndex > 0) {
        truncated = truncated.slice(0, lastSpaceIndex)
      }
    }
    
    return truncated + options.ellipsis
  }
}

// 导出默认实例
export default TextUtils