// Content Script 主入口文件

import { pageInfoService } from '../services/pageInfoService'

console.log('Universe Bag Content Script 已加载')

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  console.log('Content Script 收到消息:', message)
  
  // 异步处理消息
  handleContentMessage(message)
    .then(response => sendResponse(response))
    .catch(error => {
      console.error('Content Script 处理消息失败:', error)
      sendResponse({ error: error.message })
    })
  
  // 返回true表示异步响应
  return true
})

// 处理消息的主函数
async function handleContentMessage(message: any) {
  switch (message.type) {
    case 'GET_PAGE_INFO':
      return handleGetPageInfo(message.data)
    
    case 'GET_LINK_INFO':
      return handleGetLinkInfo(message.data)
    
    case 'GET_SELECTED_TEXT':
      return handleGetSelectedText()
    
    case 'PING':
      return { status: 'pong', url: window.location.href }
    
    default:
      throw new Error(`未知的消息类型: ${message.type}`)
  }
}

// 处理获取页面信息请求
async function handleGetPageInfo(data: any) {
  try {
    // 使用页面信息提取服务
    const pageInfo = await pageInfoService.extractPageInfo({
      selectedText: data?.selectedText,
      includeMetadata: data?.includeMetadata || true
    })
    
    console.log('页面信息提取成功:', pageInfo)
    return pageInfo
  } catch (error) {
    console.error('提取页面信息失败:', error)
    throw error
  }
}

// 处理获取链接信息请求
async function handleGetLinkInfo(data: any) {
  try {
    const linkUrl = data.url
    
    // 使用页面信息提取服务获取链接信息
    const linkInfo = await pageInfoService.extractLinkInfo(linkUrl)
    
    console.log('链接信息提取成功:', linkInfo)
    return linkInfo
  } catch (error) {
    console.error('提取链接信息失败:', error)
    throw error
  }
}

// 处理获取选中文字请求
async function handleGetSelectedText() {
  try {
    const selection = window.getSelection()
    const selectedText = selection ? selection.toString().trim() : ''
    
    console.log('获取选中文字成功:', selectedText)
    return { selectedText }
  } catch (error) {
    console.error('获取选中文字失败:', error)
    throw error
  }
}

// 页面加载完成后的初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize)
} else {
  initialize()
}

function initialize() {
  console.log('Content Script 初始化完成')
  
  // 向background script发送页面加载完成的消息
  chrome.runtime.sendMessage({
    type: 'PAGE_LOADED',
    data: {
      url: window.location.href,
      title: document.title
    }
  }).catch(error => {
    console.error('发送页面加载消息失败:', error)
  })
}