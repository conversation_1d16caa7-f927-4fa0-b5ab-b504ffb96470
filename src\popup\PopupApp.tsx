import React, { useState, useEffect } from 'react'
import {
  Bookmark,
  Settings,
  Star,
  Plus,
  Menu,
  Check,
  FileText,
  Zap,
  Shield,
  Eye,
  RefreshCw,
  ExternalLink,
  Sparkles
} from 'lucide-react'
import { <PERSON><PERSON> } from '../components/ui/button'
import { Card, CardContent, CardHeader } from '../components/ui/card'
import { Switch } from '../components/ui/switch'
import { Separator } from '../components/ui/separator'
import DetailedBookmarkForm from './components/DetailedBookmarkForm'
import QuickBookmarkForm from './components/QuickBookmarkForm'

interface PopupAppProps {}

interface AppSettings {
  autoTagging: boolean
  duplicateDetection: boolean
  floatingWidget: boolean
  aiAssistant: boolean
  smartRecognition: boolean
}

const PopupApp: React.FC<PopupAppProps> = () => {
  const [currentTab, setCurrentTab] = useState<chrome.tabs.Tab | null>(null)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [bookmarkId, setBookmarkId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedText, setSelectedText] = useState<string>('')
  const [settings, setSettings] = useState<AppSettings>({
    autoTagging: true,
    duplicateDetection: true,
    floatingWidget: false,
    aiAssistant: true,
    smartRecognition: true
  })
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle')
  const [showDetailedForm, setShowDetailedForm] = useState(false)
  const [showQuickForm, setShowQuickForm] = useState(false)
  const [editingBookmark, setEditingBookmark] = useState<any>(null)

  // 组件挂载时获取当前标签页信息和设置
  useEffect(() => {
    getCurrentTab()
    loadSettings()
    
    // 监听收藏状态变化
    const handleBookmarkStatusChange = (message: any) => {
      if (message.type === 'BOOKMARK_STATUS_CHANGED' && currentTab?.url) {
        if (message.data.url === currentTab.url) {
          setIsBookmarked(message.data.isBookmarked)
          setBookmarkId(message.data.bookmarkId || null)
          console.log('收藏状态已更新:', message.data)
        }
      }
    }

    // 添加消息监听器
    chrome.runtime.onMessage.addListener(handleBookmarkStatusChange)
    
    // 清理函数
    return () => {
      chrome.runtime.onMessage.removeListener(handleBookmarkStatusChange)
    }
  }, [currentTab?.url])

  // 获取当前活动标签页
  const getCurrentTab = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      setCurrentTab(tab)
      
      // 检查当前页面是否已被收藏
      await checkBookmarkStatus(tab.url || '')
      
      // 获取选中的文字
      await getSelectedText(tab.id!)
      
      setLoading(false)
    } catch (error) {
      console.error('获取当前标签页失败:', error)
      setLoading(false)
    }
  }

  // 获取页面选中的文字
  const getSelectedText = async (tabId: number) => {
    try {
      const response = await chrome.tabs.sendMessage(tabId, {
        type: 'GET_SELECTED_TEXT'
      })
      if (response?.selectedText) {
        setSelectedText(response.selectedText)
      }
    } catch (error) {
      // 忽略错误，可能是content script未加载
      console.log('获取选中文字失败:', error)
    }
  }

  // 加载设置
  const loadSettings = async () => {
    try {
      const result = await chrome.storage.sync.get(['appSettings'])
      if (result.appSettings) {
        setSettings({ ...settings, ...result.appSettings })
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }

  // 保存设置
  const saveSettings = async (newSettings: Partial<AppSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings }
      setSettings(updatedSettings)
      await chrome.storage.sync.set({ appSettings: updatedSettings })
      
      // 通知background script设置已更新
      chrome.runtime.sendMessage({
        type: 'SETTINGS_UPDATED',
        data: updatedSettings
      })
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  }

  // 检查收藏状态
  const checkBookmarkStatus = async (url: string) => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'CHECK_BOOKMARK_STATUS',
        data: { url }
      })
      
      if (response?.success && response.data) {
        setIsBookmarked(response.data.isBookmarked)
        setBookmarkId(response.data.bookmarkId || null)
        console.log('收藏状态检查结果:', response.data)
      } else {
        console.warn('检查收藏状态响应异常:', response)
        setIsBookmarked(false)
        setBookmarkId(null)
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error)
      setIsBookmarked(false)
      setBookmarkId(null)
    }
  }

  // 显示快速收藏表单
  const handleShowQuickBookmark = () => {
    setShowQuickForm(true)
  }

  // 快速收藏保存
  const handleQuickBookmarkSave = async (data: any) => {
    if (!currentTab) return

    try {
      setLoading(true)

      // 向background script发送收藏请求
      const response = await chrome.runtime.sendMessage({
        type: 'SAVE_DETAILED_BOOKMARK',
        data: {
          type: 'url',
          title: data.title,
          url: data.url,
          tags: data.tags,
          category: '默认分类',
          favicon: data.favIconUrl,
          metadata: {
            pageTitle: data.title,
            siteName: data.url ? new URL(data.url).hostname : undefined,
            publishDate: new Date(),
            aiGenerated: false
          }
        }
      })

      if (response?.success) {
        setIsBookmarked(true)
        setBookmarkId(response.data?.bookmarkId || null)
        setShowQuickForm(false)
        console.log('快速收藏成功:', response.data)

        // 如果智能识别开关开启，自动触发智能识别
        if (settings.smartRecognition && response.data?.bookmarkId) {
          try {
            console.log('自动触发智能识别...')
            await chrome.runtime.sendMessage({
              type: 'AI_RECOMMEND_ALL',
              data: {
                title: data.title,
                url: data.url,
                maxRecommendations: 6
              }
            })
          } catch (aiError) {
            console.error('自动智能识别失败:', aiError)
            // 不影响收藏成功的状态
          }
        }
      } else {
        console.error('快速收藏失败:', response?.error)
      }

    } catch (error) {
      console.error('收藏页面失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 取消快速收藏
  const handleQuickBookmarkCancel = () => {
    setShowQuickForm(false)
  }

  // 收藏选中文字
  const handleBookmarkSelectedText = async () => {
    if (!currentTab || !selectedText) return

    try {
      setLoading(true)
      
      const response = await chrome.runtime.sendMessage({
        type: 'BOOKMARK_SELECTED_TEXT',
        data: {
          url: currentTab.url,
          title: currentTab.title,
          selectedText: selectedText,
          timestamp: new Date().toISOString()
        }
      })
      
      if (response?.success) {
        // 显示成功提示
        console.log('文字收藏成功')
      }
      
    } catch (error) {
      console.error('收藏文字失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 打开详细收藏界面
  const handleDetailedBookmark = () => {
    setShowDetailedForm(true)
  }

  // 保存详细收藏
  const handleSaveDetailedBookmark = async (formData: any) => {
    try {
      if (editingBookmark) {
        // 编辑模式：更新现有收藏
        const response = await chrome.runtime.sendMessage({
          type: 'UPDATE_BOOKMARK',
          data: {
            id: editingBookmark.id,
            updates: {
              title: formData.title,
              description: formData.description,
              tags: formData.tags,
              category: formData.category,
              metadata: {
                ...editingBookmark.metadata,
                ...formData.metadata
              }
            }
          }
        })

        if (response?.success) {
          setShowDetailedForm(false)
          setEditingBookmark(null)
          // 重新检查收藏状态以更新界面
          if (currentTab?.url) {
            await checkBookmarkStatus(currentTab.url)
          }
        }
      } else {
        // 新建模式：创建新收藏
        const response = await chrome.runtime.sendMessage({
          type: 'SAVE_DETAILED_BOOKMARK',
          data: {
            type: formData.notes ? 'text' : 'url',
            title: formData.title,
            url: formData.url,
            content: formData.notes,
            description: formData.description,
            tags: formData.tags,
            category: formData.category,
            favicon: currentTab?.favIconUrl,
            metadata: {
              pageTitle: formData.title,
              siteName: formData.url ? new URL(formData.url).hostname : undefined,
              publishDate: new Date(),
              aiGenerated: false
            }
          }
        })

        if (response?.success) {
          setIsBookmarked(true)
          setBookmarkId(response.data?.bookmarkId || null)
          setShowDetailedForm(false)
          console.log('详细收藏保存成功:', response.data)
          
          // 图标状态更新已在background script中处理
        } else {
          console.error('详细收藏保存失败:', response?.error)
          throw new Error(response?.error || '保存失败')
        }
      }
    } catch (error) {
      console.error('保存详细收藏失败:', error)
      throw error
    }
  }

  // 取消详细收藏
  const handleCancelDetailedBookmark = () => {
    setShowDetailedForm(false)
    setEditingBookmark(null)
  }

  // 编辑现有收藏
  const handleEditBookmark = async () => {
    if (!bookmarkId) return
    
    try {
      setLoading(true)
      
      // 获取现有收藏数据
      const response = await chrome.runtime.sendMessage({
        type: 'GET_BOOKMARK',
        data: { id: bookmarkId }
      })
      
      if (response?.success && response.data) {
        // 保存编辑的收藏数据并显示编辑表单
        setEditingBookmark(response.data)
        setShowDetailedForm(true)
      } else {
        console.error('获取收藏数据失败:', response?.error)
      }
    } catch (error) {
      console.error('获取收藏数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 打开管理页面
  const handleOpenManagement = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('src/options/index.html') })
    window.close()
  }

  // 打开管理页面并定位到当前收藏项
  const handleOpenManagementWithBookmark = () => {
    if (bookmarkId) {
      // 通过URL参数传递收藏项ID，以便管理页面定位
      const url = chrome.runtime.getURL(`src/options/index.html?highlight=${bookmarkId}`)
      chrome.tabs.create({ url })
    } else {
      // 如果没有收藏项ID，则打开普通管理页面
      handleOpenManagement()
    }
    window.close()
  }

  // 打开设置页面
  const handleOpenSettings = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('src/options/index.html#settings') })
    window.close()
  }

  // 手动同步
  const handleSync = async () => {
    try {
      setSyncStatus('syncing')
      await chrome.runtime.sendMessage({ type: 'MANUAL_SYNC' })
      setSyncStatus('idle')
    } catch (error) {
      console.error('同步失败:', error)
      setSyncStatus('error')
      setTimeout(() => setSyncStatus('idle'), 3000)
    }
  }

  // 切换设置
  const handleToggleSetting = (key: keyof AppSettings) => {
    saveSettings({ [key]: !settings[key] })
  }

  if (loading) {
    return (
      <Card className="w-full h-96">
        <CardContent className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    )
  }

  // 如果显示详细表单，则渲染表单组件
  if (showDetailedForm) {
    return (
      <DetailedBookmarkForm
        initialData={editingBookmark ? {
          title: editingBookmark.title || '',
          url: editingBookmark.url || '',
          description: editingBookmark.description || '',
          tags: editingBookmark.tags || [],
          category: editingBookmark.category || '默认分类',
          notes: editingBookmark.content || ''
        } : {
          title: currentTab?.title || '',
          url: currentTab?.url || '',
          description: '',
          tags: [],
          category: '默认分类',
          notes: selectedText || ''
        }}
        onSave={handleSaveDetailedBookmark}
        onCancel={handleCancelDetailedBookmark}
        loading={loading}
        isEditing={!!editingBookmark}
      />
    )
  }

  // 如果显示快速收藏表单，则渲染快速表单组件
  if (showQuickForm) {
    return (
      <QuickBookmarkForm
        initialData={{
          title: currentTab?.title || '',
          url: currentTab?.url || '',
          favIconUrl: currentTab?.favIconUrl
        }}
        onSave={handleQuickBookmarkSave}
        onCancel={handleQuickBookmarkCancel}
        loading={loading}
        smartRecognitionEnabled={settings.smartRecognition}
      />
    )
  }

  return (
    <Card className="w-full">
      {/* 头部 */}
      <CardHeader className="bg-gradient-to-r from-primary to-primary/90 text-primary-foreground">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Star className="w-5 h-5" />
            <h1 className="text-lg font-semibold">Universe Bag</h1>
          </div>
          <div className="flex items-center space-x-2">
            {/* 同步状态指示 */}
            <Button
              onClick={handleSync}
              disabled={syncStatus === 'syncing'}
              variant="ghost"
              size="sm"
              className={`p-1 h-auto ${
                syncStatus === 'error'
                  ? 'text-destructive hover:text-destructive'
                  : 'text-primary-foreground hover:bg-primary-foreground/20'
              }`}
              title={
                syncStatus === 'syncing' ? '同步中...' :
                syncStatus === 'error' ? '同步失败' : '手动同步'
              }
            >
              <RefreshCw className={`w-4 h-4 ${syncStatus === 'syncing' ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              onClick={handleOpenSettings}
              variant="ghost"
              size="sm"
              className="p-1 h-auto text-primary-foreground hover:bg-primary-foreground/20"
              title="设置"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* 当前页面信息 */}
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <img
            src={currentTab?.favIconUrl || '/icons/icon-16.png'}
            alt="网站图标"
            className="w-4 h-4 mt-1 flex-shrink-0"
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/icons/icon-16.png'
            }}
          />
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-foreground truncate">
              {currentTab?.title || '未知页面'}
            </h3>
            <p className="text-xs text-muted-foreground truncate mt-1">
              {currentTab?.url || ''}
            </p>
          </div>
          <div className="flex-shrink-0">
            {isBookmarked && (
              <Check className="w-4 h-4 text-green-500" />
            )}
          </div>
        </div>
      </CardContent>
      
      <Separator />

      {/* 主要操作按钮 */}
      <CardContent className="p-4 space-y-3">
        {isBookmarked ? (
          /* 已收藏状态 */
          <>
            {/* 已收藏提示 */}
            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <Star className="w-5 h-5 text-green-600 fill-current" />
                  <span className="text-green-800 font-medium">已收藏</span>
                </div>
                <p className="text-green-600 text-sm">此页面已在您的收藏夹中</p>
              </CardContent>
            </Card>

            {/* 编辑和管理按钮 */}
            <div className="space-y-3">
              <Button
                onClick={handleEditBookmark}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <Settings className="w-4 h-4 mr-2" />
                编辑收藏
              </Button>
              
              <Button
                onClick={handleOpenManagementWithBookmark}
                variant="outline"
                className="w-full"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                在管理页面打开
              </Button>
            </div>
          </>
        ) : (
          /* 未收藏状态 */
          <>
            {/* 快速收藏按钮 */}
            <Button
              onClick={handleShowQuickBookmark}
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  处理中...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  收藏当前页面
                </>
              )}
            </Button>

            {/* 收藏选中文字按钮 */}
            {selectedText && (
              <Button
                onClick={handleBookmarkSelectedText}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <FileText className="w-4 h-4 mr-2" />
                收藏选中文字
              </Button>
            )}

            {/* 详细收藏按钮 */}
            <Button
              onClick={handleDetailedBookmark}
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              <Bookmark className="w-4 h-4 mr-2" />
              详细收藏
            </Button>
          </>
        )}
      </CardContent>
      
      <Separator />

      {/* 功能开关 */}
      <CardContent className="p-4">
        <h4 className="text-sm font-medium text-foreground mb-3 flex items-center space-x-2">
          <Zap className="w-4 h-4" />
          <span>功能设置</span>
        </h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-foreground">自动标签生成</span>
            </div>
            <Switch
              checked={settings.autoTagging}
              onCheckedChange={(checked) => saveSettings({ autoTagging: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-foreground">内容去重检测</span>
            </div>
            <Switch
              checked={settings.duplicateDetection}
              onCheckedChange={(checked) => saveSettings({ duplicateDetection: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Eye className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-foreground">页面浮窗</span>
            </div>
            <Switch
              checked={settings.floatingWidget}
              onCheckedChange={(checked) => saveSettings({ floatingWidget: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Star className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-foreground">AI智能助手</span>
            </div>
            <Switch
              checked={settings.aiAssistant}
              onCheckedChange={(checked) => saveSettings({ aiAssistant: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Sparkles className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-foreground">智能识别</span>
            </div>
            <Switch
              checked={settings.smartRecognition}
              onCheckedChange={(checked) => saveSettings({ smartRecognition: checked })}
            />
          </div>
        </div>
      </CardContent>
      
      <Separator />

      {/* 底部导航 */}
      <CardContent className="p-4">
        <div className="grid grid-cols-2 gap-2">
          <Button
            onClick={handleOpenManagement}
            variant="ghost"
            className="flex items-center justify-center space-x-2"
          >
            <Menu className="w-4 h-4" />
            <span>管理收藏</span>
          </Button>
          <Button
            onClick={handleOpenSettings}
            variant="ghost"
            className="flex items-center justify-center space-x-2"
          >
            <ExternalLink className="w-4 h-4" />
            <span>设置</span>
          </Button>
        </div>
        
        {/* 同步状态信息 */}
        {syncStatus !== 'idle' && (
          <Card className={`mt-3 ${
            syncStatus === 'syncing' 
              ? 'bg-primary/10 border-primary/20' 
              : 'bg-destructive/10 border-destructive/20'
          }`}>
            <CardContent className="p-2 text-xs text-center">
              <span className={
                syncStatus === 'syncing' 
                  ? 'text-primary' 
                  : 'text-destructive'
              }>
                {syncStatus === 'syncing' ? '正在同步数据...' : '同步失败，请稍后重试'}
              </span>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  )
}

export default PopupApp