#!/usr/bin/env node

/**
 * 性能监控工具单元测试
 * 
 * 功能说明:
 * - 测试PerformanceMonitor类的所有功能
 * - 测试MemoryMonitor类的内存监控功能
 * - 测试debounce和throttle工具函数
 * - 验证单例模式的正确实现
 * - 确保性能监控的准确性和可靠性
 */

// Mock performance API
global.performance = {
  now: () => Date.now(),
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024,    // 50MB
    totalJSHeapSize: 100 * 1024 * 1024,  // 100MB
    jsHeapSizeLimit: 200 * 1024 * 1024   // 200MB
  }
}

// Mock setTimeout and clearTimeout
const originalSetTimeout = global.setTimeout
const originalClearTimeout = global.clearTimeout
const originalSetInterval = global.setInterval
const originalClearInterval = global.clearInterval

// 创建简单的mock函数
function createMockFunction() {
  const calls = []
  const mockFn = function(...args) {
    calls.push(args)
    return undefined
  }
  mockFn.mock = { calls }
  mockFn.mockClear = () => { calls.length = 0 }
  mockFn.mockImplementation = (impl) => {
    mockFn.implementation = impl
    return mockFn
  }
  return mockFn
}

global.setTimeout = createMockFunction()
global.clearTimeout = createMockFunction()
global.setInterval = createMockFunction()
global.clearInterval = createMockFunction()

console.log('🧪 开始测试性能监控工具...\n')

/**
 * 轻量级测试框架类
 */
class TestRunner {
  constructor() {
    this.tests = []
    this.passed = 0
    this.failed = 0
  }

  test(name, testFn) {
    this.tests.push({ name, testFn })
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  assertEqual(actual, expected, message) {
    if (actual !== expected) {
      throw new Error(`${message} - 期望: ${expected}, 实际: ${actual}`)
    }
  }

  assertGreaterThan(actual, expected, message) {
    if (actual <= expected) {
      throw new Error(`${message} - 期望大于: ${expected}, 实际: ${actual}`)
    }
  }

  assertLessThan(actual, expected, message) {
    if (actual >= expected) {
      throw new Error(`${message} - 期望小于: ${expected}, 实际: ${actual}`)
    }
  }

  assertNotNull(value, message) {
    if (value === null || value === undefined) {
      throw new Error(`${message} - 值不应为null或undefined`)
    }
  }

  async run() {
    console.log(`运行 ${this.tests.length} 个测试用例...\n`)

    for (const test of this.tests) {
      try {
        await test.testFn()
        console.log(`✅ ${test.name}`)
        this.passed++
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`)
        this.failed++
      }
    }

    console.log('\n' + '='.repeat(50))
    console.log(`测试结果: ${this.passed} 通过, ${this.failed} 失败`)

    if (this.failed > 0) {
      process.exit(1)
    }
  }
}

const runner = new TestRunner()

// 动态导入模块进行测试
let PerformanceMonitor, MemoryMonitor, debounce, throttle, performanceMonitor, memoryMonitor

// 测试模块导入
runner.test('模块导入测试', async () => {
  try {
    const module = await import('../src/utils/performance.ts')
    PerformanceMonitor = module.PerformanceMonitor
    MemoryMonitor = module.MemoryMonitor
    debounce = module.debounce
    throttle = module.throttle
    performanceMonitor = module.performanceMonitor
    memoryMonitor = module.memoryMonitor
    
    runner.assert(PerformanceMonitor, 'PerformanceMonitor类应该存在')
    runner.assert(MemoryMonitor, 'MemoryMonitor类应该存在')
    runner.assert(typeof debounce === 'function', 'debounce应该是函数')
    runner.assert(typeof throttle === 'function', 'throttle应该是函数')
    runner.assert(performanceMonitor, 'performanceMonitor实例应该存在')
    runner.assert(memoryMonitor, 'memoryMonitor实例应该存在')
  } catch (error) {
    throw new Error(`模块导入失败: ${error.message}`)
  }
})

// PerformanceMonitor 测试
runner.test('PerformanceMonitor - 单例模式', () => {
  const instance1 = PerformanceMonitor.getInstance()
  const instance2 = PerformanceMonitor.getInstance()
  
  runner.assert(instance1 === instance2, '应该返回相同的实例')
  runner.assert(instance1 === performanceMonitor, '导出的实例应该与getInstance返回的相同')
})

runner.test('PerformanceMonitor - 计时器功能', () => {
  const monitor = PerformanceMonitor.getInstance()
  
  // 模拟时间流逝
  let currentTime = 1000
  performance.now.mockImplementation(() => currentTime)
  
  // 开始计时
  monitor.startTimer('test-operation')
  
  // 模拟操作耗时
  currentTime = 1500
  
  // 结束计时
  const duration = monitor.endTimer('test-operation')
  
  runner.assertEqual(duration, 500, '计时器应该返回正确的耗时')
})

runner.test('PerformanceMonitor - 指标记录', () => {
  const monitor = PerformanceMonitor.getInstance()
  
  // 清除之前的指标
  monitor.clearMetrics()
  
  // 记录指标
  monitor.recordMetric('test-metric', 100)
  monitor.recordMetric('test-metric', 200)
  monitor.recordMetric('test-metric', 150)
  
  // 获取统计信息
  const stats = monitor.getMetricStats('test-metric')
  
  runner.assertNotNull(stats, '应该返回统计信息')
  runner.assertEqual(stats.count, 3, '记录次数应该正确')
  runner.assertEqual(stats.total, 450, '总和应该正确')
  runner.assertEqual(stats.average, 150, '平均值应该正确')
  runner.assertEqual(stats.min, 100, '最小值应该正确')
  runner.assertEqual(stats.max, 200, '最大值应该正确')
})

runner.test('PerformanceMonitor - 获取所有统计', () => {
  const monitor = PerformanceMonitor.getInstance()
  
  // 清除之前的指标
  monitor.clearMetrics()
  
  // 记录多个指标
  monitor.recordMetric('metric1', 100)
  monitor.recordMetric('metric2', 200)
  
  const allStats = monitor.getAllStats()
  
  runner.assert(typeof allStats === 'object', '应该返回对象')
  runner.assert('metric1' in allStats, '应该包含metric1')
  runner.assert('metric2' in allStats, '应该包含metric2')
  runner.assertNotNull(allStats.metric1, 'metric1统计应该存在')
  runner.assertNotNull(allStats.metric2, 'metric2统计应该存在')
})

runner.test('PerformanceMonitor - 清除指标', () => {
  const monitor = PerformanceMonitor.getInstance()
  
  // 记录指标
  monitor.recordMetric('test-clear', 100)
  runner.assertNotNull(monitor.getMetricStats('test-clear'), '指标应该存在')
  
  // 清除特定指标
  monitor.clearMetrics('test-clear')
  runner.assertEqual(monitor.getMetricStats('test-clear'), null, '指标应该被清除')
  
  // 记录多个指标
  monitor.recordMetric('metric1', 100)
  monitor.recordMetric('metric2', 200)
  
  // 清除所有指标
  monitor.clearMetrics()
  runner.assertEqual(monitor.getMetricStats('metric1'), null, '所有指标应该被清除')
  runner.assertEqual(monitor.getMetricStats('metric2'), null, '所有指标应该被清除')
})

runner.test('PerformanceMonitor - 错误处理', () => {
  const monitor = PerformanceMonitor.getInstance()
  
  // 测试结束不存在的计时器
  const duration = monitor.endTimer('non-existent-timer')
  runner.assertEqual(duration, 0, '不存在的计时器应该返回0')
  
  // 测试获取不存在的指标统计
  const stats = monitor.getMetricStats('non-existent-metric')
  runner.assertEqual(stats, null, '不存在的指标应该返回null')
})

// MemoryMonitor 测试
runner.test('MemoryMonitor - 单例模式', () => {
  const instance1 = MemoryMonitor.getInstance()
  const instance2 = MemoryMonitor.getInstance()
  
  runner.assert(instance1 === instance2, '应该返回相同的实例')
  runner.assert(instance1 === memoryMonitor, '导出的实例应该与getInstance返回的相同')
})

runner.test('MemoryMonitor - 获取内存使用情况', () => {
  const monitor = MemoryMonitor.getInstance()
  
  const memoryInfo = monitor.getCurrentMemoryUsage()
  
  runner.assertNotNull(memoryInfo, '应该返回内存信息')
  runner.assertEqual(typeof memoryInfo.used, 'number', 'used应该是数字')
  runner.assertEqual(typeof memoryInfo.total, 'number', 'total应该是数字')
  runner.assertEqual(typeof memoryInfo.limit, 'number', 'limit应该是数字')
  runner.assertEqual(typeof memoryInfo.percentage, 'number', 'percentage应该是数字')
  
  // 验证计算正确性
  runner.assertEqual(memoryInfo.used, 48, '已使用内存应该正确计算') // 50MB / 1024 / 1024 ≈ 48MB
  runner.assertEqual(memoryInfo.total, 95, '总内存应该正确计算')   // 100MB / 1024 / 1024 ≈ 95MB
  runner.assertEqual(memoryInfo.limit, 191, '内存限制应该正确计算') // 200MB / 1024 / 1024 ≈ 191MB
})

runner.test('MemoryMonitor - 开始和停止监控', () => {
  const monitor = MemoryMonitor.getInstance()
  
  // 开始监控
  monitor.startMonitoring(1000)
  runner.assert(setInterval.mock.calls.length > 0, '应该调用setInterval')
  
  // 停止监控
  monitor.stopMonitoring()
  runner.assert(clearInterval.mock.calls.length > 0, '应该调用clearInterval')
})

// debounce 函数测试
runner.test('debounce - 基本功能', async () => {
  const mockFn = createMockFunction()
  const debouncedFn = debounce(mockFn, 100)
  
  // 快速连续调用
  debouncedFn('arg1')
  debouncedFn('arg2')
  debouncedFn('arg3')
  
  // 等待一小段时间让setTimeout执行
  await new Promise(resolve => originalSetTimeout(resolve, 150))
  
  // 验证函数被调用
  runner.assert(mockFn.mock.calls.length > 0, '防抖函数应该被调用')
  
  // 验证最后一次调用的参数
  const lastCall = mockFn.mock.calls[mockFn.mock.calls.length - 1]
  runner.assertEqual(lastCall[0], 'arg3', '应该使用最后一次调用的参数')
})

runner.test('debounce - 参数传递', async () => {
  const mockFn = createMockFunction()
  const debouncedFn = debounce(mockFn, 100)
  
  debouncedFn('test', 123, { key: 'value' })
  
  // 等待防抖延迟
  await new Promise(resolve => originalSetTimeout(resolve, 150))
  
  runner.assert(mockFn.mock.calls.length > 0, '函数应该被调用')
  const args = mockFn.mock.calls[0]
  runner.assertEqual(args[0], 'test', '第一个参数应该正确')
  runner.assertEqual(args[1], 123, '第二个参数应该正确')
  runner.assertEqual(args[2].key, 'value', '对象参数应该正确')
})

// throttle 函数测试
runner.test('throttle - 基本功能', () => {
  const mockFn = createMockFunction()
  const throttledFn = throttle(mockFn, 100)
  
  // 模拟时间
  let currentTime = 1000
  const originalDateNow = Date.now
  Date.now = () => currentTime
  
  try {
    // 第一次调用应该立即执行
    throttledFn('arg1')
    runner.assertEqual(mockFn.mock.calls.length, 1, '第一次调用应该立即执行')
    
    // 在节流时间内的调用应该被忽略
    currentTime = 1050 // 50ms后
    throttledFn('arg2')
    runner.assertEqual(mockFn.mock.calls.length, 1, '节流时间内的调用应该被忽略')
    
    // 超过节流时间的调用应该执行
    currentTime = 1150 // 150ms后
    throttledFn('arg3')
    runner.assertEqual(mockFn.mock.calls.length, 2, '超过节流时间的调用应该执行')
  } finally {
    // 恢复原始Date.now
    Date.now = originalDateNow
  }
})

runner.test('throttle - 参数传递', () => {
  const mockFn = createMockFunction()
  const throttledFn = throttle(mockFn, 100)
  
  const originalDateNow = Date.now
  Date.now = () => 1000
  
  try {
    throttledFn('test', 456, { prop: 'value' })
    
    runner.assert(mockFn.mock.calls.length > 0, '函数应该被调用')
    const args = mockFn.mock.calls[0]
    runner.assertEqual(args[0], 'test', '第一个参数应该正确')
    runner.assertEqual(args[1], 456, '第二个参数应该正确')
    runner.assertEqual(args[2].prop, 'value', '对象参数应该正确')
  } finally {
    Date.now = originalDateNow
  }
})

// 集成测试
runner.test('性能监控集成测试', () => {
  const monitor = PerformanceMonitor.getInstance()
  
  // 清除之前的数据
  monitor.clearMetrics()
  
  // 模拟完整的性能监控流程
  let currentTime = 2000
  performance.now.mockImplementation(() => currentTime)
  
  // 开始监控操作
  monitor.startTimer('integration-test')
  
  // 模拟操作执行
  currentTime = 2300 // 300ms后
  
  // 结束监控
  const duration = monitor.endTimer('integration-test')
  
  // 验证结果
  runner.assertEqual(duration, 300, '集成测试耗时应该正确')
  
  // 获取统计信息
  const stats = monitor.getMetricStats('integration-test')
  runner.assertNotNull(stats, '应该有统计信息')
  runner.assertEqual(stats.count, 1, '应该有一次记录')
  runner.assertEqual(stats.average, 300, '平均耗时应该正确')
})

runner.test('内存监控集成测试', () => {
  const monitor = MemoryMonitor.getInstance()
  
  // 获取内存信息
  const memoryInfo = monitor.getCurrentMemoryUsage()
  
  runner.assertNotNull(memoryInfo, '应该返回内存信息')
  
  // 验证内存使用百分比计算
  const expectedPercentage = Math.round((memoryInfo.used / memoryInfo.limit) * 100)
  runner.assertEqual(memoryInfo.percentage, expectedPercentage, '内存使用百分比应该正确计算')
})

runner.test('防抖节流集成测试', async () => {
  const performanceTracker = createMockFunction()
  
  // 创建防抖版本的性能追踪函数
  const debouncedTracker = debounce(performanceTracker, 50) // 减少延迟以加快测试
  
  // 测试防抖
  debouncedTracker('debounce-test')
  
  // 等待防抖延迟
  await new Promise(resolve => originalSetTimeout(resolve, 100))
  
  runner.assert(performanceTracker.mock.calls.length > 0, '防抖函数应该被调用')
  
  // 重置mock
  performanceTracker.mockClear()
  
  // 创建节流版本的性能追踪函数
  const throttledTracker = throttle(performanceTracker, 100)
  
  // 测试节流
  const originalDateNow = Date.now
  Date.now = () => 3000
  
  try {
    throttledTracker('throttle-test')
    runner.assert(performanceTracker.mock.calls.length > 0, '节流函数应该被调用')
  } finally {
    Date.now = originalDateNow
  }
})

// 错误处理测试
runner.test('性能监控错误处理', () => {
  const monitor = PerformanceMonitor.getInstance()
  
  // 测试printReport在没有数据时的行为
  monitor.clearMetrics()
  
  // 这应该不会抛出错误
  runner.assert(() => {
    monitor.printReport()
    return true
  }, 'printReport应该能处理空数据')
  
  // 测试内存监控在不支持的环境中的行为
  const originalMemory = performance.memory
  delete performance.memory
  
  const memoryMonitor = MemoryMonitor.getInstance()
  const memoryInfo = memoryMonitor.getCurrentMemoryUsage()
  
  runner.assertEqual(memoryInfo, null, '不支持内存API时应该返回null')
  
  // 恢复memory对象
  performance.memory = originalMemory
})

// 运行所有测试
runner.run().catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})