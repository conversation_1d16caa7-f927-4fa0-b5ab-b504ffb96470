#!/usr/bin/env node

/**
 * 构建测试脚本
 * 验证项目配置是否正确，无需实际安装依赖
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 测试项目配置...\n')

/**
 * 检查项目必要文件是否存在
 * @returns {boolean} 所有必要文件都存在时返回true
 * @description 验证Chrome扩展项目的核心文件是否完整，包括配置文件、源代码文件等
 */
function checkRequiredFiles() {
  const requiredFiles = [
    'package.json',
    'manifest.json',
    'vite.config.ts',
    'tsconfig.json',
    'tailwind.config.js',
    'postcss.config.js',
    'src/background/index.ts',
    'src/content/index.ts',
    'src/popup/index.html',
    'src/popup/index.tsx',
    'src/options/index.html',
    'src/options/index.tsx',
    'src/types/index.ts',
    'src/styles/globals.css'
  ]

  let allExist = true

  console.log('📁 检查必要文件:')
  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    if (fs.existsSync(filePath)) {
      console.log(`  ✅ ${file}`)
    } else {
      console.log(`  ❌ ${file} - 文件不存在`)
      allExist = false
    }
  })

  return allExist
}

/**
 * 验证package.json配置的完整性
 * @returns {boolean} 配置有效时返回true
 * @description 检查package.json中的必要字段、脚本命令和依赖包是否正确配置
 */
function validatePackageJson() {
  console.log('\n📦 验证package.json:')
  
  try {
    const packagePath = path.join(process.cwd(), 'package.json')
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    
    // 检查必要字段
    const requiredFields = ['name', 'version', 'scripts', 'dependencies', 'devDependencies']
    let valid = true
    
    requiredFields.forEach(field => {
      if (packageJson[field]) {
        console.log(`  ✅ ${field}: 已定义`)
      } else {
        console.log(`  ❌ ${field}: 缺失`)
        valid = false
      }
    })
    
    // 检查关键脚本
    const requiredScripts = ['dev', 'build']
    requiredScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        console.log(`  ✅ script.${script}: ${packageJson.scripts[script]}`)
      } else {
        console.log(`  ❌ script.${script}: 缺失`)
        valid = false
      }
    })
    
    // 检查关键依赖
    const keyDependencies = ['react', 'react-dom', 'lucide-react']
    const keyDevDependencies = ['vite', '@vitejs/plugin-react', 'typescript', 'tailwindcss']
    
    keyDependencies.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`  ✅ dependency.${dep}: ${packageJson.dependencies[dep]}`)
      } else {
        console.log(`  ❌ dependency.${dep}: 缺失`)
        valid = false
      }
    })
    
    keyDevDependencies.forEach(dep => {
      if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
        console.log(`  ✅ devDependency.${dep}: ${packageJson.devDependencies[dep]}`)
      } else {
        console.log(`  ❌ devDependency.${dep}: 缺失`)
        valid = false
      }
    })
    
    return valid
  } catch (error) {
    console.log(`  ❌ 解析失败: ${error.message}`)
    return false
  }
}

/**
 * 验证Chrome扩展manifest.json配置
 * @returns {boolean} 配置符合Manifest V3规范时返回true
 * @description 检查Chrome扩展清单文件是否符合Manifest V3规范，包括权限、背景脚本等配置
 */
function validateManifest() {
  console.log('\n🔧 验证manifest.json:')
  
  try {
    const manifestPath = path.join(process.cwd(), 'manifest.json')
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    
    // 检查必要字段
    const requiredFields = [
      'manifest_version',
      'name',
      'version',
      'description',
      'permissions',
      'background',
      'content_scripts',
      'action'
    ]
    
    let valid = true
    
    requiredFields.forEach(field => {
      if (manifest[field]) {
        console.log(`  ✅ ${field}: 已定义`)
      } else {
        console.log(`  ❌ ${field}: 缺失`)
        valid = false
      }
    })
    
    // 检查Manifest V3特定配置
    if (manifest.manifest_version === 3) {
      console.log('  ✅ 使用Manifest V3')
      
      if (manifest.background && manifest.background.service_worker) {
        console.log(`  ✅ service_worker: ${manifest.background.service_worker}`)
      } else {
        console.log('  ❌ service_worker配置缺失')
        valid = false
      }
    } else {
      console.log('  ❌ 应使用Manifest V3')
      valid = false
    }
    
    return valid
  } catch (error) {
    console.log(`  ❌ 解析失败: ${error.message}`)
    return false
  }
}

/**
 * 验证TypeScript配置的正确性
 * @returns {boolean} 配置适合Chrome扩展开发时返回true
 * @description 检查tsconfig.json中的编译选项、路径映射和Chrome扩展类型定义
 */
function validateTypeScript() {
  console.log('\n📝 验证TypeScript配置:')
  
  try {
    const tsconfigPath = path.join(process.cwd(), 'tsconfig.json')
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'))
    
    let valid = true
    
    // 检查编译选项
    if (tsconfig.compilerOptions) {
      console.log('  ✅ compilerOptions: 已定义')
      
      const importantOptions = ['target', 'module', 'jsx', 'strict']
      importantOptions.forEach(option => {
        if (tsconfig.compilerOptions[option]) {
          console.log(`  ✅ ${option}: ${tsconfig.compilerOptions[option]}`)
        } else {
          console.log(`  ⚠️  ${option}: 未设置`)
        }
      })
      
      // 检查路径映射
      if (tsconfig.compilerOptions.paths && tsconfig.compilerOptions.paths['@/*']) {
        console.log('  ✅ 路径映射(@/*): 已配置')
      } else {
        console.log('  ⚠️  路径映射(@/*): 未配置')
      }
      
      // 检查Chrome扩展类型
      if (tsconfig.compilerOptions.types && tsconfig.compilerOptions.types.includes('chrome')) {
        console.log('  ✅ Chrome扩展类型: 已包含')
      } else {
        console.log('  ❌ Chrome扩展类型: 缺失')
        valid = false
      }
    } else {
      console.log('  ❌ compilerOptions: 缺失')
      valid = false
    }
    
    return valid
  } catch (error) {
    console.log(`  ❌ 解析失败: ${error.message}`)
    return false
  }
}

/**
 * 检查源代码目录结构的完整性
 * @returns {boolean} 所有预期文件和目录都存在时返回true
 * @description 验证src目录下的各个模块文件夹和文件是否按照预期结构存在
 */
function checkSourceStructure() {
  console.log('\n📂 检查源代码结构:')
  
  const expectedStructure = {
    'src/background': ['index.ts'],
    'src/content': ['index.ts', 'style.css'],
    'src/popup': ['index.html', 'index.tsx', 'PopupApp.tsx'],
    'src/options': ['index.html', 'index.tsx', 'OptionsApp.tsx'],
    'src/types': ['index.ts'],
    'src/styles': ['globals.css']
  }
  
  let valid = true
  
  Object.entries(expectedStructure).forEach(([dir, files]) => {
    const dirPath = path.join(process.cwd(), dir)
    if (fs.existsSync(dirPath)) {
      console.log(`  ✅ ${dir}/`)
      
      files.forEach(file => {
        const filePath = path.join(dirPath, file)
        if (fs.existsSync(filePath)) {
          console.log(`    ✅ ${file}`)
        } else {
          console.log(`    ❌ ${file}`)
          valid = false
        }
      })
    } else {
      console.log(`  ❌ ${dir}/ - 目录不存在`)
      valid = false
    }
  })
  
  return valid
}

/**
 * 主测试函数，协调执行所有验证测试
 * @returns {void} 通过console输出结果，失败时退出进程
 * @description 依次执行所有验证函数，收集结果并输出详细报告，提供下一步操作指导
 */
function main() {
  const tests = [
    { name: '必要文件检查', fn: checkRequiredFiles },
    { name: 'package.json验证', fn: validatePackageJson },
    { name: 'manifest.json验证', fn: validateManifest },
    { name: 'TypeScript配置验证', fn: validateTypeScript },
    { name: '源代码结构检查', fn: checkSourceStructure }
  ]
  
  let allPassed = true
  
  tests.forEach(test => {
    try {
      const result = test.fn()
      if (!result) {
        allPassed = false
      }
    } catch (error) {
      console.log(`❌ ${test.name}失败: ${error.message}`)
      allPassed = false
    }
  })
  
  console.log('\n' + '='.repeat(50))
  
  if (allPassed) {
    console.log('🎉 项目配置验证通过！')
    console.log('\n✅ 项目结构正确')
    console.log('✅ 配置文件完整')
    console.log('✅ Chrome扩展配置正确')
    console.log('\n📋 下一步:')
    console.log('1. 安装Node.js (https://nodejs.org/)')
    console.log('2. 运行: npm install')
    console.log('3. 运行: npm run dev')
    console.log('4. 在Chrome中加载扩展进行测试')
  } else {
    console.log('❌ 项目配置验证失败')
    console.log('\n请检查并修复上述问题后重试')
    process.exit(1)
  }
}

// 运行测试
main()