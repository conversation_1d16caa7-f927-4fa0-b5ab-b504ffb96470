// 帮助内容配置 - 外部化帮助文本，便于维护和国际化

export type HelpContext = 'export' | 'import' | 'conflict' | 'settings'

export interface HelpContentItem {
  title: string
  description: string
  steps?: string[]
  tips?: string[]
}

/**
 * 帮助内容配置
 * 集中管理所有帮助文本，便于维护和扩展
 */
export const HELP_CONTENT: Record<HelpContext, HelpContentItem> = {
  export: {
    title: '数据导出帮助',
    description: '导出功能让您可以备份和分享您的收藏数据。支持多种导出类型和格式。',
    steps: [
      '选择要导出的数据类型（全部数据、收藏夹、分类或标签）',
      '选择导出格式（JSON推荐用于备份，CSV适合数据分析，HTML兼容浏览器）',
      '配置导出选项，如日期范围、包含的数据类型等',
      '点击导出按钮，选择保存位置'
    ],
    tips: [
      '建议定期导出数据作为备份',
      'JSON格式包含最完整的数据信息',
      '大量数据导出可能需要较长时间，请耐心等待'
    ]
  },
  import: {
    title: '数据导入帮助',
    description: '导入功能让您可以从备份文件或其他来源恢复数据。支持智能冲突检测和解决。',
    steps: [
      '点击"选择文件"按钮，选择要导入的文件',
      '系统会自动验证文件格式和内容',
      '配置导入选项，特别是冲突处理方式',
      '点击"开始导入"，等待处理完成',
      '如有冲突，按提示进行处理'
    ],
    tips: [
      '导入前建议先备份当前数据',
      '大文件导入建议选择"提示处理"冲突方式',
      '可以分批导入大量数据以提高成功率'
    ]
  },
  conflict: {
    title: '冲突处理帮助',
    description: '当导入数据与现有数据存在冲突时，您可以选择不同的解决方案。',
    steps: [
      '查看冲突列表，了解冲突的类型和详情',
      '对每个冲突选择合适的解决方案',
      '使用批量操作处理大量相似冲突',
      '预览合并结果，确认无误后应用'
    ],
    tips: [
      '智能合并通常能提供最好的结果',
      '对于明确的重复项，建议保留现有数据',
      '手动编辑适用于需要精确控制的情况'
    ]
  },
  settings: {
    title: '设置帮助',
    description: '在设置中可以配置导入导出的默认行为和性能参数。',
    steps: [
      '调整默认的导出格式和选项',
      '设置冲突检测的敏感度',
      '配置性能参数如批处理大小',
      '启用或禁用安全验证功能'
    ],
    tips: [
      '合适的批处理大小可以提高性能',
      '安全验证建议始终保持启用',
      '可以根据使用习惯调整默认设置'
    ]
  }
}

/**
 * 获取帮助内容
 * @param context 帮助上下文
 * @returns 帮助内容项
 */
export const getHelpContent = (context: HelpContext): HelpContentItem => {
  return HELP_CONTENT[context]
}