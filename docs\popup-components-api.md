# Popup 组件 API 文档

## 概述

`src/popup/components/` 目录包含了 Universe Bag Chrome 扩展弹出窗口界面的所有可复用组件。这些组件采用模块化设计，提供了完整的类型定义和统一的导出接口。

## 组件导出

### 主导出文件：`src/popup/components/index.ts`

```typescript
export { default as Toggle } from './Toggle'
export { default as DetailedBookmarkForm } from './DetailedBookmarkForm'
```

**功能**: 统一导出所有弹出窗口组件，提供便捷的导入接口  
**用途**: 简化组件导入，维护清晰的模块结构

**使用示例**:
```typescript
// 单个组件导入
import { Toggle } from '@/popup/components'

// 多个组件导入
import { Toggle, DetailedBookmarkForm } from '@/popup/components'

// 全部组件导入
import * as PopupComponents from '@/popup/components'
```

---

## 组件详细文档

### 1. Toggle 组件

#### 接口定义

```typescript
interface ToggleProps {
  checked: boolean
  onChange: (checked: boolean) => void
  disabled?: boolean
  size?: 'sm' | 'md'
}
```

#### 组件签名

```typescript
const Toggle: React.FC<ToggleProps>
```

#### 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `checked` | `boolean` | ✅ | - | 开关状态 |
| `onChange` | `(checked: boolean) => void` | ✅ | - | 状态变化回调函数 |
| `disabled` | `boolean` | ❌ | `false` | 是否禁用开关 |
| `size` | `'sm' \| 'md'` | ❌ | `'md'` | 开关尺寸 |

#### 功能特性

- **响应式设计**: 支持不同尺寸适配
- **无障碍支持**: 完整的 ARIA 属性支持
- **动画效果**: 平滑的状态切换动画
- **键盘导航**: 支持键盘操作
- **禁用状态**: 支持禁用状态的视觉反馈

#### 样式规格

**尺寸配置**:
```typescript
const sizeClasses = {
  sm: {
    container: 'w-8 h-4',    // 容器尺寸
    thumb: 'w-3 h-3',        // 滑块尺寸
    translate: 'translate-x-4' // 滑动距离
  },
  md: {
    container: 'w-10 h-5',
    thumb: 'w-4 h-4',
    translate: 'translate-x-5'
  }
}
```

**颜色主题**:
- 开启状态: `bg-primary-600`
- 关闭状态: `bg-gray-200`
- 禁用状态: `opacity-50`
- 焦点状态: `focus:ring-2 focus:ring-primary-500`

#### 使用示例

```typescript
import React, { useState } from 'react'
import { Toggle } from '@/popup/components'

const SettingsPanel: React.FC = () => {
  const [autoTagging, setAutoTagging] = useState(true)
  const [syncEnabled, setSyncEnabled] = useState(false)

  return (
    <div className="space-y-4">
      {/* 基础用法 */}
      <div className="flex items-center justify-between">
        <span>自动标签生成</span>
        <Toggle
          checked={autoTagging}
          onChange={setAutoTagging}
        />
      </div>

      {/* 小尺寸开关 */}
      <div className="flex items-center justify-between">
        <span>云端同步</span>
        <Toggle
          checked={syncEnabled}
          onChange={setSyncEnabled}
          size="sm"
        />
      </div>

      {/* 禁用状态 */}
      <div className="flex items-center justify-between">
        <span>高级功能（即将推出）</span>
        <Toggle
          checked={false}
          onChange={() => {}}
          disabled={true}
        />
      </div>
    </div>
  )
}
```

---

### 2. DetailedBookmarkForm 组件

#### 接口定义

```typescript
interface BookmarkFormData {
  title: string
  url: string
  description: string
  tags: string[]
  category: string
  notes: string
}

interface DetailedBookmarkFormProps {
  initialData?: Partial<BookmarkFormData>
  onSave: (data: BookmarkFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
}
```

#### 组件签名

```typescript
const DetailedBookmarkForm: React.FC<DetailedBookmarkFormProps>
```

#### 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `initialData` | `Partial<BookmarkFormData>` | ❌ | `{}` | 表单初始数据 |
| `onSave` | `(data: BookmarkFormData) => Promise<void>` | ✅ | - | 保存回调函数 |
| `onCancel` | `() => void` | ✅ | - | 取消回调函数 |
| `loading` | `boolean` | ❌ | `false` | 加载状态 |

#### 功能特性

- **表单验证**: 必填字段验证和格式检查
- **AI辅助**: 智能生成标签、分类和描述
- **标签管理**: 动态添加、删除和建议标签
- **分类选择**: 预设分类选择和自定义分类
- **实时保存**: 异步保存处理和错误处理
- **响应式布局**: 适配不同屏幕尺寸

#### 表单字段

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `title` | `string` | ✅ | 收藏标题 |
| `url` | `string` | ❌ | 网页链接 |
| `description` | `string` | ❌ | 内容描述 |
| `tags` | `string[]` | ❌ | 标签列表 |
| `category` | `string` | ❌ | 所属分类 |
| `notes` | `string` | ❌ | 个人笔记 |

#### 内部状态管理

```typescript
// 表单数据状态
const [formData, setFormData] = useState<BookmarkFormData>({
  title: initialData.title || '',
  url: initialData.url || '',
  description: initialData.description || '',
  tags: initialData.tags || [],
  category: initialData.category || '默认分类',
  notes: initialData.notes || ''
})

// 标签输入状态
const [tagInput, setTagInput] = useState('')

// AI功能状态
const [aiLoading, setAiLoading] = useState(false)
const [suggestedTags, setSuggestedTags] = useState<string[]>([])

// 预设分类
const [categories] = useState(['默认分类', '工作', '学习', '娱乐', '工具', '资源'])
```

#### 核心方法

##### handleFieldChange()
```typescript
const handleFieldChange = (field: keyof BookmarkFormData, value: string) => void
```

**功能**: 处理表单字段值变化  
**参数**:
- `field`: 要更新的字段名
- `value`: 新的字段值

##### handleAddTag()
```typescript
const handleAddTag = (tag: string) => void
```

**功能**: 添加新标签到标签列表  
**参数**:
- `tag`: 要添加的标签文本

**特性**:
- 自动去除空白字符
- 防止重复标签
- 清空输入框

##### handleRemoveTag()
```typescript
const handleRemoveTag = (tagToRemove: string) => void
```

**功能**: 从标签列表中删除指定标签  
**参数**:
- `tagToRemove`: 要删除的标签文本

##### handleAIAssist()
```typescript
const handleAIAssist = async () => Promise<void>
```

**功能**: 调用AI服务生成标签和分类建议  
**返回值**: `Promise<void>`

**AI请求格式**:
```typescript
const response = await chrome.runtime.sendMessage({
  type: 'AI_GENERATE_SUGGESTIONS',
  data: {
    title: formData.title,
    url: formData.url,
    description: formData.description
  }
})
```

**AI响应格式**:
```typescript
{
  success: boolean,
  data: {
    tags: string[],      // 建议的标签
    category: string,    // 建议的分类
    description: string  // 生成的描述
  }
}
```

##### handleSubmit()
```typescript
const handleSubmit = async (e: React.FormEvent) => Promise<void>
```

**功能**: 处理表单提交  
**参数**:
- `e`: 表单提交事件

**验证规则**:
- 标题字段必填
- URL格式验证（如果提供）
- 标签数量限制

#### 键盘交互

**标签输入框快捷键**:
- `Enter` 或 `,`: 添加当前输入的标签
- `Backspace`: 当输入框为空时，删除最后一个标签

```typescript
const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
  if (e.key === 'Enter' || e.key === ',') {
    e.preventDefault()
    if (tagInput.trim()) {
      handleAddTag(tagInput)
    }
  } else if (e.key === 'Backspace' && !tagInput && formData.tags.length > 0) {
    handleRemoveTag(formData.tags[formData.tags.length - 1])
  }
}
```

#### 使用示例

```typescript
import React, { useState } from 'react'
import { DetailedBookmarkForm } from '@/popup/components'

const BookmarkManager: React.FC = () => {
  const [showForm, setShowForm] = useState(false)
  const [loading, setLoading] = useState(false)

  // 保存收藏处理
  const handleSaveBookmark = async (formData: BookmarkFormData) => {
    try {
      setLoading(true)
      
      const response = await chrome.runtime.sendMessage({
        type: 'SAVE_DETAILED_BOOKMARK',
        data: {
          ...formData,
          timestamp: new Date().toISOString()
        }
      })

      if (response?.success) {
        setShowForm(false)
        console.log('收藏保存成功')
      }
    } catch (error) {
      console.error('保存失败:', error)
      throw error // 让组件处理错误显示
    } finally {
      setLoading(false)
    }
  }

  // 取消收藏处理
  const handleCancelBookmark = () => {
    setShowForm(false)
  }

  if (showForm) {
    return (
      <DetailedBookmarkForm
        initialData={{
          title: '示例页面标题',
          url: 'https://example.com',
          description: '',
          tags: ['示例'],
          category: '默认分类',
          notes: ''
        }}
        onSave={handleSaveBookmark}
        onCancel={handleCancelBookmark}
        loading={loading}
      />
    )
  }

  return (
    <button onClick={() => setShowForm(true)}>
      打开详细收藏表单
    </button>
  )
}
```

#### 样式定制

**CSS类名约定**:
- 使用 Tailwind CSS 工具类
- 遵循项目的设计系统
- 支持深色模式（未来版本）

**主要样式类**:
```css
/* 输入框样式 */
.input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

/* 标签样式 */
.tag-primary {
  @apply inline-flex items-center px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full;
}

/* 建议标签样式 */
.tag-suggested {
  @apply px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs rounded-full transition-colors;
}
```

---

## 组件集成

### 在 PopupApp 中的使用

```typescript
import React, { useState } from 'react'
import { Toggle, DetailedBookmarkForm } from './components'

const PopupApp: React.FC = () => {
  const [showDetailedForm, setShowDetailedForm] = useState(false)
  const [settings, setSettings] = useState({
    autoTagging: true,
    duplicateDetection: true
  })

  // 组件集成示例
  return (
    <div>
      {showDetailedForm ? (
        <DetailedBookmarkForm
          onSave={handleSaveBookmark}
          onCancel={() => setShowDetailedForm(false)}
        />
      ) : (
        <div>
          {/* 设置开关 */}
          <Toggle
            checked={settings.autoTagging}
            onChange={(checked) => setSettings(prev => ({
              ...prev,
              autoTagging: checked
            }))}
          />
          
          {/* 其他UI组件 */}
        </div>
      )}
    </div>
  )
}
```

---

## 测试指南

### 单元测试示例

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Toggle, DetailedBookmarkForm } from '@/popup/components'

describe('Toggle Component', () => {
  test('应该正确切换状态', () => {
    const handleChange = jest.fn()
    
    render(
      <Toggle checked={false} onChange={handleChange} />
    )
    
    const toggle = screen.getByRole('switch')
    fireEvent.click(toggle)
    
    expect(handleChange).toHaveBeenCalledWith(true)
  })

  test('禁用状态下不应该响应点击', () => {
    const handleChange = jest.fn()
    
    render(
      <Toggle checked={false} onChange={handleChange} disabled />
    )
    
    const toggle = screen.getByRole('switch')
    fireEvent.click(toggle)
    
    expect(handleChange).not.toHaveBeenCalled()
  })
})

describe('DetailedBookmarkForm Component', () => {
  test('应该正确提交表单数据', async () => {
    const handleSave = jest.fn().mockResolvedValue(undefined)
    const handleCancel = jest.fn()
    
    render(
      <DetailedBookmarkForm
        onSave={handleSave}
        onCancel={handleCancel}
        initialData={{ title: '测试标题' }}
      />
    )
    
    const submitButton = screen.getByText('保存收藏')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(handleSave).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '测试标题'
        })
      )
    })
  })
})
```

---

## 性能优化

### 组件优化策略

1. **React.memo**: 对纯展示组件使用 memo 优化
2. **useCallback**: 缓存事件处理函数
3. **useMemo**: 缓存计算结果
4. **懒加载**: 大型组件的动态导入

### 示例优化

```typescript
import React, { memo, useCallback, useMemo } from 'react'

const OptimizedToggle = memo<ToggleProps>(({ checked, onChange, disabled, size }) => {
  const sizeClasses = useMemo(() => ({
    sm: { container: 'w-8 h-4', thumb: 'w-3 h-3', translate: 'translate-x-4' },
    md: { container: 'w-10 h-5', thumb: 'w-4 h-4', translate: 'translate-x-5' }
  }), [])

  const handleClick = useCallback(() => {
    if (!disabled) {
      onChange(!checked)
    }
  }, [checked, disabled, onChange])

  // 组件渲染逻辑...
})
```

---

## 错误处理

### 组件错误边界

```typescript
import React, { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

class ComponentErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('组件错误:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-red-800 font-medium">组件加载失败</h3>
          <p className="text-red-600 text-sm mt-1">
            {this.state.error?.message || '未知错误'}
          </p>
        </div>
      )
    }

    return this.props.children
  }
}
```

---

## 扩展指南

### 添加新组件

1. **创建组件文件**: `src/popup/components/NewComponent.tsx`
2. **定义接口**: 明确的 TypeScript 接口定义
3. **实现组件**: 遵循项目代码规范
4. **添加导出**: 在 `index.ts` 中添加导出
5. **编写测试**: 完整的单元测试覆盖
6. **更新文档**: 在本文档中添加组件说明

### 组件开发规范

```typescript
// 1. 导入依赖
import React, { useState, useCallback } from 'react'
import { SomeIcon } from 'lucide-react'

// 2. 定义接口
interface NewComponentProps {
  // 属性定义
}

// 3. 实现组件
const NewComponent: React.FC<NewComponentProps> = ({
  // 解构属性
}) => {
  // 4. 状态管理
  const [state, setState] = useState()

  // 5. 事件处理
  const handleEvent = useCallback(() => {
    // 处理逻辑
  }, [])

  // 6. 渲染组件
  return (
    <div className="component-container">
      {/* 组件内容 */}
    </div>
  )
}

// 7. 导出组件
export default NewComponent
```

---

## 依赖关系

### 外部依赖
- `react`: React 核心库
- `lucide-react`: 图标组件库
- `tailwindcss`: CSS 框架

### 内部依赖
- `@/types`: TypeScript 类型定义
- `@/styles/globals.css`: 全局样式
- Chrome Extension APIs: 扩展功能集成

### 版本兼容性
- React: ^18.2.0
- TypeScript: ^5.0.2
- Node.js: >=16.0.0

---

## 更新日志

### v1.0.0 (当前版本)
- ✅ Toggle 组件实现
- ✅ DetailedBookmarkForm 组件实现
- ✅ 统一导出接口
- ✅ 完整的 TypeScript 类型定义
- ✅ AI辅助功能集成

### 计划功能
- 🔄 更多UI组件（按钮、输入框、模态框等）
- 🔄 主题切换支持
- 🔄 国际化支持
- 🔄 无障碍功能增强