/**
 * Chrome 测试工具函数
 * 提供安全的 Chrome 对象模拟和清理功能
 */

/**
 * 安全地清理 Chrome 对象
 * 避免在某些环境中删除不可删除的属性时出现错误
 */
export function safeCleanupChrome(): void {
  try {
    // 尝试删除 chrome 对象
    delete (window as any).chrome
  } catch (error) {
    // 如果删除失败，则设置为 undefined
    try {
      ;(window as any).chrome = undefined
    } catch (secondError) {
      // 如果连设置 undefined 都失败，则忽略
      console.warn('无法清理 chrome 对象:', secondError)
    }
  }
}

/**
 * 安全地设置 Chrome 对象
 * 检查是否可以设置，避免覆盖原生 chrome 对象
 */
export function safeSetChrome(mockChrome: any): () => void {
  const originalChrome = (window as any).chrome
  
  try {
    ;(window as any).chrome = mockChrome
    
    // 返回清理函数
    return () => {
      try {
        if (originalChrome) {
          // 恢复原始 chrome 对象
          ;(window as any).chrome = originalChrome
        } else {
          // 清理模拟的 chrome 对象
          safeCleanupChrome()
        }
      } catch (error) {
        console.warn('清理 chrome 对象时出错:', error)
      }
    }
  } catch (error) {
    console.warn('设置 chrome 对象时出错:', error)
    // 返回空的清理函数
    return () => {}
  }
}

/**
 * 创建标准的 Chrome API 模拟对象
 */
export function createMockChrome() {
  return {
    tabs: {
      query: async (queryInfo: any) => {
        console.log('模拟 chrome.tabs.query 调用:', queryInfo)
        return [{
          id: 1,
          title: '测试页面 - GitHub',
          url: 'https://github.com/test/repo',
          favIconUrl: 'https://github.com/favicon.ico'
        }]
      },
      sendMessage: async (tabId: number, message: any) => {
        console.log('模拟 chrome.tabs.sendMessage 调用:', { tabId, message })
        if (message.type === 'GET_SELECTED_TEXT') {
          return { selectedText: '这是一段选中的测试文字，用于演示收藏功能。' }
        }
        return {}
      },
      create: (createProperties: any) => {
        console.log('模拟 chrome.tabs.create 调用:', createProperties)
        alert(`模拟打开新标签页: ${createProperties.url}`)
      }
    },
    runtime: {
      sendMessage: async (message: any) => {
        console.log('模拟 chrome.runtime.sendMessage 调用:', message)
        
        // 模拟不同类型的响应
        switch (message.type) {
          case 'CHECK_BOOKMARK_STATUS':
            return {
              success: true,
              data: {
                isBookmarked: Math.random() > 0.5,
                bookmarkId: Math.random() > 0.5 ? 'test-bookmark-id' : null
              }
            }
          
          case 'QUICK_BOOKMARK':
            return {
              success: true,
              data: {
                bookmarkId: 'new-bookmark-id-' + Date.now()
              }
            }
          
          case 'BOOKMARK_SELECTED_TEXT':
            return {
              success: true,
              data: {
                bookmarkId: 'text-bookmark-id-' + Date.now()
              }
            }
          
          case 'SAVE_DETAILED_BOOKMARK':
            return {
              success: true,
              data: {
                bookmarkId: 'detailed-bookmark-id-' + Date.now()
              }
            }
          
          case 'UPDATE_BOOKMARK':
            return {
              success: true,
              data: {
                bookmarkId: message.data.id
              }
            }
          
          case 'GET_BOOKMARK':
            return {
              success: true,
              data: {
                id: message.data.id,
                title: '测试收藏项',
                url: 'https://github.com/test/repo',
                description: '这是一个测试收藏项的描述',
                tags: ['测试', 'GitHub', 'shadcn'],
                category: '开发工具',
                content: '这是收藏的内容文本',
                metadata: {
                  pageTitle: '测试页面',
                  siteName: 'github.com',
                  publishDate: new Date(),
                  aiGenerated: false
                }
              }
            }
          
          case 'MANUAL_SYNC':
            // 模拟同步延迟
            await new Promise(resolve => setTimeout(resolve, 2000))
            return { success: true }
          
          case 'SETTINGS_UPDATED':
            return { success: true }
          
          default:
            return { success: true }
        }
      },
      onMessage: {
        addListener: (callback: any) => {
          console.log('模拟 chrome.runtime.onMessage.addListener')
        },
        removeListener: (callback: any) => {
          console.log('模拟 chrome.runtime.onMessage.removeListener')
        }
      },
      getURL: (path: string) => {
        console.log('模拟 chrome.runtime.getURL 调用:', path)
        return `chrome-extension://test-extension-id/${path}`
      }
    },
    storage: {
      sync: {
        get: async (keys: any) => {
          console.log('模拟 chrome.storage.sync.get 调用:', keys)
          return {
            appSettings: {
              autoTagging: true,
              duplicateDetection: true,
              floatingWidget: false,
              aiAssistant: true
            }
          }
        },
        set: async (items: any) => {
          console.log('模拟 chrome.storage.sync.set 调用:', items)
          return {}
        }
      }
    }
  }
}

/**
 * 检查是否在 Chrome 扩展环境中
 */
export function isInChromeExtension(): boolean {
  return typeof chrome !== 'undefined' && 
         chrome.runtime && 
         chrome.runtime.id !== undefined
}

/**
 * 安全的 Chrome API 调用包装器
 * 在非扩展环境中提供模拟响应
 */
export function safeChromeCall<T>(
  chromeCall: () => Promise<T>,
  mockResponse: T
): Promise<T> {
  if (isInChromeExtension()) {
    return chromeCall().catch(error => {
      console.warn('Chrome API 调用失败，使用模拟响应:', error)
      return mockResponse
    })
  } else {
    console.log('非扩展环境，使用模拟响应')
    return Promise.resolve(mockResponse)
  }
}