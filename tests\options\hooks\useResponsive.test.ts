/**
 * useResponsive Hook 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useResponsive, useIsMobile, useBreakpoint } from '../../../src/options/hooks/useResponsive'

// 模拟 window 对象
const mockWindow = {
  innerWidth: 1024,
  innerHeight: 768,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
}

Object.defineProperty(window, 'innerWidth', {
  writable: true,
  configurable: true,
  value: 1024
})

Object.defineProperty(window, 'innerHeight', {
  writable: true,
  configurable: true,
  value: 768
})

describe('useResponsive', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    window.innerWidth = 1024
    window.innerHeight = 768
  })

  it('应该返回初始的响应式状态', () => {
    const { result } = renderHook(() => useResponsive())
    
    expect(result.current.width).toBe(1024)
    expect(result.current.height).toBe(768)
    expect(result.current.breakpoint).toBe('lg')
    expect(result.current.isMobile).toBe(false)
    expect(result.current.isTablet).toBe(false)
    expect(result.current.isDesktop).toBe(true)
    expect(result.current.isLargeDesktop).toBe(false)
    expect(result.current.orientation).toBe('landscape')
  })

  it('应该检测移动设备', () => {
    window.innerWidth = 640
    window.innerHeight = 960
    
    const { result } = renderHook(() => useResponsive())
    
    expect(result.current.isMobile).toBe(true)
    expect(result.current.isTablet).toBe(false)
    expect(result.current.isDesktop).toBe(false)
    expect(result.current.breakpoint).toBe('sm')
    expect(result.current.orientation).toBe('portrait')
  })

  it('应该检测平板设备', () => {
    window.innerWidth = 800
    window.innerHeight = 600
    
    const { result } = renderHook(() => useResponsive())
    
    expect(result.current.isMobile).toBe(false)
    expect(result.current.isTablet).toBe(true)
    expect(result.current.isDesktop).toBe(false)
    expect(result.current.breakpoint).toBe('md')
  })

  it('应该检测大屏幕设备', () => {
    window.innerWidth = 1400
    window.innerHeight = 900
    
    const { result } = renderHook(() => useResponsive())
    
    expect(result.current.isLargeDesktop).toBe(true)
    expect(result.current.breakpoint).toBe('xl')
  })

  it('应该支持自定义断点', () => {
    const customBreakpoints = {
      sm: 480,
      md: 768,
      lg: 1200
    }
    
    window.innerWidth = 1000
    
    const { result } = renderHook(() => useResponsive(customBreakpoints))
    
    expect(result.current.breakpoint).toBe('md')
    expect(result.current.isDesktop).toBe(false)
    expect(result.current.isTablet).toBe(true)
  })

  it('应该正确匹配断点', () => {
    window.innerWidth = 1024
    
    const { result } = renderHook(() => useResponsive())
    
    expect(result.current.matches('sm')).toBe(true)
    expect(result.current.matches('md')).toBe(true)
    expect(result.current.matches('lg')).toBe(true)
    expect(result.current.matches('xl')).toBe(false)
  })

  it('应该检查断点范围', () => {
    window.innerWidth = 800
    
    const { result } = renderHook(() => useResponsive())
    
    expect(result.current.between('md', 'lg')).toBe(true)
    expect(result.current.between('sm', 'md')).toBe(false)
    expect(result.current.between('lg', 'xl')).toBe(false)
  })

  it('应该返回响应式值', () => {
    window.innerWidth = 800
    
    const { result } = renderHook(() => useResponsive())
    
    const value = result.current.getResponsiveValue({
      sm: 'small',
      md: 'medium',
      lg: 'large'
    }, 'default')
    
    expect(value).toBe('medium')
  })

  it('应该返回默认值当没有匹配的断点时', () => {
    window.innerWidth = 400
    
    const { result } = renderHook(() => useResponsive())
    
    const value = result.current.getResponsiveValue({
      lg: 'large',
      xl: 'extra-large'
    }, 'default')
    
    expect(value).toBe('default')
  })

  it('应该监听窗口大小变化', () => {
    const { result } = renderHook(() => useResponsive())
    
    // 模拟窗口大小变化
    act(() => {
      window.innerWidth = 640
      window.innerHeight = 960
      window.dispatchEvent(new Event('resize'))
    })
    
    // 由于使用了防抖，需要等待
    setTimeout(() => {
      expect(result.current.width).toBe(640)
      expect(result.current.height).toBe(960)
      expect(result.current.isMobile).toBe(true)
    }, 150)
  })

  it('应该监听设备方向变化', () => {
    const { result } = renderHook(() => useResponsive())
    
    act(() => {
      window.innerWidth = 960
      window.innerHeight = 640
      window.dispatchEvent(new Event('orientationchange'))
    })
    
    setTimeout(() => {
      expect(result.current.orientation).toBe('landscape')
    }, 150)
  })

  it('应该清理事件监听器', () => {
    const addEventListenerSpy = vi.spyOn(window, 'addEventListener')
    const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')
    
    const { unmount } = renderHook(() => useResponsive())
    
    expect(addEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function))
    expect(addEventListenerSpy).toHaveBeenCalledWith('orientationchange', expect.any(Function))
    
    unmount()
    
    expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function))
    expect(removeEventListenerSpy).toHaveBeenCalledWith('orientationchange', expect.any(Function))
  })
})

describe('useIsMobile', () => {
  it('应该返回移动设备状态', () => {
    window.innerWidth = 640
    
    const { result } = renderHook(() => useIsMobile())
    
    expect(result.current).toBe(true)
  })
})

describe('useBreakpoint', () => {
  it('应该返回断点匹配状态', () => {
    window.innerWidth = 1024
    
    const { result } = renderHook(() => useBreakpoint('lg'))
    
    expect(result.current).toBe(true)
  })

  it('应该返回断点不匹配状态', () => {
    window.innerWidth = 640
    
    const { result } = renderHook(() => useBreakpoint('lg'))
    
    expect(result.current).toBe(false)
  })
})