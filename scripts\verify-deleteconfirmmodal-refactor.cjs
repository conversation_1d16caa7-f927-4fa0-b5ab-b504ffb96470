// DeleteConfirmModal shadcn重构验证脚本

const fs = require('fs')
const path = require('path')

/**
 * 验证DeleteConfirmModal组件的shadcn重构
 */
function verifyDeleteConfirmModalRefactor() {
  console.log('🔍 开始验证DeleteConfirmModal shadcn重构...\n')

  const results = {
    passed: 0,
    failed: 0,
    details: []
  }

  // 检查项目列表
  const checks = [
    {
      name: '检查DeleteConfirmModal组件文件存在',
      check: () => fs.existsSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'))
    },
    {
      name: '检查shadcn AlertDialog导入',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return content.includes('from \'@/components/ui/alert-dialog\'') &&
               content.includes('AlertDialog,') &&
               content.includes('AlertDialogAction,') &&
               content.includes('AlertDialogCancel,') &&
               content.includes('AlertDialogContent,')
      }
    },
    {
      name: '检查shadcn Button导入',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return content.includes('from \'@/components/ui/button\'')
      }
    },
    {
      name: '检查移除了自定义模态窗口结构',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return !content.includes('fixed inset-0 z-50 overflow-y-auto') &&
               !content.includes('bg-black bg-opacity-50') &&
               !content.includes('flex min-h-full items-center justify-center')
      }
    },
    {
      name: '检查使用AlertDialog组件',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return content.includes('<AlertDialog') &&
               content.includes('<AlertDialogContent') &&
               content.includes('<AlertDialogHeader') &&
               content.includes('<AlertDialogFooter')
      }
    },
    {
      name: '检查使用destructive变体按钮',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return content.includes('bg-destructive') &&
               content.includes('text-destructive-foreground') &&
               content.includes('hover:bg-destructive/90')
      }
    },
    {
      name: '检查使用shadcn主题颜色变量',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return content.includes('text-destructive') &&
               content.includes('text-muted-foreground') &&
               content.includes('bg-muted/50') &&
               content.includes('text-foreground')
      }
    },
    {
      name: '检查移除了自定义CSS类',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return !content.includes('bg-white') &&
               !content.includes('text-gray-900') &&
               !content.includes('text-gray-500') &&
               !content.includes('border-gray-200')
      }
    },
    {
      name: '检查保持了原有功能逻辑',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return content.includes('handleConfirm') &&
               content.includes('isDeleting') &&
               content.includes('getBookmarkIcon') &&
               content.includes('getBookmarkTypeText') &&
               content.includes('formatDate')
      }
    },
    {
      name: '检查shadcn测试文件存在',
      check: () => fs.existsSync(path.join(__dirname, '../tests/DeleteConfirmModal.shadcn.test.tsx'))
    },
    {
      name: '检查演示组件存在',
      check: () => fs.existsSync(path.join(__dirname, '../src/components/examples/DeleteConfirmModalDemo.tsx'))
    },
    {
      name: '检查中文注释保持',
      check: () => {
        const content = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
        return content.includes('删除确认模态组件') &&
               content.includes('使用shadcn AlertDialog重构') &&
               content.includes('处理删除确认')
      }
    }
  ]

  // 执行检查
  checks.forEach((check, index) => {
    try {
      const passed = check.check()
      if (passed) {
        console.log(`✅ ${check.name}`)
        results.passed++
      } else {
        console.log(`❌ ${check.name}`)
        results.failed++
      }
      results.details.push({
        name: check.name,
        passed,
        index: index + 1
      })
    } catch (error) {
      console.log(`❌ ${check.name} (错误: ${error.message})`)
      results.failed++
      results.details.push({
        name: check.name,
        passed: false,
        error: error.message,
        index: index + 1
      })
    }
  })

  // 输出结果摘要
  console.log('\n📊 验证结果摘要:')
  console.log(`✅ 通过: ${results.passed}`)
  console.log(`❌ 失败: ${results.failed}`)
  console.log(`📈 成功率: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`)

  // 详细的shadcn重构检查
  console.log('\n🎨 shadcn重构特性检查:')
  
  try {
    const componentContent = fs.readFileSync(path.join(__dirname, '../src/components/DeleteConfirmModal.tsx'), 'utf8')
    
    // 检查shadcn组件使用
    const shadcnComponents = [
      'AlertDialog',
      'AlertDialogContent',
      'AlertDialogHeader',
      'AlertDialogTitle',
      'AlertDialogDescription',
      'AlertDialogFooter',
      'AlertDialogAction',
      'AlertDialogCancel'
    ]
    
    shadcnComponents.forEach(component => {
      if (componentContent.includes(`<${component}`)) {
        console.log(`✅ 使用了 ${component} 组件`)
      } else {
        console.log(`❌ 未使用 ${component} 组件`)
      }
    })
    
    // 检查shadcn主题变量使用
    console.log('\n🎨 shadcn主题变量使用:')
    const themeVariables = [
      'text-destructive',
      'bg-destructive',
      'text-muted-foreground',
      'bg-muted',
      'text-foreground',
      'bg-background',
      'text-primary',
      'bg-secondary',
      'text-secondary-foreground'
    ]
    
    themeVariables.forEach(variable => {
      if (componentContent.includes(variable)) {
        console.log(`✅ 使用了 ${variable} 主题变量`)
      }
    })
    
  } catch (error) {
    console.log(`❌ 读取组件文件时出错: ${error.message}`)
  }

  // 检查测试文件内容
  console.log('\n🧪 shadcn测试文件检查:')
  try {
    if (fs.existsSync(path.join(__dirname, '../tests/DeleteConfirmModal.shadcn.test.tsx'))) {
      const testContent = fs.readFileSync(path.join(__dirname, '../tests/DeleteConfirmModal.shadcn.test.tsx'), 'utf8')
      
      const testChecks = [
        { name: 'shadcn AlertDialog渲染测试', pattern: 'shadcn AlertDialog' },
        { name: 'destructive主题样式测试', pattern: 'destructive主题样式' },
        { name: 'shadcn主题颜色变量测试', pattern: 'shadcn的颜色类' },
        { name: 'AlertDialog交互测试', pattern: 'AlertDialog交互测试' },
        { name: '加载状态shadcn样式测试', pattern: '加载状态shadcn样式' }
      ]
      
      testChecks.forEach(testCheck => {
        if (testContent.includes(testCheck.pattern)) {
          console.log(`✅ 包含${testCheck.name}`)
        } else {
          console.log(`⚠️  缺少${testCheck.name}`)
        }
      })
    }
  } catch (error) {
    console.log(`❌ 读取测试文件时出错: ${error.message}`)
  }

  // 最终评估
  console.log('\n🎯 重构质量评估:')
  const successRate = (results.passed / (results.passed + results.failed)) * 100
  
  if (successRate >= 90) {
    console.log('🌟 优秀! DeleteConfirmModal shadcn重构质量很高')
  } else if (successRate >= 75) {
    console.log('👍 良好! DeleteConfirmModal shadcn重构基本完成，有少量改进空间')
  } else if (successRate >= 60) {
    console.log('⚠️  一般! DeleteConfirmModal shadcn重构需要进一步完善')
  } else {
    console.log('❌ 需要改进! DeleteConfirmModal shadcn重构存在较多问题')
  }

  return results
}

// 如果直接运行此脚本
if (require.main === module) {
  verifyDeleteConfirmModalRefactor()
}

module.exports = { verifyDeleteConfirmModalRefactor }