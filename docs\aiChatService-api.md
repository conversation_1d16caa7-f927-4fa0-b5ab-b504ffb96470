# AI对话服务API文档

## 概述

`AIChatService` 是一个专门处理与AI模型实际对话交互的服务类，支持本地AI服务和云端AI服务的统一对话接口。

## 文件位置

- **源文件**: `src/services/aiChatService.ts`
- **测试文件**: `tests/aiChatService.test.ts` (待创建)

## 类和接口

### AIChatService 类

主要的AI对话服务类，提供统一的对话接口。

```typescript
export class AIChatService {
  // 对话方法
  async chatWithLocalService(serviceUrl: string, modelId: string, messages: ChatMessage[], settings?: ChatSettings): Promise<ChatResponse>
  async chatWithCloudService(provider: AIProviderConfig, modelId: string, messages: ChatMessage[], settings?: ChatSettings): Promise<ChatResponse>
  
  // 工具方法
  async testModelConnection(serviceUrl: string | AIProviderConfig, modelId: string): Promise<boolean>
  async getAvailableModels(serviceUrl: string): Promise<string[]>
  async streamChat(serviceUrl: string | AIProviderConfig, modelId: string, messages: ChatMessage[], settings: ChatSettings, onChunk: (chunk: string) => void): Promise<void>
}
```

### 接口定义

#### ChatMessage 接口

```typescript
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'  // 消息角色
  content: string                        // 消息内容
}
```

#### ChatSettings 接口

```typescript
export interface ChatSettings {
  temperature?: number        // 温度参数 (0-2)
  maxTokens?: number         // 最大令牌数
  stream?: boolean           // 是否流式输出
  topP?: number             // Top-P 采样参数
  frequencyPenalty?: number // 频率惩罚
  presencePenalty?: number  // 存在惩罚
}
```

#### ChatResponse 接口

```typescript
export interface ChatResponse {
  content: string           // 回复内容
  usage?: {                // 使用统计
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model?: string           // 使用的模型
  finishReason?: string    // 完成原因
}
```

## 核心方法

### chatWithLocalService()

与本地AI服务进行对话。

**函数签名**:
```typescript
async chatWithLocalService(
  serviceUrl: string,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings = {}
): Promise<ChatResponse>
```

**参数**:
- `serviceUrl` (string): 本地AI服务的URL地址
- `modelId` (string): 要使用的模型ID
- `messages` (ChatMessage[]): 对话消息历史
- `settings` (ChatSettings): 对话设置参数

**返回值**: Promise<ChatResponse> - 对话响应

**支持的本地服务**:
- Ollama (端口11434) - 使用Ollama原生API格式
- LM Studio (端口1234) - 使用OpenAI兼容格式
- LocalAI (端口8080) - 使用OpenAI兼容格式
- 其他OpenAI兼容服务

**异常处理**:
- 网络连接错误
- HTTP状态错误
- 响应解析错误
- 超时错误 (60秒)

### chatWithCloudService()

与云端AI服务进行对话。

**函数签名**:
```typescript
async chatWithCloudService(
  provider: AIProviderConfig,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings = {}
): Promise<ChatResponse>
```

**参数**:
- `provider` (AIProviderConfig): AI提供商配置
- `modelId` (string): 要使用的模型ID
- `messages` (ChatMessage[]): 对话消息历史
- `settings` (ChatSettings): 对话设置参数

**返回值**: Promise<ChatResponse> - 对话响应

**支持的云端服务**:
- OpenAI - 标准OpenAI API格式
- Claude - Anthropic API格式
- Gemini - Google AI API格式
- DeepSeek - OpenAI兼容格式
- 智谱AI - OpenAI兼容格式
- 通义千问 - OpenAI兼容格式

### testModelConnection()

测试模型连接状态。

**函数签名**:
```typescript
async testModelConnection(
  serviceUrl: string | AIProviderConfig,
  modelId: string
): Promise<boolean>
```

**参数**:
- `serviceUrl` (string | AIProviderConfig): 服务URL或提供商配置
- `modelId` (string): 模型ID

**返回值**: Promise<boolean> - 连接是否成功

**测试方法**:
- 发送简单的测试消息
- 验证响应格式
- 检查连接稳定性

### getAvailableModels()

获取服务的可用模型列表。

**函数签名**:
```typescript
async getAvailableModels(serviceUrl: string): Promise<string[]>
```

**参数**:
- `serviceUrl` (string): 服务URL地址

**返回值**: Promise<string[]> - 可用模型ID列表

**支持的端点**:
- Ollama: `/api/tags`
- OpenAI兼容: `/models` 或 `/v1/models`

### streamChat()

流式对话功能（暂未实现）。

**函数签名**:
```typescript
async streamChat(
  serviceUrl: string | AIProviderConfig,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings,
  onChunk: (chunk: string) => void
): Promise<void>
```

**状态**: 🚧 待实现

## 私有方法

### buildRequestForLocalService()

构建本地服务的请求数据。

**功能**:
- 自动检测服务类型
- 适配不同的API格式
- 处理参数映射

### buildRequestForCloudService()

构建云端服务的请求数据。

**功能**:
- 根据提供商类型调整格式
- 处理特定API参数
- 统一请求结构

### parseLocalServiceResponse()

解析本地服务响应。

**支持格式**:
- Ollama原生格式
- OpenAI兼容格式

### parseCloudServiceResponse()

解析云端服务响应。

**支持格式**:
- OpenAI标准格式
- 其他兼容格式

## 错误处理

### 常见错误类型

1. **网络连接错误**
   - ECONNREFUSED: 服务未运行
   - 超时错误: 服务响应缓慢

2. **HTTP错误**
   - 401: 认证失败
   - 404: 端点不存在
   - 500: 服务器内部错误

3. **API错误**
   - 模型不存在
   - 参数无效
   - 配额超限

4. **响应解析错误**
   - 格式不匹配
   - 数据缺失

### 错误处理策略

```typescript
try {
  const response = await aiChatService.chatWithLocalService(url, model, messages)
  // 处理成功响应
} catch (error) {
  if (error.message.includes('ECONNREFUSED')) {
    // 服务未运行
  } else if (error.message.includes('HTTP 401')) {
    // 认证失败
  } else if (error.message.includes('超时')) {
    // 连接超时
  } else {
    // 其他错误
  }
}
```

## 使用示例

### 基本对话示例

```typescript
import { aiChatService, ChatMessage } from '../services/aiChatService'

// 准备对话消息
const messages: ChatMessage[] = [
  { role: 'system', content: '你是一个有用的AI助手。' },
  { role: 'user', content: '你好，请介绍一下自己。' }
]

// 与本地Ollama服务对话
try {
  const response = await aiChatService.chatWithLocalService(
    'http://localhost:11434',
    'llama2:7b',
    messages,
    {
      temperature: 0.7,
      maxTokens: 1000
    }
  )
  
  console.log('AI回复:', response.content)
  console.log('使用的模型:', response.model)
  console.log('令牌使用:', response.usage)
} catch (error) {
  console.error('对话失败:', error.message)
}
```

### 云端服务对话示例

```typescript
import { aiIntegrationService } from '../services/aiIntegrationService'

// 获取配置的提供商
const providers = await aiIntegrationService.getConfiguredProviders()
const openaiProvider = providers.find(p => p.type === 'openai')

if (openaiProvider) {
  const messages: ChatMessage[] = [
    { role: 'user', content: '解释一下量子计算的基本原理。' }
  ]
  
  try {
    const response = await aiChatService.chatWithCloudService(
      openaiProvider,
      'gpt-3.5-turbo',
      messages,
      {
        temperature: 0.3,
        maxTokens: 500
      }
    )
    
    console.log('GPT回复:', response.content)
  } catch (error) {
    console.error('对话失败:', error.message)
  }
}
```

### 连接测试示例

```typescript
// 测试本地服务连接
const isOllamaConnected = await aiChatService.testModelConnection(
  'http://localhost:11434',
  'llama2:7b'
)

console.log('Ollama连接状态:', isOllamaConnected ? '正常' : '失败')

// 测试云端服务连接
if (openaiProvider) {
  const isOpenAIConnected = await aiChatService.testModelConnection(
    openaiProvider,
    'gpt-3.5-turbo'
  )
  
  console.log('OpenAI连接状态:', isOpenAIConnected ? '正常' : '失败')
}
```

### 获取模型列表示例

```typescript
// 获取Ollama可用模型
const ollamaModels = await aiChatService.getAvailableModels('http://localhost:11434')
console.log('Ollama模型:', ollamaModels)

// 获取LM Studio可用模型
const lmStudioModels = await aiChatService.getAvailableModels('http://localhost:1234/v1')
console.log('LM Studio模型:', lmStudioModels)
```

### 多轮对话示例

```typescript
class ChatSession {
  private messages: ChatMessage[] = []
  
  constructor(systemPrompt?: string) {
    if (systemPrompt) {
      this.messages.push({ role: 'system', content: systemPrompt })
    }
  }
  
  async sendMessage(content: string): Promise<string> {
    // 添加用户消息
    this.messages.push({ role: 'user', content })
    
    try {
      const response = await aiChatService.chatWithLocalService(
        'http://localhost:11434',
        'llama2:7b',
        this.messages
      )
      
      // 添加AI回复到历史
      this.messages.push({ role: 'assistant', content: response.content })
      
      return response.content
    } catch (error) {
      throw new Error(`对话失败: ${error.message}`)
    }
  }
  
  getHistory(): ChatMessage[] {
    return [...this.messages]
  }
  
  clearHistory(): void {
    this.messages = this.messages.filter(msg => msg.role === 'system')
  }
}

// 使用示例
const chat = new ChatSession('你是一个编程助手，专门帮助解决代码问题。')

const reply1 = await chat.sendMessage('如何在JavaScript中实现深拷贝？')
console.log('回复1:', reply1)

const reply2 = await chat.sendMessage('能给个具体的代码示例吗？')
console.log('回复2:', reply2)
```

## 配置参数说明

### 温度 (Temperature)

- **范围**: 0.0 - 2.0
- **默认值**: 0.7
- **说明**: 控制输出的随机性
  - 0.0: 最确定性的输出
  - 1.0: 平衡的创造性
  - 2.0: 最高的随机性

### 最大令牌数 (Max Tokens)

- **范围**: 1 - 模型上限
- **默认值**: 2048
- **说明**: 限制生成文本的最大长度

### Top-P 采样

- **范围**: 0.0 - 1.0
- **默认值**: 0.9
- **说明**: 核采样参数，控制词汇选择范围

### 频率惩罚 (Frequency Penalty)

- **范围**: -2.0 - 2.0
- **默认值**: 0.0
- **说明**: 减少重复词汇的出现

### 存在惩罚 (Presence Penalty)

- **范围**: -2.0 - 2.0
- **默认值**: 0.0
- **说明**: 鼓励谈论新话题

## 性能优化

### 连接池管理

```typescript
// 建议实现连接池以提高性能
class ConnectionPool {
  private connections = new Map<string, any>()
  
  getConnection(serviceUrl: string) {
    // 复用连接逻辑
  }
}
```

### 缓存策略

```typescript
// 缓存模型列表以减少API调用
const modelCache = new Map<string, { models: string[], expiry: number }>()
```

### 超时设置

- **本地服务**: 60秒 (可能需要较长时间生成)
- **云端服务**: 根据提供商配置 (默认60秒)
- **连接测试**: 10秒
- **模型列表**: 10秒

## 安全考虑

### API密钥保护

- 云端服务的API密钥通过 `AIProviderConfig` 传递
- 不在日志中记录敏感信息
- 使用HTTPS连接云端服务

### 输入验证

- 验证消息内容长度
- 检查参数范围
- 防止注入攻击

### 错误信息

- 不暴露内部实现细节
- 提供用户友好的错误信息
- 记录详细错误用于调试

## 扩展性

### 添加新的AI服务

1. 在 `buildRequestForLocalService` 中添加服务检测逻辑
2. 在 `buildRequestForCloudService` 中添加提供商类型
3. 更新响应解析逻辑
4. 添加相应的测试用例

### 支持新的API格式

1. 扩展 `ChatMessage` 接口
2. 更新请求构建方法
3. 添加响应解析器
4. 更新文档和示例

## 相关文档

- [AI集成服务API](./aiIntegrationService-api.md)
- [AI提供商服务API](./aiProviderService-api.md)
- [本地AI服务适配器API](./localAIServiceAdapter-api.md)
- [AI模型服务API](./aiModelService-api.md)
- [AI集成测试指南](./local-ai-service-test-guide.md)

## 版本历史

- **v1.0.0** (2024-12-17): 初始版本
  - 支持本地和云端AI服务对话
  - 实现连接测试功能
  - 支持模型列表获取
  - 提供统一的错误处理