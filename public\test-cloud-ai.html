<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云端AI服务集成测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1, h2 {
            color: #333;
            margin-top: 0;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #005a9e;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .info {
            color: #17a2b8;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007acc;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .provider-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        
        .provider-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .provider-name {
            font-weight: bold;
            font-size: 16px;
        }
        
        .provider-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-unknown {
            background: #fff3cd;
            color: #856404;
        }
        
        .provider-details {
            font-size: 14px;
            color: #666;
        }
        
        .model-list {
            margin-top: 10px;
        }
        
        .model-item {
            padding: 8px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #007acc;
        }
        
        .model-name {
            font-weight: bold;
            color: #333;
        }
        
        .model-details {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007acc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .input-group {
            margin: 10px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .flex {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .tabs {
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
        }
        
        .tab-button.active {
            color: #007acc;
            border-bottom-color: #007acc;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>☁️ 云端AI服务集成测试</h1>
        <p>这个页面用于测试云端AI服务的配置、连接和对话功能。</p>
    </div>

    <div class="container">
        <div class="tabs">
            <button class="tab-button active" onclick="showTab('providers')">提供商管理</button>
            <button class="tab-button" onclick="showTab('testing')">模型测试</button>
            <button class="tab-button" onclick="showTab('results')">测试结果</button>
        </div>

        <!-- 提供商管理标签页 -->
        <div id="providers" class="tab-content active">
            <div class="section">
                <h2>🔧 AI提供商配置</h2>
                <p>添加和管理云端AI服务提供商。</p>
                
                <div class="grid">
                    <div class="input-group">
                        <label>提供商类型:</label>
                        <select id="providerType" onchange="updateProviderDefaults()">
                            <option value="">选择提供商类型</option>
                            <option value="openai">OpenAI</option>
                            <option value="claude">Anthropic Claude</option>
                            <option value="gemini">Google Gemini</option>
                            <option value="deepseek">DeepSeek</option>
                            <option value="zhipu">智谱AI</option>
                            <option value="qwen">通义千问</option>
                            <option value="custom">自定义API</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label>提供商名称:</label>
                        <input type="text" id="providerName" placeholder="例如: My OpenAI" />
                    </div>
                    
                    <div class="input-group">
                        <label>API基础URL:</label>
                        <input type="text" id="baseUrl" placeholder="例如: https://api.openai.com/v1" />
                    </div>
                    
                    <div class="input-group">
                        <label>API密钥:</label>
                        <input type="password" id="apiKey" placeholder="输入API密钥" />
                    </div>
                </div>
                
                <div class="flex">
                    <button onclick="addProvider()">➕ 添加提供商</button>
                    <button onclick="loadProviders()">🔄 刷新列表</button>
                    <button onclick="testAllProviders()">🧪 测试所有</button>
                </div>
                
                <div id="providerResult" class="result" style="display: none;"></div>
            </div>

            <div class="section">
                <h2>📊 已配置的提供商</h2>
                <div id="providersPanel">
                    <p class="info">点击"刷新列表"加载已配置的提供商...</p>
                </div>
            </div>
        </div>

        <!-- 模型测试标签页 -->
        <div id="testing" class="tab-content">
            <div class="section">
                <h2>🧪 模型对话测试</h2>
                <p>选择提供商和模型进行对话测试。</p>
                
                <div class="grid">
                    <div class="input-group">
                        <label>选择提供商:</label>
                        <select id="testProvider" onchange="loadProviderModels()">
                            <option value="">请选择提供商</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label>选择模型:</label>
                        <select id="testModel">
                            <option value="">请先选择提供商</option>
                        </select>
                    </div>
                </div>
                
                <div class="input-group">
                    <label>测试消息:</label>
                    <input type="text" id="testMessage" placeholder="输入要发送给AI的测试消息" 
                           value="你好，这是一个连接测试。请简单回复'连接成功'。" />
                </div>
                
                <button onclick="testModelChat()">💬 发送测试消息</button>
                
                <div id="testingResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <!-- 测试结果标签页 */
        <div id="results" class="tab-content">
            <div class="section">
                <h2>📈 测试结果</h2>
                <div class="flex">
                    <button onclick="clearResults()">🗑️ 清空结果</button>
                    <button onclick="exportResults()">📥 导出结果</button>
                </div>
                
                <div id="resultsPanel">
                    <p class="info">还没有测试结果...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let configuredProviders = [];
        let testResults = [];
        let currentTab = 'providers';

        // 提供商默认配置
        const providerDefaults = {
            openai: {
                name: 'OpenAI',
                baseUrl: 'https://api.openai.com/v1'
            },
            claude: {
                name: 'Anthropic Claude',
                baseUrl: 'https://api.anthropic.com/v1'
            },
            gemini: {
                name: 'Google Gemini',
                baseUrl: 'https://generativelanguage.googleapis.com/v1'
            },
            deepseek: {
                name: 'DeepSeek',
                baseUrl: 'https://api.deepseek.com'
            },
            zhipu: {
                name: '智谱AI',
                baseUrl: 'https://open.bigmodel.cn/api/paas/v4'
            },
            qwen: {
                name: '通义千问',
                baseUrl: 'https://dashscope.aliyuncs.com/api/v1'
            },
            custom: {
                name: '自定义API',
                baseUrl: ''
            }
        };

        // 模拟模型数据
        const mockModels = {
            openai: [
                { id: 'gpt-4', name: 'GPT-4', description: 'OpenAI最先进的大型语言模型' },
                { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'OpenAI高效的对话模型' }
            ],
            claude: [
                { id: 'claude-3-opus', name: 'Claude 3 Opus', description: 'Anthropic最强大的AI模型' },
                { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', description: 'Anthropic平衡性能的AI模型' }
            ],
            gemini: [
                { id: 'gemini-pro', name: 'Gemini Pro', description: 'Google先进的多模态AI模型' }
            ],
            deepseek: [
                { id: 'deepseek-chat', name: 'DeepSeek Chat', description: 'DeepSeek对话模型' }
            ],
            zhipu: [
                { id: 'glm-4', name: 'GLM-4', description: '智谱AI最新一代对话模型' }
            ],
            qwen: [
                { id: 'qwen-turbo', name: '通义千问 Turbo', description: '阿里云通义千问高效模型' }
            ]
        };

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // 更新提供商默认值
        function updateProviderDefaults() {
            const type = document.getElementById('providerType').value;
            if (type && providerDefaults[type]) {
                document.getElementById('providerName').value = providerDefaults[type].name;
                document.getElementById('baseUrl').value = providerDefaults[type].baseUrl;
            }
        }

        // 添加提供商
        function addProvider() {
            const type = document.getElementById('providerType').value;
            const name = document.getElementById('providerName').value;
            const baseUrl = document.getElementById('baseUrl').value;
            const apiKey = document.getElementById('apiKey').value;

            if (!type || !name || !baseUrl) {
                showResult('providerResult', '❌ 请填写完整的提供商信息', 'error');
                return;
            }

            // 模拟添加提供商
            const provider = {
                id: `${type}_${Date.now()}`,
                type,
                name,
                baseUrl,
                apiKey: apiKey || undefined,
                enabled: true,
                createdAt: new Date()
            };

            configuredProviders.push(provider);
            
            showResult('providerResult', `✅ 添加提供商成功: ${name}`, 'success');
            
            // 清空表单
            document.getElementById('providerType').value = '';
            document.getElementById('providerName').value = '';
            document.getElementById('baseUrl').value = '';
            document.getElementById('apiKey').value = '';
            
            // 刷新提供商列表
            updateProvidersPanel();
            updateTestProviderSelect();
        }

        // 加载提供商列表
        function loadProviders() {
            showResult('providerResult', '🔄 正在加载提供商列表...', 'info');
            
            // 模拟加载延迟
            setTimeout(() => {
                showResult('providerResult', `✅ 加载完成，共 ${configuredProviders.length} 个提供商`, 'success');
                updateProvidersPanel();
                updateTestProviderSelect();
            }, 1000);
        }

        // 测试所有提供商
        async function testAllProviders() {
            if (configuredProviders.length === 0) {
                showResult('providerResult', '❌ 没有配置的提供商可供测试', 'error');
                return;
            }

            showResult('providerResult', '🧪 开始测试所有提供商连接...', 'info');
            
            for (const provider of configuredProviders) {
                await testProviderConnection(provider);
            }
            
            showResult('providerResult', '✅ 所有提供商测试完成', 'success');
            updateProvidersPanel();
        }

        // 测试单个提供商连接
        async function testProviderConnection(provider) {
            return new Promise(resolve => {
                // 模拟连接测试
                setTimeout(() => {
                    const success = Math.random() > 0.3; // 70% 成功率
                    const responseTime = Math.floor(Math.random() * 2000) + 100;
                    
                    provider.status = {
                        success,
                        responseTime,
                        error: success ? undefined : '连接超时或API密钥无效',
                        testedAt: new Date()
                    };
                    
                    resolve(provider.status);
                }, Math.random() * 1000 + 500);
            });
        }

        // 更新提供商面板
        function updateProvidersPanel() {
            const panel = document.getElementById('providersPanel');
            
            if (configuredProviders.length === 0) {
                panel.innerHTML = '<p class="info">还没有配置任何提供商。</p>';
                return;
            }

            let html = '';
            
            configuredProviders.forEach(provider => {
                const status = provider.status;
                const statusClass = status ? (status.success ? 'status-online' : 'status-offline') : 'status-unknown';
                const statusText = status ? (status.success ? '在线' : '离线') : '未测试';
                
                html += `
                    <div class="provider-card">
                        <div class="provider-header">
                            <div class="provider-name">${provider.name}</div>
                            <div class="provider-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="provider-details">
                            <div>类型: ${provider.type}</div>
                            <div>地址: ${provider.baseUrl}</div>
                            ${status ? `
                                <div>响应时间: ${status.responseTime || 0}ms</div>
                                <div>测试时间: ${status.testedAt.toLocaleTimeString()}</div>
                                ${status.error ? `<div class="error">错误: ${status.error}</div>` : ''}
                            ` : ''}
                        </div>
                        <div style="margin-top: 10px;">
                            <button onclick="testSingleProvider('${provider.id}')">🧪 测试连接</button>
                            <button onclick="loadModels('${provider.id}')">📦 获取模型</button>
                            <button onclick="removeProvider('${provider.id}')" style="background: #dc3545;">🗑️ 删除</button>
                        </div>
                    </div>
                `;
            });
            
            panel.innerHTML = html;
        }

        // 测试单个提供商
        async function testSingleProvider(providerId) {
            const provider = configuredProviders.find(p => p.id === providerId);
            if (!provider) return;

            showResult('providerResult', `🧪 正在测试 ${provider.name}...`, 'info');
            
            await testProviderConnection(provider);
            
            const status = provider.status;
            if (status.success) {
                showResult('providerResult', `✅ ${provider.name} 连接成功 (${status.responseTime}ms)`, 'success');
            } else {
                showResult('providerResult', `❌ ${provider.name} 连接失败: ${status.error}`, 'error');
            }
            
            updateProvidersPanel();
        }

        // 加载模型列表
        function loadModels(providerId) {
            const provider = configuredProviders.find(p => p.id === providerId);
            if (!provider) return;

            showResult('providerResult', `📦 正在获取 ${provider.name} 模型列表...`, 'info');
            
            // 模拟获取模型列表
            setTimeout(() => {
                const models = mockModels[provider.type] || [];
                provider.models = models;
                
                showResult('providerResult', `✅ ${provider.name} 找到 ${models.length} 个模型`, 'success');
                updateProvidersPanel();
            }, 1000);
        }

        // 删除提供商
        function removeProvider(providerId) {
            const provider = configuredProviders.find(p => p.id === providerId);
            if (!provider) return;

            if (!confirm(`确定要删除提供商 "${provider.name}" 吗？`)) {
                return;
            }

            configuredProviders = configuredProviders.filter(p => p.id !== providerId);
            showResult('providerResult', `✅ 删除提供商成功: ${provider.name}`, 'success');
            
            updateProvidersPanel();
            updateTestProviderSelect();
        }

        // 更新测试提供商选择器
        function updateTestProviderSelect() {
            const select = document.getElementById('testProvider');
            select.innerHTML = '<option value="">请选择提供商</option>';
            
            configuredProviders
                .filter(p => p.enabled && p.status?.success)
                .forEach(provider => {
                    const option = document.createElement('option');
                    option.value = provider.id;
                    option.textContent = provider.name;
                    select.appendChild(option);
                });
        }

        // 加载提供商模型
        function loadProviderModels() {
            const providerId = document.getElementById('testProvider').value;
            const modelSelect = document.getElementById('testModel');
            
            modelSelect.innerHTML = '<option value="">请选择模型</option>';
            
            if (!providerId) return;
            
            const provider = configuredProviders.find(p => p.id === providerId);
            if (!provider || !provider.models) {
                modelSelect.innerHTML = '<option value="">请先获取模型列表</option>';
                return;
            }
            
            provider.models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                modelSelect.appendChild(option);
            });
        }

        // 测试模型对话
        function testModelChat() {
            const providerId = document.getElementById('testProvider').value;
            const modelId = document.getElementById('testModel').value;
            const message = document.getElementById('testMessage').value;

            if (!providerId || !modelId || !message.trim()) {
                showResult('testingResult', '❌ 请选择提供商、模型并输入测试消息', 'error');
                return;
            }

            const provider = configuredProviders.find(p => p.id === providerId);
            const model = provider.models.find(m => m.id === modelId);
            
            if (!provider || !model) {
                showResult('testingResult', '❌ 未找到选中的提供商或模型', 'error');
                return;
            }

            showResult('testingResult', `💬 正在测试 ${provider.name} - ${model.name}...`, 'info');
            
            // 模拟对话测试
            const startTime = Date.now();
            
            setTimeout(() => {
                const success = Math.random() > 0.2; // 80% 成功率
                const responseTime = Date.now() - startTime;
                
                const result = {
                    providerId: provider.id,
                    providerName: provider.name,
                    modelId: model.id,
                    modelName: model.name,
                    success,
                    responseTime,
                    response: success ? `连接成功！我是 ${model.name}，很高兴为您服务。` : undefined,
                    error: success ? undefined : 'API调用失败或模型不可用',
                    timestamp: new Date()
                };
                
                testResults.unshift(result);
                
                if (success) {
                    showResult('testingResult', `✅ 对话测试成功 (${responseTime}ms)\n回复: ${result.response}`, 'success');
                } else {
                    showResult('testingResult', `❌ 对话测试失败 (${responseTime}ms)\n错误: ${result.error}`, 'error');
                }
                
                updateResultsPanel();
            }, Math.random() * 2000 + 1000);
        }

        // 更新结果面板
        function updateResultsPanel() {
            const panel = document.getElementById('resultsPanel');
            
            if (testResults.length === 0) {
                panel.innerHTML = '<p class="info">还没有测试结果...</p>';
                return;
            }

            let html = '';
            
            testResults.forEach((result, index) => {
                const statusClass = result.success ? 'success' : 'error';
                const statusIcon = result.success ? '✅' : '❌';
                
                html += `
                    <div class="provider-card" style="border-left: 4px solid ${result.success ? '#28a745' : '#dc3545'};">
                        <div class="provider-header">
                            <div class="provider-name">${statusIcon} ${result.providerName} - ${result.modelName}</div>
                            <div class="provider-status ${result.success ? 'status-online' : 'status-offline'}">
                                ${result.success ? '成功' : '失败'}
                            </div>
                        </div>
                        <div class="provider-details">
                            <div>响应时间: ${result.responseTime}ms</div>
                            <div>测试时间: ${result.timestamp.toLocaleTimeString()}</div>
                            ${result.success && result.response ? `
                                <div style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                    <strong>AI回复:</strong><br>${result.response}
                                </div>
                            ` : ''}
                            ${!result.success && result.error ? `
                                <div style="margin-top: 10px; padding: 10px; background: #f8d7da; border-radius: 4px; color: #721c24;">
                                    <strong>错误信息:</strong><br>${result.error}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });
            
            panel.innerHTML = html;
        }

        // 清空结果
        function clearResults() {
            testResults = [];
            updateResultsPanel();
            showResult('testingResult', '🗑️ 测试结果已清空', 'info');
        }

        // 导出结果
        function exportResults() {
            if (testResults.length === 0) {
                alert('没有测试结果可导出');
                return;
            }

            const exportData = {
                exportedAt: new Date().toISOString(),
                totalResults: testResults.length,
                results: testResults
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `cloud-ai-test-results-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            URL.revokeObjectURL(url);
            alert('测试结果已导出');
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = message;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('云端AI服务测试页面已加载');
            
            // 添加一些示例提供商用于演示
            configuredProviders = [
                {
                    id: 'demo_openai',
                    type: 'openai',
                    name: '演示 OpenAI',
                    baseUrl: 'https://api.openai.com/v1',
                    enabled: true,
                    createdAt: new Date(),
                    models: mockModels.openai
                },
                {
                    id: 'demo_claude',
                    type: 'claude',
                    name: '演示 Claude',
                    baseUrl: 'https://api.anthropic.com/v1',
                    enabled: true,
                    createdAt: new Date(),
                    models: mockModels.claude
                }
            ];
            
            updateProvidersPanel();
            updateTestProviderSelect();
        });
    </script>
</body>
</html>