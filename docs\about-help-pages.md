# 关于我们和帮助中心页面功能文档

## 概述

本文档描述了为 Universe Bag（乾坤袋）扩展添加的"关于我们"和"帮助中心"页面功能。这两个页面为用户提供了扩展信息展示和使用帮助功能。

## 功能特性

### 关于我们页面

- **扩展信息展示**：自动从 manifest.json 获取扩展名称、版本、描述等信息
- **开发者信息**：显示开发团队信息和联系方式
- **技术信息**：展示运行环境、构建版本、Manifest 版本等技术详情
- **权限详情**：列出扩展所需的所有权限及其说明
- **许可证信息**：显示软件许可证类型和相关链接

### 帮助中心页面

- **分类浏览**：按使用指南、常见问题、故障排除分类展示帮助内容
- **搜索功能**：支持全文搜索，包含搜索建议和历史记录
- **内容展开/折叠**：支持单个或批量展开/折叠帮助内容
- **快速导航**：提供章节锚点导航功能
- **联系支持**：提供技术支持联系方式

## 技术架构

### 组件结构

```
src/options/
├── components/
│   ├── AboutTab.tsx              # 关于我们页面组件
│   ├── HelpCenterTab.tsx         # 帮助中心页面组件
│   ├── HelpSearchBox.tsx         # 帮助搜索组件
│   ├── ThemeToggle.tsx           # 主题切换组件
│   └── PageErrorBoundary.tsx     # 错误边界组件
├── data/
│   ├── aboutInfo.ts              # 关于页面数据配置
│   └── helpContent.ts            # 帮助内容数据
├── hooks/
│   ├── useTheme.ts               # 主题管理 Hook
│   ├── useResponsive.ts          # 响应式设计 Hook
│   ├── useLazyLoad.ts            # 懒加载 Hook
│   └── useCache.ts               # 缓存管理 Hook
└── utils/
    ├── manifestReader.ts         # manifest.json 读取工具
    ├── helpSearch.ts             # 帮助内容搜索工具
    └── performance.ts            # 性能监控工具
```

### 数据流

1. **关于页面数据流**：
   - 组件挂载 → 读取 manifest.json → 获取扩展信息 → 渲染页面
   - 错误处理：读取失败时使用默认数据确保页面正常显示

2. **帮助中心数据流**：
   - 组件挂载 → 加载帮助内容 → 渲染分类和内容列表
   - 搜索流程：用户输入 → 防抖处理 → 缓存检查 → 执行搜索 → 显示结果

## 使用指南

### 添加新的帮助内容

1. 编辑 `src/options/data/helpContent.ts` 文件
2. 在 `helpSections` 数组中添加新的章节：

```typescript
{
  id: 'new-section',
  title: '新功能说明',
  content: `
# 新功能说明

这里是新功能的详细说明...

## 使用步骤

1. 第一步操作
2. 第二步操作
3. 第三步操作
  `,
  category: 'guide', // 或 'faq', 'troubleshooting'
  keywords: ['新功能', '使用', '说明']
}
```

3. 如需添加新分类，在 `helpCategories` 数组中添加：

```typescript
{
  id: 'new-category',
  name: '新分类',
  description: '新分类的描述',
  icon: 'icon-name', // 对应 lucide-react 图标名
  order: 4
}
```

### 更新关于页面信息

1. 编辑 `src/options/data/aboutInfo.ts` 文件
2. 修改 `defaultAboutData` 对象中的相应字段：

```typescript
export const defaultAboutData: AboutPageData = {
  // ... 其他字段
  developerInfo: {
    name: '新的开发团队名称',
    website: 'https://new-website.com',
    email: '<EMAIL>'
  },
  licenseInfo: {
    type: '新的许可证类型',
    text: '新的许可证说明',
    url: 'https://new-license-url.com'
  }
}
```

### 自定义主题

1. 主题配置在 `src/options/hooks/useTheme.ts` 中
2. 支持三种主题模式：
   - `light`：浅色主题
   - `dark`：深色主题
   - `system`：跟随系统主题

3. 主题切换组件使用：

```tsx
import ThemeToggle from '../components/ThemeToggle'

// 基本使用
<ThemeToggle />

// 带标签的下拉选择
<ThemeToggle showLabel={true} />

// 不同尺寸
<ThemeToggle size="sm" /> // 小尺寸
<ThemeToggle size="lg" /> // 大尺寸
```

## 性能优化

### 懒加载

帮助内容支持懒加载，减少初始加载时间：

```typescript
import { useLazyLoad } from '../hooks/useLazyLoad'

const { elementRef, isVisible } = useLazyLoad({
  threshold: 0.1,
  rootMargin: '50px',
  triggerOnce: true
})
```

### 搜索缓存

搜索结果会自动缓存，提升搜索性能：

```typescript
import { useSearchCache } from '../hooks/useCache'

const { getCachedResults, setCachedResults } = useSearchCache(50) // 最多缓存50个搜索结果
```

### 性能监控

开发环境下自动启用性能监控：

```typescript
import { performanceMonitor } from '../utils/performance'

// 记录性能指标
performanceMonitor.record('component.render', renderTime, 'timing')

// 测量函数执行时间
const result = await performanceMonitor.measure('search.execute', () => {
  return searchFunction()
})
```

## 响应式设计

### 断点配置

使用 Tailwind CSS 兼容的断点：

- `sm`: 640px
- `md`: 768px  
- `lg`: 1024px
- `xl`: 1280px
- `2xl`: 1536px

### 响应式 Hook 使用

```typescript
import { useResponsive } from '../hooks/useResponsive'

const { isMobile, isTablet, breakpoint, getResponsiveValue } = useResponsive()

// 根据屏幕尺寸返回不同值
const columns = getResponsiveValue({
  sm: 1,
  md: 2,
  lg: 3,
  xl: 4
}, 1)
```

## 错误处理

### 错误边界

所有标签页都被错误边界包装，确保单个页面错误不影响整个应用：

```typescript
import PageErrorBoundary from '../components/PageErrorBoundary'

<PageErrorBoundary
  onError={(error, errorInfo) => {
    console.error('页面错误:', error, errorInfo)
  }}
  maxRetries={2}
>
  <YourComponent />
</PageErrorBoundary>
```

### 数据加载错误处理

- **关于页面**：manifest.json 读取失败时使用默认数据
- **帮助中心**：内容加载失败时显示错误提示和基本帮助信息

## 测试

### 单元测试

运行单元测试：

```bash
npm test -- --testPathPattern="about-help-pages"
```

### 集成测试

运行集成测试：

```bash
npm test -- tests/options/integration/about-help-pages.test.tsx
```

### 端到端测试

使用 Playwright 运行 E2E 测试：

```bash
npx playwright test tests/e2e/about-help-pages.spec.ts
```

## 维护指南

### 定期维护任务

1. **更新帮助内容**：根据功能更新及时更新帮助文档
2. **检查外部链接**：定期验证邮箱、网站等外部链接的有效性
3. **性能监控**：关注性能指标，及时优化慢查询和渲染问题
4. **错误监控**：检查错误日志，修复用户反馈的问题

### 版本更新流程

1. 更新 `manifest.json` 中的版本号
2. 更新 `aboutInfo.ts` 中的默认数据（如需要）
3. 添加新功能的帮助内容
4. 运行完整测试套件
5. 更新文档

### 故障排除

#### 常见问题

1. **关于页面显示"开发环境"**
   - 原因：Chrome 扩展 API 不可用
   - 解决：确保在扩展环境中运行，或检查 manifest.json 配置

2. **帮助内容搜索无结果**
   - 原因：搜索索引未正确构建
   - 解决：检查 `helpContent.ts` 中的 keywords 配置

3. **主题切换不生效**
   - 原因：CSS 类名未正确应用
   - 解决：检查 Tailwind CSS 的 dark 模式配置

4. **页面加载缓慢**
   - 原因：帮助内容过多或搜索算法效率低
   - 解决：启用懒加载或优化搜索算法

#### 调试工具

1. **性能监控**：在开发者工具中查看 `[Performance]` 日志
2. **缓存状态**：使用 `cache.size()` 检查缓存使用情况
3. **错误边界**：查看错误边界捕获的错误信息

## 扩展开发

### 添加新的标签页

1. 创建新的组件文件
2. 在 `OptionsApp.tsx` 中添加标签页配置
3. 更新路由处理逻辑
4. 添加相应的测试

### 集成外部服务

可以集成以下外部服务：

- **错误监控**：Sentry、Bugsnag
- **分析服务**：Google Analytics
- **帮助内容 CMS**：Contentful、Strapi
- **用户反馈**：Intercom、Zendesk

## 最佳实践

1. **性能优先**：使用懒加载、缓存等技术优化性能
2. **用户体验**：提供清晰的错误提示和加载状态
3. **可访问性**：支持键盘导航和屏幕阅读器
4. **响应式设计**：确保在各种设备上都有良好体验
5. **错误处理**：优雅处理各种异常情况
6. **测试覆盖**：保持高测试覆盖率
7. **文档维护**：及时更新文档和帮助内容

## 贡献指南

1. Fork 项目仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'feat: 添加新功能'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

请确保：
- 遵循现有的代码风格
- 添加适当的测试
- 更新相关文档
- 通过所有测试检查