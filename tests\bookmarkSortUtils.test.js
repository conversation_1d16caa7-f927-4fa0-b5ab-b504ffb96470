// BookmarkSortUtils 单元测试

const { describe, it, expect, beforeEach } = require('./test-framework')

// 模拟 BookmarkSortUtils
const mockBookmarkSortUtils = {
  sortBookmarks: (bookmarks, sortOption) => {
    const sorted = [...bookmarks]
    
    switch (sortOption) {
      case 'created-desc':
        return sorted.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      case 'created-asc':
        return sorted.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
      case 'updated-desc':
        return sorted.sort((a, b) => {
          const dateA = new Date(a.updatedAt || a.createdAt)
          const dateB = new Date(b.updatedAt || b.createdAt)
          return dateB - dateA
        })
      case 'updated-asc':
        return sorted.sort((a, b) => {
          const dateA = new Date(a.updatedAt || a.createdAt)
          const dateB = new Date(b.updatedAt || b.createdAt)
          return dateA - dateB
        })
      case 'title-asc':
        return sorted.sort((a, b) => (a.title || '').localeCompare(b.title || ''))
      case 'title-desc':
        return sorted.sort((a, b) => (b.title || '').localeCompare(a.title || ''))
      case 'category-asc':
        return sorted.sort((a, b) => {
          const categoryCompare = (a.category || '未分类').localeCompare(b.category || '未分类')
          if (categoryCompare !== 0) return categoryCompare
          return new Date(b.createdAt) - new Date(a.createdAt)
        })
      case 'category-desc':
        return sorted.sort((a, b) => {
          const categoryCompare = (b.category || '未分类').localeCompare(a.category || '未分类')
          if (categoryCompare !== 0) return categoryCompare
          return new Date(b.createdAt) - new Date(a.createdAt)
        })
      default:
        return sorted.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    }
  },
  
  getSortOptionLabel: (sortOption) => {
    const labels = {
      'created-desc': '最新添加',
      'created-asc': '最早添加',
      'updated-desc': '最近更新',
      'updated-asc': '最久未更新',
      'title-asc': '标题 A-Z',
      'title-desc': '标题 Z-A',
      'category-asc': '分类 A-Z',
      'category-desc': '分类 Z-A'
    }
    return labels[sortOption] || '最新添加'
  },
  
  getSortOptionDescription: (sortOption) => {
    const descriptions = {
      'created-desc': '按创建时间降序，最新添加的收藏显示在前面',
      'created-asc': '按创建时间升序，最早添加的收藏显示在前面',
      'updated-desc': '按更新时间降序，最近更新的收藏显示在前面',
      'updated-asc': '按更新时间升序，最久未更新的收藏显示在前面',
      'title-asc': '按标题字母顺序升序排列',
      'title-desc': '按标题字母顺序降序排列',
      'category-asc': '按分类字母顺序升序排列，同分类内按创建时间降序',
      'category-desc': '按分类字母顺序降序排列，同分类内按创建时间降序'
    }
    return descriptions[sortOption] || '按创建时间降序排列'
  },
  
  isValidSortOption: (sortOption) => {
    const validOptions = [
      'created-desc', 'created-asc',
      'updated-desc', 'updated-asc',
      'title-asc', 'title-desc',
      'category-asc', 'category-desc'
    ]
    return validOptions.includes(sortOption)
  },
  
  getDefaultSortOption: () => 'created-desc'
}

describe('BookmarkSortUtils 测试', () => {
  let testBookmarks

  beforeEach(() => {
    // 创建测试数据
    const now = new Date('2024-01-15T12:00:00Z')
    testBookmarks = [
      {
        id: '1',
        title: 'React 文档',
        category: '技术',
        createdAt: new Date(now.getTime() - 86400000 * 1), // 1天前
        updatedAt: new Date(now.getTime() - 43200000) // 12小时前
      },
      {
        id: '2',
        title: 'Vue 指南',
        category: '技术',
        createdAt: new Date(now.getTime() - 86400000 * 3), // 3天前
        updatedAt: new Date(now.getTime() - 86400000 * 3) // 3天前，未更新
      },
      {
        id: '3',
        title: 'Angular 教程',
        category: '学习',
        createdAt: new Date(now.getTime() - 86400000 * 2), // 2天前
        updatedAt: new Date(now.getTime() - 3600000) // 1小时前
      }
    ]
  })

  it('应该按创建时间降序排序（默认）', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'created-desc')
    
    expect(sorted[0].title).toBe('React 文档') // 1天前，最新
    expect(sorted[1].title).toBe('Angular 教程') // 2天前
    expect(sorted[2].title).toBe('Vue 指南') // 3天前，最旧
  })

  it('应该按创建时间升序排序', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'created-asc')
    
    expect(sorted[0].title).toBe('Vue 指南') // 3天前，最旧
    expect(sorted[1].title).toBe('Angular 教程') // 2天前
    expect(sorted[2].title).toBe('React 文档') // 1天前，最新
  })

  it('应该按更新时间降序排序', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'updated-desc')
    
    expect(sorted[0].title).toBe('Angular 教程') // 1小时前更新，最新
    expect(sorted[1].title).toBe('React 文档') // 12小时前更新
    expect(sorted[2].title).toBe('Vue 指南') // 3天前，最久未更新
  })

  it('应该按更新时间升序排序', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'updated-asc')
    
    expect(sorted[0].title).toBe('Vue 指南') // 3天前，最久未更新
    expect(sorted[1].title).toBe('React 文档') // 12小时前更新
    expect(sorted[2].title).toBe('Angular 教程') // 1小时前更新，最新
  })

  it('应该按标题升序排序', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'title-asc')
    
    expect(sorted[0].title).toBe('Angular 教程') // A开头
    expect(sorted[1].title).toBe('React 文档') // R开头
    expect(sorted[2].title).toBe('Vue 指南') // V开头
  })

  it('应该按标题降序排序', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'title-desc')
    
    expect(sorted[0].title).toBe('Vue 指南') // V开头
    expect(sorted[1].title).toBe('React 文档') // R开头
    expect(sorted[2].title).toBe('Angular 教程') // A开头
  })

  it('应该按分类升序排序', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'category-asc')
    
    expect(sorted[0].category).toBe('学习') // 学习分类
    expect(sorted[1].category).toBe('技术') // 技术分类
    expect(sorted[2].category).toBe('技术') // 技术分类
    
    // 同分类内按创建时间降序
    expect(sorted[1].title).toBe('React 文档') // 技术分类中较新的
    expect(sorted[2].title).toBe('Vue 指南') // 技术分类中较旧的
  })

  it('应该按分类降序排序', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'category-desc')
    
    expect(sorted[0].category).toBe('技术') // 技术分类
    expect(sorted[1].category).toBe('技术') // 技术分类
    expect(sorted[2].category).toBe('学习') // 学习分类
    
    // 同分类内按创建时间降序
    expect(sorted[0].title).toBe('React 文档') // 技术分类中较新的
    expect(sorted[1].title).toBe('Vue 指南') // 技术分类中较旧的
  })

  it('应该正确获取排序选项标签', () => {
    expect(mockBookmarkSortUtils.getSortOptionLabel('created-desc')).toBe('最新添加')
    expect(mockBookmarkSortUtils.getSortOptionLabel('title-asc')).toBe('标题 A-Z')
    expect(mockBookmarkSortUtils.getSortOptionLabel('category-desc')).toBe('分类 Z-A')
  })

  it('应该正确获取排序选项描述', () => {
    const desc = mockBookmarkSortUtils.getSortOptionDescription('created-desc')
    expect(desc).toBe('按创建时间降序，最新添加的收藏显示在前面')
    
    const titleDesc = mockBookmarkSortUtils.getSortOptionDescription('title-asc')
    expect(titleDesc).toBe('按标题字母顺序升序排列')
  })

  it('应该正确验证排序选项', () => {
    expect(mockBookmarkSortUtils.isValidSortOption('created-desc')).toBe(true)
    expect(mockBookmarkSortUtils.isValidSortOption('title-asc')).toBe(true)
    expect(mockBookmarkSortUtils.isValidSortOption('invalid-option')).toBe(false)
    expect(mockBookmarkSortUtils.isValidSortOption('')).toBe(false)
  })

  it('应该返回正确的默认排序选项', () => {
    expect(mockBookmarkSortUtils.getDefaultSortOption()).toBe('created-desc')
  })

  it('应该处理空数组', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks([], 'created-desc')
    expect(sorted).toEqual([])
  })

  it('应该处理缺失字段的数据', () => {
    const incompleteBookmarks = [
      { id: '1', createdAt: new Date() },
      { id: '2', title: '', createdAt: new Date() },
      { id: '3', title: null, category: null, createdAt: new Date() }
    ]
    
    const sorted = mockBookmarkSortUtils.sortBookmarks(incompleteBookmarks, 'title-asc')
    expect(sorted).toHaveLength(3)
    expect(sorted[0].id).toBe('1') // 空标题排在前面
  })

  it('应该不修改原数组', () => {
    const originalBookmarks = [...testBookmarks]
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'title-asc')
    
    expect(testBookmarks).toEqual(originalBookmarks)
    expect(sorted).not.toBe(testBookmarks)
  })

  it('应该处理无效排序选项，使用默认排序', () => {
    const sorted = mockBookmarkSortUtils.sortBookmarks(testBookmarks, 'invalid-option')
    
    // 应该使用默认排序（创建时间降序）
    expect(sorted[0].title).toBe('React 文档') // 最新
    expect(sorted[2].title).toBe('Vue 指南') // 最旧
  })
})