// DeleteConfirmModal独立测试页面

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

import { AlertTriangle, Trash2, CheckCircle, XCircle } from 'lucide-react'
import DeleteConfirmModal from '../DeleteConfirmModal'
import { useTagColors } from '../../hooks/useTagColors'

/**
 * DeleteConfirmModal独立测试页面
 * 专门用于测试shadcn重构后的删除确认模态窗口
 */
const DeleteConfirmModalTestPage: React.FC = () => {
  const { getTagColor } = useTagColors()
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedBookmark, setSelectedBookmark] = useState<any>(null)
  const [testResults, setTestResults] = useState<Array<{
    id: string
    timestamp: string
    action: string
    status: 'success' | 'error' | 'info'
    details: string
  }>>([])

  // 测试用的收藏数据集
  const testBookmarks = [
    {
      id: 'url-normal',
      title: 'React官方文档 - 学习现代Web开发',
      url: 'https://react.dev/learn',
      type: 'url' as const,
      category: '技术文档',
      tags: ['React', 'JavaScript', '前端开发'],
      createdAt: new Date('2024-01-15T10:30:00Z')
    },
    {
      id: 'text-normal',
      title: '重要会议记录',
      content: '今天的产品会议讨论了新功能的开发计划，包括用户界面改进、性能优化和新的API集成。团队决定采用shadcn/ui作为新的UI组件库。',
      type: 'text' as const,
      category: '工作笔记',
      tags: ['会议', '产品', '开发'],
      createdAt: new Date('2024-01-10T14:20:00Z')
    },
    {
      id: 'image-normal',
      title: '设计灵感图片',
      url: 'https://example.com/design-inspiration.jpg',
      type: 'image' as const,
      category: '设计',
      tags: ['UI设计', '灵感', '配色'],
      createdAt: new Date('2024-01-08T09:15:00Z')
    },
    {
      id: 'url-no-title',
      title: '',
      url: 'https://example.com/untitled-page',
      type: 'url' as const,
      category: '未分类',
      tags: [],
      createdAt: new Date('2024-01-05T16:45:00Z')
    },
    {
      id: 'url-many-tags',
      title: '多标签测试收藏',
      url: 'https://example.com/many-tags',
      type: 'url' as const,
      category: '测试',
      tags: ['标签1', '标签2', '标签3', '标签4', '标签5', '标签6', '标签7'],
      createdAt: new Date('2024-01-01T12:00:00Z')
    },
    {
      id: 'text-long-content',
      title: '超长文本内容测试',
      content: '这是一个超长的文本内容测试，用来验证文本类型收藏在删除确认对话框中的显示效果。内容包含了很多文字，需要测试截断和换行的处理。这段文本会继续很长很长，包含更多的信息来测试组件如何处理大量文本内容的显示和截断功能。我们需要确保即使在内容很长的情况下，对话框仍然保持良好的用户体验和视觉效果。',
      type: 'text' as const,
      category: '测试',
      tags: ['长文本', '测试'],
      createdAt: new Date('2024-01-03T08:30:00Z')
    }
  ]

  // 添加测试结果
  const addTestResult = (action: string, status: 'success' | 'error' | 'info', details: string) => {
    const result = {
      id: Date.now().toString(),
      timestamp: new Date().toLocaleTimeString(),
      action,
      status,
      details
    }
    setTestResults(prev => [result, ...prev.slice(0, 19)]) // 保持最新20条记录
  }

  // 处理删除确认
  const handleConfirm = async (bookmarkId: string) => {
    addTestResult('删除确认', 'info', `开始删除收藏: ${bookmarkId}`)
    setLoading(true)
    
    try {
      // 模拟删除操作（包含随机失败）
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 10%概率模拟失败
      if (Math.random() < 0.1) {
        throw new Error('模拟删除失败')
      }
      
      addTestResult('删除成功', 'success', `成功删除收藏: ${bookmarkId}`)
      setLoading(false)
      setIsOpen(false)
      setSelectedBookmark(null)
    } catch (error) {
      addTestResult('删除失败', 'error', `删除失败: ${error instanceof Error ? error.message : '未知错误'}`)
      setLoading(false)
      // 不关闭对话框，让用户看到错误状态
    }
  }

  // 处理取消
  const handleCancel = () => {
    addTestResult('取消删除', 'info', '用户取消了删除操作')
    setIsOpen(false)
    setSelectedBookmark(null)
  }

  // 打开删除确认对话框
  const openDeleteModal = (bookmark: any, testName: string) => {
    addTestResult('打开对话框', 'info', `测试场景: ${testName}`)
    setSelectedBookmark(bookmark)
    setIsOpen(true)
  }

  // 清空测试结果
  const clearResults = () => {
    setTestResults([])
    addTestResult('清空日志', 'info', '测试日志已清空')
  }

  // 获取状态图标
  const getStatusIcon = (status: 'success' | 'error' | 'info') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'info':
      default:
        return <AlertTriangle className="w-4 h-4 text-blue-500" />
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2 flex items-center justify-center space-x-2">
          <Trash2 className="w-8 h-8 text-destructive" />
          <span>DeleteConfirmModal 测试页面</span>
        </h1>
        <p className="text-muted-foreground">
          专门测试shadcn重构后的删除确认模态窗口功能和样式效果
        </p>
      </div>

      <div className="border-t border-border"></div>

      {/* 重构特性说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Badge variant="secondary">shadcn重构</Badge>
            <span>组件特性说明</span>
          </CardTitle>
          <CardDescription>
            DeleteConfirmModal组件已完全使用shadcn/ui AlertDialog重构
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">🎨 shadcn组件使用</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• AlertDialog - 主对话框容器</li>
                <li>• AlertDialogContent - 内容区域</li>
                <li>• AlertDialogHeader - 头部区域</li>
                <li>• AlertDialogTitle - 标题组件</li>
                <li>• AlertDialogDescription - 描述组件</li>
                <li>• AlertDialogFooter - 底部按钮区域</li>
                <li>• AlertDialogAction - 确认按钮</li>
                <li>• AlertDialogCancel - 取消按钮</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">🎯 主要改进</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 使用destructive按钮变体</li>
                <li>• 应用shadcn主题颜色系统</li>
                <li>• 移除所有自定义CSS样式</li>
                <li>• 标准确认对话框交互模式</li>
                <li>• 更好的可访问性支持</li>
                <li>• 内置动画和过渡效果</li>
                <li>• 响应式设计优化</li>
                <li>• 键盘导航支持</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试用例区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 测试收藏列表 */}
        <Card>
          <CardHeader>
            <CardTitle>测试收藏数据</CardTitle>
            <CardDescription>
              点击删除按钮测试不同场景下的删除确认对话框效果
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {testBookmarks.map((bookmark, index) => (
              <div key={bookmark.id} className="p-3 border rounded-lg space-y-2 hover:bg-muted/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0 space-y-1">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {bookmark.type === 'url' ? '网页' : 
                         bookmark.type === 'text' ? '文本' : '图片'}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        测试用例 {index + 1}
                      </span>
                    </div>
                    
                    <h4 className="font-medium text-sm truncate">
                      {bookmark.title || '无标题'}
                    </h4>
                    
                    {bookmark.url && (
                      <p className="text-xs text-primary truncate">
                        {bookmark.url}
                      </p>
                    )}
                    
                    {bookmark.content && (
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {bookmark.content}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-muted-foreground">
                        {bookmark.category}
                      </span>
                      {bookmark.tags && bookmark.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {bookmark.tags.slice(0, 2).map((tag, tagIndex) => (
                            <Badge 
                              key={tagIndex} 
                              variant="outline" 
                              className="text-xs px-1 py-0 border"
                              style={{ 
                                borderColor: getTagColor(tag),
                                backgroundColor: `${getTagColor(tag)}15`,
                                color: getTagColor(tag)
                              }}
                            >
                              {tag}
                            </Badge>
                          ))}
                          {bookmark.tags.length > 2 && (
                            <span className="text-xs text-muted-foreground">
                              +{bookmark.tags.length - 2}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => openDeleteModal(bookmark, `${bookmark.type}类型收藏`)}
                    className="ml-2 flex-shrink-0"
                  >
                    <Trash2 className="w-3 h-3 mr-1" />
                    删除
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* 测试结果日志 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>测试结果日志</span>
              <Button variant="outline" size="sm" onClick={clearResults}>
                清空日志
              </Button>
            </CardTitle>
            <CardDescription>
              实时记录组件交互和操作结果
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted/30 rounded-lg p-3 h-80 overflow-y-auto">
              {testResults.length === 0 ? (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <AlertTriangle className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">暂无测试记录</p>
                    <p className="text-xs">点击删除按钮开始测试</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {testResults.map((result) => (
                    <div key={result.id} className="flex items-start space-x-2 text-sm">
                      <div className="flex-shrink-0 mt-0.5">
                        {getStatusIcon(result.status)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{result.action}</span>
                          <span className="text-xs text-muted-foreground">
                            {result.timestamp}
                          </span>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {result.details}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 边界测试用例 */}
      <Card>
        <CardHeader>
          <CardTitle>边界情况测试</CardTitle>
          <CardDescription>
            测试特殊场景和边界情况下的组件表现
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <Button
              variant="outline"
              onClick={() => openDeleteModal({
                ...testBookmarks[0],
                title: '超长标题测试：这是一个非常非常长的收藏标题，用来测试标题截断和显示效果，看看组件如何处理长文本内容的显示和布局问题'
              }, '超长标题测试')}
              className="h-auto py-3 text-left"
            >
              <div>
                <div className="font-medium">超长标题</div>
                <div className="text-xs text-muted-foreground">测试标题截断</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => openDeleteModal({
                id: 'empty-test',
                title: '',
                type: 'url' as const,
                tags: [],
                createdAt: new Date()
              }, '空内容测试')}
              className="h-auto py-3 text-left"
            >
              <div>
                <div className="font-medium">空内容</div>
                <div className="text-xs text-muted-foreground">无标题无标签</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => openDeleteModal({
                ...testBookmarks[5],
                content: testBookmarks[5].content + ' 这里添加更多内容来测试超长文本的处理效果。'
              }, '超长文本测试')}
              className="h-auto py-3 text-left"
            >
              <div>
                <div className="font-medium">超长文本</div>
                <div className="text-xs text-muted-foreground">测试文本截断</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => openDeleteModal({
                ...testBookmarks[4],
                tags: [...testBookmarks[4].tags, '额外标签8', '额外标签9', '额外标签10']
              }, '超多标签测试')}
              className="h-auto py-3 text-left"
            >
              <div>
                <div className="font-medium">超多标签</div>
                <div className="text-xs text-muted-foreground">测试标签显示</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 当前状态显示 */}
      <Card>
        <CardHeader>
          <CardTitle>组件状态监控</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">对话框状态：</span>
              <Badge variant={isOpen ? "default" : "secondary"}>
                {isOpen ? '已打开' : '已关闭'}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">加载状态：</span>
              <Badge variant={loading ? "destructive" : "secondary"}>
                {loading ? '删除中' : '空闲'}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">选中收藏：</span>
              <span className="text-sm text-muted-foreground">
                {selectedBookmark ? selectedBookmark.id : '无'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">测试记录：</span>
              <Badge variant="outline">
                {testResults.length} 条
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* DeleteConfirmModal组件 */}
      <DeleteConfirmModal
        isOpen={isOpen}
        bookmark={selectedBookmark}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        loading={loading}
        enableUndo={true}
      />
    </div>
  )
}

export default DeleteConfirmModalTestPage