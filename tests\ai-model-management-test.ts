// AI模型管理功能测试脚本
// 测试AI集成页面和默认AI设置页面之间的数据一致性

import { aiIntegrationService } from '../src/services/aiIntegrationService'
import { defaultAIModelService } from '../src/services/defaultAIModelService'
import { ChromeStorageService } from '../src/utils/chromeStorage'

/**
 * 测试AI模型管理功能的数据一致性
 */
export class AIModelManagementTest {
  
  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<void> {
    console.log('🧪 开始AI模型管理功能测试...')
    
    try {
      await this.testModelFilteringConsistency()
      await this.testDataSynchronization()
      await this.testProviderNameAutoFill()
      await this.testUserExperienceImprovements()
      
      console.log('✅ 所有测试通过！')
    } catch (error) {
      console.error('❌ 测试失败:', error)
      throw error
    }
  }

  /**
   * 测试模型过滤一致性
   * 验证默认AI设置页面只显示已选择的模型
   */
  static async testModelFilteringConsistency(): Promise<void> {
    console.log('📋 测试模型过滤一致性...')
    
    // 模拟添加提供商和选择模型
    const mockProvider = {
      name: '测试OpenAI',
      type: 'openai' as const,
      baseUrl: 'https://api.openai.com/v1',
      apiKey: 'test-key',
      enabled: true
    }
    
    // 添加提供商
    await aiIntegrationService.addProvider(mockProvider)
    const providers = await aiIntegrationService.getConfiguredProviders()
    const addedProvider = providers.find(p => p.name === '测试OpenAI')
    
    if (!addedProvider) {
      throw new Error('提供商添加失败')
    }
    
    // 模拟选择模型
    await aiIntegrationService.saveSelectedModel(addedProvider.id, 'gpt-4')
    
    // 获取默认AI设置页面的可用模型
    const availableModels = await defaultAIModelService.getAvailableModels()
    
    // 验证只显示已选择的模型
    const hasSelectedModel = availableModels.some(model => 
      model.providerId === addedProvider.id && model.name === 'gpt-4'
    )
    
    if (!hasSelectedModel) {
      throw new Error('默认AI设置页面未显示已选择的模型')
    }
    
    // 清理测试数据
    await aiIntegrationService.removeProvider(addedProvider.id)
    
    console.log('✅ 模型过滤一致性测试通过')
  }

  /**
   * 测试数据同步机制
   * 验证删除提供商时两个页面数据同步
   */
  static async testDataSynchronization(): Promise<void> {
    console.log('🔄 测试数据同步机制...')
    
    // 模拟添加提供商和配置默认模型
    const mockProvider = {
      name: '测试Claude',
      type: 'claude' as const,
      baseUrl: 'https://api.anthropic.com',
      apiKey: 'test-key',
      enabled: true
    }
    
    await aiIntegrationService.addProvider(mockProvider)
    const providers = await aiIntegrationService.getConfiguredProviders()
    const addedProvider = providers.find(p => p.name === '测试Claude')
    
    if (!addedProvider) {
      throw new Error('提供商添加失败')
    }
    
    // 选择模型
    await aiIntegrationService.saveSelectedModel(addedProvider.id, 'claude-3')
    
    // 配置默认模型
    const modelId = `${addedProvider.id}_claude-3`
    await defaultAIModelService.updateUsageModel('default-chat', modelId, null)
    
    // 删除提供商
    await aiIntegrationService.removeProvider(addedProvider.id)
    
    // 验证默认AI设置中的配置也被清除
    await defaultAIModelService.syncWithAIIntegration()
    const usages = await defaultAIModelService.getDefaultModelUsages()
    const chatUsage = usages.find(u => u.id === 'default-chat')
    
    if (chatUsage?.selectedModelId === modelId) {
      throw new Error('删除提供商后默认AI设置未同步清除')
    }
    
    console.log('✅ 数据同步机制测试通过')
  }

  /**
   * 测试供应商名称自动填入
   */
  static async testProviderNameAutoFill(): Promise<void> {
    console.log('🏷️ 测试供应商名称自动填入...')
    
    // 这个测试主要验证前端逻辑，这里只做基本验证
    const supportedProviders = [
      { type: 'openai', expectedName: 'OpenAI' },
      { type: 'claude', expectedName: 'Claude' },
      { type: 'ollama', expectedName: 'Ollama' }
    ]
    
    for (const provider of supportedProviders) {
      // 验证提供商信息存在
      const providerInfo = aiIntegrationService['supportedProviders']?.find(
        (p: any) => p.type === provider.type
      )
      
      if (!providerInfo) {
        throw new Error(`未找到提供商类型: ${provider.type}`)
      }
    }
    
    console.log('✅ 供应商名称自动填入测试通过')
  }

  /**
   * 测试用户体验改进
   */
  static async testUserExperienceImprovements(): Promise<void> {
    console.log('🎨 测试用户体验改进...')
    
    // 测试无模型时的提示
    const availableModels = await defaultAIModelService.getAvailableModels()
    
    // 如果没有模型，验证系统能正确处理
    if (availableModels.length === 0) {
      console.log('✅ 无模型状态处理正确')
    }
    
    // 测试同步功能
    await defaultAIModelService.syncWithAIIntegration()
    
    console.log('✅ 用户体验改进测试通过')
  }

  /**
   * 清理所有测试数据
   */
  static async cleanupTestData(): Promise<void> {
    console.log('🧹 清理测试数据...')
    
    try {
      // 清理提供商配置
      const providers = await aiIntegrationService.getConfiguredProviders()
      for (const provider of providers) {
        if (provider.name.startsWith('测试')) {
          await aiIntegrationService.removeProvider(provider.id)
        }
      }
      
      // 清理默认模型配置
      const defaultUsages = await defaultAIModelService.getDefaultModelUsages()
      const cleanedUsages = defaultUsages.map(usage => ({
        ...usage,
        selectedModelId: null,
        fallbackModelId: null
      }))
      await defaultAIModelService.saveDefaultModelUsages(cleanedUsages)
      
      console.log('✅ 测试数据清理完成')
    } catch (error) {
      console.warn('⚠️ 清理测试数据时出现警告:', error)
    }
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location?.href?.includes('test')) {
  AIModelManagementTest.runAllTests()
    .then(() => console.log('🎉 测试完成'))
    .catch(error => console.error('💥 测试失败:', error))
    .finally(() => AIModelManagementTest.cleanupTestData())
}
