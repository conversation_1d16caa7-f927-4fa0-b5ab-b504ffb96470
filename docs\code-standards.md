# 关于我们和帮助中心页面代码规范

## 代码风格

### TypeScript 规范

1. **接口命名**：使用 PascalCase，以 `I` 开头（可选）
```typescript
interface AboutPageData {
  extensionInfo: ExtensionInfo
  buildInfo: BuildInfo
}
```

2. **类型定义**：优先使用 `interface` 而不是 `type`
```typescript
// 推荐
interface HelpSection {
  id: string
  title: string
  content: string
}

// 避免（除非需要联合类型）
type HelpSection = {
  id: string
  title: string
  content: string
}
```

3. **函数类型**：使用箭头函数类型
```typescript
interface SearchOptions {
  onResults: (results: HelpSearchResult[]) => void
  onError?: (error: Error) => void
}
```

### React 组件规范

1. **组件定义**：使用函数组件和 TypeScript
```typescript
interface ComponentProps {
  title: string
  onAction?: () => void
}

const Component: React.FC<ComponentProps> = ({ title, onAction }) => {
  return <div>{title}</div>
}

export default Component
```

2. **Props 解构**：在参数中直接解构
```typescript
// 推荐
const AboutTab: React.FC<AboutTabProps> = ({ aboutData }) => {
  // ...
}

// 避免
const AboutTab: React.FC<AboutTabProps> = (props) => {
  const { aboutData } = props
  // ...
}
```

3. **状态管理**：使用 `useState` 和 `useEffect`
```typescript
const [loading, setLoading] = useState(false)
const [data, setData] = useState<DataType | null>(null)

useEffect(() => {
  // 副作用逻辑
}, [dependency])
```

### 样式规范

1. **Tailwind CSS 类名顺序**：
   - 布局：`flex`, `grid`, `block`
   - 定位：`relative`, `absolute`
   - 尺寸：`w-`, `h-`, `max-w-`
   - 间距：`m-`, `p-`
   - 颜色：`bg-`, `text-`, `border-`
   - 其他：`rounded-`, `shadow-`

```tsx
<div className="flex items-center justify-between w-full p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
```

2. **响应式类名**：按断点顺序
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
```

3. **深色模式**：使用 `dark:` 前缀
```tsx
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
```

## 文件组织

### 目录结构

```
src/options/
├── components/           # React 组件
│   ├── AboutTab.tsx
│   ├── HelpCenterTab.tsx
│   └── index.ts         # 导出文件
├── hooks/               # 自定义 Hooks
│   ├── useTheme.ts
│   └── useResponsive.ts
├── utils/               # 工具函数
│   ├── helpSearch.ts
│   └── performance.ts
├── data/                # 数据配置
│   ├── aboutInfo.ts
│   └── helpContent.ts
└── types/               # 类型定义
    └── index.ts
```

### 文件命名

1. **组件文件**：PascalCase，如 `AboutTab.tsx`
2. **Hook 文件**：camelCase，以 `use` 开头，如 `useTheme.ts`
3. **工具文件**：camelCase，如 `helpSearch.ts`
4. **类型文件**：camelCase，如 `types.ts`

### 导入顺序

```typescript
// 1. React 相关
import React, { useState, useEffect } from 'react'

// 2. 第三方库
import { Search, X } from 'lucide-react'

// 3. 内部组件
import HelpSearchBox from './HelpSearchBox'

// 4. 内部 Hooks
import { useTheme } from '../hooks/useTheme'

// 5. 内部工具
import { searchHelpContent } from '../utils/helpSearch'

// 6. 类型定义
import type { HelpSection } from '../data/helpContent'
```

## 注释规范

### 文件头注释

```typescript
/**
 * 关于我们页面组件
 * 显示扩展的基本信息、版本、开发者信息等
 */
```

### 函数注释

```typescript
/**
 * 搜索帮助内容
 * @param sections 帮助章节数组
 * @param query 搜索关键词
 * @param config 搜索配置选项
 * @returns 搜索结果数组
 */
export const searchHelpContent = (
  sections: HelpSection[],
  query: string,
  config: SearchConfig = {}
): HelpSearchResult[] => {
  // 实现逻辑
}
```

### 复杂逻辑注释

```typescript
// 检查是否在扩展环境中运行
const extensionEnv = isExtensionEnvironment()
setIsExtensionEnv(extensionEnv)

// 获取扩展信息，如果失败则使用默认数据
const extensionInfo = getExtensionInfo()
const buildInfo = getBuildInfo()
```

### TODO 注释

```typescript
// TODO: 添加搜索结果高亮功能
// FIXME: 修复在某些情况下的内存泄漏问题
// HACK: 临时解决方案，需要在下个版本中重构
```

## 错误处理规范

### 异步函数错误处理

```typescript
const loadData = async () => {
  try {
    setLoading(true)
    const data = await fetchData()
    setData(data)
  } catch (error) {
    console.error('加载数据失败:', error)
    setError(error instanceof Error ? error.message : '未知错误')
  } finally {
    setLoading(false)
  }
}
```

### 错误边界使用

```typescript
<PageErrorBoundary
  onError={(error, errorInfo) => {
    console.error('页面错误:', error, errorInfo)
  }}
  maxRetries={2}
>
  <ComponentThatMightFail />
</PageErrorBoundary>
```

### 用户友好的错误信息

```typescript
// 避免
throw new Error('Failed to fetch data')

// 推荐
throw new Error('无法加载帮助内容，请检查网络连接后重试')
```

## 性能优化规范

### 组件优化

1. **使用 React.memo**：对于纯展示组件
```typescript
const HelpSection = React.memo<HelpSectionProps>(({ section }) => {
  return <div>{section.title}</div>
})
```

2. **使用 useCallback**：对于传递给子组件的函数
```typescript
const handleSearch = useCallback((query: string) => {
  // 搜索逻辑
}, [dependencies])
```

3. **使用 useMemo**：对于计算密集的操作
```typescript
const filteredSections = useMemo(() => {
  return sections.filter(section => 
    section.title.includes(searchQuery)
  )
}, [sections, searchQuery])
```

### 懒加载

```typescript
const { elementRef, isVisible } = useLazyLoad({
  threshold: 0.1,
  rootMargin: '50px'
})

return (
  <div ref={elementRef}>
    {isVisible && <ExpensiveComponent />}
  </div>
)
```

### 缓存使用

```typescript
const { getCachedResults, setCachedResults } = useSearchCache()

const performSearch = (query: string) => {
  const cached = getCachedResults(query)
  if (cached) {
    return cached
  }
  
  const results = searchFunction(query)
  setCachedResults(query, results)
  return results
}
```

## 测试规范

### 测试文件命名

- 单元测试：`Component.test.tsx`
- 集成测试：`feature.integration.test.tsx`
- E2E 测试：`feature.e2e.test.ts`

### 测试结构

```typescript
describe('ComponentName', () => {
  beforeEach(() => {
    // 测试前准备
  })

  afterEach(() => {
    // 测试后清理
  })

  describe('功能分组', () => {
    it('应该执行特定行为', () => {
      // 测试逻辑
    })
  })
})
```

### 测试覆盖率

- 单元测试覆盖率：> 80%
- 集成测试覆盖率：> 60%
- 关键路径测试覆盖率：100%

## Git 提交规范

### 提交信息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 类型说明

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例

```
feat(help-center): 添加搜索功能

- 实现全文搜索功能
- 添加搜索建议和历史记录
- 优化搜索性能

Closes #123
```

## 代码审查清单

### 功能性

- [ ] 功能是否按预期工作
- [ ] 是否处理了所有边界情况
- [ ] 错误处理是否完善
- [ ] 是否有适当的测试覆盖

### 性能

- [ ] 是否有不必要的重新渲染
- [ ] 是否使用了适当的缓存策略
- [ ] 是否有内存泄漏风险
- [ ] 是否优化了网络请求

### 可维护性

- [ ] 代码是否易于理解
- [ ] 是否遵循了项目的代码规范
- [ ] 是否有适当的注释
- [ ] 是否有重复代码需要提取

### 用户体验

- [ ] 是否有适当的加载状态
- [ ] 错误信息是否用户友好
- [ ] 是否支持键盘导航
- [ ] 是否支持响应式设计

### 安全性

- [ ] 是否有 XSS 风险
- [ ] 用户输入是否经过验证
- [ ] 是否泄露敏感信息
- [ ] 是否遵循最小权限原则

## 部署检查清单

### 发布前检查

- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 文档已更新
- [ ] 版本号已更新
- [ ] 变更日志已更新

### 发布后验证

- [ ] 功能正常工作
- [ ] 性能指标正常
- [ ] 错误率在预期范围内
- [ ] 用户反馈良好

## 工具配置

### ESLint 规则

```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "react/prop-types": "off",
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

### Prettier 配置

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100
}
```

### TypeScript 配置

```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```