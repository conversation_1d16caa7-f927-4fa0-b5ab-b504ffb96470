// BookmarkRow组件单元测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import '@testing-library/jest-dom'
import BookmarkRow from '../src/components/BookmarkRow'
import type { Bookmark } from '../src/types'

// 模拟TruncatedTitle组件
vi.mock('../src/components/TruncatedTitle', () => ({
  default: function MockTruncatedTitle({ title, className }: any) {
    return <span className={className}>{title}</span>
  }
}))

// 创建测试用的收藏数据
const createMockBookmark = (overrides: Partial<Bookmark> = {}): Bookmark => ({
  id: 'test-bookmark-1',
  type: 'url',
  title: '测试收藏标题',
  url: 'https://example.com',
  description: '测试描述',
  tags: ['测试', '标签'],
  category: '测试分类',
  favicon: 'https://example.com/favicon.ico',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  metadata: {
    pageTitle: '测试页面标题',
    siteName: 'example.com',
    aiGenerated: false
  },
  ...overrides
})

describe('BookmarkRow组件', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    // 模拟window.open
    global.open = vi.fn()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('基础渲染', () => {
    it('应该正确渲染收藏行', () => {
      const bookmark = createMockBookmark()
      
      render(
        <BookmarkRow 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onClick={mockOnClick}
        />
      )

      // 验证标题显示
      expect(screen.getByText('测试收藏标题')).toBeInTheDocument()
      
      // 验证URL显示
      expect(screen.getByText('https://example.com')).toBeInTheDocument()
      
      // 验证分类显示
      expect(screen.getByText('测试分类')).toBeInTheDocument()
      
      // 验证时间显示 - 应该显示具体的日期格式
      expect(screen.getByText('1月1日')).toBeInTheDocument()
    })

    it('应该正确显示网站图标', () => {
      const bookmark = createMockBookmark()
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      const favicon = screen.getByAltText('')
      expect(favicon).toHaveAttribute('src', 'https://example.com/favicon.ico')
    })

    it('当没有网站图标时应该显示默认图标', () => {
      const bookmark = createMockBookmark({ favicon: undefined })
      
      const { container } = render(<BookmarkRow bookmark={bookmark} />)
      
      // 应该显示Star图标（通过SVG元素查找）
      const starIcon = container.querySelector('svg')
      expect(starIcon).toBeInTheDocument()
      expect(starIcon).not.toHaveClass('hidden')
    })

    it('应该正确处理无标题的情况', () => {
      const bookmark = createMockBookmark({ title: '' })
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      expect(screen.getByText('无标题')).toBeInTheDocument()
    })

    it('应该正确处理无URL的情况', () => {
      const bookmark = createMockBookmark({ url: undefined })
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      // URL不应该显示
      expect(screen.queryByText('https://example.com')).not.toBeInTheDocument()
      
      // 外部链接按钮不应该显示
      expect(screen.queryByLabelText('在新标签页中打开')).not.toBeInTheDocument()
    })
  })

  describe('高亮状态', () => {
    it('应该正确应用高亮样式', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow bookmark={bookmark} isHighlighted={true} />
      )
      
      const rowElement = container.querySelector('[class*="bg-accent"]') as HTMLElement
      expect(rowElement).toHaveClass('bg-accent', 'border-primary')
    })

    it('默认情况下不应该有高亮样式', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow bookmark={bookmark} />
      )
      
      const rowElement = container.querySelector('[class*="bg-accent"]') as HTMLElement
      expect(rowElement).not.toHaveClass('bg-accent', 'border-primary')
    })
  })

  describe('交互功能', () => {
    it('点击行应该触发onClick回调', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onClick={mockOnClick}
        />
      )
      
      const row = container.firstChild as HTMLElement
      fireEvent.click(row)
      
      expect(mockOnClick).toHaveBeenCalledWith(bookmark)
    })

    it('点击编辑按钮应该触发onEdit回调', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onEdit={mockOnEdit}
        />
      )
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        const editButton = screen.getByLabelText('编辑收藏')
        fireEvent.click(editButton)
      })
      
      expect(mockOnEdit).toHaveBeenCalledWith(bookmark)
    })

    it('点击删除按钮应该触发onDelete回调', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        const deleteButton = screen.getByLabelText('删除收藏')
        fireEvent.click(deleteButton)
      })
      
      expect(mockOnDelete).toHaveBeenCalledWith(bookmark)
    })

    it('点击外部链接按钮应该打开新窗口', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(<BookmarkRow bookmark={bookmark} />)
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        const linkButton = screen.getByLabelText('在新标签页中打开')
        fireEvent.click(linkButton)
      })
      
      expect(global.open).toHaveBeenCalledWith(
        'https://example.com', 
        '_blank', 
        'noopener,noreferrer'
      )
    })
  })

  describe('时间格式化', () => {
    it('应该正确显示今天的时间', () => {
      const today = new Date()
      const bookmark = createMockBookmark({ createdAt: today })
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      expect(screen.getByText('今天')).toBeInTheDocument()
    })

    it('应该正确显示昨天的时间', () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const bookmark = createMockBookmark({ createdAt: yesterday })
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      expect(screen.getByText('昨天')).toBeInTheDocument()
    })

    it('应该正确显示几天前的时间', () => {
      const threeDaysAgo = new Date()
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
      const bookmark = createMockBookmark({ createdAt: threeDaysAgo })
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      expect(screen.getByText('3天前')).toBeInTheDocument()
    })

    it('应该正确显示较早的日期', () => {
      const longAgo = new Date('2023-06-15')
      const bookmark = createMockBookmark({ createdAt: longAgo })
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      // 应该显示月日格式
      expect(screen.getByText(/6月|15/)).toBeInTheDocument()
    })
  })

  describe('图标错误处理', () => {
    it('当网站图标加载失败时应该显示默认图标', () => {
      const bookmark = createMockBookmark()
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      const favicon = screen.getByAltText('')
      
      // 模拟图标加载失败
      fireEvent.error(favicon)
      
      // 验证图标被隐藏
      expect(favicon).toHaveStyle('display: none')
    })
  })

  describe('可访问性', () => {
    it('操作按钮应该有正确的aria-label', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        expect(screen.getByLabelText('在新标签页中打开')).toBeInTheDocument()
        expect(screen.getByLabelText('编辑收藏')).toBeInTheDocument()
        expect(screen.getByLabelText('删除收藏')).toBeInTheDocument()
      })
    })
  })

  describe('自定义样式', () => {
    it('应该正确应用自定义CSS类名', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow bookmark={bookmark} className="custom-class" />
      )
      
      const rowElement = container.firstChild as HTMLElement
      expect(rowElement).toHaveClass('custom-class')
    })
  })
})