// 内存优化处理器单元测试

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { 
  MemoryOptimizedProcessor, 
  ProcessingConfig, 
  MemoryStrategy,
  ProgressReporter 
} from '../src/services/MemoryOptimizedProcessor'

// 模拟内存策略
class MockMemoryStrategy implements MemoryStrategy {
  shouldTriggerGC = vi.fn().mockReturnValue(false)
  getOptimalBatchSize = vi.fn().mockReturnValue(50)
  getOptimalConcurrency = vi.fn().mockReturnValue(2)
}

// 模拟进度报告器
class MockProgressReporter implements ProgressReporter {
  reportProgress = vi.fn()
  reportBatchComplete = vi.fn()
  reportError = vi.fn()
}

describe('MemoryOptimizedProcessor', () => {
  let processor: MemoryOptimizedProcessor
  let mockStrategy: MockMemoryStrategy

  beforeEach(() => {
    mockStrategy = new MockMemoryStrategy()
    processor = new MemoryOptimizedProcessor(mockStrategy)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('processBatches', () => {
    it('应该成功处理小批量数据', async () => {
      const testData = [1, 2, 3, 4, 5]
      const mockProcessor = vi.fn().mockResolvedValue([10, 20, 30])
      
      const result = await processor.processBatches(
        testData,
        mockProcessor,
        { batchSize: 2, maxConcurrency: 1 }
      )

      expect(result.results).toHaveLength(9) // 3批次 × 3个结果
      expect(result.totalBatches).toBe(3)
      expect(result.totalProcessed).toBe(9)
      expect(result.errors).toHaveLength(0)
      expect(mockProcessor).toHaveBeenCalledTimes(3)
    })

    it('应该处理批处理错误', async () => {
      const testData = [1, 2, 3, 4]
      const mockProcessor = vi.fn()
        .mockResolvedValueOnce([10, 20])
        .mockRejectedValueOnce(new Error('处理失败'))
        .mockResolvedValueOnce([30, 40])

      const result = await processor.processBatches(
        testData,
        mockProcessor,
        { batchSize: 2, maxConcurrency: 1 }
      )

      expect(result.results).toHaveLength(2) // 只有成功的批次结果
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].batchIndex).toBe(1)
      expect(result.errors[0].error.message).toBe('处理失败')
    })

    it('应该正确报告进度', async () => {
      const testData = [1, 2, 3, 4]
      const mockProcessor = vi.fn().mockResolvedValue([10, 20])
      const progressCallback = vi.fn()

      await processor.processBatches(
        testData,
        mockProcessor,
        { 
          batchSize: 2, 
          maxConcurrency: 1,
          progressCallback 
        }
      )

      expect(progressCallback).toHaveBeenCalled()
      // 验证进度回调被调用了正确的次数
      expect(progressCallback).toHaveBeenCalledTimes(2) // 2个批次
    })

    it('应该使用策略获取最优配置', async () => {
      const testData = [1, 2, 3, 4, 5]
      const mockProcessor = vi.fn().mockResolvedValue([10])

      // 调用 getProcessingRecommendations 来触发策略方法
      processor.getProcessingRecommendations(testData.length, 1)

      // 验证策略方法被调用
      expect(mockStrategy.getOptimalBatchSize).toHaveBeenCalled()
      expect(mockStrategy.getOptimalConcurrency).toHaveBeenCalled()
    })
  })

  describe('processStream', () => {
    it('应该流式处理数据', async () => {
      const testData = [1, 2, 3, 4, 5]
      const mockProcessor = vi.fn()
        .mockImplementation((item: number) => Promise.resolve(item * 10))

      const results: number[] = []
      for await (const result of processor.processStream(
        testData,
        mockProcessor,
        { batchSize: 2 }
      )) {
        results.push(result)
      }

      expect(results).toEqual([10, 20, 30, 40, 50])
      expect(mockProcessor).toHaveBeenCalledTimes(5)
    })

    it('应该处理流式处理中的错误', async () => {
      const testData = [1, 2, 3]
      const mockProcessor = vi.fn()
        .mockResolvedValueOnce(10)
        .mockRejectedValueOnce(new Error('处理失败'))
        .mockResolvedValueOnce(30)

      const results: number[] = []
      for await (const result of processor.processStream(
        testData,
        mockProcessor,
        { batchSize: 1 }
      )) {
        results.push(result)
      }

      // 只有成功的结果会被yield
      expect(results).toEqual([10, 30])
    })
  })

  describe('safeTransform', () => {
    it('应该安全地转换数据', async () => {
      const testData = { value: 42 }
      const transformer = vi.fn().mockResolvedValue({ transformed: 84 })

      const result = await processor.safeTransform(testData, transformer)

      expect(result).toEqual({ transformed: 84 })
      expect(transformer).toHaveBeenCalledWith(testData)
    })

    it('应该在转换失败时清理内存', async () => {
      const testData = { value: 42 }
      const transformer = vi.fn().mockRejectedValue(new Error('转换失败'))
      const gcSpy = vi.spyOn(processor, 'performGarbageCollection')

      await expect(processor.safeTransform(testData, transformer))
        .rejects.toThrow('转换失败')

      expect(gcSpy).toHaveBeenCalled()
    })
  })

  describe('getMemoryUsage', () => {
    it('应该返回内存使用信息', () => {
      const memoryInfo = processor.getMemoryUsage()

      expect(memoryInfo).toHaveProperty('used')
      expect(memoryInfo).toHaveProperty('total')
      expect(memoryInfo).toHaveProperty('percentage')
      expect(memoryInfo).toHaveProperty('available')
      expect(typeof memoryInfo.used).toBe('number')
      expect(typeof memoryInfo.percentage).toBe('number')
    })
  })

  describe('needsMemoryOptimization', () => {
    it('应该正确判断是否需要内存优化', () => {
      // 模拟高内存使用情况
      vi.spyOn(processor, 'getMemoryUsage').mockReturnValue({
        used: 150,
        total: 200,
        percentage: 85,
        available: 50
      })

      // 设置策略的返回值
      mockStrategy.shouldTriggerGC.mockReturnValueOnce(true).mockReturnValueOnce(false)

      expect(processor.needsMemoryOptimization(100)).toBe(true)
      expect(processor.needsMemoryOptimization(200)).toBe(false)
    })
  })

  describe('getProcessingRecommendations', () => {
    it('应该返回处理建议', () => {
      vi.spyOn(processor, 'getMemoryUsage').mockReturnValue({
        used: 50,
        total: 100,
        percentage: 50,
        available: 50
      })

      const recommendations = processor.getProcessingRecommendations(1000, 1)

      expect(recommendations).toHaveProperty('batchSize')
      expect(recommendations).toHaveProperty('maxConcurrency')
      expect(recommendations).toHaveProperty('estimatedMemory')
      expect(recommendations).toHaveProperty('recommendations')
      expect(Array.isArray(recommendations.recommendations)).toBe(true)
    })

    it('应该为大数据量提供保守建议', () => {
      vi.spyOn(processor, 'getMemoryUsage').mockReturnValue({
        used: 80,
        total: 100,
        percentage: 80,
        available: 20
      })

      const recommendations = processor.getProcessingRecommendations(50000, 10)

      expect(recommendations.recommendations.length).toBeGreaterThan(0)
      expect(recommendations.recommendations.some(r => 
        r.includes('数据量') || r.includes('内存')
      )).toBe(true)
    })
  })

  describe('performGarbageCollection', () => {
    it('应该执行垃圾回收', async () => {
      // 这个测试主要验证方法不会抛出错误
      await expect(processor.performGarbageCollection()).resolves.not.toThrow()
    })
  })
})

describe('MemoryStrategy', () => {
  it('默认策略应该正确工作', () => {
    const processor = new MemoryOptimizedProcessor()
    const recommendations = processor.getProcessingRecommendations(1000, 1)
    
    expect(recommendations.batchSize).toBeGreaterThan(0)
    expect(recommendations.maxConcurrency).toBeGreaterThan(0)
  })
})