// HelpTooltip组件单元测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { 
  HelpTooltip, 
  HelpIcon, 
  HelpButton, 
  FeatureIntro, 
  ContextHelp 
} from '../src/components/HelpTooltip'

describe('HelpTooltip组件测试', () => {
  describe('HelpTooltip基础功能', () => {
    it('应该正确渲染子元素', () => {
      render(
        <HelpTooltip content="测试提示内容">
          <button>测试按钮</button>
        </HelpTooltip>
      )
      
      expect(screen.getByText('测试按钮')).toBeInTheDocument()
    })

    it('应该在鼠标悬停时显示提示内容', async () => {
      render(
        <HelpTooltip content="测试提示内容">
          <button>测试按钮</button>
        </HelpTooltip>
      )
      
      const button = screen.getByText('测试按钮')
      fireEvent.mouseEnter(button)
      
      await waitFor(() => {
        expect(screen.getByText('测试提示内容')).toBeInTheDocument()
      })
    })

    it('应该在鼠标离开时隐藏提示内容', async () => {
      render(
        <HelpTooltip content="测试提示内容">
          <button>测试按钮</button>
        </HelpTooltip>
      )
      
      const button = screen.getByText('测试按钮')
      fireEvent.mouseEnter(button)
      
      await waitFor(() => {
        expect(screen.getByText('测试提示内容')).toBeInTheDocument()
      })
      
      fireEvent.mouseLeave(button)
      
      await waitFor(() => {
        expect(screen.queryByText('测试提示内容')).not.toBeInTheDocument()
      })
    })

    it('应该支持显示标题', async () => {
      render(
        <HelpTooltip content="测试提示内容" title="测试标题">
          <button>测试按钮</button>
        </HelpTooltip>
      )
      
      const button = screen.getByText('测试按钮')
      fireEvent.mouseEnter(button)
      
      await waitFor(() => {
        expect(screen.getByText('测试标题')).toBeInTheDocument()
        expect(screen.getByText('测试提示内容')).toBeInTheDocument()
      })
    })

    it('应该支持延迟显示', async () => {
      render(
        <HelpTooltip content="测试提示内容" delay={100}>
          <button>测试按钮</button>
        </HelpTooltip>
      )
      
      const button = screen.getByText('测试按钮')
      fireEvent.mouseEnter(button)
      
      // 立即检查，应该还没有显示
      expect(screen.queryByText('测试提示内容')).not.toBeInTheDocument()
      
      // 等待延迟后应该显示
      await waitFor(() => {
        expect(screen.getByText('测试提示内容')).toBeInTheDocument()
      }, { timeout: 200 })
    })

    it('应该支持不同的位置', () => {
      const positions = ['top', 'bottom', 'left', 'right'] as const
      
      positions.forEach(position => {
        const { unmount } = render(
          <HelpTooltip content="测试提示内容" position={position}>
            <button>测试按钮</button>
          </HelpTooltip>
        )
        
        // 验证组件能正常渲染
        expect(screen.getByText('测试按钮')).toBeInTheDocument()
        unmount()
      })
    })
  })

  describe('HelpIcon组件测试', () => {
    it('应该渲染问号图标', () => {
      render(<HelpIcon content="测试提示内容" />)
      
      const icon = screen.getByRole('img', { hidden: true })
      expect(icon).toBeInTheDocument()
    })

    it('应该在悬停时显示提示内容', async () => {
      render(<HelpIcon content="测试提示内容" />)
      
      const icon = screen.getByRole('img', { hidden: true })
      fireEvent.mouseEnter(icon.parentElement!)
      
      await waitFor(() => {
        expect(screen.getByText('测试提示内容')).toBeInTheDocument()
      })
    })
  })

  describe('HelpButton组件测试', () => {
    it('应该渲染帮助按钮', () => {
      render(<HelpButton context="export" />)
      
      expect(screen.getByText('帮助')).toBeInTheDocument()
    })

    it('应该在点击时显示帮助对话框', async () => {
      render(<HelpButton context="export" />)
      
      const button = screen.getByText('帮助')
      fireEvent.click(button)
      
      await waitFor(() => {
        expect(screen.getByText('数据导出帮助')).toBeInTheDocument()
      })
    })

    it('应该支持不同的变体', () => {
      const variants = ['text', 'icon', 'button'] as const
      
      variants.forEach(variant => {
        const { unmount } = render(
          <HelpButton context="export" variant={variant} />
        )
        
        // 验证组件能正常渲染
        const button = screen.getByRole('button')
        expect(button).toBeInTheDocument()
        unmount()
      })
    })
  })

  describe('FeatureIntro组件测试', () => {
    const mockOnClose = vi.fn()

    beforeEach(() => {
      mockOnClose.mockClear()
    })

    it('应该渲染功能介绍内容', () => {
      render(
        <FeatureIntro
          title="测试标题"
          description="测试描述"
          onClose={mockOnClose}
        />
      )
      
      expect(screen.getByText('测试标题')).toBeInTheDocument()
      expect(screen.getByText('测试描述')).toBeInTheDocument()
    })

    it('应该渲染操作步骤', () => {
      const steps = ['步骤1', '步骤2', '步骤3']
      
      render(
        <FeatureIntro
          title="测试标题"
          description="测试描述"
          steps={steps}
          onClose={mockOnClose}
        />
      )
      
      expect(screen.getByText('操作步骤：')).toBeInTheDocument()
      steps.forEach(step => {
        expect(screen.getByText(step)).toBeInTheDocument()
      })
    })

    it('应该渲染小贴士', () => {
      const tips = ['贴士1', '贴士2', '贴士3']
      
      render(
        <FeatureIntro
          title="测试标题"
          description="测试描述"
          tips={tips}
          onClose={mockOnClose}
        />
      )
      
      expect(screen.getByText('💡 小贴士：')).toBeInTheDocument()
      tips.forEach(tip => {
        expect(screen.getByText(tip)).toBeInTheDocument()
      })
    })

    it('应该在点击关闭按钮时调用onClose', () => {
      render(
        <FeatureIntro
          title="测试标题"
          description="测试描述"
          onClose={mockOnClose}
        />
      )
      
      const closeButton = screen.getByText('我知道了')
      fireEvent.click(closeButton)
      
      expect(mockOnClose).toHaveBeenCalledTimes(1)
    })

    it('应该在点击X按钮时调用onClose', () => {
      render(
        <FeatureIntro
          title="测试标题"
          description="测试描述"
          onClose={mockOnClose}
        />
      )
      
      const xButton = screen.getByRole('button', { name: /close/i })
      fireEvent.click(xButton)
      
      expect(mockOnClose).toHaveBeenCalledTimes(1)
    })
  })

  describe('ContextHelp组件测试', () => {
    const mockOnClose = vi.fn()

    beforeEach(() => {
      mockOnClose.mockClear()
    })

    it('应该在不可见时不渲染', () => {
      render(
        <ContextHelp
          context="export"
          isVisible={false}
          onClose={mockOnClose}
        />
      )
      
      expect(screen.queryByText('数据导出帮助')).not.toBeInTheDocument()
    })

    it('应该在可见时渲染导出帮助内容', () => {
      render(
        <ContextHelp
          context="export"
          isVisible={true}
          onClose={mockOnClose}
        />
      )
      
      expect(screen.getByText('数据导出帮助')).toBeInTheDocument()
      expect(screen.getByText(/导出功能让您可以备份和分享/)).toBeInTheDocument()
    })

    it('应该在可见时渲染导入帮助内容', () => {
      render(
        <ContextHelp
          context="import"
          isVisible={true}
          onClose={mockOnClose}
        />
      )
      
      expect(screen.getByText('数据导入帮助')).toBeInTheDocument()
      expect(screen.getByText(/导入功能让您可以从备份文件/)).toBeInTheDocument()
    })

    it('应该在可见时渲染冲突处理帮助内容', () => {
      render(
        <ContextHelp
          context="conflict"
          isVisible={true}
          onClose={mockOnClose}
        />
      )
      
      expect(screen.getByText('冲突处理帮助')).toBeInTheDocument()
      expect(screen.getByText(/当导入数据与现有数据存在冲突时/)).toBeInTheDocument()
    })

    it('应该在可见时渲染设置帮助内容', () => {
      render(
        <ContextHelp
          context="settings"
          isVisible={true}
          onClose={mockOnClose}
        />
      )
      
      expect(screen.getByText('设置帮助')).toBeInTheDocument()
      expect(screen.getByText(/在设置中可以配置导入导出/)).toBeInTheDocument()
    })
  })

  describe('可访问性测试', () => {
    it('应该具有正确的ARIA属性', async () => {
      render(
        <HelpTooltip content="测试提示内容">
          <button>测试按钮</button>
        </HelpTooltip>
      )
      
      const container = screen.getByText('测试按钮').parentElement!
      fireEvent.mouseEnter(container)
      
      await waitFor(() => {
        expect(container).toHaveAttribute('aria-describedby', 'tooltip-content')
        const tooltip = screen.getByRole('tooltip')
        expect(tooltip).toHaveAttribute('aria-live', 'polite')
      })
    })

    it('应该支持键盘导航', async () => {
      render(
        <HelpTooltip content="测试提示内容">
          <button>测试按钮</button>
        </HelpTooltip>
      )
      
      const button = screen.getByText('测试按钮')
      fireEvent.focus(button)
      
      await waitFor(() => {
        expect(screen.getByText('测试提示内容')).toBeInTheDocument()
      })
      
      fireEvent.blur(button)
      
      await waitFor(() => {
        expect(screen.queryByText('测试提示内容')).not.toBeInTheDocument()
      })
    })
  })
})