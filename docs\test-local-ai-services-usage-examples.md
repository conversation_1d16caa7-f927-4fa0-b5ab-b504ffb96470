# 本地AI服务测试脚本使用示例

## 快速开始

### 基本使用

```bash
# 直接运行测试脚本
node scripts/test-local-ai-services.js

# 通过npm脚本运行
npm run test:local-ai
```

### 运行前准备

1. **确保项目已构建**：
```bash
npm run build
```

2. **启动本地AI服务**（可选）：
```bash
# 启动Ollama（如果已安装）
ollama serve

# 启动LM Studio（如果已安装）
# 在LM Studio中启用本地服务器

# 启动Xinference（如果已安装）
xinference-local --host 0.0.0.0 --port 9997
```

## 测试输出示例

### 成功运行输出

```
🚀 开始本地AI服务集成功能测试

============================================================
🤖 默认服务配置测试
============================================================
✅ 获取到 5 个默认服务配置

1. Ollama
   地址: http://localhost:11434
   端口: 11434
   超时: 10000ms

2. LM Studio
   地址: http://localhost:1234
   端口: 1234
   超时: 10000ms

============================================================
🤖 本地服务发现测试
============================================================
ℹ️  开始扫描本地AI服务...
✅ 发现 2 个本地AI服务

1. Ollama
   地址: http://localhost:11434
   端口: 11434
   协议: http
   健康检查: /api/version
   模型端点: /api/tags

2. LM Studio
   地址: http://localhost:1234/v1
   端口: 1234
   协议: http
   健康检查: /v1/models
   模型端点: /v1/models

============================================================
🤖 服务连接和模型测试
============================================================

--- 测试 Ollama ---
ℹ️  测试 Ollama 连接...
✅ Ollama 连接成功 (156ms)
   模型数量: 3

ℹ️  获取 Ollama 模型列表...
✅ Ollama 找到 3 个模型

   1. Llama 2 (7b)
      ID: llama2:7b
      描述: Meta开发的大型语言模型 (7B参数, GGUF格式)
      大小: 3.54 GB
      参数: 7B
      能力: chat, completion
      标签: llama, Q4_0, 7B
      推荐: 是
      热门: 是

--- 测试 LM Studio ---
ℹ️  测试 LM Studio 连接...
✅ LM Studio 连接成功 (89ms)
   模型数量: 1

ℹ️  获取 LM Studio 模型列表...
✅ LM Studio 找到 1 个模型

   1. Llama 2 (llama-2-7b-chat)
      ID: models/llama-2-7b-chat.gguf
      描述: Meta开发的大型语言模型 (LM Studio本地部署)
      能力: chat, completion, instruction-following
      标签: LM Studio, 对话, 7B参数
      推荐: 是
      热门: 是

============================================================
🤖 测试完成
============================================================
✅ 所有测试已完成！

ℹ️  测试总结:
   - 发现的服务数量: 2
   - 在线服务数量: 2
   - 测试用例: 全部通过
```

### 无服务运行时的输出

```
🚀 开始本地AI服务集成功能测试

============================================================
🤖 本地服务发现测试
============================================================
ℹ️  开始扫描本地AI服务...
✅ 发现 0 个本地AI服务

⚠️  扫描过程中遇到 10 个错误:
   - 端口 1234: 无法连接到端口 1234
   - 端口 5000: 无法连接到端口 5000
   - 端口 7860: 无法连接到端口 7860
   - 端口 8000: 无法连接到端口 8000
   - 端口 8080: 无法连接到端口 8080
   - 端口 9997: 无法连接到端口 9997
   - 端口 11434: 无法连接到端口 11434
   - 端口 8888: 无法连接到端口 8888
   - 端口 3000: 无法连接到端口 3000
   - 端口 4000: 无法连接到端口 4000

============================================================
🤖 测试完成
============================================================
✅ 所有测试已完成！

ℹ️  测试总结:
   - 发现的服务数量: 0
   - 在线服务数量: 0
   - 测试用例: 全部通过
```

### 连接失败时的输出

```
--- 测试 Ollama ---
ℹ️  测试 Ollama 连接...
❌ Ollama 连接失败: 连接超时，请检查Ollama服务是否运行

--- 测试 LM Studio ---
ℹ️  测试 LM Studio 连接...
❌ LM Studio 连接失败: LM Studio本地服务器未启动或端口不正确
```

## 自定义配置示例

### 修改扫描端口

编辑脚本中的 `testLocalServiceDiscovery` 函数：

```javascript
// 原始代码
const result = await adapter.discoverLocalServices([8888, 9999]);

// 修改为自定义端口
const result = await adapter.discoverLocalServices([7777, 8888, 9999, 12345]);
```

### 添加自定义服务测试

```javascript
async function testCustomAIService() {
    header('自定义AI服务测试');
    
    const customService = {
        name: 'My Custom AI',
        baseUrl: 'http://localhost:8080',
        port: 8080,
        protocol: 'http',
        healthCheckPath: '/health',
        modelsPath: '/models',
        timeout: 15000
    };
    
    const result = await localAIServiceAdapter.testLocalServiceConnection(customService);
    
    if (result.success) {
        success(`自定义服务连接成功 (${result.responseTime}ms)`);
        const models = await localAIServiceAdapter.getLocalServiceModels(customService);
        info(`找到 ${models.length} 个模型`);
    } else {
        error(`自定义服务连接失败: ${result.error}`);
    }
}
```

## 常见问题解决

### 1. 模块加载失败

**错误信息**：
```
❌ 无法加载服务模块: Cannot resolve module
💡 提示: 请确保项目已正确构建
```

**解决方法**：
```bash
# 重新构建项目
npm run build

# 检查构建输出
ls dist/
```

### 2. 权限错误

**错误信息**：
```
❌ 测试失败: Permission denied
```

**解决方法**：
```bash
# Windows
# 以管理员身份运行命令提示符

# 或者修改脚本权限
chmod +x scripts/test-local-ai-services.js
```

### 3. 端口被占用

**错误信息**：
```
❌ Ollama 连接失败: EADDRINUSE: address already in use
```

**解决方法**：
```bash
# 检查端口占用
netstat -ano | findstr :11434

# 停止占用端口的进程
taskkill /PID <进程ID> /F
```

### 4. 服务未启动

**错误信息**：
```
❌ 连接失败: ECONNREFUSED
```

**解决方法**：
```bash
# 启动对应的AI服务
ollama serve                    # 启动Ollama
# 或在LM Studio中启用本地服务器
# 或启动其他AI服务
```

## 集成到开发流程

### package.json 脚本配置

```json
{
  "scripts": {
    "test:local-ai": "node scripts/test-local-ai-services.js",
    "test:ai-full": "npm run build && npm run test:local-ai",
    "dev:ai": "npm run build && npm run test:local-ai && npm run dev"
  }
}
```

### CI/CD 集成示例

```yaml
# .github/workflows/test.yml
name: AI Services Test

on: [push, pull_request]

jobs:
  test-ai-services:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm install
      
    - name: Build project
      run: npm run build
      
    - name: Test AI services
      run: npm run test:local-ai
```

### 开发环境检查脚本

```bash
#!/bin/bash
# dev-check.sh

echo "🔍 检查开发环境..."

# 检查Node.js版本
node --version

# 构建项目
echo "🔨 构建项目..."
npm run build

# 测试AI服务
echo "🤖 测试AI服务..."
npm run test:local-ai

echo "✅ 开发环境检查完成"
```

## 扩展功能

### 添加新的AI服务支持

1. **在 `localAIServiceAdapter.ts` 中添加服务配置**：
```typescript
{
  name: 'New AI Service',
  baseUrl: 'http://localhost:9000',
  port: 9000,
  protocol: 'http',
  healthCheckPath: '/status',
  modelsPath: '/api/models',
  timeout: 10000
}
```

2. **在测试脚本中添加特定测试**：
```javascript
async function testNewAIService(adapter) {
  // 添加特定的测试逻辑
}
```

### 生成测试报告

```javascript
// 在 runTests 函数末尾添加
const report = {
  timestamp: new Date().toISOString(),
  totalServices: discoveredServices.length,
  onlineServices: discoveredServices.filter(s => s.status === 'online').length,
  testResults: results
};

// 保存报告到文件
fs.writeFileSync('test-report.json', JSON.stringify(report, null, 2));
```

## 相关文档

- [本地AI服务测试脚本API文档](./test-local-ai-services-script-api.md)
- [本地AI服务适配器API文档](./localAIServiceAdapter-api.md)
- [AI集成任务2完成总结](./ai-integration-task2-completion-summary.md)
- [本地AI服务测试指南](./local-ai-service-test-guide.md)