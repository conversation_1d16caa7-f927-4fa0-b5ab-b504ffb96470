// 冲突解决对话框组件单元测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import ConflictResolutionDialog from '../src/components/ConflictResolutionDialog'
import { ConflictItem } from '../src/types'

// 模拟测试数据
const mockConflicts: ConflictItem[] = [
  {
    id: 'conflict-1',
    type: 'bookmark',
    conflictType: 'duplicate',
    existingData: {
      id: 'existing-1',
      title: '现有收藏',
      url: 'https://example.com',
      description: '现有描述',
      tags: ['标签1']
    },
    importData: {
      title: '导入收藏',
      url: 'https://example.com',
      description: '导入描述',
      tags: ['标签2']
    },
    conflictFields: ['title', 'description', 'tags'],
    similarity: 0.9
  },
  {
    id: 'conflict-2',
    type: 'category',
    conflictType: 'name_conflict',
    existingData: {
      id: 'existing-2',
      name: '技术',
      description: '现有分类描述',
      color: '#blue'
    },
    importData: {
      name: '技术',
      description: '导入分类描述',
      color: '#red'
    },
    conflictFields: ['description', 'color'],
    similarity: 0.8
  }
]

describe('ConflictResolutionDialog', () => {
  const mockOnResolve = vi.fn()
  const mockOnCancel = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染对话框', () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    expect(screen.getByText('解决数据冲突')).toBeInTheDocument()
    expect(screen.getByText('发现 2 个冲突项，已解决 0 个')).toBeInTheDocument()
  })

  it('应该显示冲突列表', () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    expect(screen.getByText('冲突列表')).toBeInTheDocument()
    expect(screen.getByText('收藏 1 · 分类 1 · 标签 0')).toBeInTheDocument()
    expect(screen.getAllByText('导入收藏')).toHaveLength(3) // 在列表、详情和数据对比中都会显示
    expect(screen.getByText('技术')).toBeInTheDocument()
  })

  it('应该显示当前冲突的详细信息', () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    expect(screen.getAllByText('导入收藏')).toHaveLength(3) // 在列表、详情和数据对比中都会显示
    expect(screen.getByText('相似度: 90% · 冲突字段: title, description, tags')).toBeInTheDocument()
    expect(screen.getByText('现有数据')).toBeInTheDocument()
    expect(screen.getByText('导入数据')).toBeInTheDocument()
  })

  it('应该显示解决方案选项', () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    expect(screen.getByText('保留现有')).toBeInTheDocument()
    expect(screen.getByText('使用导入')).toBeInTheDocument()
    expect(screen.getByText('智能合并')).toBeInTheDocument()
    expect(screen.getByText('手动编辑')).toBeInTheDocument()
  })

  it('应该能够选择解决方案', async () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    const keepExistingButton = screen.getByText('保留现有').closest('button')!
    fireEvent.click(keepExistingButton)

    await waitFor(() => {
      expect(screen.getByText('发现 2 个冲突项，已解决 1 个')).toBeInTheDocument()
    })
  })

  it('应该能够导航到不同的冲突', async () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    // 点击第二个冲突项
    const secondConflictItem = screen.getByText('技术').closest('div')!
    fireEvent.click(secondConflictItem)

    await waitFor(() => {
      expect(screen.getByText('2 / 2')).toBeInTheDocument()
    })
  })

  it('应该能够进行批量操作', async () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    // 打开批量操作面板
    const batchButton = screen.getByText('批量操作')
    fireEvent.click(batchButton)

    await waitFor(() => {
      expect(screen.getByText('批量处理剩余 2 个冲突')).toBeInTheDocument()
    })

    // 执行批量保留现有
    const batchKeepButton = screen.getByText('全部保留现有')
    fireEvent.click(batchKeepButton)

    await waitFor(() => {
      expect(screen.getByText('发现 2 个冲突项，已解决 2 个')).toBeInTheDocument()
    })
  })

  it('应该能够手动编辑数据', async () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    // 点击手动编辑按钮
    const manualEditButton = screen.getByText('手动编辑').closest('button')!
    fireEvent.click(manualEditButton)

    await waitFor(() => {
      expect(screen.getByText('手动编辑数据')).toBeInTheDocument()
    })

    // 修改标题
    const titleInput = screen.getByDisplayValue('导入收藏')
    fireEvent.change(titleInput, { target: { value: '修改后的标题' } })

    // 保存修改
    const saveButton = screen.getByText('保存')
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.getByText('发现 2 个冲突项，已解决 1 个')).toBeInTheDocument()
    })
  })

  it('应该能够完成解决并调用回调', async () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    // 解决所有冲突
    const keepExistingButton = screen.getByText('保留现有').closest('button')!
    fireEvent.click(keepExistingButton)

    // 导航到第二个冲突 - 使用更具体的选择器
    const navigationButtons = screen.getAllByRole('button')
    const nextButton = navigationButtons.find(button => 
      button.querySelector('svg') && !button.disabled
    )
    if (nextButton) {
      fireEvent.click(nextButton)
    }

    await waitFor(() => {
      const useImportedButton = screen.getByText('使用导入').closest('button')!
      fireEvent.click(useImportedButton)
    })

    await waitFor(() => {
      const completeButton = screen.getByText('完成解决并导入')
      expect(completeButton).not.toBeDisabled()
      fireEvent.click(completeButton)
    })

    expect(mockOnResolve).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          conflictId: 'conflict-1',
          action: 'keep_existing'
        }),
        expect.objectContaining({
          conflictId: 'conflict-2',
          action: 'use_imported'
        })
      ])
    )
  })

  it('应该能够取消对话框', () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    const cancelButton = screen.getByText('取消导入')
    fireEvent.click(cancelButton)

    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('应该能够重置所有解决方案', async () => {
    render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    // 先解决一个冲突
    const keepExistingButton = screen.getByText('保留现有').closest('button')!
    fireEvent.click(keepExistingButton)

    await waitFor(() => {
      expect(screen.getByText('发现 2 个冲突项，已解决 1 个')).toBeInTheDocument()
    })

    // 重置所有解决方案
    const resetButton = screen.getByTitle('重置所有解决方案')
    fireEvent.click(resetButton)

    await waitFor(() => {
      expect(screen.getByText('发现 2 个冲突项，已解决 0 个')).toBeInTheDocument()
    })
  })

  it('当对话框关闭时不应该渲染', () => {
    const { container } = render(
      <ConflictResolutionDialog
        conflicts={mockConflicts}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={false}
      />
    )

    expect(container.firstChild).toBeNull()
  })

  it('当没有冲突时不应该渲染', () => {
    const { container } = render(
      <ConflictResolutionDialog
        conflicts={[]}
        onResolve={mockOnResolve}
        onCancel={mockOnCancel}
        isOpen={true}
      />
    )

    expect(container.firstChild).toBeNull()
  })
})