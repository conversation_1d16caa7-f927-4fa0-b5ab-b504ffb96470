# BookmarkService API 文档

## 概览

BookmarkService 是负责收藏功能核心业务逻辑的服务类，提供完整的收藏数据管理功能。

- **类名**: BookmarkService
- **文件路径**: src/services/bookmarkService.ts
- **公有方法**: 14 个
- **私有方法**: 6 个
- **总方法数**: 20 个

## 依赖导入

**从 `../types` 导入**:
- `Bookmark`
- `BookmarkInput`
- `BookmarkUpdate`
- `PageInfo`
- `BookmarkFilter`
- `SortOptions`
- `DuplicateGroup`

**从 `../utils/indexedDB` 导入**:
- `indexedDBService`

**从 `../utils/modelFactory` 导入**:
- `ModelFactory`

**从 `../utils/validation` 导入**:
- `ValidationUtils`

## 公有方法

### 保存操作

#### saveBookmark

**签名**: `async saveBookmark(input: BookmarkInput): Promise<string>`

**参数**:
- `input` (`BookmarkInput`)

**异步方法**: 是

**使用示例**:
```typescript
// 保存收藏
const bookmarkInput: BookmarkInput = {
  type: 'url',
  title: '示例网站',
  url: 'https://example.com',
  category: '默认分类',
  tags: ['示例', '网站']
}

try {
  const bookmarkId = await bookmarkService.saveBookmark(bookmarkInput)
  console.log('收藏保存成功:', bookmarkId)
} catch (error) {
  console.error('保存失败:', error)
}
```

---

#### saveBookmarkFromPageInfo

**签名**: `async saveBookmarkFromPageInfo(pageInfo: PageInfo, additionalData?: Partial<BookmarkInput>): Promise<string>`

**参数**:
- `pageInfo` (`PageInfo`)
- `additionalData` (`Partial<BookmarkInput>`) (可选)

**异步方法**: 是

**使用示例**:
```typescript
// 使用 saveBookmarkFromPageInfo 方法
try {
  const result = await bookmarkService.saveBookmarkFromPageInfo(/* 参数 */)
  console.log('操作成功:', result)
} catch (error) {
  console.error('操作失败:', error)
}
```

---

### 工具方法

#### quickBookmark

**签名**: `async quickBookmark(title: string, url: string, favicon?: string, selectedText?: string): Promise<string>`

**参数**:
- `title` (`string`)
- `url` (`string`)
- `favicon` (`string`) (可选)
- `selectedText` (`string`) (可选)

**异步方法**: 是

**使用示例**:
```typescript
// 快速收藏
try {
  const bookmarkId = await bookmarkService.quickBookmark(
    '页面标题',
    'https://example.com',
    'https://example.com/favicon.ico',
    '选中的文字内容'
  )
  console.log('快速收藏成功:', bookmarkId)
} catch (error) {
  console.error('快速收藏失败:', error)
}
```

---

#### bookmarkSelectedText

**签名**: `async bookmarkSelectedText(selectedText: string, url: string, title: string, context?: string): Promise<string>`

**参数**:
- `selectedText` (`string`)
- `url` (`string`)
- `title` (`string`)
- `context` (`string`) (可选)

**异步方法**: 是

**使用示例**:
```typescript
// 使用 bookmarkSelectedText 方法
try {
  const result = await bookmarkService.bookmarkSelectedText(/* 参数 */)
  console.log('操作成功:', result)
} catch (error) {
  console.error('操作失败:', error)
}
```

---

### 查询操作

#### getBookmark

**签名**: `async getBookmark(id: string): Promise<Bookmark | null>`

**参数**:
- `id` (`string`)

**异步方法**: 是

**使用示例**:
```typescript
// 获取单个收藏
try {
  const bookmark = await bookmarkService.getBookmark('bookmark-id')
  if (bookmark) {
    console.log('收藏信息:', bookmark)
  } else {
    console.log('收藏不存在')
  }
} catch (error) {
  console.error('获取失败:', error)
}
```

---

#### getBookmarks

**签名**: `async getBookmarks(filter?: BookmarkFilter, sort?: SortOptions): Promise<Bookmark[]>`

**参数**:
- `filter` (`BookmarkFilter`) (可选)
- `sort` (`SortOptions`) (可选)

**异步方法**: 是

**使用示例**:
```typescript
// 获取收藏列表
const filter: BookmarkFilter = {
  query: '搜索关键词',
  categories: ['技术'],
  limit: 10
}

const sort: SortOptions = {
  field: 'createdAt',
  direction: 'desc'
}

try {
  const bookmarks = await bookmarkService.getBookmarks(filter, sort)
  console.log('收藏列表:', bookmarks)
} catch (error) {
  console.error('获取失败:', error)
}
```

---

#### findBookmarkByUrl

**签名**: `async findBookmarkByUrl(url: string): Promise<Bookmark | null>`

**参数**:
- `url` (`string`)

**异步方法**: 是

**使用示例**:
```typescript
// 使用 findBookmarkByUrl 方法
try {
  const result = await bookmarkService.findBookmarkByUrl(/* 参数 */)
  console.log('操作成功:', result)
} catch (error) {
  console.error('操作失败:', error)
}
```

---

#### searchBookmarks

**签名**: `async searchBookmarks(query: string, filter?: Omit<BookmarkFilter): Promise<Bookmark[]>`

**参数**:
- `query` (`string`)
- `filter` (`Omit<BookmarkFilter`) (可选)

**异步方法**: 是

**使用示例**:
```typescript
// 搜索收藏
try {
  const results = await bookmarkService.searchBookmarks('关键词', {
    categories: ['技术'],
    type: 'url'
  })
  console.log('搜索结果:', results)
} catch (error) {
  console.error('搜索失败:', error)
}
```

---

#### getBookmarkStats

**签名**: `async getBookmarkStats(): Promise<`

**异步方法**: 是

**使用示例**:
```typescript
// 获取统计信息
try {
  const stats = await bookmarkService.getBookmarkStats()
  console.log('总收藏数:', stats.totalCount)
  console.log('类型统计:', stats.typeStats)
  console.log('分类统计:', stats.categoryStats)
  console.log('标签统计:', stats.tagStats)
  console.log('最近收藏:', stats.recentCount)
} catch (error) {
  console.error('获取统计失败:', error)
}
```

---

### 更新操作

#### updateBookmark

**签名**: `async updateBookmark(id: string, updates: BookmarkUpdate): Promise<void>`

**参数**:
- `id` (`string`)
- `updates` (`BookmarkUpdate`)

**异步方法**: 是

**使用示例**:
```typescript
// 更新收藏
const updates: BookmarkUpdate = {
  title: '新标题',
  tags: ['新标签1', '新标签2'],
  category: '新分类'
}

try {
  await bookmarkService.updateBookmark('bookmark-id', updates)
  console.log('更新成功')
} catch (error) {
  console.error('更新失败:', error)
}
```

---

### 删除操作

#### deleteBookmark

**签名**: `async deleteBookmark(id: string): Promise<void>`

**参数**:
- `id` (`string`)

**异步方法**: 是

**使用示例**:
```typescript
// 删除收藏
try {
  await bookmarkService.deleteBookmark('bookmark-id')
  console.log('删除成功')
} catch (error) {
  console.error('删除失败:', error)
}
```

---

#### deleteBookmarks

**签名**: `async deleteBookmarks(ids: string[]): Promise<void>`

**参数**:
- `ids` (`string[]`)

**异步方法**: 是

**使用示例**:
```typescript
// 使用 deleteBookmarks 方法
try {
  const result = await bookmarkService.deleteBookmarks(/* 参数 */)
  console.log('操作成功:', result)
} catch (error) {
  console.error('操作失败:', error)
}
```

---

### 验证操作

#### checkBookmarkStatus

**签名**: `async checkBookmarkStatus(url: string): string): Promise<`

**参数**:
- `url` (`string`)

**异步方法**: 是

**使用示例**:
```typescript
// 检查收藏状态
try {
  const status = await bookmarkService.checkBookmarkStatus('https://example.com')
  if (status.isBookmarked) {
    console.log('已收藏，ID:', status.bookmarkId)
  } else {
    console.log('未收藏')
  }
} catch (error) {
  console.error('检查失败:', error)
}
```

---

#### detectDuplicates

**签名**: `async detectDuplicates(threshold: number = 0.8): Promise<DuplicateGroup[]>`

**参数**:
- `threshold` (`number = 0.8`)

**异步方法**: 是

**使用示例**:
```typescript
// 检测重复收藏
try {
  const duplicates = await bookmarkService.detectDuplicates(0.8)
  console.log('发现重复组:', duplicates.length)
  duplicates.forEach(group => {
    console.log('重复类型:', group.type)
    console.log('相似度:', group.similarity)
    console.log('收藏数量:', group.bookmarks.length)
  })
} catch (error) {
  console.error('检测失败:', error)
}
```

---

## 私有方法

以下方法为内部使用，不建议直接调用：

### updateExistingBookmark

**签名**: `async updateExistingBookmark(existingBookmark: Bookmark, newInput: BookmarkInput): Promise<string>`

---

### updateTagUsageCount

**签名**: `async updateTagUsageCount(tags: string[], delta: number): Promise<void>`

---

### updateCategoryBookmarkCount

**签名**: `async updateCategoryBookmarkCount(categoryName: string, delta: number): Promise<void>`

---

### extractSiteName

**签名**: `extractSiteName(url: string): string`

---

### normalizeUrl

**签名**: `normalizeUrl(url: string): string`

---

### determineDuplicateType

**签名**: `determineDuplicateType(bookmarks: Bookmark[]): 'url' | 'content' | 'title'`

---

## 导出内容

- **class**: `BookmarkService`
- **const**: `bookmarkService`

## 使用指南

### 基本使用

```typescript
import { bookmarkService } from '../services/bookmarkService'

// BookmarkService 提供单例实例，可直接使用
const result = await bookmarkService.someMethod()
```

### 错误处理

所有异步方法都可能抛出错误，建议使用 try-catch 进行错误处理：

```typescript
try {
  const result = await bookmarkService.saveBookmark(input)
  // 处理成功结果
} catch (error) {
  console.error('操作失败:', error.message)
  // 处理错误情况
}
```

### 类型安全

BookmarkService 使用 TypeScript 编写，提供完整的类型支持：

```typescript
import { BookmarkInput, BookmarkFilter, SortOptions } from '../types'

// 类型安全的参数传递
const input: BookmarkInput = {
  type: 'url',
  title: '标题',
  url: 'https://example.com',
  category: '分类',
  tags: ['标签1', '标签2']
}
```

### 最佳实践

1. **数据验证**: 保存收藏前会自动进行数据验证
2. **重复检测**: 保存时会自动检查URL重复
3. **统计更新**: 操作会自动更新相关统计信息
4. **错误恢复**: 内部错误不会影响主要功能
5. **性能优化**: 支持分页和筛选以提高性能

### 依赖关系

BookmarkService 依赖以下模块：
- `indexedDBService`: 数据持久化
- `ModelFactory`: 数据模型创建
- `ValidationUtils`: 数据验证

### 单例模式

BookmarkService 使用单例模式，确保全局只有一个实例：

```typescript
// 导出的是单例实例，不是类
export const bookmarkService = new BookmarkService()
```

## 技术特性

- ✅ **类型安全**: 完整的 TypeScript 类型支持
- ✅ **异步操作**: 所有数据操作都是异步的
- ✅ **错误处理**: 完善的错误捕获和报告
- ✅ **数据验证**: 自动验证输入数据
- ✅ **重复检测**: 智能检测重复收藏
- ✅ **统计维护**: 自动维护统计信息
- ✅ **性能优化**: 支持分页、筛选和排序
- ✅ **模块化设计**: 低耦合的模块化架构

## 版本信息

- **当前版本**: 1.0.0
- **最后更新**: 2025/7/21
- **兼容性**: Chrome Extension Manifest V3
- **依赖**: IndexedDB, Chrome Storage API
