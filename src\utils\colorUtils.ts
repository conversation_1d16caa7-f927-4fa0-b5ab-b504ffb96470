// 颜色工具函数

/**
 * RGB颜色接口
 */
export interface RgbColor {
  r: number
  g: number
  b: number
}

/**
 * HSL颜色接口
 */
export interface HslColor {
  h: number
  s: number
  l: number
}

/**
 * 颜色工具类
 * 提供颜色相关的工具函数，包括颜色转换、验证、生成等功能
 */
export class ColorUtils {

  /**
   * 预设颜色列表
   */
  static readonly PRESET_COLORS: string[] = [
    '#3B82F6', // 蓝色
    '#10B981', // 绿色
    '#F59E0B', // 黄色
    '#EF4444', // 红色
    '#8B5CF6', // 紫色
    '#06B6D4', // 青色
    '#F97316', // 橙色
    '#84CC16', // 青绿色
    '#EC4899', // 粉色
    '#6B7280', // 灰色
    '#14B8A6', // 蓝绿色
    '#F472B6', // 粉红色
    '#A855F7', // 紫罗兰色
    '#22D3EE', // 天蓝色
    '#FDE047', // 柠檬黄
    '#FB7185', // 玫瑰色
    '#34D399', // 翠绿色
    '#FBBF24', // 琥珀色
    '#F87171', // 珊瑚红
    '#60A5FA'  // 天空蓝
  ]

  /**
   * 浅色主题预设颜色
   */
  static readonly LIGHT_THEME_COLORS: string[] = [
    '#EFF6FF', // 浅蓝色
    '#ECFDF5', // 浅绿色
    '#FFFBEB', // 浅黄色
    '#FEF2F2', // 浅红色
    '#F5F3FF', // 浅紫色
    '#ECFEFF', // 浅青色
    '#FFF7ED', // 浅橙色
    '#F7FEE7', // 浅青绿色
    '#FDF2F8', // 浅粉色
    '#F9FAFB'  // 浅灰色
  ]

  /**
   * 深色主题预设颜色
   */
  static readonly DARK_THEME_COLORS: string[] = [
    '#1E3A8A', // 深蓝色
    '#065F46', // 深绿色
    '#92400E', // 深黄色
    '#991B1B', // 深红色
    '#5B21B6', // 深紫色
    '#0E7490', // 深青色
    '#C2410C', // 深橙色
    '#365314', // 深青绿色
    '#BE185D', // 深粉色
    '#374151'  // 深灰色
  ]

  /**
   * 生成基于字符串的颜色
   * @param str 输入字符串
   * @param saturation 饱和度 (0-100)
   * @param lightness 亮度 (0-100)
   * @returns 十六进制颜色值
   */
  static generateColorFromString(
    str: string, 
    saturation: number = 70, 
    lightness: number = 50
  ): string {
    // 计算字符串的哈希值
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      hash = ((hash << 5) - hash + str.charCodeAt(i)) & 0xffffffff
    }

    // 将哈希值转换为色相值 (0-360)
    const hue = Math.abs(hash) % 360

    // 转换为HSL颜色
    const hslColor: HslColor = {
      h: hue,
      s: Math.max(0, Math.min(100, saturation)),
      l: Math.max(0, Math.min(100, lightness))
    }

    // 转换为十六进制
    return this.hslToHex(hslColor)
  }

  /**
   * 验证颜色格式
   * @param color 颜色字符串
   * @returns 是否为有效颜色
   */
  static isValidColor(color: string): boolean {
    if (!color || typeof color !== 'string') {
      return false
    }

    // 验证十六进制格式 (#ffffff, #fff)
    const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    if (hexPattern.test(color)) {
      return true
    }

    // 验证RGB格式 (rgb(255,255,255))
    const rgbPattern = /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/
    const rgbMatch = color.match(rgbPattern)
    if (rgbMatch) {
      const [, r, g, b] = rgbMatch
      return this.isValidRgbValue(parseInt(r)) && 
             this.isValidRgbValue(parseInt(g)) && 
             this.isValidRgbValue(parseInt(b))
    }

    // 验证RGBA格式
    const rgbaPattern = /^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(0|1|0?\.\d+)\s*\)$/
    const rgbaMatch = color.match(rgbaPattern)
    if (rgbaMatch) {
      const [, r, g, b, a] = rgbaMatch
      return this.isValidRgbValue(parseInt(r)) && 
             this.isValidRgbValue(parseInt(g)) && 
             this.isValidRgbValue(parseInt(b)) &&
             this.isValidAlphaValue(parseFloat(a))
    }

    // 验证HSL格式
    const hslPattern = /^hsl\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*\)$/
    const hslMatch = color.match(hslPattern)
    if (hslMatch) {
      const [, h, s, l] = hslMatch
      return parseInt(h) <= 360 && parseInt(s) <= 100 && parseInt(l) <= 100
    }

    return false
  }

  /**
   * 十六进制转RGB
   * @param hex 十六进制颜色值
   * @returns RGB颜色对象
   */
  static hexToRgb(hex: string): RgbColor | null {
    // 移除#号
    const cleanHex = hex.replace('#', '')
    
    // 处理3位十六进制
    let fullHex = cleanHex
    if (cleanHex.length === 3) {
      fullHex = cleanHex.split('').map(char => char + char).join('')
    }
    
    if (fullHex.length !== 6) {
      return null
    }

    const r = parseInt(fullHex.substr(0, 2), 16)
    const g = parseInt(fullHex.substr(2, 2), 16)
    const b = parseInt(fullHex.substr(4, 2), 16)

    if (isNaN(r) || isNaN(g) || isNaN(b)) {
      return null
    }

    return { r, g, b }
  }

  /**
   * RGB转十六进制
   * @param r 红色值 (0-255)
   * @param g 绿色值 (0-255)
   * @param b 蓝色值 (0-255)
   * @returns 十六进制颜色值
   */
  static rgbToHex(r: number, g: number, b: number): string {
    const toHex = (value: number): string => {
      const hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
  }

  /**
   * HSL转十六进制
   * @param hsl HSL颜色对象
   * @returns 十六进制颜色值
   */
  static hslToHex(hsl: HslColor): string {
    const rgb = this.hslToRgb(hsl)
    return this.rgbToHex(rgb.r, rgb.g, rgb.b)
  }

  /**
   * HSL转RGB
   * @param hsl HSL颜色对象
   * @returns RGB颜色对象
   */
  static hslToRgb(hsl: HslColor): RgbColor {
    const h = hsl.h / 360
    const s = hsl.s / 100
    const l = hsl.l / 100

    const hue2rgb = (p: number, q: number, t: number): number => {
      if (t < 0) t += 1
      if (t > 1) t -= 1
      if (t < 1/6) return p + (q - p) * 6 * t
      if (t < 1/2) return q
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
      return p
    }

    let r: number, g: number, b: number

    if (s === 0) {
      r = g = b = l // 无饱和度时为灰色
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      r = hue2rgb(p, q, h + 1/3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1/3)
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    }
  }

  /**
   * RGB转HSL
   * @param rgb RGB颜色对象
   * @returns HSL颜色对象
   */
  static rgbToHsl(rgb: RgbColor): HslColor {
    const r = rgb.r / 255
    const g = rgb.g / 255
    const b = rgb.b / 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h: number, s: number
    const l = (max + min) / 2

    if (max === min) {
      h = s = 0 // 无色相
    } else {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break
        case g: h = (b - r) / d + 2; break
        case b: h = (r - g) / d + 4; break
        default: h = 0
      }
      h /= 6
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    }
  }

  /**
   * 获取对比色（黑色或白色）
   * @param backgroundColor 背景颜色
   * @returns 对比色（#000000 或 #ffffff）
   */
  static getContrastColor(backgroundColor: string): string {
    const luminance = this.getLuminance(backgroundColor)
    return luminance > 0.5 ? '#000000' : '#ffffff'
  }

  /**
   * 计算颜色亮度
   * @param color 颜色值
   * @returns 亮度值 (0-1)
   */
  static getLuminance(color: string): number {
    const rgb = this.hexToRgb(color)
    if (!rgb) return 0

    // 转换为相对亮度
    const toLinear = (value: number): number => {
      const normalized = value / 255
      return normalized <= 0.03928 
        ? normalized / 12.92 
        : Math.pow((normalized + 0.055) / 1.055, 2.4)
    }

    const r = toLinear(rgb.r)
    const g = toLinear(rgb.g)
    const b = toLinear(rgb.b)

    // 使用ITU-R BT.709标准计算亮度
    return 0.2126 * r + 0.7152 * g + 0.0722 * b
  }

  /**
   * 计算两个颜色的相似度
   * @param color1 颜色1
   * @param color2 颜色2
   * @returns 相似度 (0-1)
   */
  static getColorSimilarity(color1: string, color2: string): number {
    const rgb1 = this.hexToRgb(color1)
    const rgb2 = this.hexToRgb(color2)
    
    if (!rgb1 || !rgb2) return 0

    // 计算欧几里得距离
    const distance = Math.sqrt(
      Math.pow(rgb1.r - rgb2.r, 2) +
      Math.pow(rgb1.g - rgb2.g, 2) +
      Math.pow(rgb1.b - rgb2.b, 2)
    )

    // 最大距离为sqrt(3 * 255^2) ≈ 441.67
    const maxDistance = Math.sqrt(3 * Math.pow(255, 2))
    
    // 转换为相似度 (距离越小，相似度越高)
    return 1 - (distance / maxDistance)
  }

  /**
   * 生成颜色变体
   * @param baseColor 基础颜色
   * @param options 生成选项
   * @returns 颜色变体数组
   */
  static generateColorVariants(
    baseColor: string, 
    options: {
      lighterSteps?: number
      darkerSteps?: number
      stepSize?: number
    } = {}
  ): string[] {
    const { lighterSteps = 3, darkerSteps = 3, stepSize = 10 } = options
    const variants: string[] = []
    
    const rgb = this.hexToRgb(baseColor)
    if (!rgb) return [baseColor]

    const hsl = this.rgbToHsl(rgb)

    // 生成更亮的变体
    for (let i = 1; i <= lighterSteps; i++) {
      const lightness = Math.min(100, hsl.l + (stepSize * i))
      const variant = this.hslToHex({ ...hsl, l: lightness })
      variants.push(variant)
    }

    // 添加原色
    variants.push(baseColor)

    // 生成更暗的变体
    for (let i = 1; i <= darkerSteps; i++) {
      const lightness = Math.max(0, hsl.l - (stepSize * i))
      const variant = this.hslToHex({ ...hsl, l: lightness })
      variants.push(variant)
    }

    return variants
  }

  /**
   * 调整颜色亮度
   * @param color 原始颜色
   * @param amount 调整量 (-100 到 100)
   * @returns 调整后的颜色
   */
  static adjustBrightness(color: string, amount: number): string {
    const rgb = this.hexToRgb(color)
    if (!rgb) return color

    const hsl = this.rgbToHsl(rgb)
    const newLightness = Math.max(0, Math.min(100, hsl.l + amount))
    
    return this.hslToHex({ ...hsl, l: newLightness })
  }

  /**
   * 调整颜色饱和度
   * @param color 原始颜色
   * @param amount 调整量 (-100 到 100)
   * @returns 调整后的颜色
   */
  static adjustSaturation(color: string, amount: number): string {
    const rgb = this.hexToRgb(color)
    if (!rgb) return color

    const hsl = this.rgbToHsl(rgb)
    const newSaturation = Math.max(0, Math.min(100, hsl.s + amount))
    
    return this.hslToHex({ ...hsl, s: newSaturation })
  }

  /**
   * 获取互补色
   * @param color 原始颜色
   * @returns 互补色
   */
  static getComplementaryColor(color: string): string {
    const rgb = this.hexToRgb(color)
    if (!rgb) return color

    const hsl = this.rgbToHsl(rgb)
    const complementaryHue = (hsl.h + 180) % 360
    
    return this.hslToHex({ ...hsl, h: complementaryHue })
  }

  /**
   * 获取类似色
   * @param color 原始颜色
   * @param count 返回数量
   * @param range 色相范围
   * @returns 类似色数组
   */
  static getAnalogousColors(color: string, count: number = 2, range: number = 30): string[] {
    const rgb = this.hexToRgb(color)
    if (!rgb) return [color]

    const hsl = this.rgbToHsl(rgb)
    const colors: string[] = []
    
    const step = range / count
    for (let i = 1; i <= count; i++) {
      const hue1 = (hsl.h + (step * i)) % 360
      const hue2 = (hsl.h - (step * i) + 360) % 360
      
      colors.push(this.hslToHex({ ...hsl, h: hue1 }))
      if (colors.length < count) {
        colors.push(this.hslToHex({ ...hsl, h: hue2 }))
      }
    }
    
    return colors.slice(0, count)
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 验证RGB值是否有效
   * @param value RGB值
   * @returns 是否有效
   */
  private static isValidRgbValue(value: number): boolean {
    return !isNaN(value) && value >= 0 && value <= 255
  }

  /**
   * 验证Alpha值是否有效
   * @param value Alpha值
   * @returns 是否有效
   */
  private static isValidAlphaValue(value: number): boolean {
    return !isNaN(value) && value >= 0 && value <= 1
  }
}