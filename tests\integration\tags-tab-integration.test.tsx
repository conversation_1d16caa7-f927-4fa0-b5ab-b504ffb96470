// TagsTab集成测试 - 验证与OptionsApp的兼容性

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import TagsTab from '../../src/components/TagsTab'
import { tagService } from '../../src/services/tagService'

// Mock tagService
vi.mock('../../src/services/tagService', () => ({
  tagService: {
    syncTagsFromBookmarks: vi.fn(),
    getAllTagsWithStats: vi.fn(),
    createTag: vi.fn(),
    updateTag: vi.fn(),
    deleteTag: vi.fn(),
    validateTagName: vi.fn(),
    getTagUsageCount: vi.fn(),
    getBookmarksByTag: vi.fn(),
    searchTags: vi.fn()
  }
}))

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn(),
    onMessage: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  },
  storage: {
    local: {
      get: vi.fn(),
      set: vi.fn(),
      remove: vi.fn()
    }
  }
}

// 设置全局Chrome对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

describe('TagsTab集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 默认成功的mock响应
    vi.mocked(tagService.syncTagsFromBookmarks).mockResolvedValue(undefined)
    vi.mocked(tagService.getAllTagsWithStats).mockResolvedValue([])
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('与OptionsApp的兼容性', () => {
    it('应该作为标签页内容正常渲染', async () => {
      const { container } = render(<TagsTab />)
      
      // 组件应该正常渲染
      expect(container.firstChild).toBeInTheDocument()
      
      // 等待初始化完成
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 应该显示标签管理功能说明
      expect(screen.getByText('标签管理功能')).toBeInTheDocument()
    })

    it('应该集成TagManagementTab组件', async () => {
      render(<TagsTab />)
      
      // 等待初始化完成
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 应该显示TagManagementTab的内容
      expect(screen.getByText('标签管理')).toBeInTheDocument()
      expect(screen.getByText('新建标签')).toBeInTheDocument()
    })

    it('应该支持自定义CSS类名', async () => {
      const customClass = 'custom-tags-tab'
      const { container } = render(<TagsTab className={customClass} />)
      
      expect(container.firstChild).toHaveClass(customClass)
    })

    it('应该处理组件卸载', async () => {
      const { unmount } = render(<TagsTab />)
      
      expect(() => unmount()).not.toThrow()
    })
  })

  describe('数据同步功能', () => {
    it('应该支持手动同步标签', async () => {
      render(<TagsTab />)
      
      // 等待初始化完成
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 点击手动同步按钮
      const syncButton = screen.getByRole('button', { name: '手动同步' })
      fireEvent.click(syncButton)
      
      // 验证同步服务被调用
      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalled()
      })
    })

    it('应该处理同步错误', async () => {
      // 模拟同步失败
      vi.mocked(tagService.syncTagsFromBookmarks).mockRejectedValue(new Error('同步失败'))
      
      render(<TagsTab />)
      
      // 等待初始化完成
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 点击手动同步按钮
      const syncButton = screen.getByRole('button', { name: '手动同步' })
      fireEvent.click(syncButton)
      
      // 应该显示错误信息
      await waitFor(() => {
        expect(screen.getByText('标签同步失败')).toBeInTheDocument()
      })
    })
  })

  describe('错误处理', () => {
    it('应该处理Chrome API不可用的情况', async () => {
      // 临时设置Chrome API为undefined
      const originalChrome = global.chrome
      ;(global as any).chrome = undefined
      
      render(<TagsTab />)
      
      // 应该显示初始化错误
      await waitFor(() => {
        expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
        expect(screen.getByText(/Chrome扩展API不可用/)).toBeInTheDocument()
      })
      
      // 恢复Chrome API
      global.chrome = originalChrome
    })

    it('应该提供重试功能', async () => {
      // 模拟Chrome API不可用
      const originalChrome = global.chrome
      ;(global as any).chrome = undefined
      
      render(<TagsTab />)
      
      // 等待错误显示
      await waitFor(() => {
        expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
      })
      
      // 恢复Chrome API
      global.chrome = originalChrome
      
      // 点击重试按钮
      const retryButton = screen.getByRole('button', { name: /重试/ })
      fireEvent.click(retryButton)
      
      // 应该重新初始化成功
      await waitFor(() => {
        expect(screen.queryByText('标签管理初始化失败')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 应该显示正常内容
      expect(screen.getByText('标签管理功能')).toBeInTheDocument()
    })
  })

  describe('用户界面', () => {
    it('应该显示功能说明', async () => {
      render(<TagsTab />)
      
      // 等待初始化完成
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 应该显示功能说明
      expect(screen.getByText('标签管理功能')).toBeInTheDocument()
      expect(screen.getByText(/在这里您可以查看、创建、编辑和删除标签/)).toBeInTheDocument()
    })

    it('应该提供用户友好的界面', async () => {
      render(<TagsTab />)
      
      // 等待初始化完成
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 应该有手动同步按钮
      expect(screen.getByRole('button', { name: '手动同步' })).toBeInTheDocument()
      
      // 应该有标签管理相关的按钮
      expect(screen.getByRole('button', { name: '新建标签' })).toBeInTheDocument()
    })
  })

  describe('性能和稳定性', () => {
    it('应该处理快速重新渲染', async () => {
      const { rerender } = render(<TagsTab />)
      
      // 快速重新渲染多次
      for (let i = 0; i < 5; i++) {
        rerender(<TagsTab />)
      }
      
      // 组件应该仍然正常工作
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      expect(screen.getByText('标签管理功能')).toBeInTheDocument()
    })

    it('应该正确清理资源', async () => {
      const { unmount } = render(<TagsTab />)
      
      // 等待初始化完成
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 卸载组件不应该抛出错误
      expect(() => unmount()).not.toThrow()
    })
  })
})