// AI聊天服务修复测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { aiChatService } from '../src/services/aiChatService'
import { aiIntegrationService } from '../src/services/aiIntegrationService'
import { DefaultAIModelAPI } from '../src/services/defaultAIModelAPI'
import { ChromeStorageService } from '../src/utils/chromeStorage'

// Mock Chrome Storage
vi.mock('../src/utils/chromeStorage', () => ({
  ChromeStorageService: {
    getSyncSetting: vi.fn(),
    saveSyncSetting: vi.fn(),
    removeSyncSetting: vi.fn(),
    getLocalSetting: vi.fn(),
    saveLocalSetting: vi.fn()
  }
}))

// Mock AI Integration Service
vi.mock('../src/services/aiIntegrationService', () => ({
  aiIntegrationService: {
    getConfiguredProviders: vi.fn(),
    getAvailableModels: vi.fn(),
    testConnection: vi.fn()
  }
}))

// Mock Default AI Model API
vi.mock('../src/services/defaultAIModelAPI', () => ({
  DefaultAIModelAPI: {
    getDefaultChatModel: vi.fn()
  }
}))

describe('AI聊天服务修复测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('提供商配置不存在问题修复', () => {
    it('应该能处理模型ID格式不匹配的情况', async () => {
      // 模拟默认聊天模型返回旧格式的提供商ID
      const mockDefaultModel = {
        id: 'ollama_1755502009689',
        name: 'llama2',
        displayName: 'Llama 2',
        provider: 'Ollama',
        providerId: 'ollama'
      }

      // 模拟配置的提供商使用新格式的ID
      const mockProviders = [
        {
          id: 'ollama_1703123456789',
          name: 'Ollama本地',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true
        }
      ]

      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockDefaultModel)
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue(mockProviders)
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue()

      const request = {
        prompt: '请为以下内容生成一个简洁明了的描述：测试内容',
        generationType: 'description' as const,
        maxLength: 200
      }

      // 第一次调用应该会发现提供商ID不匹配，然后按类型查找并更新配置
      try {
        await aiChatService.generateText(request)
      } catch (error) {
        // 预期会有错误，因为我们没有完全模拟所有依赖
        expect(error).toBeDefined()
      }

      // 验证是否尝试按类型查找提供商
      expect(aiIntegrationService.getConfiguredProviders).toHaveBeenCalled()
    })

    it('应该能正确解析新格式的模型ID', async () => {
      const mockDefaultModel = {
        id: 'ollama_1703123456789_llama2',
        name: 'llama2',
        displayName: 'Llama 2',
        provider: 'Ollama本地',
        providerId: 'ollama_1703123456789'
      }

      const mockProviders = [
        {
          id: 'ollama_1703123456789',
          name: 'Ollama本地',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true
        }
      ]

      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockDefaultModel)
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue(mockProviders)

      const request = {
        prompt: '测试提示词',
        generationType: 'description' as const
      }

      try {
        await aiChatService.generateText(request)
      } catch (error) {
        // 预期会有错误，因为我们没有完全模拟所有依赖
        expect(error).toBeDefined()
      }

      // 验证提供商查找逻辑
      expect(aiIntegrationService.getConfiguredProviders).toHaveBeenCalled()
    })

    it('应该能处理没有配置默认模型的情况', async () => {
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(null)
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(null)
      
      const mockProviders = [
        {
          id: 'ollama_1703123456789',
          name: 'Ollama本地',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true
        }
      ]

      const mockModels = [
        {
          id: 'llama2',
          name: 'llama2',
          displayName: 'Llama 2',
          isRecommended: true
        }
      ]

      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue(mockProviders)
      vi.mocked(aiIntegrationService.getAvailableModels).mockResolvedValue(mockModels)
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue()

      const request = {
        prompt: '测试提示词',
        generationType: 'description' as const
      }

      try {
        await aiChatService.generateText(request)
      } catch (error) {
        // 预期会有错误，因为我们没有完全模拟所有依赖
        expect(error).toBeDefined()
      }

      // 验证是否尝试自动配置默认模型
      expect(aiIntegrationService.getAvailableModels).toHaveBeenCalledWith('ollama_1703123456789')
      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalled()
    })
  })

  describe('错误处理改进', () => {
    it('应该提供更详细的错误信息', async () => {
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(null)
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(null)
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue([])

      const request = {
        prompt: '测试提示词',
        generationType: 'description' as const
      }

      await expect(aiChatService.generateText(request)).rejects.toThrow(
        '未找到可用的AI模型，请先在"默认AI模型"页面配置模型'
      )
    })

    it('应该能处理提供商连接失败的情况', async () => {
      const mockDefaultModel = {
        id: 'ollama_1703123456789_llama2',
        name: 'llama2',
        displayName: 'Llama 2',
        provider: 'Ollama本地',
        providerId: 'ollama_1703123456789'
      }

      const mockProviders = [
        {
          id: 'ollama_1703123456789',
          name: 'Ollama本地',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true
        }
      ]

      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockDefaultModel)
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue(mockProviders)

      const request = {
        prompt: '测试提示词',
        generationType: 'description' as const
      }

      try {
        await aiChatService.generateText(request)
      } catch (error) {
        expect(error).toBeDefined()
      }

      // 验证提供商查找
      expect(aiIntegrationService.getConfiguredProviders).toHaveBeenCalled()
    })
  })

  describe('兼容性处理', () => {
    it('应该能处理旧版本的配置格式', async () => {
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(null)
      
      const legacyConfig = {
        providerId: 'ollama',
        modelId: 'llama2'
      }

      const mockProviders = [
        {
          id: 'ollama_1703123456789',
          name: 'Ollama本地',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(legacyConfig)
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue(mockProviders)
      vi.mocked(ChromeStorageService.removeSyncSetting).mockResolvedValue()

      const request = {
        prompt: '测试提示词',
        generationType: 'description' as const
      }

      try {
        await aiChatService.generateText(request)
      } catch (error) {
        expect(error).toBeDefined()
      }

      // 验证是否清理了无效的旧配置
      expect(ChromeStorageService.removeSyncSetting).toHaveBeenCalled()
    })
  })
})