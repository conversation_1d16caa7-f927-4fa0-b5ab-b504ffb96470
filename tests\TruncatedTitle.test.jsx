// 标题截断组件单元测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import TruncatedTitle from '../src/components/TruncatedTitle'

import { vi } from 'vitest'

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn((element) => {
    // 模拟ResizeObserver回调
    setTimeout(() => {
      callback([{
        target: element,
        contentRect: { width: 300, height: 20 }
      }])
    }, 0)
  }),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock Canvas for text measurement
HTMLCanvasElement.prototype.getContext = vi.fn(() => ({
  measureText: vi.fn(() => ({ width: 100 })),
  font: ''
}))

describe('TruncatedTitle', () => {
  const longTitle = '这是一个非常长的标题，用来测试截断功能是否正常工作，应该会被截断显示'
  const shortTitle = '短标题'

  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()
  })

  describe('基本渲染', () => {
    test('应该正确渲染短标题', () => {
      render(<TruncatedTitle title={shortTitle} />)
      
      expect(screen.getByText(shortTitle)).toBeInTheDocument()
    })

    test('应该截断长标题', () => {
      render(<TruncatedTitle title={longTitle} maxLength={20} />)
      
      const titleElement = screen.getByText(/这是一个非常长的标题/)
      expect(titleElement.textContent).toHaveLength(20)
      expect(titleElement.textContent).toMatch(/\.\.\./)
    })

    test('应该应用自定义CSS类名', () => {
      const customClass = 'custom-title-class'
      render(<TruncatedTitle title={shortTitle} className={customClass} />)
      
      const titleElement = screen.getByText(shortTitle)
      expect(titleElement).toHaveClass(customClass)
    })
  })

  describe('截断位置', () => {
    const testTitle = '1234567890ABCDEFGHIJ'
    const maxLen = 10

    test('应该在末尾截断（默认）', () => {
      render(<TruncatedTitle title={testTitle} maxLength={maxLen} />)
      
      const titleElement = screen.getByText(/1234567\.\.\./)
      expect(titleElement.textContent).toBe('1234567...')
    })

    test('应该在开头截断', () => {
      render(<TruncatedTitle title={testTitle} maxLength={maxLen} truncateAt="start" />)
      
      const titleElement = screen.getByText(/\.\.\./)
      expect(titleElement.textContent).toMatch(/^\.\.\./)
      expect(titleElement.textContent.length).toBe(maxLen)
    })

    test('应该在中间截断', () => {
      render(<TruncatedTitle title={testTitle} maxLength={maxLen} truncateAt="middle" />)
      
      const titleElement = screen.getByText(/123\.\.\.HIJ/)
      expect(titleElement.textContent).toBe('123...HIJ')
    })
  })

  describe('自定义省略号', () => {
    test('应该使用自定义省略号', () => {
      const customEllipsis = '---'
      render(
        <TruncatedTitle 
          title={longTitle} 
          maxLength={20} 
          ellipsis={customEllipsis}
        />
      )
      
      const titleElement = screen.getByText(/---/)
      expect(titleElement.textContent).toMatch(/---/)
      expect(titleElement.textContent.length).toBe(20)
    })
  })

  describe('悬停提示', () => {
    test('应该在悬停时显示完整标题', async () => {
      render(<TruncatedTitle title={longTitle} maxLength={20} />)
      
      const titleElement = screen.getByText(/这是一个非常长的标题/)
      
      // 模拟鼠标悬停
      fireEvent.mouseEnter(titleElement)
      
      // 等待提示框出现
      await waitFor(() => {
        expect(screen.getByText(longTitle)).toBeInTheDocument()
      })
    })

    test('应该在鼠标离开时隐藏提示', async () => {
      render(<TruncatedTitle title={longTitle} maxLength={20} />)
      
      const titleElement = screen.getByText(/这是一个非常长的标题/)
      
      // 悬停显示提示
      fireEvent.mouseEnter(titleElement)
      await waitFor(() => {
        expect(screen.getByText(longTitle)).toBeInTheDocument()
      })
      
      // 鼠标离开隐藏提示
      fireEvent.mouseLeave(titleElement)
      await waitFor(() => {
        expect(screen.queryByText(longTitle)).not.toBeInTheDocument()
      })
    })

    test('应该可以禁用悬停提示', () => {
      render(<TruncatedTitle title={longTitle} maxLength={20} showTooltip={false} />)
      
      const titleElement = screen.getByText(/这是一个非常长的标题/)
      
      // 悬停不应该显示提示
      fireEvent.mouseEnter(titleElement)
      
      expect(screen.queryByText(longTitle)).not.toBeInTheDocument()
    })

    test('短标题不应该显示悬停提示', () => {
      render(<TruncatedTitle title={shortTitle} maxLength={50} />)
      
      const titleElement = screen.getByText(shortTitle)
      
      // 悬停不应该显示提示
      fireEvent.mouseEnter(titleElement)
      
      expect(titleElement).not.toHaveAttribute('title')
    })
  })

  describe('响应式行为', () => {
    test('应该在窗口大小变化时重新检测溢出', () => {
      render(<TruncatedTitle title={longTitle} maxLength={20} />)
      
      // 模拟窗口大小变化
      fireEvent(window, new Event('resize'))
      
      // 验证组件仍然正常工作
      expect(screen.getByText(/这是一个非常长的标题/)).toBeInTheDocument()
    })
  })

  describe('边界情况', () => {
    test('应该处理空标题', () => {
      const { container } = render(<TruncatedTitle title="" />)
      
      const titleElement = container.querySelector('span')
      expect(titleElement).toBeInTheDocument()
      expect(titleElement.textContent).toBe('')
    })

    test('应该处理null或undefined标题', () => {
      // 使用空字符串作为默认值
      const { container } = render(<TruncatedTitle title={null || ''} />)
      
      const titleElement = container.querySelector('span')
      expect(titleElement).toBeInTheDocument()
      expect(titleElement.textContent).toBe('')
    })

    test('应该处理maxLength为0的情况', () => {
      render(<TruncatedTitle title={longTitle} maxLength={0} />)
      
      // maxLength为0时，应该显示原文本（因为没有有效的截断长度）
      const titleElement = screen.getByText(longTitle)
      expect(titleElement.textContent).toBe(longTitle)
    })

    test('应该处理maxLength小于省略号长度的情况', () => {
      render(<TruncatedTitle title={longTitle} maxLength={2} />)
      
      const titleElement = screen.getByText('...')
      expect(titleElement.textContent).toBe('...')
    })
  })

  describe('基于容器宽度的截断', () => {
    test('应该基于容器宽度进行截断', async () => {
      render(
        <div style={{ width: '200px' }}>
          <TruncatedTitle 
            title={longTitle} 
            useContainerWidth={true}
            fontSize={14}
          />
        </div>
      )
      
      // 等待ResizeObserver触发
      await waitFor(() => {
        const titleElement = screen.getByText(/这是一个/)
        expect(titleElement).toBeInTheDocument()
      })
    })

    test('应该可以禁用基于容器宽度的截断', () => {
      render(
        <TruncatedTitle 
          title={longTitle} 
          maxLength={20}
          useContainerWidth={false}
        />
      )
      
      const titleElement = screen.getByText(/这是一个非常长的标题/)
      expect(titleElement.textContent).toHaveLength(20)
    })
  })

  describe('多行文本支持', () => {
    test('应该支持多行文本显示', () => {
      const multilineTitle = '第一行文本\n第二行文本\n第三行文本'
      render(
        <TruncatedTitle 
          title={multilineTitle} 
          maxLines={2}
        />
      )
      
      const titleElement = screen.getByText(/第一行文本/)
      // 检查是否应用了多行样式
      expect(titleElement).toHaveStyle({
        display: '-webkit-box'
      })
      // 检查WebkitLineClamp属性
      expect(titleElement.style.WebkitLineClamp).toBe('2')
    })

    test('单行模式应该使用truncate样式', () => {
      render(<TruncatedTitle title={longTitle} maxLines={1} />)
      
      const titleElement = screen.getByText(/这是一个/)
      expect(titleElement).toHaveClass('truncate')
    })
  })

  describe('字体和样式配置', () => {
    test('应该应用自定义字体大小', () => {
      const customFontSize = 16
      render(
        <TruncatedTitle 
          title={shortTitle} 
          fontSize={customFontSize}
        />
      )
      
      const titleElement = screen.getByText(shortTitle)
      expect(titleElement).toHaveStyle({
        fontSize: `${customFontSize}px`
      })
    })

    test('应该应用自定义字体族', () => {
      const customFontFamily = 'Arial, sans-serif'
      render(
        <TruncatedTitle 
          title={shortTitle} 
          fontFamily={customFontFamily}
        />
      )
      
      const titleElement = screen.getByText(shortTitle)
      expect(titleElement).toHaveStyle({
        fontFamily: customFontFamily
      })
    })
  })

  describe('容器样式', () => {
    test('容器应该有正确的样式以防止溢出', () => {
      const { container } = render(<TruncatedTitle title={longTitle} />)
      
      const containerDiv = container.firstChild
      expect(containerDiv).toHaveStyle({
        minWidth: '0'
      })
      expect(containerDiv).toHaveClass('relative', 'w-full')
    })

    test('文本元素应该有正确的溢出处理样式', () => {
      render(<TruncatedTitle title={longTitle} maxLength={20} />)
      
      const titleElement = screen.getByText(/这是一个/)
      expect(titleElement).toHaveStyle({
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        wordBreak: 'break-word',
        overflowWrap: 'break-word'
      })
    })
  })

  describe('可访问性', () => {
    test('应该为截断的标题设置title属性', () => {
      render(<TruncatedTitle title={longTitle} maxLength={20} />)
      
      const titleElement = screen.getByText(/这是一个非常长的标题/)
      expect(titleElement).toHaveAttribute('title', longTitle)
    })

    test('应该为长标题设置适当的cursor样式', () => {
      render(<TruncatedTitle title={longTitle} maxLength={20} />)
      
      const titleElement = screen.getByText(/这是一个非常长的标题/)
      expect(titleElement).toHaveClass('cursor-help')
    })

    test('短标题不应该有特殊的cursor样式', () => {
      render(<TruncatedTitle title={shortTitle} />)
      
      const titleElement = screen.getByText(shortTitle)
      expect(titleElement).toHaveClass('cursor-default')
      expect(titleElement).not.toHaveClass('cursor-help')
    })
  })
})