// 缓存管理器 - 提供解析结果、冲突检测结果和用户配置的缓存功能

/**
 * 缓存项接口
 */
export interface CacheItem<T> {
  key: string
  data: T
  timestamp: Date
  expiresAt: Date
  accessCount: number
  lastAccessed: Date
  size: number // 数据大小估算（字节）
}

/**
 * 缓存键生成器接口
 */
export interface CacheKeyGenerator {
  generateKey(prefix: string, identifier: string): string
}

/**
 * 数据压缩器接口
 */
export interface DataCompressor {
  compress(data: any): any
  decompress(data: any): any
}

/**
 * 缓存清理策略接口
 */
export interface CacheEvictionStrategy {
  selectItemsForRemoval(items: Map<string, CacheItem<any>>, removeCount: number): string[]
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  maxSize: number // 最大缓存大小（MB）
  maxItems: number // 最大缓存项数
  defaultTTL: number // 默认过期时间（毫秒）
  cleanupInterval: number // 清理间隔（毫秒）
  enableCompression: boolean // 是否启用压缩
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  totalItems: number
  totalSize: number // MB
  hitCount: number
  missCount: number
  hitRate: number
  oldestItem?: Date
  newestItem?: Date
  averageAccessCount: number
}

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  LRU = 'LRU', // 最近最少使用
  LFU = 'LFU', // 最少使用频率
  FIFO = 'FIFO', // 先进先出
  TTL = 'TTL' // 基于时间过期
}

/**
 * 默认缓存键生成器
 */
class DefaultCacheKeyGenerator implements CacheKeyGenerator {
  generateKey(prefix: string, identifier: string): string {
    return `${prefix}_${identifier}`
  }
}

/**
 * 默认数据压缩器（无压缩）
 */
class DefaultDataCompressor implements DataCompressor {
  compress(data: any): any {
    // 这里可以实现实际的压缩算法，如 LZ4、Gzip 等
    return data
  }

  decompress(data: any): any {
    // 这里可以实现实际的解压缩算法
    return data
  }
}

/**
 * LRU 清理策略
 */
class LRUEvictionStrategy implements CacheEvictionStrategy {
  selectItemsForRemoval(items: Map<string, CacheItem<any>>, removeCount: number): string[] {
    return Array.from(items.entries())
      .sort((a, b) => a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime())
      .slice(0, removeCount)
      .map(([key]) => key)
  }
}

/**
 * LFU 清理策略
 */
class LFUEvictionStrategy implements CacheEvictionStrategy {
  selectItemsForRemoval(items: Map<string, CacheItem<any>>, removeCount: number): string[] {
    return Array.from(items.entries())
      .sort((a, b) => a[1].accessCount - b[1].accessCount)
      .slice(0, removeCount)
      .map(([key]) => key)
  }
}

/**
 * FIFO 清理策略
 */
class FIFOEvictionStrategy implements CacheEvictionStrategy {
  selectItemsForRemoval(items: Map<string, CacheItem<any>>, removeCount: number): string[] {
    return Array.from(items.entries())
      .sort((a, b) => a[1].timestamp.getTime() - b[1].timestamp.getTime())
      .slice(0, removeCount)
      .map(([key]) => key)
  }
}

/**
 * TTL 清理策略
 */
class TTLEvictionStrategy implements CacheEvictionStrategy {
  selectItemsForRemoval(items: Map<string, CacheItem<any>>, removeCount: number): string[] {
    return Array.from(items.entries())
      .sort((a, b) => a[1].expiresAt.getTime() - b[1].expiresAt.getTime())
      .slice(0, removeCount)
      .map(([key]) => key)
  }
}

/**
 * 清理策略工厂
 */
class EvictionStrategyFactory {
  private static strategies = new Map<CacheStrategy, CacheEvictionStrategy>([
    [CacheStrategy.LRU, new LRUEvictionStrategy()],
    [CacheStrategy.LFU, new LFUEvictionStrategy()],
    [CacheStrategy.FIFO, new FIFOEvictionStrategy()],
    [CacheStrategy.TTL, new TTLEvictionStrategy()]
  ])

  static getStrategy(strategy: CacheStrategy): CacheEvictionStrategy {
    const evictionStrategy = this.strategies.get(strategy)
    if (!evictionStrategy) {
      throw new Error(`不支持的缓存策略: ${strategy}`)
    }
    return evictionStrategy
  }
}

/**
 * 缓存管理器类
 * 提供多种缓存策略和自动清理机制
 */
export class CacheManager {
  private cache: Map<string, CacheItem<any>> = new Map()
  private config: CacheConfig
  private stats = {
    hitCount: 0,
    missCount: 0
  }
  private cleanupTimer?: NodeJS.Timeout
  private keyGenerator: CacheKeyGenerator
  private compressor: DataCompressor

  private readonly defaultConfig: CacheConfig = {
    maxSize: 50, // 50MB
    maxItems: 1000,
    defaultTTL: 30 * 60 * 1000, // 30分钟
    cleanupInterval: 5 * 60 * 1000, // 5分钟
    enableCompression: true
  }

  constructor(
    config: Partial<CacheConfig> = {},
    keyGenerator: CacheKeyGenerator = new DefaultCacheKeyGenerator(),
    compressor: DataCompressor = new DefaultDataCompressor()
  ) {
    this.config = { ...this.defaultConfig, ...config }
    this.keyGenerator = keyGenerator
    this.compressor = compressor
    this.startCleanupTimer()
  }

  /**
   * 设置缓存项
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 过期时间（毫秒）
   * @returns boolean 是否设置成功
   */
  set<T>(key: string, data: T, ttl?: number): boolean {
    try {
      const now = new Date()
      const expirationTime = ttl || this.config.defaultTTL
      const size = this.estimateSize(data)

      // 检查是否需要清理空间
      if (this.needsCleanup(size)) {
        this.cleanup()
      }

      // 如果仍然没有足够空间，拒绝缓存
      if (!this.hasSpace(size)) {
        console.warn(`缓存空间不足，无法缓存键: ${key}`)
        return false
      }

      const cacheItem: CacheItem<T> = {
        key,
        data: this.config.enableCompression ? this.compressor.compress(data) : data,
        timestamp: now,
        expiresAt: new Date(now.getTime() + expirationTime),
        accessCount: 0,
        lastAccessed: now,
        size
      }

      this.cache.set(key, cacheItem)
      return true

    } catch (error) {
      console.error(`设置缓存失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 获取缓存项
   * @param key 缓存键
   * @returns 缓存数据或undefined
   */
  get<T>(key: string): T | undefined {
    const item = this.cache.get(key)

    if (!item) {
      this.stats.missCount++
      return undefined
    }

    // 检查是否过期
    if (this.isExpired(item)) {
      this.cache.delete(key)
      this.stats.missCount++
      return undefined
    }

    // 更新访问统计
    item.accessCount++
    item.lastAccessed = new Date()
    this.stats.hitCount++

    // 解压缩数据
    const data = this.config.enableCompression ? this.compressor.decompress(item.data) : item.data
    return data as T
  }

  /**
   * 检查缓存项是否存在且未过期
   * @param key 缓存键
   * @returns boolean
   */
  has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) return false

    if (this.isExpired(item)) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * 删除缓存项
   * @param key 缓存键
   * @returns boolean 是否删除成功
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
    this.stats.hitCount = 0
    this.stats.missCount = 0
  }

  /**
   * 获取缓存统计信息
   * @returns CacheStats
   */
  getStats(): CacheStats {
    const items = Array.from(this.cache.values())
    const totalSize = items.reduce((sum, item) => sum + item.size, 0) / (1024 * 1024) // 转换为MB
    const totalAccess = items.reduce((sum, item) => sum + item.accessCount, 0)

    return {
      totalItems: this.cache.size,
      totalSize: Math.round(totalSize * 100) / 100,
      hitCount: this.stats.hitCount,
      missCount: this.stats.missCount,
      hitRate: this.stats.hitCount + this.stats.missCount > 0 
        ? Math.round((this.stats.hitCount / (this.stats.hitCount + this.stats.missCount)) * 100) / 100
        : 0,
      oldestItem: items.length > 0 ? new Date(Math.min(...items.map(i => i.timestamp.getTime()))) : undefined,
      newestItem: items.length > 0 ? new Date(Math.max(...items.map(i => i.timestamp.getTime()))) : undefined,
      averageAccessCount: items.length > 0 ? Math.round(totalAccess / items.length) : 0
    }
  }

  /**
   * 手动清理过期和低优先级缓存项
   * @param strategy 清理策略
   * @returns 清理的项目数
   */
  cleanup(strategy: CacheStrategy = CacheStrategy.LRU): number {
    const initialSize = this.cache.size
    const now = new Date()

    // 首先清理过期项
    for (const [key, item] of this.cache.entries()) {
      if (this.isExpired(item)) {
        this.cache.delete(key)
      }
    }

    // 如果仍然超出限制，根据策略清理
    if (this.isOverCapacity()) {
      const itemsToRemove = this.selectItemsForRemoval(strategy)
      itemsToRemove.forEach(key => this.cache.delete(key))
    }

    const cleanedCount = initialSize - this.cache.size
    if (cleanedCount > 0) {
      console.log(`缓存清理完成，清理了 ${cleanedCount} 个项目`)
    }

    return cleanedCount
  }

  /**
   * 预热缓存 - 预加载常用数据
   * @param preloadData 预加载数据映射
   */
  async warmup(preloadData: Map<string, any>): Promise<void> {
    console.log('开始缓存预热...')
    
    let successCount = 0
    let failCount = 0

    for (const [key, data] of preloadData.entries()) {
      if (this.set(key, data)) {
        successCount++
      } else {
        failCount++
      }
    }

    console.log(`缓存预热完成: 成功 ${successCount} 项, 失败 ${failCount} 项`)
  }

  /**
   * 导出缓存数据
   * @returns 缓存数据的JSON字符串
   */
  export(): string {
    const exportData = {
      timestamp: new Date().toISOString(),
      config: this.config,
      stats: this.getStats(),
      items: Array.from(this.cache.entries()).map(([key, item]) => ({
        key,
        data: item.data,
        timestamp: item.timestamp.toISOString(),
        expiresAt: item.expiresAt.toISOString(),
        accessCount: item.accessCount,
        size: item.size
      }))
    }

    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 导入缓存数据
   * @param jsonData 缓存数据的JSON字符串
   * @returns boolean 是否导入成功
   */
  import(jsonData: string): boolean {
    try {
      const importData = JSON.parse(jsonData)
      
      if (!importData.items || !Array.isArray(importData.items)) {
        throw new Error('无效的缓存数据格式')
      }

      this.clear()

      let importCount = 0
      const now = new Date()

      for (const item of importData.items) {
        const expiresAt = new Date(item.expiresAt)
        
        // 跳过已过期的项目
        if (expiresAt <= now) {
          continue
        }

        const cacheItem: CacheItem<any> = {
          key: item.key,
          data: item.data,
          timestamp: new Date(item.timestamp),
          expiresAt,
          accessCount: item.accessCount || 0,
          lastAccessed: new Date(item.timestamp),
          size: item.size || this.estimateSize(item.data)
        }

        this.cache.set(item.key, cacheItem)
        importCount++
      }

      console.log(`缓存导入完成: 导入 ${importCount} 个项目`)
      return true

    } catch (error) {
      console.error('缓存导入失败:', error)
      return false
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
    this.clear()
  }

  // ==================== 私有方法 ====================

  /**
   * 检查缓存项是否过期
   */
  private isExpired(item: CacheItem<any>): boolean {
    return new Date() > item.expiresAt
  }

  /**
   * 估算数据大小
   */
  private estimateSize(data: any): number {
    try {
      const jsonString = JSON.stringify(data)
      // 在Node.js环境中使用Buffer，在浏览器环境中使用Blob
      if (typeof Buffer !== 'undefined') {
        return Buffer.byteLength(jsonString, 'utf8')
      } else if (typeof Blob !== 'undefined') {
        return new Blob([jsonString]).size
      } else {
        // 简单估算：UTF-8字符平均2字节
        return jsonString.length * 2
      }
    } catch {
      // 简单估算
      return JSON.stringify(data).length * 2
    }
  }

  /**
   * 获取当前缓存总大小（MB）
   */
  private getCurrentSize(): number {
    const totalBytes = Array.from(this.cache.values())
      .reduce((sum, item) => sum + item.size, 0)
    return totalBytes / (1024 * 1024)
  }

  /**
   * 检查是否需要清理
   */
  private needsCleanup(newItemSize: number): boolean {
    const currentSize = this.getCurrentSize()
    const newItemSizeMB = newItemSize / (1024 * 1024)
    
    return this.cache.size >= this.config.maxItems || 
           (currentSize + newItemSizeMB) > this.config.maxSize
  }

  /**
   * 检查是否有足够空间（清理后）
   */
  private hasSpace(newItemSize: number): boolean {
    // 如果需要清理，先进行清理
    if (this.needsCleanup(newItemSize)) {
      this.cleanup()
    }
    
    const currentSize = this.getCurrentSize()
    const newItemSizeMB = newItemSize / (1024 * 1024)
    
    return this.cache.size < this.config.maxItems && 
           (currentSize + newItemSizeMB) <= this.config.maxSize
  }

  /**
   * 根据策略选择要移除的项目
   */
  private selectItemsForRemoval(strategy: CacheStrategy): string[] {
    const removeCount = this.calculateRemoveCount()
    const evictionStrategy = EvictionStrategyFactory.getStrategy(strategy)
    return evictionStrategy.selectItemsForRemoval(this.cache, removeCount)
  }

  /**
   * 计算需要移除的项目数量
   */
  private calculateRemoveCount(): number {
    return Math.max(
      this.cache.size - this.config.maxItems + 1,
      Math.ceil(this.cache.size * 0.1) // 至少清理10%
    )
  }

  /**
   * 检查是否超出容量限制
   */
  private isOverCapacity(): boolean {
    return this.cache.size > this.config.maxItems || this.getCurrentSize() > this.config.maxSize
  }



  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }
}

/**
 * 缓存键常量
 */
const CACHE_KEYS = {
  PARSE: 'parse',
  CONFLICT: 'conflict',
  CONFIG: 'config'
} as const

/**
 * 缓存TTL常量（毫秒）
 */
const CACHE_TTL = {
  PARSE_RESULT: 2 * 60 * 60 * 1000, // 2小时
  CONFLICT_RESULT: 30 * 60 * 1000, // 30分钟
  USER_CONFIG: 24 * 60 * 60 * 1000 // 24小时
} as const

/**
 * 哈希生成器工具类
 */
class HashGenerator {
  /**
   * 生成数据哈希
   * @param data 数据
   * @returns 哈希字符串
   */
  static generateHash(data: any): string {
    const str = JSON.stringify(data)
    let hash = 0
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }

  /**
   * 生成文件哈希（基于文件内容和元数据）
   * @param file 文件对象
   * @returns Promise<string>
   */
  static async generateFileHash(file: File): Promise<string> {
    const metadata = {
      name: file.name,
      size: file.size,
      lastModified: file.lastModified,
      type: file.type
    }
    
    try {
      // 对于小文件，包含部分内容；对于大文件，只使用元数据
      if (file.size < 1024 * 1024) { // 1MB以下
        const content = await file.text()
        return this.generateHash({ metadata, content: content.substring(0, 1000) })
      } else {
        return this.generateHash(metadata)
      }
    } catch (error) {
      // 如果读取文件内容失败，只使用元数据
      console.warn('读取文件内容失败，使用元数据生成哈希:', error)
      return this.generateHash(metadata)
    }
  }
}

/**
 * 专门的导入导出缓存管理器
 */
export class ImportExportCacheManager extends CacheManager {
  constructor() {
    super({
      maxSize: 100, // 100MB
      maxItems: 500,
      defaultTTL: 60 * 60 * 1000, // 1小时
      cleanupInterval: 10 * 60 * 1000, // 10分钟
      enableCompression: true
    })
  }

  /**
   * 缓存解析结果
   * @param fileHash 文件哈希
   * @param parseResult 解析结果
   * @returns boolean 是否缓存成功
   */
  cacheParseResult(fileHash: string, parseResult: any): boolean {
    const key = this.generateCacheKey(CACHE_KEYS.PARSE, fileHash)
    return this.set(key, parseResult, CACHE_TTL.PARSE_RESULT)
  }

  /**
   * 获取解析结果缓存
   * @param fileHash 文件哈希
   * @returns 解析结果或undefined
   */
  getParseResult(fileHash: string): any {
    const key = this.generateCacheKey(CACHE_KEYS.PARSE, fileHash)
    return this.get(key)
  }

  /**
   * 缓存冲突检测结果
   * @param dataHash 数据哈希
   * @param conflictResult 冲突检测结果
   * @returns boolean 是否缓存成功
   */
  cacheConflictResult(dataHash: string, conflictResult: any): boolean {
    const key = this.generateCacheKey(CACHE_KEYS.CONFLICT, dataHash)
    return this.set(key, conflictResult, CACHE_TTL.CONFLICT_RESULT)
  }

  /**
   * 获取冲突检测结果缓存
   * @param dataHash 数据哈希
   * @returns 冲突检测结果或undefined
   */
  getConflictResult(dataHash: string): any {
    const key = this.generateCacheKey(CACHE_KEYS.CONFLICT, dataHash)
    return this.get(key)
  }

  /**
   * 缓存用户配置
   * @param userId 用户ID
   * @param config 配置数据
   * @returns boolean 是否缓存成功
   */
  cacheUserConfig(userId: string, config: any): boolean {
    const key = this.generateCacheKey(CACHE_KEYS.CONFIG, userId)
    return this.set(key, config, CACHE_TTL.USER_CONFIG)
  }

  /**
   * 获取用户配置缓存
   * @param userId 用户ID
   * @returns 配置数据或undefined
   */
  getUserConfig(userId: string): any {
    const key = this.generateCacheKey(CACHE_KEYS.CONFIG, userId)
    return this.get(key)
  }

  /**
   * 生成缓存键
   * @param prefix 前缀
   * @param identifier 标识符
   * @returns 缓存键
   */
  private generateCacheKey(prefix: string, identifier: string): string {
    return `${prefix}_${identifier}`
  }

  /**
   * 生成数据哈希
   * @param data 数据
   * @returns 哈希字符串
   */
  generateHash(data: any): string {
    return HashGenerator.generateHash(data)
  }

  /**
   * 生成文件哈希
   * @param file 文件对象
   * @returns Promise<string>
   */
  async generateFileHash(file: File): Promise<string> {
    return HashGenerator.generateFileHash(file)
  }

  /**
   * 批量清理特定类型的缓存
   * @param cacheType 缓存类型
   * @returns 清理的项目数
   */
  clearCacheByType(cacheType: keyof typeof CACHE_KEYS): number {
    const prefix = CACHE_KEYS[cacheType]
    let clearedCount = 0
    
    for (const [key] of this.cache.entries()) {
      if (key.startsWith(`${prefix}_`)) {
        this.delete(key)
        clearedCount++
      }
    }
    
    return clearedCount
  }

  /**
   * 获取特定类型缓存的统计信息
   * @param cacheType 缓存类型
   * @returns 统计信息
   */
  getCacheStatsByType(cacheType: keyof typeof CACHE_KEYS): {
    count: number
    totalSize: number
  } {
    const prefix = CACHE_KEYS[cacheType]
    let count = 0
    let totalSize = 0
    
    for (const [key, item] of this.cache.entries()) {
      if (key.startsWith(`${prefix}_`)) {
        count++
        totalSize += item.size
      }
    }
    
    return {
      count,
      totalSize: Math.round((totalSize / (1024 * 1024)) * 100) / 100 // 转换为MB
    }
  }
}

// 导出单例实例
export const importExportCache = new ImportExportCacheManager()