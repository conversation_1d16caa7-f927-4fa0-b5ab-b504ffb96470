// 标签工具函数

import { Tag, ValidationResult, ValidationError } from '../types'

/**
 * 标签排序选项
 */
export type TagSortOption = 
  | 'name-asc' | 'name-desc'
  | 'usage-asc' | 'usage-desc'
  | 'created-asc' | 'created-desc'
  | 'updated-asc' | 'updated-desc'

/**
 * 标签分组接口
 */
export interface TagGroup {
  range: string
  tags: TagWithStats[]
}

/**
 * 标签工具类
 * 提供标签相关的工具函数，包括验证、排序、筛选等功能
 */
export class TagUtils {

  /**
   * 验证标签名称
   * @param name 标签名称
   * @returns 验证结果
   */
  static validateTagName(name: string): ValidationResult {
    const errors: ValidationError[] = []

    // 检查是否为空
    if (!name || name.trim().length === 0) {
      errors.push({
        field: 'name',
        message: '标签名称不能为空',
        code: 'NAME_REQUIRED'
      })
    } else {
      // 检查长度
      const trimmedName = name.trim()
      if (trimmedName.length > 50) {
        errors.push({
          field: 'name',
          message: '标签名称长度不能超过50个字符',
          code: 'NAME_TOO_LONG'
        })
      }

      if (trimmedName.length < 1) {
        errors.push({
          field: 'name',
          message: '标签名称不能为空',
          code: 'NAME_TOO_SHORT'
        })
      }

      // 检查特殊字符
      const invalidChars = /[<>\"'&]/
      if (invalidChars.test(trimmedName)) {
        errors.push({
          field: 'name',
          message: '标签名称不能包含特殊字符 < > " \' &',
          code: 'INVALID_CHARACTERS'
        })
      }

      // 检查是否只包含空白字符
      if (trimmedName.replace(/\s/g, '').length === 0) {
        errors.push({
          field: 'name',
          message: '标签名称不能只包含空白字符',
          code: 'ONLY_WHITESPACE'
        })
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 标签名称标准化
   * @param name 原始标签名称
   * @returns 标准化后的标签名称
   */
  static normalizeTagName(name: string): string {
    if (!name) return ''
    
    return name
      .trim() // 去除首尾空白
      .replace(/\s+/g, ' ') // 多个空白字符替换为单个空格
      .toLowerCase() // 转换为小写（用于比较）
  }

  /**
   * 排序标签
   * @param tags 标签数组
   * @param sortBy 排序选项
   * @returns 排序后的标签数组
   */
  static sortTags<T extends Tag>(tags: T[], sortBy: TagSortOption): T[] {
    return [...tags].sort((a, b) => {
      switch (sortBy) {
        case 'name-asc':
          return a.name.localeCompare(b.name)
        case 'name-desc':
          return b.name.localeCompare(a.name)
        case 'usage-asc':
          const aUsage = 'usageCount' in a ? (a as any).usageCount : 0
          const bUsage = 'usageCount' in b ? (b as any).usageCount : 0
          return aUsage - bUsage
        case 'usage-desc':
          const aUsageDesc = 'usageCount' in a ? (a as any).usageCount : 0
          const bUsageDesc = 'usageCount' in b ? (b as any).usageCount : 0
          return bUsageDesc - aUsageDesc
        case 'created-asc':
          return a.createdAt.getTime() - b.createdAt.getTime()
        case 'created-desc':
          return b.createdAt.getTime() - a.createdAt.getTime()
        case 'updated-asc':
          return a.updatedAt.getTime() - b.updatedAt.getTime()
        case 'updated-desc':
          return b.updatedAt.getTime() - a.updatedAt.getTime()
        default:
          return 0
      }
    })
  }

  /**
   * 筛选标签
   * @param tags 标签数组
   * @param query 搜索关键词
   * @returns 筛选后的标签数组
   */
  static filterTags<T extends Tag>(tags: T[], query: string): T[] {
    if (!query.trim()) {
      return tags
    }

    const searchQuery = query.toLowerCase().trim()
    return tags.filter(tag => 
      tag.name.toLowerCase().includes(searchQuery)
    )
  }

  /**
   * 获取标签建议
   * @param input 输入的部分标签名称
   * @param existingTags 现有标签列表
   * @param limit 返回建议数量限制
   * @returns 建议的标签名称数组
   */
  static getTagSuggestions(input: string, existingTags: Tag[], limit: number = 5): string[] {
    if (!input.trim()) {
      return []
    }

    const searchQuery = input.toLowerCase().trim()
    const suggestions: Array<{ name: string, score: number }> = []

    existingTags.forEach(tag => {
      const tagName = tag.name.toLowerCase()
      
      // 完全匹配得分最高
      if (tagName === searchQuery) {
        suggestions.push({ name: tag.name, score: 100 })
      }
      // 开头匹配得分较高
      else if (tagName.startsWith(searchQuery)) {
        suggestions.push({ name: tag.name, score: 80 })
      }
      // 包含匹配得分中等
      else if (tagName.includes(searchQuery)) {
        suggestions.push({ name: tag.name, score: 60 })
      }
      // 模糊匹配得分较低
      else if (this.fuzzyMatch(tagName, searchQuery)) {
        suggestions.push({ name: tag.name, score: 40 })
      }
    })

    // 按得分排序并返回指定数量的建议
    return suggestions
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.name)
  }

  /**
   * 标签去重
   * @param tags 标签名称数组
   * @returns 去重后的标签名称数组
   */
  static deduplicateTags(tags: string[]): string[] {
    const seen = new Set<string>()
    const result: string[] = []

    tags.forEach(tag => {
      const normalizedTag = this.normalizeTagName(tag)
      if (normalizedTag && !seen.has(normalizedTag)) {
        seen.add(normalizedTag)
        result.push(tag.trim()) // 保留原始格式，但去除首尾空白
      }
    })

    return result
  }

  /**
   * 按使用次数分组标签
   * @param tags 包含统计信息的标签数组
   * @returns 分组后的标签
   */
  static groupTagsByUsage(tags: TagWithStats[]): TagGroup[] {
    const groups: TagGroup[] = [
      { range: '高频使用 (20+)', tags: [] },
      { range: '中频使用 (5-19)', tags: [] },
      { range: '低频使用 (1-4)', tags: [] },
      { range: '未使用 (0)', tags: [] }
    ]

    tags.forEach(tag => {
      if (tag.usageCount >= 20) {
        groups[0].tags.push(tag)
      } else if (tag.usageCount >= 5) {
        groups[1].tags.push(tag)
      } else if (tag.usageCount >= 1) {
        groups[2].tags.push(tag)
      } else {
        groups[3].tags.push(tag)
      }
    })

    // 过滤掉空分组
    return groups.filter(group => group.tags.length > 0)
  }

  /**
   * 生成标签颜色（基于名称）
   * @param name 标签名称
   * @returns 颜色值（十六进制）
   */
  static generateTagColor(name: string): string {
    // 预设颜色列表
    const colors = [
      '#3B82F6', // 蓝色
      '#10B981', // 绿色
      '#F59E0B', // 黄色
      '#EF4444', // 红色
      '#8B5CF6', // 紫色
      '#06B6D4', // 青色
      '#F97316', // 橙色
      '#84CC16', // 青绿色
      '#EC4899', // 粉色
      '#6B7280', // 灰色
      '#14B8A6', // 蓝绿色
      '#F472B6', // 粉红色
      '#A855F7', // 紫罗兰色
      '#22D3EE', // 天蓝色
      '#FDE047', // 柠檬黄
      '#FB7185', // 玫瑰色
      '#34D399', // 翠绿色
      '#FBBF24', // 琥珀色
      '#F87171', // 珊瑚红
      '#60A5FA'  // 天空蓝
    ]
    
    // 使用名称的哈希值选择颜色
    let hash = 0
    for (let i = 0; i < name.length; i++) {
      hash = ((hash << 5) - hash + name.charCodeAt(i)) & 0xffffffff
    }
    
    return colors[Math.abs(hash) % colors.length]
  }

  /**
   * 计算标签相似度
   * @param tag1 标签1
   * @param tag2 标签2
   * @returns 相似度（0-1之间）
   */
  static calculateTagSimilarity(tag1: Tag, tag2: Tag): number {
    // 名称相似度
    const nameSimilarity = this.calculateStringSimilarity(tag1.name, tag2.name)
    
    // 颜色相似度（如果都有颜色）
    let colorSimilarity = 0
    if (tag1.color && tag2.color) {
      colorSimilarity = tag1.color === tag2.color ? 1 : 0
    }
    
    // 综合相似度（名称权重更高）
    return nameSimilarity * 0.8 + colorSimilarity * 0.2
  }

  /**
   * 获取相似标签
   * @param targetTag 目标标签
   * @param allTags 所有标签
   * @param threshold 相似度阈值
   * @param limit 返回数量限制
   * @returns 相似标签数组
   */
  static getSimilarTags(
    targetTag: Tag, 
    allTags: Tag[], 
    threshold: number = 0.6, 
    limit: number = 5
  ): Array<{ tag: Tag, similarity: number }> {
    const similarities: Array<{ tag: Tag, similarity: number }> = []

    allTags.forEach(tag => {
      if (tag.id !== targetTag.id) {
        const similarity = this.calculateTagSimilarity(targetTag, tag)
        if (similarity >= threshold) {
          similarities.push({ tag, similarity })
        }
      }
    })

    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
  }

  /**
   * 验证标签批量操作
   * @param tagIds 标签ID数组
   * @param operation 操作类型
   * @returns 验证结果
   */
  static validateBatchOperation(
    tagIds: string[], 
    operation: 'delete' | 'merge' | 'color'
  ): ValidationResult {
    const errors: ValidationError[] = []

    if (!tagIds || tagIds.length === 0) {
      errors.push({
        field: 'tagIds',
        message: '请选择要操作的标签',
        code: 'NO_TAGS_SELECTED'
      })
    }

    if (operation === 'merge' && tagIds.length < 2) {
      errors.push({
        field: 'tagIds',
        message: '合并操作至少需要选择2个标签',
        code: 'INSUFFICIENT_TAGS_FOR_MERGE'
      })
    }

    if (tagIds.length > 100) {
      errors.push({
        field: 'tagIds',
        message: '批量操作最多支持100个标签',
        code: 'TOO_MANY_TAGS'
      })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 模糊匹配
   * @param text 文本
   * @param pattern 模式
   * @returns 是否匹配
   */
  private static fuzzyMatch(text: string, pattern: string): boolean {
    let textIndex = 0
    let patternIndex = 0

    while (textIndex < text.length && patternIndex < pattern.length) {
      if (text[textIndex] === pattern[patternIndex]) {
        patternIndex++
      }
      textIndex++
    }

    return patternIndex === pattern.length
  }

  /**
   * 计算字符串相似度
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 相似度（0-1之间）
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1
    if (!str1 || !str2) return 0

    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1

    if (longer.length === 0) return 1

    const editDistance = this.calculateLevenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  /**
   * 计算编辑距离（Levenshtein距离）
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 编辑距离
   */
  private static calculateLevenshteinDistance(str1: string, str2: string): number {
    const matrix = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // 替换
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j] + 1      // 删除
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }
}