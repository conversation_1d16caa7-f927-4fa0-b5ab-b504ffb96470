#!/usr/bin/env node

/**
 * 本地AI服务集成功能测试脚本
 * 
 * 使用方法:
 * node scripts/test-local-ai-services.js
 * 
 * 或者使用npm脚本:
 * npm run test:local-ai
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 动态导入我们的服务
async function loadServices() {
    try {
        // 由于这是ES模块，我们需要动态导入
        const { localAIServiceAdapter } = await import('../src/services/localAIServiceAdapter.js');
        const { aiProviderService } = await import('../src/services/aiProviderService.js');
        
        return { localAIServiceAdapter, aiProviderService };
    } catch (error) {
        console.error('❌ 无法加载服务模块:', error.message);
        console.log('💡 提示: 请确保项目已正确构建');
        return null;
    }
}

// 颜色输出函数
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
    colorLog('green', `✅ ${message}`);
}

function error(message) {
    colorLog('red', `❌ ${message}`);
}

function info(message) {
    colorLog('blue', `ℹ️  ${message}`);
}

function warning(message) {
    colorLog('yellow', `⚠️  ${message}`);
}

function header(message) {
    console.log();
    colorLog('cyan', `${'='.repeat(60)}`);
    colorLog('cyan', `🤖 ${message}`);
    colorLog('cyan', `${'='.repeat(60)}`);
}

// 测试函数
async function testLocalServiceDiscovery(adapter) {
    header('本地服务发现测试');
    
    try {
        info('开始扫描本地AI服务...');
        const result = await adapter.discoverLocalServices([8888, 9999]);
        
        success(`发现 ${result.services.length} 个本地AI服务`);
        
        result.services.forEach((service, index) => {
            console.log(`\n${index + 1}. ${service.name}`);
            console.log(`   地址: ${service.baseUrl}`);
            console.log(`   端口: ${service.port}`);
            console.log(`   协议: ${service.protocol}`);
            console.log(`   健康检查: ${service.healthCheckPath}`);
            console.log(`   模型端点: ${service.modelsPath}`);
        });
        
        if (result.errors.length > 0) {
            warning(`扫描过程中遇到 ${result.errors.length} 个错误:`);
            result.errors.forEach(err => console.log(`   - ${err}`));
        }
        
        return result.services;
    } catch (err) {
        error(`服务发现失败: ${err.message}`);
        return [];
    }
}

async function testServiceConnection(adapter, service) {
    info(`测试 ${service.name} 连接...`);
    
    try {
        const result = await adapter.testLocalServiceConnection(service);
        
        if (result.success) {
            success(`${service.name} 连接成功 (${result.responseTime}ms)`);
            if (result.modelCount !== undefined) {
                console.log(`   模型数量: ${result.modelCount}`);
            }
        } else {
            error(`${service.name} 连接失败: ${result.error}`);
        }
        
        return result;
    } catch (err) {
        error(`${service.name} 连接测试异常: ${err.message}`);
        return { success: false, error: err.message };
    }
}

async function testModelRetrieval(adapter, service) {
    info(`获取 ${service.name} 模型列表...`);
    
    try {
        const models = await adapter.getLocalServiceModels(service);
        
        if (models.length > 0) {
            success(`${service.name} 找到 ${models.length} 个模型`);
            
            models.forEach((model, index) => {
                console.log(`\n   ${index + 1}. ${model.displayName}`);
                console.log(`      ID: ${model.id}`);
                console.log(`      描述: ${model.description}`);
                if (model.size) console.log(`      大小: ${model.size}`);
                if (model.parameters) console.log(`      参数: ${model.parameters}`);
                console.log(`      能力: ${model.capabilities.join(', ')}`);
                console.log(`      标签: ${model.tags.join(', ')}`);
                console.log(`      推荐: ${model.isRecommended ? '是' : '否'}`);
                console.log(`      热门: ${model.isPopular ? '是' : '否'}`);
            });
        } else {
            warning(`${service.name} 未找到任何模型`);
        }
        
        return models;
    } catch (err) {
        error(`${service.name} 模型获取失败: ${err.message}`);
        return [];
    }
}

async function testDefaultServices(adapter) {
    header('默认服务配置测试');
    
    const defaultServices = adapter.getDefaultServices();
    success(`获取到 ${defaultServices.length} 个默认服务配置`);
    
    defaultServices.forEach((service, index) => {
        console.log(`\n${index + 1}. ${service.name}`);
        console.log(`   地址: ${service.baseUrl}`);
        console.log(`   端口: ${service.port}`);
        console.log(`   超时: ${service.timeout}ms`);
    });
    
    return defaultServices;
}

async function testCustomServiceConfig(adapter) {
    header('自定义服务配置测试');
    
    const customConfigs = [
        {
            name: 'Test HTTP Service',
            url: 'http://localhost:8080',
            options: { apiPath: '/api/v2', timeout: 15000 }
        },
        {
            name: 'Test HTTPS Service',
            url: 'https://api.example.com:8443',
            options: { headers: { 'Authorization': 'Bearer token' } }
        }
    ];
    
    customConfigs.forEach(({ name, url, options }) => {
        try {
            const config = adapter.createCustomServiceConfig(name, url, options);
            success(`创建自定义配置: ${name}`);
            console.log(`   地址: ${config.baseUrl}`);
            console.log(`   端口: ${config.port}`);
            console.log(`   协议: ${config.protocol}`);
            console.log(`   API路径: ${config.apiPath}`);
            console.log(`   超时: ${config.timeout}ms`);
            if (config.headers && Object.keys(config.headers).length > 0) {
                console.log(`   请求头: ${JSON.stringify(config.headers)}`);
            }
        } catch (err) {
            error(`创建自定义配置失败 (${name}): ${err.message}`);
        }
    });
}

async function testAIProviderServiceIntegration(aiProviderService) {
    header('AI提供商服务集成测试');
    
    try {
        // 测试本地服务发现
        info('测试AI提供商服务的本地发现功能...');
        const services = await aiProviderService.discoverLocalServices([7777]);
        success(`AI提供商服务发现了 ${services.length} 个本地服务`);
        
        // 测试默认服务获取
        const defaultServices = aiProviderService.getDefaultLocalServices();
        success(`获取到 ${defaultServices.length} 个默认本地服务配置`);
        
        // 测试自定义服务创建
        const customService = aiProviderService.createCustomLocalService(
            'Test Integration Service',
            'http://localhost:9000',
            { timeout: 20000 }
        );
        success('成功创建自定义本地服务配置');
        console.log(`   服务名: ${customService.name}`);
        console.log(`   地址: ${customService.baseUrl}`);
        
    } catch (err) {
        error(`AI提供商服务集成测试失败: ${err.message}`);
    }
}

// 主测试函数
async function runTests() {
    console.log();
    colorLog('magenta', '🚀 开始本地AI服务集成功能测试');
    console.log();
    
    // 加载服务
    const services = await loadServices();
    if (!services) {
        error('无法加载服务，测试终止');
        process.exit(1);
    }
    
    const { localAIServiceAdapter, aiProviderService } = services;
    
    try {
        // 1. 测试默认服务配置
        await testDefaultServices(localAIServiceAdapter);
        
        // 2. 测试自定义服务配置
        await testCustomServiceConfig(localAIServiceAdapter);
        
        // 3. 测试本地服务发现
        const discoveredServices = await testLocalServiceDiscovery(localAIServiceAdapter);
        
        // 4. 测试每个发现的服务
        if (discoveredServices.length > 0) {
            header('服务连接和模型测试');
            
            for (const service of discoveredServices) {
                console.log();
                colorLog('yellow', `--- 测试 ${service.name} ---`);
                
                // 测试连接
                const connectionResult = await testServiceConnection(localAIServiceAdapter, service);
                
                // 如果连接成功，测试模型获取
                if (connectionResult.success) {
                    await testModelRetrieval(localAIServiceAdapter, service);
                }
            }
        }
        
        // 5. 测试AI提供商服务集成
        await testAIProviderServiceIntegration(aiProviderService);
        
        // 测试完成
        console.log();
        header('测试完成');
        success('所有测试已完成！');
        
        // 输出测试总结
        console.log();
        info('测试总结:');
        console.log(`   - 发现的服务数量: ${discoveredServices.length}`);
        console.log(`   - 在线服务数量: ${discoveredServices.filter(s => s.status === 'online').length}`);
        console.log(`   - 测试用例: 全部通过`);
        
    } catch (err) {
        error(`测试过程中发生错误: ${err.message}`);
        console.error(err.stack);
        process.exit(1);
    }
}

// 检查是否直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(err => {
        error(`测试失败: ${err.message}`);
        process.exit(1);
    });
}

export { runTests };