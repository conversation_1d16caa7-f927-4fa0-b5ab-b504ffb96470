/**
 * 简单的类型检查文件
 * 验证主要组件的类型定义是否正确
 */

// 检查主要类型导入
import type { Theme } from './src/options/hooks/useTheme';
import type { BreakpointConfig, ResponsiveState } from './src/options/hooks/useResponsive';
import type { AboutPageData } from './src/options/data/aboutInfo';
import type { HelpContent, HelpSection, HelpSearchResult } from './src/options/data/helpContent';

// 验证类型定义
const testTheme: Theme = 'light';
const testBreakpoint: keyof BreakpointConfig = 'md';

console.log('✅ 类型检查通过');
console.log('主题类型:', testTheme);
console.log('断点类型:', testBreakpoint);

export {};