// 页面信息提取服务单元测试

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { PageInfoService } from '../src/services/pageInfoService'

// 模拟DOM环境
const mockDocument = {
  title: '测试页面标题',
  documentElement: { lang: 'zh-CN' },
  querySelector: vi.fn(),
  querySelectorAll: vi.fn(),
  body: { textContent: '这是页面的主要内容，包含一些中文文字和English words。' }
}

const mockWindow = {
  location: {
    href: 'https://example.com/test-page',
    protocol: 'https:',
    host: 'example.com',
    hostname: 'example.com'
  },
  getSelection: vi.fn()
}

// 模拟全局对象
global.document = mockDocument
global.window = mockWindow
global.URL = class URL {
  constructor(url, base) {
    if (base) {
      this.href = new globalThis.URL(url, base).href
    } else {
      this.href = url
    }
    const parsed = new globalThis.URL(this.href)
    this.protocol = parsed.protocol
    this.host = parsed.host
    this.hostname = parsed.hostname
    this.pathname = parsed.pathname
  }
}

describe('PageInfoService', () => {
  let pageInfoService

  beforeEach(() => {
    pageInfoService = new PageInfoService()
    vi.clearAllMocks()
    
    // 重置mock返回值
    mockDocument.querySelector.mockReturnValue(null)
    mockDocument.querySelectorAll.mockReturnValue([])
    mockWindow.getSelection.mockReturnValue(null)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('extractPageInfo', () => {
    it('应该提取基本页面信息', async () => {
      // 模拟favicon元素
      const mockFaviconElement = { href: 'https://example.com/favicon.ico' }
      mockDocument.querySelector.mockImplementation((selector) => {
        if (selector === 'link[rel="icon"]') {
          return mockFaviconElement
        }
        return null
      })

      // 执行测试
      const result = await pageInfoService.extractPageInfo()

      // 验证结果
      expect(result).toEqual({
        title: '测试页面标题',
        url: 'https://example.com/test-page',
        favicon: 'https://example.com/favicon.ico',
        selectedText: undefined,
        timestamp: expect.any(Date)
      })
    })

    it('应该包含选中的文字', async () => {
      // 模拟选中文字
      const mockSelection = {
        toString: () => '选中的文字内容'
      }
      mockWindow.getSelection.mockReturnValue(mockSelection)

      // 执行测试
      const result = await pageInfoService.extractPageInfo({
        selectedText: '手动传入的选中文字'
      })

      // 验证结果
      expect(result.selectedText).toBe('手动传入的选中文字')
    })

    it('应该在包含元数据选项时提取元数据', async () => {
      // 模拟meta元素
      const mockMetaDescription = { content: '这是页面描述' }
      const mockMetaKeywords = { content: '关键词1,关键词2' }
      const mockMetaAuthor = { content: '作者姓名' }

      mockDocument.querySelector.mockImplementation((selector) => {
        switch (selector) {
          case 'meta[name="description"]':
            return mockMetaDescription
          case 'meta[name="keywords"]':
            return mockMetaKeywords
          case 'meta[name="author"]':
            return mockMetaAuthor
          default:
            return null
        }
      })

      // 模拟图片和链接
      mockDocument.querySelectorAll.mockImplementation((selector) => {
        if (selector === 'img') {
          return [{ src: 'image1.jpg' }, { src: 'image2.jpg' }]
        }
        if (selector === 'a') {
          return [{ href: 'link1.html' }, { href: 'link2.html' }, { href: 'link3.html' }]
        }
        return []
      })

      // 执行测试
      const result = await pageInfoService.extractPageInfo({
        includeMetadata: true
      })

      // 验证元数据被包含
      expect(result.description).toBe('这是页面描述')
      expect(result.keywords).toBe('关键词1,关键词2')
      expect(result.author).toBe('作者姓名')
      expect(result.language).toBe('zh-CN')
      expect(result.pageType).toBe('website')
      expect(result.wordCount).toBeGreaterThan(0)
      expect(result.imageCount).toBe(2)
      expect(result.linkCount).toBe(3)
    })

    it('应该处理缺失的favicon', async () => {
      // 不模拟任何favicon元素
      mockDocument.querySelector.mockReturnValue(null)

      // 执行测试
      const result = await pageInfoService.extractPageInfo()

      // 验证默认favicon
      expect(result.favicon).toBe('https://example.com/favicon.ico')
    })

    it('应该处理错误情况', async () => {
      // 模拟抛出错误
      mockDocument.querySelector.mockImplementation(() => {
        throw new Error('DOM访问错误')
      })

      // 执行测试并验证错误
      await expect(pageInfoService.extractPageInfo())
        .rejects.toThrow('页面信息提取失败: Error: DOM访问错误')
    })
  })

  describe('extractLinkInfo', () => {
    it('应该提取链接信息', async () => {
      // 准备测试数据
      const linkUrl = 'https://example.com/target-page'
      const mockLinkElement = {
        title: '链接标题',
        textContent: '链接文字',
        getAttribute: vi.fn().mockReturnValue('链接描述')
      }

      mockDocument.querySelector.mockReturnValue(mockLinkElement)

      // 执行测试
      const result = await pageInfoService.extractLinkInfo(linkUrl)

      // 验证结果
      expect(result).toEqual({
        title: '链接标题',
        description: '链接描述',
        favicon: 'https://example.com/favicon.ico'
      })
      expect(mockDocument.querySelector).toHaveBeenCalledWith(`a[href="${linkUrl}"]`)
    })

    it('应该处理不存在的链接', async () => {
      // 模拟链接不存在
      const linkUrl = 'https://example.com/non-existent'
      mockDocument.querySelector.mockReturnValue(null)

      // 执行测试
      const result = await pageInfoService.extractLinkInfo(linkUrl)

      // 验证结果
      expect(result.title).toBe('non-existent') // 从URL推断
      expect(result.favicon).toBe('https://example.com/favicon.ico')
    })

    it('应该处理无效URL', async () => {
      // 准备无效URL
      const invalidUrl = 'not-a-valid-url'

      // 执行测试
      const result = await pageInfoService.extractLinkInfo(invalidUrl)

      // 验证返回空对象
      expect(result).toEqual({})
    })
  })

  describe('detectPageType', () => {
    it('应该检测文章页面', async () => {
      // 模拟文章页面特征
      mockDocument.querySelector.mockImplementation((selector) => {
        if (selector === 'article') {
          return { tagName: 'ARTICLE' }
        }
        return null
      })

      // 修改URL为文章URL
      mockWindow.location.href = 'https://example.com/article/test-article'

      // 执行测试
      const result = await pageInfoService.extractPageInfo({ includeMetadata: true })

      // 验证页面类型
      expect(result.pageType).toBe('article')
    })

    it('应该检测视频页面', async () => {
      // 模拟视频页面特征
      mockDocument.querySelector.mockImplementation((selector) => {
        if (selector === 'video') {
          return { tagName: 'VIDEO' }
        }
        return null
      })

      // 执行测试
      const result = await pageInfoService.extractPageInfo({ includeMetadata: true })

      // 验证页面类型
      expect(result.pageType).toBe('video')
    })

    it('应该检测商品页面', async () => {
      // 修改URL为商品URL
      mockWindow.location.href = 'https://example.com/product/test-product'

      // 执行测试
      const result = await pageInfoService.extractPageInfo({ includeMetadata: true })

      // 验证页面类型
      expect(result.pageType).toBe('product')
    })

    it('应该默认为网站类型', async () => {
      // 不模拟任何特殊页面特征
      mockDocument.querySelector.mockReturnValue(null)

      // 执行测试
      const result = await pageInfoService.extractPageInfo({ includeMetadata: true })

      // 验证默认页面类型
      expect(result.pageType).toBe('website')
    })
  })

  describe('calculateWordCount', () => {
    it('应该正确计算中英文字数', async () => {
      // 模拟内容区域
      const mockContentElement = {
        textContent: '这是中文内容 with some English words 和更多中文。'
      }
      mockDocument.querySelector.mockImplementation((selector) => {
        if (selector === 'article') {
          return mockContentElement
        }
        return null
      })

      // 执行测试
      const result = await pageInfoService.extractPageInfo({ includeMetadata: true })

      // 验证字数统计
      expect(result.wordCount).toBeGreaterThan(0)
      // 应该包含中文字符和英文单词
    })

    it('应该在没有内容区域时使用body', async () => {
      // 不模拟任何内容区域
      mockDocument.querySelector.mockReturnValue(null)

      // 执行测试
      const result = await pageInfoService.extractPageInfo({ includeMetadata: true })

      // 验证使用了body的内容
      expect(result.wordCount).toBeGreaterThan(0)
    })
  })

  describe('extractImages', () => {
    it('应该提取页面图片信息', () => {
      // 模拟图片元素
      const mockImages = [
        {
          src: 'https://example.com/image1.jpg',
          alt: '图片1',
          title: '图片标题1',
          naturalWidth: 800,
          naturalHeight: 600,
          width: 800,
          height: 600
        },
        {
          src: 'https://example.com/image2.png',
          alt: '图片2',
          title: '',
          naturalWidth: 1200,
          naturalHeight: 800,
          width: 1200,
          height: 800
        },
        {
          src: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
          alt: 'Base64图片',
          naturalWidth: 1,
          naturalHeight: 1,
          width: 1,
          height: 1
        }
      ]

      mockDocument.querySelectorAll.mockReturnValue(mockImages)

      // 执行测试
      const result = pageInfoService.extractImages()

      // 验证结果（应该过滤掉base64图片和小图片）
      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        src: 'https://example.com/image1.jpg',
        alt: '图片1',
        title: '图片标题1',
        width: 800,
        height: 600
      })
      expect(result[1]).toEqual({
        src: 'https://example.com/image2.png',
        alt: '图片2',
        title: '',
        width: 1200,
        height: 800
      })
    })
  })

  describe('isSinglePageApp', () => {
    it('应该检测React应用', () => {
      // 模拟React环境
      global.window.React = {}

      // 执行测试
      const result = pageInfoService.isSinglePageApp()

      // 验证结果
      expect(result).toBe(true)

      // 清理
      delete global.window.React
    })

    it('应该检测Vue应用', () => {
      // 模拟Vue环境
      global.window.Vue = {}

      // 执行测试
      const result = pageInfoService.isSinglePageApp()

      // 验证结果
      expect(result).toBe(true)

      // 清理
      delete global.window.Vue
    })

    it('应该检测Angular应用', () => {
      // 模拟Angular特征
      mockDocument.querySelector.mockImplementation((selector) => {
        if (selector === '[ng-app]') {
          return { tagName: 'DIV' }
        }
        return null
      })

      // 执行测试
      const result = pageInfoService.isSinglePageApp()

      // 验证结果
      expect(result).toBe(true)
    })

    it('应该在非SPA时返回false', () => {
      // 不模拟任何SPA特征
      mockDocument.querySelector.mockReturnValue(null)

      // 执行测试
      const result = pageInfoService.isSinglePageApp()

      // 验证结果
      expect(result).toBe(false)
    })
  })

  describe('observePageChanges', () => {
    it('应该监听URL变化', () => {
      const callback = vi.fn()

      // 执行测试
      const cleanup = pageInfoService.observePageChanges(callback)

      // 模拟URL变化
      const originalHref = mockWindow.location.href
      mockWindow.location.href = 'https://example.com/new-page'

      // 触发popstate事件
      const popstateEvent = new Event('popstate')
      global.window.dispatchEvent(popstateEvent)

      // 验证回调被调用
      expect(callback).toHaveBeenCalled()

      // 清理
      cleanup()
      mockWindow.location.href = originalHref
    })

    it('应该监听标题变化', () => {
      const callback = vi.fn()

      // 执行测试
      const cleanup = pageInfoService.observePageChanges(callback)

      // 模拟标题变化
      const originalTitle = mockDocument.title
      mockDocument.title = '新标题'

      // 这里需要模拟MutationObserver的行为
      // 在实际测试中，可能需要更复杂的模拟

      // 清理
      cleanup()
      mockDocument.title = originalTitle
    })
  })
})