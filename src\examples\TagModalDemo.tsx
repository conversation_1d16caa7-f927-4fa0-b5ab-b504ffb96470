// TagModal组件演示页面

import React, { useState } from 'react'
import TagModal from '../components/TagModal'
import type { Tag, TagInput, TagUpdate } from '../types'

/**
 * TagModal演示组件
 * 展示标签模态窗口的各种使用场景
 */
const TagModalDemo: React.FC = () => {
  // 模态窗口状态
  const [isOpen, setIsOpen] = useState(false)
  const [modalType, setModalType] = useState<'create' | 'edit' | 'delete'>('create')
  const [loading, setLoading] = useState(false)

  // 示例标签数据
  const [sampleTag] = useState<Tag>({
    id: '1',
    name: '前端开发',
    color: '#3B82F6',
    usageCount: 15,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  })

  // 现有标签列表（用于重复检查）
  const existingTags = [
    { id: '1', name: '前端开发' },
    { id: '2', name: '后端开发' },
    { id: '3', name: '数据库' },
    { id: '4', name: '算法' }
  ]

  // 处理保存
  const handleSave = async (data: TagInput | TagUpdate) => {
    setLoading(true)
    console.log('保存标签数据:', data)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    setLoading(false)
    setIsOpen(false)
    alert(`标签${modalType === 'create' ? '创建' : '更新'}成功！`)
  }

  // 处理删除
  const handleDelete = async () => {
    setLoading(true)
    console.log('删除标签:', sampleTag.id)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    setLoading(false)
    setIsOpen(false)
    alert('标签删除成功！')
  }

  // 处理关闭
  const handleClose = () => {
    if (!loading) {
      setIsOpen(false)
    }
  }

  // 打开模态窗口
  const openModal = (type: 'create' | 'edit' | 'delete') => {
    setModalType(type)
    setIsOpen(true)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            TagModal 组件演示
          </h1>
          <p className="text-gray-600">
            展示标签模态窗口的创建、编辑和删除功能
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            模态窗口操作
          </h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => openModal('create')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              创建标签
            </button>
            <button
              onClick={() => openModal('edit')}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
            >
              编辑标签
            </button>
            <button
              onClick={() => openModal('delete')}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
            >
              删除标签
            </button>
          </div>
        </div>

        {/* 示例标签信息 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            示例标签信息
          </h2>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: sampleTag.color }}
              />
              <div>
                <p className="font-medium text-gray-900">{sampleTag.name}</p>
                <p className="text-sm text-gray-600">
                  使用次数: {sampleTag.usageCount} | 
                  创建时间: {sampleTag.createdAt.toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              现有标签列表
            </h3>
            <div className="flex flex-wrap gap-2">
              {existingTags.map(tag => (
                <span
                  key={tag.id}
                  className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                >
                  {tag.name}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* TagModal组件 */}
        <TagModal
          isOpen={isOpen}
          type={modalType}
          tag={modalType !== 'create' ? sampleTag : undefined}
          bookmarkCount={modalType === 'delete' ? sampleTag.usageCount : undefined}
          onSave={handleSave}
          onDelete={handleDelete}
          onClose={handleClose}
          loading={loading}
          existingTags={existingTags}
        />
      </div>
    </div>
  )
}

export default TagModalDemo