{"name": "universe-bag", "version": "2.0.0", "description": "Universe Bag（乾坤袋）- 智能收藏管理Chrome扩展", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node scripts/post-build.js && node scripts/build-checks.cjs", "build:only": "vite build", "build:check": "node scripts/build-checks.cjs", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "vitest --run --no-watch", "test:watch": "vitest", "test:run": "vitest --run --no-watch", "test:coverage": "vitest --run --coverage --no-watch", "test:ui": "vitest --ui", "test:single": "vitest --run --reporter=verbose --no-watch", "test:quick": "node scripts/quick-test.js", "test:config": "node scripts/test-build.js", "test:scripts": "node scripts/test-build.test.js", "test:background": "node tests/background-service-worker.test.js", "test:components": "node tests/popup-components.test.js", "test:services": "vitest --run tests/*Service.test.js", "test:all": "npm run test:config && npm run test:scripts && npm run test:background && npm run test:components && npm run test:services", "test:local-ai": "node scripts/test-local-ai-services.js", "test:local-ai:quick": "node scripts/quick-test-local-ai.js", "serve:test": "node scripts/serve-test-page.js", "shadcn:check": "node scripts/check-shadcn-migration.js", "shadcn:migrate": "node scripts/shadcn-migration-assistant.js", "shadcn:test": "vitest --run --reporter=verbose --no-watch **/*.shadcn.test.tsx"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.17"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/chrome": "^0.0.243", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "fake-indexeddb": "^6.0.1", "jsdom": "^26.1.0", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-web-extension": "^4.1.1", "vitest": "^3.2.4"}}