# AI对话服务函数签名文档

## 概述

本文档提供 `AIChatService` 类中所有公共方法和私有方法的详细函数签名和类型定义。

## 导出内容

### 类

#### AIChatService
```typescript
export class AIChatService
```

### 接口

#### ChatMessage
```typescript
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}
```

#### ChatSettings
```typescript
export interface ChatSettings {
  temperature?: number        // 温度参数 (0-2)
  maxTokens?: number         // 最大令牌数
  stream?: boolean           // 是否流式输出
  topP?: number             // Top-P 采样参数
  frequencyPenalty?: number // 频率惩罚
  presencePenalty?: number  // 存在惩罚
}
```

#### ChatResponse
```typescript
export interface ChatResponse {
  content: string           // 回复内容
  usage?: {                // 使用统计
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model?: string           // 使用的模型
  finishReason?: string    // 完成原因
}
```

### 单例实例

#### aiChatService
```typescript
export const aiChatService: AIChatService
```

## 公共方法

### chatWithLocalService()

与本地AI服务进行对话。

```typescript
async chatWithLocalService(
  serviceUrl: string,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings = {}
): Promise<ChatResponse>
```

**参数**:
- `serviceUrl` (string): 本地AI服务的URL地址
- `modelId` (string): 要使用的模型ID
- `messages` (ChatMessage[]): 对话消息历史
- `settings` (ChatSettings): 对话设置参数，可选

**返回值**: Promise<ChatResponse> - 对话响应

**异常**:
- `Error` - 网络连接错误、HTTP错误、响应解析错误等

### chatWithCloudService()

与云端AI服务进行对话。

```typescript
async chatWithCloudService(
  provider: AIProviderConfig,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings = {}
): Promise<ChatResponse>
```

**参数**:
- `provider` (AIProviderConfig): AI提供商配置
- `modelId` (string): 要使用的模型ID
- `messages` (ChatMessage[]): 对话消息历史
- `settings` (ChatSettings): 对话设置参数，可选

**返回值**: Promise<ChatResponse> - 对话响应

**异常**:
- `Error` - 认证失败、网络错误、API错误等

### testModelConnection()

测试模型连接状态。

```typescript
async testModelConnection(
  serviceUrl: string | AIProviderConfig,
  modelId: string
): Promise<boolean>
```

**参数**:
- `serviceUrl` (string | AIProviderConfig): 服务URL或提供商配置
- `modelId` (string): 模型ID

**返回值**: Promise<boolean> - 连接是否成功

**异常**: 不抛出异常，错误时返回false

### getAvailableModels()

获取服务的可用模型列表。

```typescript
async getAvailableModels(serviceUrl: string): Promise<string[]>
```

**参数**:
- `serviceUrl` (string): 服务URL地址

**返回值**: Promise<string[]> - 可用模型ID列表

**异常**: 不抛出异常，错误时返回空数组

### streamChat()

流式对话功能（暂未实现）。

```typescript
async streamChat(
  serviceUrl: string | AIProviderConfig,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings,
  onChunk: (chunk: string) => void
): Promise<void>
```

**参数**:
- `serviceUrl` (string | AIProviderConfig): 服务URL或提供商配置
- `modelId` (string): 模型ID
- `messages` (ChatMessage[]): 对话消息历史
- `settings` (ChatSettings): 对话设置参数
- `onChunk` ((chunk: string) => void): 数据块回调函数

**返回值**: Promise<void>

**异常**:
- `Error` - 抛出"流式对话功能暂未实现"错误

## 私有方法

### buildRequestForLocalService()

构建本地服务的请求数据。

```typescript
private buildRequestForLocalService(
  serviceUrl: string,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings
): object
```

**参数**:
- `serviceUrl` (string): 服务URL
- `modelId` (string): 模型ID
- `messages` (ChatMessage[]): 对话消息
- `settings` (ChatSettings): 对话设置

**返回值**: object - 请求数据对象

**功能**:
- 自动检测服务类型（Ollama或OpenAI兼容）
- 根据服务类型构建相应的请求格式
- 处理参数映射和默认值

### buildRequestForCloudService()

构建云端服务的请求数据。

```typescript
private buildRequestForCloudService(
  provider: AIProviderConfig,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings
): object
```

**参数**:
- `provider` (AIProviderConfig): 提供商配置
- `modelId` (string): 模型ID
- `messages` (ChatMessage[]): 对话消息
- `settings` (ChatSettings): 对话设置

**返回值**: object - 请求数据对象

**功能**:
- 根据提供商类型调整请求格式
- 处理特定API参数
- 统一请求结构

### parseLocalServiceResponse()

解析本地服务响应。

```typescript
private parseLocalServiceResponse(data: any): ChatResponse
```

**参数**:
- `data` (any): 原始响应数据

**返回值**: ChatResponse - 标准化的对话响应

**异常**:
- `Error` - 当无法解析响应格式时抛出

**功能**:
- 支持Ollama原生格式解析
- 支持OpenAI兼容格式解析
- 统一响应数据结构

### parseCloudServiceResponse()

解析云端服务响应。

```typescript
private parseCloudServiceResponse(data: any): ChatResponse
```

**参数**:
- `data` (any): 原始响应数据

**返回值**: ChatResponse - 标准化的对话响应

**异常**:
- `Error` - 当无法解析响应格式时抛出

**功能**:
- 解析OpenAI标准格式
- 处理其他兼容格式
- 提取使用统计信息

## 类型依赖

### 外部类型

#### AIProviderConfig
```typescript
// 从 '../types/ai' 导入
interface AIProviderConfig {
  id: string
  name: string
  type: AIProvider
  baseUrl: string
  apiKey?: string
  headers?: Record<string, string>
  timeout?: number
  enabled: boolean
  createdAt: Date
  updatedAt: Date
}
```

## 常量定义

### 超时设置
- **本地服务对话**: 60000ms (60秒)
- **云端服务对话**: 根据提供商配置或60000ms
- **连接测试**: 内部使用较短超时
- **模型列表获取**: 10000ms (10秒)

### 默认参数值
- **temperature**: 0.7
- **maxTokens**: 2048
- **topP**: 0.9
- **frequencyPenalty**: 0
- **presencePenalty**: 0
- **stream**: false

### 服务检测规则
- **Ollama检测**: URL包含`:11434`
- **API端点映射**:
  - Ollama: `/chat/completions` (请求) + `/api/tags` (模型列表)
  - OpenAI兼容: `/chat/completions` (请求) + `/models` (模型列表)

## 错误类型

### 网络错误
```typescript
// 连接被拒绝
Error: "ECONNREFUSED"

// 连接超时
Error: "The operation was aborted" (name: "AbortError")
```

### HTTP错误
```typescript
// 一般HTTP错误
Error: "HTTP {status}: {statusText}"

// 带详细错误信息的API错误
Error: "HTTP {status}: {errorMessage}"
```

### 解析错误
```typescript
// 响应格式无法解析
Error: "无法解析响应格式"

// JSON解析失败
Error: "Invalid JSON"
```

### 功能错误
```typescript
// 流式对话未实现
Error: "流式对话功能暂未实现"

// 对话失败的包装错误
Error: "对话失败: {originalError}"
```

## 使用模式

### 基本对话模式
```typescript
const messages: ChatMessage[] = [
  { role: 'user', content: '用户输入' }
]

const response = await aiChatService.chatWithLocalService(
  'http://localhost:11434',
  'llama2:7b',
  messages
)
```

### 带设置的对话模式
```typescript
const settings: ChatSettings = {
  temperature: 0.7,
  maxTokens: 1000,
  topP: 0.9
}

const response = await aiChatService.chatWithLocalService(
  serviceUrl,
  modelId,
  messages,
  settings
)
```

### 错误处理模式
```typescript
try {
  const response = await aiChatService.chatWithLocalService(url, model, messages)
  // 处理成功响应
} catch (error) {
  if (error.message.includes('ECONNREFUSED')) {
    // 处理连接被拒绝
  } else if (error.message.includes('HTTP 401')) {
    // 处理认证失败
  } else {
    // 处理其他错误
  }
}
```

### 连接测试模式
```typescript
const isConnected = await aiChatService.testModelConnection(serviceUrl, modelId)
if (isConnected) {
  // 服务可用，进行对话
} else {
  // 服务不可用，显示错误或尝试其他服务
}
```

### 模型列表获取模式
```typescript
const models = await aiChatService.getAvailableModels(serviceUrl)
if (models.length > 0) {
  // 显示可用模型供用户选择
} else {
  // 没有可用模型或服务不可用
}
```

## 扩展指南

### 添加新的本地服务支持

1. **在 `buildRequestForLocalService` 中添加检测逻辑**:
```typescript
if (serviceUrl.includes(':新端口')) {
  // 新服务格式
  return {
    // 新服务的请求格式
  }
}
```

2. **在 `parseLocalServiceResponse` 中添加解析逻辑**:
```typescript
if (data.新服务特有字段) {
  return {
    content: data.新服务内容字段,
    model: data.新服务模型字段,
    // ...
  }
}
```

### 添加新的云端服务支持

1. **在 `buildRequestForCloudService` 中添加提供商类型**:
```typescript
case '新提供商':
  return {
    // 新提供商的请求格式
  }
```

2. **更新 `AIProviderConfig` 类型**:
```typescript
type AIProvider = 'openai' | 'claude' | 'gemini' | '新提供商' | ...
```

### 实现流式对话

1. **替换 `streamChat` 方法实现**:
```typescript
async streamChat(
  serviceUrl: string | AIProviderConfig,
  modelId: string,
  messages: ChatMessage[],
  settings: ChatSettings,
  onChunk: (chunk: string) => void
): Promise<void> {
  // 实现流式请求逻辑
  // 处理Server-Sent Events或WebSocket
  // 调用onChunk回调传递数据块
}
```

## 相关文档

- [AI对话服务API文档](./aiChatService-api.md)
- [AI对话服务使用示例](./aiChatService-usage-examples.md)
- [AI集成服务API文档](./aiIntegrationService-api.md)
- [AI提供商服务API文档](./aiProviderService-api.md)
- [本地AI服务适配器API文档](./localAIServiceAdapter-api.md)