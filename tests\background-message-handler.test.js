// Background消息处理器单元测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { BackgroundMessageHandler } from '../src/background/messageHandler'

// Mock Chrome APIs
global.chrome = {
  storage: {
    local: {
      get: vi.fn(),
      set: vi.fn()
    }
  },
  tabs: {
    sendMessage: vi.fn()
  },
  action: {
    setBadgeText: vi.fn(),
    setBadgeBackgroundColor: vi.fn(),
    setTitle: vi.fn()
  }
}

describe('Background消息处理器测试', () => {
  let messageHandler

  beforeEach(() => {
    vi.clearAllMocks()
    messageHandler = new BackgroundMessageHandler()
  })

  describe('基础消息处理', () => {
    it('应该处理PING消息', async () => {
      const message = { type: 'PING' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data.status).toBe('pong')
      expect(response.data.timestamp).toBeTypeOf('number')
    })

    it('应该处理未知消息类型', async () => {
      const message = { type: 'UNKNOWN_MESSAGE' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toContain('未知的消息类型')
    })

    it('应该验证消息格式', async () => {
      const invalidMessage = { /* 缺少type字段 */ }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(invalidMessage, sender)

      expect(response.success).toBe(false)
      expect(response.error).toContain('消息验证失败')
    })

    it('应该在响应中包含请求ID', async () => {
      const message = { type: 'PING', requestId: 'test_req_123' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.requestId).toBe('test_req_123')
    })
  })

  describe('设置管理', () => {
    it('应该获取设置', async () => {
      const mockSettings = { theme: 'dark', language: 'zh-CN' }
      chrome.storage.local.get.mockResolvedValue({ settings: mockSettings })

      const message = { type: 'GET_SETTINGS' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data).toEqual(mockSettings)
      expect(chrome.storage.local.get).toHaveBeenCalledWith(['settings'])
    })

    it('应该处理空设置', async () => {
      chrome.storage.local.get.mockResolvedValue({})

      const message = { type: 'GET_SETTINGS' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data).toEqual({})
    })

    it('应该更新设置', async () => {
      const currentSettings = { theme: 'light', language: 'zh-CN' }
      const newSettings = { theme: 'dark' }
      const expectedSettings = { theme: 'dark', language: 'zh-CN' }

      chrome.storage.local.get.mockResolvedValue({ settings: currentSettings })
      chrome.storage.local.set.mockResolvedValue()

      const message = { type: 'UPDATE_SETTINGS', data: newSettings }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data).toEqual(expectedSettings)
      expect(chrome.storage.local.set).toHaveBeenCalledWith({ settings: expectedSettings })
    })

    it('应该处理设置存储错误', async () => {
      chrome.storage.local.get.mockRejectedValue(new Error('存储访问失败'))

      const message = { type: 'GET_SETTINGS' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toContain('获取设置失败')
    })
  })

  describe('收藏功能', () => {
    it('应该处理快速收藏', async () => {
      const bookmarkData = {
        title: '测试页面',
        url: 'https://example.com',
        favIconUrl: 'https://example.com/favicon.ico'
      }

      const message = { type: 'QUICK_BOOKMARK', data: bookmarkData }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data.bookmarkId).toBeTypeOf('string')
      
      // 验证图标状态更新
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 123,
        text: '✓'
      })
    })

    it('应该处理收藏选中文字', async () => {
      const textData = {
        selectedText: '这是选中的文字内容',
        url: 'https://example.com',
        title: '测试页面'
      }

      const message = { type: 'BOOKMARK_SELECTED_TEXT', data: textData }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data.bookmarkId).toBeTypeOf('string')
    })

    it('应该处理保存详细收藏', async () => {
      const detailedData = {
        title: '详细收藏',
        url: 'https://example.com',
        description: '这是详细描述',
        tags: ['标签1', '标签2'],
        category: '技术'
      }

      const message = { type: 'SAVE_DETAILED_BOOKMARK', data: detailedData }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data.bookmarkId).toBeTypeOf('string')
      
      // 验证图标状态更新
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 123,
        text: '✓'
      })
    })

    it('应该处理更新收藏', async () => {
      const updateData = {
        id: 'bookmark_123',
        updates: {
          title: '更新后的标题',
          tags: ['新标签']
        }
      }

      const message = { type: 'UPDATE_BOOKMARK', data: updateData }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data.bookmarkId).toBe('bookmark_123')
    })

    it('应该处理删除收藏', async () => {
      const deleteData = { id: 'bookmark_123' }

      const message = { type: 'DELETE_BOOKMARK', data: deleteData }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data.bookmarkId).toBe('bookmark_123')
      
      // 验证图标状态清除
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 123,
        text: ''
      })
    })
  })

  describe('收藏状态检查', () => {
    it('应该检查收藏状态', async () => {
      const message = { type: 'CHECK_BOOKMARK_STATUS', data: { url: 'https://example.com' } }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data).toHaveProperty('isBookmarked')
      expect(typeof response.data.isBookmarked).toBe('boolean')
    })
  })

  describe('图标状态管理', () => {
    it('应该更新图标为已收藏状态', async () => {
      const message = { 
        type: 'UPDATE_ICON_STATUS', 
        data: { tabId: 123, isBookmarked: true } 
      }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 123,
        text: '✓'
      })
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({
        tabId: 123,
        color: '#10b981'
      })
      expect(chrome.action.setTitle).toHaveBeenCalledWith({
        tabId: 123,
        title: 'Universe Bag - 当前页面已收藏'
      })
    })

    it('应该清除图标收藏状态', async () => {
      const message = { 
        type: 'UPDATE_ICON_STATUS', 
        data: { tabId: 123, isBookmarked: false } 
      }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 123,
        text: ''
      })
      expect(chrome.action.setTitle).toHaveBeenCalledWith({
        tabId: 123,
        title: 'Universe Bag - 智能收藏助手'
      })
    })

    it('应该处理图标更新错误', async () => {
      chrome.action.setBadgeText.mockRejectedValue(new Error('API调用失败'))

      const message = { 
        type: 'UPDATE_ICON_STATUS', 
        data: { tabId: 123, isBookmarked: true } 
      }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toContain('更新图标状态失败')
    })
  })

  describe('AI功能', () => {
    it('应该处理AI生成建议', async () => {
      const aiData = {
        content: '这是要分析的内容',
        url: 'https://example.com',
        title: '测试页面'
      }

      const message = { type: 'AI_GENERATE_SUGGESTIONS', data: aiData }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data).toHaveProperty('tags')
      expect(response.data).toHaveProperty('category')
      expect(response.data).toHaveProperty('confidence')
      expect(Array.isArray(response.data.tags)).toBe(true)
    })
  })

  describe('同步功能', () => {
    it('应该处理手动同步', async () => {
      const message = { type: 'MANUAL_SYNC' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data).toHaveProperty('syncedCount')
      expect(response.data).toHaveProperty('lastSync')
      expect(typeof response.data.syncedCount).toBe('number')
      expect(response.data.lastSync).toBeInstanceOf(Date)
    })
  })

  describe('页面信息提取', () => {
    it('应该处理获取页面信息请求', async () => {
      const mockPageInfo = { title: '测试页面', url: 'https://example.com' }
      chrome.tabs.sendMessage.mockResolvedValue(mockPageInfo)

      const message = { type: 'GET_PAGE_INFO', data: { selectedText: '选中文字' } }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data).toEqual(mockPageInfo)
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(123, {
        type: 'EXTRACT_PAGE_INFO',
        data: { selectedText: '选中文字' }
      })
    })

    it('应该处理获取链接信息请求', async () => {
      const mockLinkInfo = { title: '链接标题', description: '链接描述' }
      chrome.tabs.sendMessage.mockResolvedValue(mockLinkInfo)

      const message = { type: 'GET_LINK_INFO', data: { url: 'https://example.com' } }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(true)
      expect(response.data).toEqual(mockLinkInfo)
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(123, {
        type: 'EXTRACT_LINK_INFO',
        data: { url: 'https://example.com' }
      })
    })

    it('应该处理content script通信失败', async () => {
      chrome.tabs.sendMessage.mockRejectedValue(new Error('无法连接到content script'))

      const message = { type: 'GET_PAGE_INFO' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toContain('获取页面信息失败')
    })

    it('应该处理缺少标签页信息的情况', async () => {
      const message = { type: 'GET_PAGE_INFO' }
      const sender = {} // 没有tab信息

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toContain('无法获取标签页信息')
    })
  })

  describe('错误处理', () => {
    it('应该处理处理器抛出的异常', async () => {
      // 模拟存储错误
      chrome.storage.local.get.mockRejectedValue(new Error('存储错误'))

      const message = { type: 'GET_SETTINGS', requestId: 'req_123' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toContain('获取设置失败')
      expect(response.requestId).toBe('req_123')
    })

    it('应该处理未知异常', async () => {
      // 创建一个会抛出非Error对象的处理器
      const originalHandler = messageHandler.handlers['PING']
      messageHandler.handlers['PING'] = () => {
        throw 'string error' // 抛出字符串而不是Error对象
      }

      const message = { type: 'PING' }
      const sender = { tab: { id: 123 } }

      const response = await messageHandler.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toBe('未知错误')

      // 恢复原处理器
      messageHandler.handlers['PING'] = originalHandler
    })
  })

  describe('ID生成', () => {
    it('应该生成唯一的ID', async () => {
      const message1 = { type: 'QUICK_BOOKMARK', data: { title: 'test1', url: 'https://test1.com' } }
      const message2 = { type: 'QUICK_BOOKMARK', data: { title: 'test2', url: 'https://test2.com' } }
      const sender = { tab: { id: 123 } }

      const response1 = await messageHandler.handleMessage(message1, sender)
      const response2 = await messageHandler.handleMessage(message2, sender)

      expect(response1.data.bookmarkId).not.toBe(response2.data.bookmarkId)
      expect(response1.data.bookmarkId).toMatch(/^[a-z0-9]+$/)
      expect(response2.data.bookmarkId).toMatch(/^[a-z0-9]+$/)
    })
  })
})