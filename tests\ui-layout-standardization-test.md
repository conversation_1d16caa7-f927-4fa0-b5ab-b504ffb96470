# UI布局统一规范测试指南

## 测试目的

验证管理页面的UI布局统一规范是否正确实现。

## 测试环境

1. 构建插件：`npm run build`
2. 在Chrome浏览器中加载 `dist` 文件夹
3. 打开插件的选项页面

## 测试项目

### 1. 页面标题区域测试

访问以下三个管理页面，验证标题区域的一致性：

#### 分类管理页面
- [ ] 标题区域使用Card组件包装
- [ ] 显示文件夹树形图标 (FolderTree)
- [ ] 标题文字："分类管理"
- [ ] 描述文字："管理您的书签分类，更好地组织收藏内容"
- [ ] 右侧有"刷新"和"新建分类"按钮

#### 标签管理页面
- [ ] 标题区域使用Card组件包装
- [ ] 显示标签图标 (Tags)
- [ ] 标题文字："标签管理"
- [ ] 描述文字："管理您的书签标签，更好地分类和查找内容"
- [ ] 右侧有"刷新"和"新建标签"按钮

#### 导入导出页面
- [ ] 标题区域使用Card组件包装
- [ ] 显示数据库图标 (Database)
- [ ] 标题文字："导入导出"
- [ ] 描述文字："导出您的收藏数据或从其他来源导入收藏"

### 2. 按钮样式测试

#### 主要操作按钮（蓝色背景）
- [ ] "新建分类"按钮样式正确
- [ ] "新建标签"按钮样式正确
- [ ] "开始导出"按钮样式正确
- [ ] "开始导入"按钮样式正确

#### 次要操作按钮（白色背景，边框）
- [ ] "刷新"按钮样式正确
- [ ] 其他次要操作按钮样式正确

#### 按钮交互状态
- [ ] hover状态正确
- [ ] disabled状态正确
- [ ] 加载状态正确（如果适用）

### 3. 错误状态显示测试

模拟错误情况（如网络断开），验证错误状态显示：

- [ ] 错误状态使用Card组件包装
- [ ] 显示错误图标
- [ ] 错误标题格式正确
- [ ] 错误描述格式正确
- [ ] "重试"按钮样式正确

### 4. 响应式布局测试

- [ ] 在不同屏幕尺寸下布局正常
- [ ] 移动设备上显示正常
- [ ] 按钮在小屏幕上不会重叠

### 5. 视觉一致性测试

- [ ] 三个页面的标题区域高度一致
- [ ] 图标大小一致（6x6）
- [ ] 图标颜色一致（text-primary-600）
- [ ] 按钮间距一致
- [ ] 字体大小和权重一致

## 预期结果

所有测试项目都应该通过，确保：

1. **视觉一致性**：三个管理页面看起来像是同一个应用的不同部分
2. **用户体验**：用户可以轻松识别和导航不同的管理页面
3. **可访问性**：所有元素都有适当的颜色对比和语义化标记
4. **响应式**：在不同设备上都能正常使用

## 问题报告

如果发现任何问题，请记录：

1. 问题描述
2. 出现问题的页面
3. 浏览器和版本
4. 屏幕截图（如果适用）
5. 重现步骤

## 测试完成标准

- [ ] 所有测试项目都通过
- [ ] 没有发现视觉不一致的问题
- [ ] 用户体验流畅
- [ ] 在主流浏览器中都能正常工作

---

*测试指南创建时间：2025年1月16日*
*对应优化文档：ui-layout-standardization-summary.md*