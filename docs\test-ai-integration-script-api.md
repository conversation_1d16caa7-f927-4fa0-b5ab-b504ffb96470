# AI集成服务测试脚本API文档

## 概述

`scripts/test-ai-integration.js` 是一个专门用于测试聚合AI服务集成功能的Node.js测试脚本。该脚本提供了完整的AI集成服务功能测试，包括基本功能、本地服务发现、对话功能和配置管理等。

## 脚本信息

- **文件路径**: `scripts/test-ai-integration.js`
- **类型**: CommonJS模块
- **运行环境**: Node.js
- **依赖**: 需要项目已构建完成

## 使用方法

### 直接运行
```bash
node scripts/test-ai-integration.js
```

### 通过npm脚本运行
```bash
npm run test:ai-integration
```

## 主要功能

### 1. Chrome扩展环境模拟

脚本自动模拟Chrome扩展的存储API，提供测试数据：

```javascript
// 模拟的提供商配置
const mockProviders = [
  {
    id: 'openai_demo',
    type: 'openai',
    name: '演示 OpenAI',
    baseUrl: 'https://api.openai.com/v1',
    apiKey: 'sk-demo-key',
    enabled: true
  },
  {
    id: 'ollama_demo', 
    type: 'ollama',
    name: '演示 Ollama',
    baseUrl: 'http://localhost:11434',
    enabled: true
  }
]
```

### 2. 测试函数

#### `testAIIntegrationBasics()`
**函数签名**:
```javascript
async function testAIIntegrationBasics(): Promise<boolean>
```

**功能**: 测试AI集成服务的基本功能

**测试内容**:
- 获取支持的提供商列表
- 获取已配置的提供商列表
- 添加新提供商配置
- 测试提供商连接
- 获取模型列表

**返回值**: `Promise<boolean>` - 测试是否通过

**使用示例**:
```javascript
const result = await testAIIntegrationBasics()
if (result) {
  console.log('AI集成服务基本功能测试通过')
}
```

#### `testLocalAIServiceDiscovery()`
**函数签名**:
```javascript
async function testLocalAIServiceDiscovery(): Promise<boolean>
```

**功能**: 测试本地AI服务发现功能

**测试内容**:
- 发现本地AI服务
- 测试本地服务连接
- 获取本地服务模型列表

**返回值**: `Promise<boolean>` - 测试是否通过

**使用示例**:
```javascript
const result = await testLocalAIServiceDiscovery()
console.log(`本地服务发现测试: ${result ? '通过' : '失败'}`)
```

#### `testAIChatFunctionality()`
**函数签名**:
```javascript
async function testAIChatFunctionality(): Promise<boolean>
```

**功能**: 测试AI对话功能

**测试内容**:
- 本地服务对话测试（模拟）
- 云端服务对话测试（模拟）
- 模型连接测试

**返回值**: `Promise<boolean>` - 测试是否通过

**使用示例**:
```javascript
const result = await testAIChatFunctionality()
console.log(`AI对话功能测试: ${result ? '通过' : '失败'}`)
```

#### `testConfigurationManagement()`
**函数签名**:
```javascript
async function testConfigurationManagement(): Promise<boolean>
```

**功能**: 测试配置管理功能

**测试内容**:
- 导出配置功能
- 获取统计信息
- 配置验证功能

**返回值**: `Promise<boolean>` - 测试是否通过

**使用示例**:
```javascript
const result = await testConfigurationManagement()
console.log(`配置管理功能测试: ${result ? '通过' : '失败'}`)
```

#### `runAllTests()`
**函数签名**:
```javascript
async function runAllTests(): Promise<void>
```

**功能**: 执行所有测试并生成汇总报告

**测试流程**:
1. 运行AI集成服务基本功能测试
2. 运行本地AI服务发现功能测试
3. 运行AI对话功能测试
4. 运行配置管理功能测试
5. 生成测试结果汇总

**使用示例**:
```javascript
await runAllTests()
// 输出完整的测试报告
```

## 模块导出

脚本导出以下函数供其他模块使用：

```javascript
module.exports = {
  testAIIntegrationBasics,
  testLocalAIServiceDiscovery, 
  testAIChatFunctionality,
  testConfigurationManagement,
  runAllTests
}
```

## 测试输出格式

### 成功信息
```
✅ 操作成功完成
```

### 错误信息
```
❌ 操作失败: 错误详情
```

### 警告信息
```
⚠️  这是一个警告
```

### 测试进度
```
📋 1. 测试项目名称
   - 子测试项目
   ✅ 子测试结果
```

## 测试报告示例

```
🚀 开始AI集成服务综合测试...
============================================================

🧪 测试AI集成服务基本功能...

📋 1. 获取支持的提供商列表
✅ 找到 14 个支持的提供商:
   - Ollama (ollama): 本地部署的开源AI模型服务
   - LM Studio (lm-studio): 本地AI模型运行环境
   - OpenRouter (openrouter): 多模型聚合API服务
   ...

📋 2. 获取已配置的提供商列表
✅ 找到 2 个已配置的提供商:
   - 演示 OpenAI (openai): https://api.openai.com/v1
   - 演示 Ollama (ollama): http://localhost:11434

📋 3. 测试添加新提供商
✅ 成功添加新提供商

📋 4. 测试提供商连接
   测试 演示 OpenAI...
   ✅ 演示 OpenAI: 1250ms
   测试 演示 Ollama...
   ❌ 演示 Ollama: 连接超时或API密钥无效

📋 5. 测试获取模型列表
   获取 演示 OpenAI 模型...
   ✅ 演示 OpenAI: 找到 2 个模型
      - GPT-4: OpenAI最先进的大型语言模型
      - GPT-3.5 Turbo: OpenAI高效的对话模型

🎉 AI集成服务基本功能测试完成！

============================================================
📊 测试结果汇总:
   ✅ AI集成服务基本功能
   ✅ 本地AI服务发现功能
   ✅ AI对话功能
   ✅ 配置管理功能

🎯 测试完成: 4/4 项通过
🎉 所有测试都通过了！聚合AI服务集成功能正常。
```

## 依赖服务

### 必需的服务模块
- `aiIntegrationService` - AI集成服务
- `aiChatService` - AI对话服务
- `localAIServiceAdapter` - 本地AI服务适配器

### 模拟环境
- Chrome扩展存储API模拟
- 测试数据模拟
- 网络请求模拟

## 错误处理

### 模块加载错误
```javascript
// 如果服务模块加载失败
❌ AI集成服务测试失败: Cannot resolve module
```

### 功能测试错误
```javascript
// 各个功能测试的错误处理
⚠️  添加提供商测试: 已存在同名的提供商配置
❌ 本地AI服务发现测试失败: 网络连接错误
```

## 扩展使用

### 单独运行特定测试
```javascript
const { testAIIntegrationBasics } = require('./scripts/test-ai-integration.js')

// 只运行基本功能测试
testAIIntegrationBasics().then(result => {
  console.log(`基本功能测试: ${result ? '通过' : '失败'}`)
})
```

### 自定义测试数据
```javascript
// 修改模拟数据
global.chrome.storage.sync.get = (keys, callback) => {
  const customMockData = {
    ai_providers: [
      // 自定义的提供商配置
    ]
  }
  // 处理逻辑...
}
```

### 集成到CI/CD
```bash
# 在构建脚本中添加
npm run build
npm run test:ai-integration
```

## 注意事项

1. **模拟环境**: 脚本使用模拟数据和环境，实际功能需要在浏览器扩展环境中测试
2. **网络依赖**: 某些测试可能需要网络连接来验证真实的API端点
3. **构建要求**: 运行前必须执行 `npm run build`
4. **测试范围**: 主要测试功能逻辑，UI交互需要在浏览器中测试

## 相关文档

- [AI集成服务API文档](./aiIntegrationService-api.md)
- [本地AI服务适配器API文档](./localAIServiceAdapter-api.md)
- [AI对话服务API文档](./aiChatService-api.md)
- [AI集成测试使用示例](./test-ai-integration-usage-examples.md)