// BookmarkRow组件演示 - 展示shadcn重构后的效果

import React from 'react'
import BookmarkRow from '../BookmarkRow'
import type { Bookmark } from '../../types'

// 创建演示用的收藏数据
const createDemoBookmarks = (): Bookmark[] => [
  {
    id: 'demo-1',
    type: 'url',
    title: 'React官方文档 - 学习现代React开发',
    url: 'https://react.dev',
    description: 'React官方文档，学习现代React开发的最佳资源',
    tags: ['React', '前端', '文档'],
    category: '开发工具',
    favicon: 'https://react.dev/favicon.ico',
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {
      pageTitle: 'React',
      siteName: 'react.dev',
      aiGenerated: false
    }
  },
  {
    id: 'demo-2',
    type: 'url',
    title: 'shadcn/ui - 美观的React组件库',
    url: 'https://ui.shadcn.com',
    description: 'shadcn/ui是一个基于Radix UI和Tailwind CSS的React组件库',
    tags: ['UI', 'React', 'Tailwind'],
    category: 'UI库',
    favicon: 'https://ui.shadcn.com/favicon.ico',
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 昨天
    updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    metadata: {
      pageTitle: 'shadcn/ui',
      siteName: 'ui.shadcn.com',
      aiGenerated: false
    }
  },
  {
    id: 'demo-3',
    type: 'url',
    title: 'TypeScript官方手册',
    url: 'https://www.typescriptlang.org/docs/',
    description: 'TypeScript官方文档和学习资源',
    tags: ['TypeScript', '编程语言'],
    category: '编程语言',
    favicon: undefined, // 测试无图标情况
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3天前
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    metadata: {
      pageTitle: 'TypeScript Documentation',
      siteName: 'typescriptlang.org',
      aiGenerated: false
    }
  },
  {
    id: 'demo-4',
    type: 'url',
    title: '无URL的收藏项目',
    url: undefined, // 测试无URL情况
    description: '这是一个没有URL的收藏项目，用于测试组件的边界情况',
    tags: ['测试'],
    category: '测试',
    favicon: undefined,
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 一周前
    updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    metadata: {
      pageTitle: '测试项目',
      siteName: '',
      aiGenerated: false
    }
  }
]

/**
 * BookmarkRow组件演示
 * 展示使用shadcn组件重构后的BookmarkRow组件效果
 */
const BookmarkRowDemo: React.FC = () => {
  const demoBookmarks = createDemoBookmarks()

  const handleEdit = (bookmark: Bookmark) => {
    console.log('编辑收藏:', bookmark.title)
    alert(`编辑收藏: ${bookmark.title}`)
  }

  const handleDelete = (bookmark: Bookmark) => {
    console.log('删除收藏:', bookmark.title)
    if (confirm(`确定要删除收藏"${bookmark.title}"吗？`)) {
      alert('收藏已删除')
    }
  }

  const handleClick = (bookmark: Bookmark) => {
    console.log('点击收藏:', bookmark.title)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="space-y-4">
        <h1 className="text-2xl font-bold text-foreground">
          BookmarkRow组件演示 (shadcn重构版)
        </h1>
        <p className="text-muted-foreground">
          展示使用shadcn/ui组件重构后的BookmarkRow组件，包括Card容器、Button操作按钮、Badge标签和Tooltip提示。
        </p>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-foreground">基础展示</h2>
        <div className="border rounded-lg overflow-hidden">
          {demoBookmarks.map((bookmark, index) => (
            <BookmarkRow
              key={bookmark.id}
              bookmark={bookmark}
              isHighlighted={index === 1} // 高亮第二个项目
              onEdit={handleEdit}
              onDelete={handleDelete}
              onClick={handleClick}
            />
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-foreground">功能特性</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <h3 className="font-medium text-foreground">shadcn组件使用</h3>
            <ul className="space-y-1 text-muted-foreground">
              <li>• Card组件作为行容器</li>
              <li>• Button组件替换操作按钮</li>
              <li>• Badge组件显示分类标签</li>
              <li>• Tooltip组件添加操作提示</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium text-foreground">交互功能</h3>
            <ul className="space-y-1 text-muted-foreground">
              <li>• 悬停显示操作按钮</li>
              <li>• 点击行触发选择事件</li>
              <li>• 编辑和删除操作</li>
              <li>• 外部链接打开</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-foreground">样式特性</h2>
        <div className="text-sm text-muted-foreground space-y-2">
          <p>• 使用shadcn原生的hover和focus状态样式</p>
          <p>• 采用shadcn的颜色系统 (foreground, muted-foreground, primary等)</p>
          <p>• 支持高亮状态显示 (bg-accent, border-primary)</p>
          <p>• 响应式设计和无障碍访问支持</p>
        </div>
      </div>
    </div>
  )
}

export default BookmarkRowDemo