// OptionsApp重构后的单元测试
import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import '@testing-library/jest-dom'
import OptionsApp from '../../src/options/OptionsApp'

// 模拟Chrome API
const mockChrome = {
  runtime: {
    id: 'test-extension-id'
  }
}

// 模拟全局Chrome对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

// 模拟localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

// 模拟window.history
const mockHistory = {
  replaceState: vi.fn()
}

Object.defineProperty(global, 'history', {
  value: mockHistory,
  writable: true
})

// 模拟window.location
Object.defineProperty(global, 'location', {
  value: {
    hash: '',
    search: ''
  },
  writable: true
})

// 模拟组件
vi.mock('../../src/components/BookmarksTab', () => ({
  default: () => <div data-testid="bookmarks-tab">收藏管理</div>
}))

vi.mock('../../src/components/CategoryManagementTab', () => ({
  default: () => <div data-testid="categories-tab">分类管理</div>
}))

vi.mock('../../src/components/TagsTab', () => ({
  default: () => <div data-testid="tags-tab">标签管理</div>
}))

vi.mock('../../src/options/hooks/useResponsive', () => ({
  useResponsive: () => ({
    isMobile: false,
    getResponsiveValue: (values: Record<string, string>, defaultValue: string) => defaultValue
  })
}))

vi.mock('../../src/utils/errorHandler', () => ({
  setupGlobalErrorHandler: vi.fn()
}))

describe('OptionsApp 重构后测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
    global.location.hash = ''
    global.location.search = ''
  })

  describe('组件初始化', () => {
    it('应该正确渲染应用头部', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
        expect(screen.getByText('智能收藏管理工具')).toBeInTheDocument()
      })
    })

    it('应该显示加载状态然后渲染主界面', async () => {
      render(<OptionsApp />)
      
      // 检查加载状态
      expect(screen.getByText('正在初始化收藏管理页面...')).toBeInTheDocument()
      
      // 等待加载完成
      await waitFor(() => {
        expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
      })
      
      // 检查主界面
      expect(screen.getByText('收藏管理')).toBeInTheDocument()
    })

    it('应该默认显示收藏管理标签页', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('bookmarks-tab')).toBeInTheDocument()
      })
    })
  })

  describe('标签页导航', () => {
    it('应该能够切换到不同的标签页', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('bookmarks-tab')).toBeInTheDocument()
      })
      
      // 点击分类管理标签页
      const categoriesTab = screen.getByRole('tab', { name: /分类管理/ })
      fireEvent.click(categoriesTab)
      
      await waitFor(() => {
        expect(screen.getByTestId('categories-tab')).toBeInTheDocument()
      })
    })

    it('应该保存标签页状态到localStorage', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('bookmarks-tab')).toBeInTheDocument()
      })
      
      // 点击标签管理标签页
      const tagsTab = screen.getByRole('tab', { name: /标签管理/ })
      fireEvent.click(tagsTab)
      
      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('options-active-tab', 'tags')
      })
    })

    it('应该更新URL hash当切换标签页时', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('bookmarks-tab')).toBeInTheDocument()
      })
      
      // 点击分类管理标签页
      const categoriesTab = screen.getByRole('tab', { name: /分类管理/ })
      fireEvent.click(categoriesTab)
      
      await waitFor(() => {
        expect(mockHistory.replaceState).toHaveBeenCalledWith(null, '', '#categories')
      })
    })
  })

  describe('键盘导航', () => {
    it('应该支持Alt+数字键快速切换标签页', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('bookmarks-tab')).toBeInTheDocument()
      })
      
      // 模拟Alt+2键（分类管理）
      fireEvent.keyDown(document, { key: '2', altKey: true })
      
      await waitFor(() => {
        expect(screen.getByTestId('categories-tab')).toBeInTheDocument()
      })
    })
  })

  describe('错误处理', () => {
    it('应该在Chrome API不可用时显示错误', async () => {
      // 临时移除Chrome API
      const originalChrome = global.chrome
      delete (global as any).chrome
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('初始化失败')).toBeInTheDocument()
        expect(screen.getByText(/Chrome扩展API不可用/)).toBeInTheDocument()
      })
      
      // 恢复Chrome API
      global.chrome = originalChrome
    })

    it('应该提供重试功能', async () => {
      // 临时移除Chrome API
      const originalChrome = global.chrome
      delete (global as any).chrome
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('初始化失败')).toBeInTheDocument()
      })
      
      // 恢复Chrome API
      global.chrome = originalChrome
      
      // 点击重试按钮
      const retryButton = screen.getByText(/重试/)
      fireEvent.click(retryButton)
      
      await waitFor(() => {
        expect(screen.getByTestId('bookmarks-tab')).toBeInTheDocument()
      })
    })
  })

  describe('测试页面显示控制', () => {
    it('默认情况下不应该显示测试页面', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.queryByText('shadcn测试')).not.toBeInTheDocument()
        expect(screen.queryByText('删除测试')).not.toBeInTheDocument()
      })
    })

    it('当URL包含dev=true时应该显示测试页面', async () => {
      global.location.search = '?dev=true'
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('shadcn测试')).toBeInTheDocument()
        expect(screen.getByText('删除测试')).toBeInTheDocument()
      })
    })
  })

  describe('响应式设计', () => {
    it('应该在移动端使用不同的布局', async () => {
      // 模拟移动端
      vi.mocked(require('../../src/options/hooks/useResponsive').useResponsive).mockReturnValue({
        isMobile: true,
        getResponsiveValue: (values: Record<string, string>, defaultValue: string) => defaultValue
      })
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        // 检查移动端特定的样式类
        const navigation = screen.getByRole('navigation')
        expect(navigation).toHaveClass('w-full')
      })
    })
  })

  describe('本地存储恢复', () => {
    it('应该从localStorage恢复上次的标签页状态', async () => {
      mockLocalStorage.getItem.mockReturnValue('tags')
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('tags-tab')).toBeInTheDocument()
      })
    })

    it('URL hash应该优先于localStorage', async () => {
      mockLocalStorage.getItem.mockReturnValue('tags')
      global.location.hash = '#categories'
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByTestId('categories-tab')).toBeInTheDocument()
      })
    })
  })
})