// 标签批量操作组件 - 提供批量删除、合并等功能

import React, { useState } from 'react'
import { Trash2, Merge, Palette, Check, X, AlertTriangle } from 'lucide-react'
import type { Tag } from '../types'
import TagColorPicker from './TagColorPicker'
import LoadingIndicator from './LoadingIndicator'

interface TagBatchActionsProps {
  /** 选中的标签列表 */
  selectedTags: Tag[]
  /** 所有标签列表（用于合并操作） */
  allTags: Tag[]
  /** 批量删除回调 */
  onBatchDelete: (tagIds: string[]) => Promise<void>
  /** 批量合并回调 */
  onBatchMerge: (sourceTagIds: string[], targetTagId: string) => Promise<void>
  /** 批量设置颜色回调 */
  onBatchSetColor: (tagIds: string[], color: string) => Promise<void>
  /** 清除选择回调 */
  onClearSelection: () => void
  /** 自定义CSS类名 */
  className?: string
}

type BatchOperation = 'delete' | 'merge' | 'color' | null

/**
 * 标签批量操作组件
 * 提供批量删除、合并、设置颜色等功能
 */
const TagBatchActions: React.FC<TagBatchActionsProps> = React.memo(({
  selectedTags,
  allTags,
  onBatchDelete,
  onBatchMerge,
  onBatchSetColor,
  onClearSelection,
  className = ''
}) => {
  const [currentOperation, setCurrentOperation] = useState<BatchOperation>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [mergeTargetId, setMergeTargetId] = useState<string>('')
  const [batchColor, setBatchColor] = useState<string>('#3B82F6')

  // 如果没有选中的标签，不显示组件
  if (selectedTags.length === 0) return null

  // 获取可用于合并的目标标签（排除已选中的标签）
  const availableTargetTags = allTags.filter(
    tag => !selectedTags.some(selected => selected.id === tag.id)
  )

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedTags.length === 0) return

    try {
      setIsLoading(true)
      const tagIds = selectedTags.map(tag => tag.id)
      await onBatchDelete(tagIds)
      setCurrentOperation(null)
      onClearSelection()
    } catch (error) {
      console.error('批量删除失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理批量合并
  const handleBatchMerge = async () => {
    if (selectedTags.length === 0 || !mergeTargetId) return

    try {
      setIsLoading(true)
      const sourceTagIds = selectedTags.map(tag => tag.id)
      await onBatchMerge(sourceTagIds, mergeTargetId)
      setCurrentOperation(null)
      setMergeTargetId('')
      onClearSelection()
    } catch (error) {
      console.error('批量合并失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理批量设置颜色
  const handleBatchSetColor = async () => {
    if (selectedTags.length === 0 || !batchColor) return

    try {
      setIsLoading(true)
      const tagIds = selectedTags.map(tag => tag.id)
      await onBatchSetColor(tagIds, batchColor)
      setCurrentOperation(null)
      onClearSelection()
    } catch (error) {
      console.error('批量设置颜色失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 取消当前操作
  const handleCancel = () => {
    setCurrentOperation(null)
    setMergeTargetId('')
    setBatchColor('#3B82F6')
  }

  // 渲染删除确认界面
  const renderDeleteConfirmation = () => (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
      <div className="flex items-start space-x-3">
        <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h4 className="text-sm font-semibold text-red-800 mb-2">
            确认批量删除
          </h4>
          <p className="text-sm text-red-700 mb-3">
            您即将删除 {selectedTags.length} 个标签：
          </p>
          <div className="flex flex-wrap gap-1 mb-4">
            {selectedTags.map(tag => (
              <span
                key={tag.id}
                className="inline-flex items-center px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full"
              >
                {tag.name}
              </span>
            ))}
          </div>
          <p className="text-sm text-red-700 mb-4">
            这些标签将从所有相关书签中移除，此操作不可撤销。
          </p>
          <div className="flex space-x-2">
            <button
              onClick={handleBatchDelete}
              disabled={isLoading}
              className="flex items-center px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 disabled:opacity-50"
            >
              {isLoading ? (
                <LoadingIndicator isVisible={true} size="small" />
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  确认删除
                </>
              )}
            </button>
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="px-3 py-2 bg-gray-200 text-gray-700 text-sm rounded-lg hover:bg-gray-300 disabled:opacity-50"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  // 渲染合并界面
  const renderMergeInterface = () => (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-start space-x-3">
        <Merge className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h4 className="text-sm font-semibold text-blue-800 mb-2">
            批量合并标签
          </h4>
          <p className="text-sm text-blue-700 mb-3">
            将以下 {selectedTags.length} 个标签合并到目标标签：
          </p>
          <div className="flex flex-wrap gap-1 mb-4">
            {selectedTags.map(tag => (
              <span
                key={tag.id}
                className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {tag.name}
              </span>
            ))}
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-blue-800 mb-2">
              选择目标标签：
            </label>
            <select
              value={mergeTargetId}
              onChange={(e) => setMergeTargetId(e.target.value)}
              className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isLoading}
            >
              <option value="">请选择目标标签</option>
              {availableTargetTags.map(tag => (
                <option key={tag.id} value={tag.id}>
                  {tag.name}
                </option>
              ))}
            </select>
          </div>
          <p className="text-sm text-blue-700 mb-4">
            所有使用这些标签的书签将改为使用目标标签，原标签将被删除。
          </p>
          <div className="flex space-x-2">
            <button
              onClick={handleBatchMerge}
              disabled={isLoading || !mergeTargetId}
              className="flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? (
                <LoadingIndicator isVisible={true} size="small" />
              ) : (
                <>
                  <Merge className="w-4 h-4 mr-2" />
                  确认合并
                </>
              )}
            </button>
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="px-3 py-2 bg-gray-200 text-gray-700 text-sm rounded-lg hover:bg-gray-300 disabled:opacity-50"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  // 渲染颜色设置界面
  const renderColorInterface = () => (
    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
      <div className="flex items-start space-x-3">
        <Palette className="w-5 h-5 text-purple-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h4 className="text-sm font-semibold text-purple-800 mb-2">
            批量设置标签颜色
          </h4>
          <p className="text-sm text-purple-700 mb-3">
            为以下 {selectedTags.length} 个标签设置统一颜色：
          </p>
          <div className="flex flex-wrap gap-1 mb-4">
            {selectedTags.map(tag => (
              <span
                key={tag.id}
                className="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
              >
                {tag.name}
              </span>
            ))}
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-purple-800 mb-2">
              选择颜色：
            </label>
            <TagColorPicker
              value={batchColor}
              onChange={setBatchColor}
              className="w-full"
            />
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleBatchSetColor}
              disabled={isLoading}
              className="flex items-center px-3 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              {isLoading ? (
                <LoadingIndicator isVisible={true} size="small" />
              ) : (
                <>
                  <Palette className="w-4 h-4 mr-2" />
                  应用颜色
                </>
              )}
            </button>
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="px-3 py-2 bg-gray-200 text-gray-700 text-sm rounded-lg hover:bg-gray-300 disabled:opacity-50"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* 操作确认界面 */}
      {currentOperation === 'delete' && renderDeleteConfirmation()}
      {currentOperation === 'merge' && renderMergeInterface()}
      {currentOperation === 'color' && renderColorInterface()}

      {/* 默认操作栏 */}
      {!currentOperation && (
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-gray-700">
                已选择 {selectedTags.length} 个标签
              </span>
              <div className="flex flex-wrap gap-1">
                {selectedTags.slice(0, 3).map(tag => (
                  <span
                    key={tag.id}
                    className="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                  >
                    {tag.name}
                  </span>
                ))}
                {selectedTags.length > 3 && (
                  <span className="text-xs text-gray-500">
                    +{selectedTags.length - 3} 个
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* 批量操作按钮 */}
              <button
                onClick={() => setCurrentOperation('delete')}
                className="flex items-center px-3 py-2 text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
                title="批量删除"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                删除
              </button>

              <button
                onClick={() => setCurrentOperation('merge')}
                disabled={availableTargetTags.length === 0}
                className="flex items-center px-3 py-2 text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50"
                title="批量合并"
              >
                <Merge className="w-4 h-4 mr-2" />
                合并
              </button>

              <button
                onClick={() => setCurrentOperation('color')}
                className="flex items-center px-3 py-2 text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
                title="批量设置颜色"
              >
                <Palette className="w-4 h-4 mr-2" />
                设置颜色
              </button>

              {/* 清除选择按钮 */}
              <button
                onClick={onClearSelection}
                className="flex items-center px-3 py-2 text-gray-600 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
                title="清除选择"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
})

// 设置显示名称便于调试
TagBatchActions.displayName = 'TagBatchActions'

export default TagBatchActions