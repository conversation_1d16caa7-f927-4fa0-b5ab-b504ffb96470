import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AdvancedComponentsDemo } from '../src/components/examples/AdvancedComponentsDemo';

/**
 * shadcn/ui 高级交互组件测试
 * 测试 DropdownMenu、Badge、Select、Tooltip 组件的正确安装和基本功能
 */
describe('shadcn/ui 高级交互组件测试', () => {
  
  describe('DropdownMenu 组件', () => {
    it('应该正确渲染 DropdownMenu 触发器', () => {
      render(<AdvancedComponentsDemo />);
      
      const trigger = screen.getByRole('button', { name: /操作菜单/i });
      expect(trigger).toBeInTheDocument();
    });

    it('应该在点击时显示下拉菜单内容', async () => {
      const user = userEvent.setup();
      render(<AdvancedComponentsDemo />);
      
      const trigger = screen.getByRole('button', { name: /操作菜单/i });
      await user.click(trigger);
      
      await waitFor(() => {
        expect(screen.getByText('收藏夹操作')).toBeInTheDocument();
        expect(screen.getByText('编辑收藏夹')).toBeInTheDocument();
        expect(screen.getByText('复制链接')).toBeInTheDocument();
        expect(screen.getByText('删除收藏夹')).toBeInTheDocument();
      });
    });
  });

  describe('Badge 组件', () => {
    it('应该正确渲染不同变体的 Badge', () => {
      render(<AdvancedComponentsDemo />);
      
      expect(screen.getByText('默认标签')).toBeInTheDocument();
      expect(screen.getByText('次要标签')).toBeInTheDocument();
      expect(screen.getByText('重要标签')).toBeInTheDocument();
      expect(screen.getByText('轮廓标签')).toBeInTheDocument();
    });

    it('应该正确渲染标签内容', () => {
      render(<AdvancedComponentsDemo />);
      
      expect(screen.getByText('技术')).toBeInTheDocument();
      expect(screen.getByText('前端')).toBeInTheDocument();
      expect(screen.getByText('React')).toBeInTheDocument();
      expect(screen.getByText('shadcn/ui')).toBeInTheDocument();
    });
  });

  describe('Select 组件', () => {
    it('应该正确渲染 Select 组件', () => {
      render(<AdvancedComponentsDemo />);
      
      const selectTrigger = screen.getByRole('combobox');
      expect(selectTrigger).toBeInTheDocument();
      expect(screen.getByText('选择分类')).toBeInTheDocument();
    });

    it('应该正确渲染 Select 组件的基本结构', () => {
      render(<AdvancedComponentsDemo />);
      
      // 验证 Select 组件的基本结构存在
      const selectTrigger = screen.getByRole('combobox');
      expect(selectTrigger).toBeInTheDocument();
      expect(selectTrigger).toHaveAttribute('data-state', 'closed');
    });
  });

  describe('Tooltip 组件', () => {
    it('应该正确渲染 Tooltip 触发器', () => {
      render(<AdvancedComponentsDemo />);
      
      expect(screen.getByRole('button', { name: /悬停查看提示/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /编辑/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /删除/i })).toBeInTheDocument();
    });

    it('应该正确渲染 Tooltip 组件的基本结构', () => {
      render(<AdvancedComponentsDemo />);
      
      // 验证 Tooltip 触发器存在
      const tooltipTriggers = screen.getAllByRole('button');
      const tooltipButton = tooltipTriggers.find(button => 
        button.textContent?.includes('悬停查看提示')
      );
      expect(tooltipButton).toBeInTheDocument();
    });
  });

  describe('组合使用', () => {
    it('应该正确渲染组合使用的示例', () => {
      render(<AdvancedComponentsDemo />);
      
      expect(screen.getByText('示例收藏夹')).toBeInTheDocument();
      // 使用 getAllByText 来处理重复的文本
      const reactBadges = screen.getAllByText('React');
      expect(reactBadges.length).toBeGreaterThan(0);
      expect(screen.getByText('教程')).toBeInTheDocument();
    });

    it('应该在组合示例中正确显示操作按钮', () => {
      render(<AdvancedComponentsDemo />);
      
      const starButton = screen.getByRole('button', { name: '⭐' });
      const menuButton = screen.getByRole('button', { name: '⋮' });
      
      expect(starButton).toBeInTheDocument();
      expect(menuButton).toBeInTheDocument();
    });
  });

  describe('组件导入验证', () => {
    it('应该能够成功导入所有高级交互组件', () => {
      // 这个测试通过组件的成功渲染来验证导入是否正确
      const { container } = render(<AdvancedComponentsDemo />);
      expect(container).toBeInTheDocument();
      
      // 验证所有组件类型都存在
      expect(screen.getByText('DropdownMenu 组件')).toBeInTheDocument();
      expect(screen.getByText('Badge 组件')).toBeInTheDocument();
      expect(screen.getByText('Select 组件')).toBeInTheDocument();
      expect(screen.getByText('Tooltip 组件')).toBeInTheDocument();
    });
  });

  describe('样式和主题', () => {
    it('应该应用正确的 shadcn 样式类', () => {
      render(<AdvancedComponentsDemo />);
      
      // 检查 Badge 组件是否有正确的样式类
      const defaultBadge = screen.getByText('默认标签');
      expect(defaultBadge).toHaveClass('inline-flex', 'items-center', 'rounded-full');
      
      // 检查 Button 组件是否有正确的样式类
      const button = screen.getByRole('button', { name: /操作菜单/i });
      expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center');
    });
  });
});