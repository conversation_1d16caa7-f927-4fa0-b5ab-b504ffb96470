// 收藏图标状态管理单元测试

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { tabStatusManager } from '../src/services/tabStatusManager'
import { bookmarkStatusService } from '../src/services/bookmarkStatusService'

// Mock Chrome APIs
const mockChrome = {
  tabs: {
    get: vi.fn(),
    query: vi.fn()
  },
  action: {
    setBadgeText: vi.fn(),
    setBadgeBackgroundColor: vi.fn(),
    setBadgeTextColor: vi.fn(),
    setTitle: vi.fn(),
    getBadgeText: vi.fn()
  }
}

// @ts-ignore
global.chrome = mockChrome

describe('收藏图标状态管理', () => {
  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()
    
    // 设置默认的mock返回值
    mockChrome.tabs.get.mockResolvedValue({
      id: 1,
      url: 'https://example.com',
      title: 'Test Page'
    })
    
    mockChrome.action.getBadgeText.mockResolvedValue('')
  })

  afterEach(() => {
    // 清理资源
    tabStatusManager.cleanup()
  })

  describe('TabStatusManager', () => {
    it('应该正确处理标签页激活事件', async () => {
      const tabId = 1
      const url = 'https://example.com'
      
      // Mock收藏状态检查
      vi.spyOn(bookmarkStatusService, 'checkBookmarkStatus').mockResolvedValue({
        isBookmarked: true,
        bookmarkId: 'test-bookmark-id'
      })
      
      await tabStatusManager.onTabActivated(tabId)
      
      // 验证是否调用了状态检查
      expect(bookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledWith(url, true)
      
      // 验证是否更新了图标状态
      expect(mockChrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: '✓'
      })
    })

    it('应该正确处理标签页更新事件', async () => {
      const tabId = 1
      const changeInfo = { status: 'complete' }
      
      // Mock收藏状态检查
      vi.spyOn(bookmarkStatusService, 'checkBookmarkStatus').mockResolvedValue({
        isBookmarked: false
      })
      
      await tabStatusManager.onTabUpdated(tabId, changeInfo)
      
      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 350))
      
      // 验证是否清除了图标状态
      expect(mockChrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: ''
      })
    })

    it('应该正确处理收藏状态变化', async () => {
      const url = 'https://example.com'
      const isBookmarked = true
      
      // Mock标签页查询
      mockChrome.tabs.query.mockResolvedValue([
        { id: 1, url },
        { id: 2, url }
      ])
      
      await tabStatusManager.handleBookmarkStatusChange(url, isBookmarked)
      
      // 验证是否查询了匹配的标签页
      expect(mockChrome.tabs.query).toHaveBeenCalledWith({ url })
      
      // 验证是否更新了所有匹配标签页的图标
      expect(mockChrome.action.setBadgeText).toHaveBeenCalledTimes(2)
    })

    it('应该避免重复更新相同的图标状态', async () => {
      const tabId = 1
      const isBookmarked = true
      
      // Mock当前徽章文字为已收藏状态
      mockChrome.action.getBadgeText.mockResolvedValue('✓')
      
      // 尝试更新为相同状态
      await tabStatusManager.checkAndUpdateIconStatus(tabId, 'https://example.com')
      
      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 250))
      
      // 验证没有进行重复更新
      expect(mockChrome.action.setBadgeText).not.toHaveBeenCalled()
    })

    it('应该正确处理标签页不存在的情况', async () => {
      const tabId = 999
      
      // Mock标签页不存在
      mockChrome.tabs.get.mockRejectedValue(new Error('Tab not found'))
      
      await tabStatusManager.onTabActivated(tabId)
      
      // 验证没有尝试更新图标
      expect(mockChrome.action.setBadgeText).not.toHaveBeenCalled()
    })

    it('应该正确清理资源', () => {
      const status = tabStatusManager.getStatus()
      
      // 添加一些待处理的更新
      tabStatusManager.onTabUpdated(1, { status: 'complete' })
      tabStatusManager.onTabUpdated(2, { status: 'complete' })
      
      const statusAfterAdd = tabStatusManager.getStatus()
      expect(statusAfterAdd.pendingUpdates).toBeGreaterThan(0)
      
      // 清理资源
      tabStatusManager.cleanup()
      
      const statusAfterCleanup = tabStatusManager.getStatus()
      expect(statusAfterCleanup.pendingUpdates).toBe(0)
      expect(statusAfterCleanup.pendingIconUpdates).toBe(0)
    })
  })

  describe('BookmarkStatusService', () => {
    it('应该正确更新状态缓存', () => {
      const url = 'https://example.com'
      const isBookmarked = true
      const bookmarkId = 'test-bookmark-id'
      
      bookmarkStatusService.updateStatusCache(url, isBookmarked, bookmarkId)
      
      // 验证缓存统计
      const stats = bookmarkStatusService.getCacheStats()
      expect(stats.size).toBe(1)
    })

    it('应该正确处理收藏添加事件', async () => {
      const url = 'https://example.com'
      const bookmarkId = 'test-bookmark-id'
      
      await bookmarkStatusService.handleBookmarkAdded(url, bookmarkId)
      
      // 验证缓存是否更新
      const stats = bookmarkStatusService.getCacheStats()
      expect(stats.size).toBe(1)
    })

    it('应该正确处理收藏删除事件', async () => {
      const url = 'https://example.com'
      
      // 先添加到缓存
      bookmarkStatusService.updateStatusCache(url, true, 'test-id')
      
      await bookmarkStatusService.handleBookmarkRemoved(url)
      
      // 验证缓存状态是否更新为未收藏
      const status = await bookmarkStatusService.checkBookmarkStatus(url)
      expect(status.isBookmarked).toBe(false)
    })

    it('应该正确清理过期缓存', () => {
      const url = 'https://example.com'
      
      // 添加缓存项
      bookmarkStatusService.updateStatusCache(url, true, 'test-id')
      
      let stats = bookmarkStatusService.getCacheStats()
      expect(stats.size).toBe(1)
      
      // 清理过期缓存
      bookmarkStatusService.cleanExpiredCache()
      
      // 由于刚添加的缓存不会过期，所以应该还在
      stats = bookmarkStatusService.getCacheStats()
      expect(stats.size).toBe(1)
    })
  })

  describe('集成测试', () => {
    it('应该正确处理完整的收藏流程', async () => {
      const url = 'https://example.com'
      const bookmarkId = 'test-bookmark-id'
      const tabId = 1
      
      // Mock标签页查询
      mockChrome.tabs.query.mockResolvedValue([{ id: tabId, url }])
      
      // 1. 处理收藏添加
      await bookmarkStatusService.handleBookmarkAdded(url, bookmarkId)
      
      // 2. 通知标签页状态管理器
      await tabStatusManager.handleBookmarkStatusChange(url, true)
      
      // 3. 验证图标状态更新
      expect(mockChrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: '✓'
      })
      
      // 4. 处理收藏删除
      await bookmarkStatusService.handleBookmarkRemoved(url)
      await tabStatusManager.handleBookmarkStatusChange(url, false)
      
      // 5. 验证图标状态清除
      expect(mockChrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: ''
      })
    })
  })
})