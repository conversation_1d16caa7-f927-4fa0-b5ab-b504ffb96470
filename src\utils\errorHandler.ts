/**
 * 全局错误处理工具
 * 用于捕获和处理各种类型的错误
 */

/**
 * Chrome 对象删除错误处理器
 */
export function handleChromeDeleteError(error: Error): void {
  if (error.message.includes("Cannot delete property 'chrome'")) {
    console.warn('Chrome 对象删除失败，这通常是正常的:', error.message)
    // 尝试设置为 undefined 作为替代方案
    try {
      ;(window as any).chrome = undefined
    } catch (secondError) {
      console.warn('无法设置 chrome 为 undefined:', secondError)
    }
    return
  }
  
  // 如果不是 chrome 删除错误，重新抛出
  throw error
}

/**
 * 安全的属性删除函数
 */
export function safeDeleteProperty(obj: any, prop: string): boolean {
  try {
    delete obj[prop]
    return true
  } catch (error) {
    console.warn(`无法删除属性 ${prop}:`, error)
    try {
      obj[prop] = undefined
      return true
    } catch (secondError) {
      console.warn(`无法设置属性 ${prop} 为 undefined:`, secondError)
      return false
    }
  }
}

/**
 * 全局错误监听器
 */
export function setupGlobalErrorHandler(): void {
  // 监听未捕获的错误
  window.addEventListener('error', (event) => {
    if (event.error && event.error.message.includes("Cannot delete property 'chrome'")) {
      console.warn('捕获到 Chrome 对象删除错误，已处理:', event.error.message)
      event.preventDefault() // 阻止错误冒泡
      return
    }
  })

  // 监听未捕获的 Promise 拒绝
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && event.reason.message.includes("Cannot delete property 'chrome'")) {
      console.warn('捕获到 Chrome 对象删除 Promise 错误，已处理:', event.reason.message)
      event.preventDefault() // 阻止错误冒泡
      return
    }
  })
}

/**
 * React 错误边界辅助函数
 */
export function isKnownSafeError(error: Error): boolean {
  const safeErrorPatterns = [
    "Cannot delete property 'chrome'",
    "chrome is not defined",
    "Cannot read properties of undefined (reading 'chrome')"
  ]
  
  return safeErrorPatterns.some(pattern => 
    error.message.includes(pattern)
  )
}

/**
 * 错误报告函数
 */
export function reportError(error: Error, context?: string): void {
  // 如果是已知的安全错误，只记录警告
  if (isKnownSafeError(error)) {
    console.warn(`已知安全错误 ${context ? `(${context})` : ''}:`, error.message)
    return
  }
  
  // 其他错误正常报告
  console.error(`错误 ${context ? `(${context})` : ''}:`, error)
  
  // 这里可以添加错误上报逻辑
  // sendErrorToService(error, context)
}