// 导入导出管理服务类 - 负责收藏、分类、标签数据的导入导出功能

import { 
  Bookmark, 
  BookmarkInput, 
  Category, 
  CategoryInput, 
  Tag, 
  TagInput,
  ExportAllOptions,
  ExportBookmarksOptions,
  ExportCategoriesOptions,
  ExportTagsOptions,
  ImportData,
  ConflictDetectionResult,
  ConflictResolution,
  ResolvedData
} from '../types'
import { bookmarkService } from './bookmarkService'
import { categoryService } from './categoryService'
import { tagService } from './tagService'
import { conflictResolverService } from './ConflictResolverService'
import { ValidationUtils } from '../utils/validation'

// 导出格式类型
export type ExportFormat = 'json' | 'csv' | 'html'

// 导入来源类型
export type ImportSource = 'json' | 'csv' | 'html' | 'chrome' | 'firefox' | 'edge'

// 导出选项
export interface ExportOptions {
  format: ExportFormat
  includeContent?: boolean
  includeMetadata?: boolean
  categories?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
}

// 导入选项
export interface ImportOptions {
  source: ImportSource
  skipDuplicates?: boolean
  mergeCategories?: boolean
  defaultCategory?: string
  validateData?: boolean
}

// 导入结果
export interface ImportResult {
  success: boolean
  totalItems: number
  importedItems: number
  skippedItems: number
  errorItems: number
  errors: string[]
  duplicates: string[]
}

// 导出结果
export interface ExportResult {
  success: boolean
  data: string | Blob
  filename: string
  format: ExportFormat
  itemCount: number
}

// 进度回调函数类型
export type ProgressCallback = (progress: number, message: string) => void

/**
 * 导入导出管理服务类
 * 提供收藏、分类、标签数据的多格式导入导出功能
 */
export class ImportExportManagerService {
  
  /**
   * 导出全部数据
   * @param options 导出选项
   * @param progressCallback 进度回调
   * @returns Promise<ExportResult>
   */
  async exportAllData(
    options: ExportAllOptions,
    progressCallback?: ProgressCallback
  ): Promise<ExportResult> {
    try {
      progressCallback?.(0, '开始导出全部数据...')
      
      const exportData: ImportData = {
        version: '2.0',
        exportDate: new Date().toISOString(),
        exportType: 'all',
        metadata: {
          source: 'Universe Bag Extension',
          totalBookmarks: 0,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: options
        }
      }
      
      let totalItems = 0
      
      // 导出收藏夹数据
      if (options.includeBookmarks) {
        progressCallback?.(20, '获取收藏夹数据...')
        const bookmarks = await this.getBookmarksForExport({
          format: 'json',
          includeContent: true,
          includeMetadata: options.includeMetadata,
          dateRange: options.dateRange
        })
        
        exportData.bookmarks = bookmarks.map(bookmark => ({
          type: bookmark.type,
          title: bookmark.title,
          url: bookmark.url,
          description: bookmark.description,
          content: bookmark.content,
          category: bookmark.category,
          tags: bookmark.tags,
          favicon: bookmark.favicon,
          metadata: bookmark.metadata
        }))
        
        exportData.metadata.totalBookmarks = bookmarks.length
        totalItems += bookmarks.length
      }
      
      // 导出分类数据
      if (options.includeCategories) {
        progressCallback?.(50, '获取分类数据...')
        const categories = await categoryService.getCategories()
        
        exportData.categories = categories.map(category => ({
          name: category.name,
          description: category.description,
          color: category.color,
          parentId: category.parentId
        }))
        
        exportData.metadata.totalCategories = categories.length
        totalItems += categories.length
      }
      
      // 导出标签数据
      if (options.includeTags) {
        progressCallback?.(70, '获取标签数据...')
        const tags = await tagService.getTags()
        
        exportData.tags = tags.map(tag => ({
          name: tag.name,
          color: tag.color
        }))
        
        exportData.metadata.totalTags = tags.length
        totalItems += tags.length
      }
      
      progressCallback?.(90, '生成导出文件...')
      const jsonString = JSON.stringify(exportData, null, 2)
      
      progressCallback?.(100, '导出完成')
      
      return {
        success: true,
        data: jsonString,
        filename: `universe_bag_all_data_${this.formatDate(new Date())}.json`,
        format: 'json',
        itemCount: totalItems
      }
      
    } catch (error) {
      console.error('导出全部数据失败:', error)
      throw error
    }
  }

  /**
   * 导出分类数据
   * @param options 导出选项
   * @param progressCallback 进度回调
   * @returns Promise<ExportResult>
   */
  async exportCategories(
    options: ExportCategoriesOptions,
    progressCallback?: ProgressCallback
  ): Promise<ExportResult> {
    try {
      progressCallback?.(0, '开始导出分类数据...')
      
      // 获取分类数据
      progressCallback?.(30, '获取分类数据...')
      let categories = await categoryService.getCategories()
      
      // 按ID筛选
      if (options.categoryIds && options.categoryIds.length > 0) {
        categories = categories.filter(category => 
          options.categoryIds!.includes(category.id)
        )
      }
      
      progressCallback?.(60, '处理分类数据...')
      
      const exportData: ImportData = {
        version: '2.0',
        exportDate: new Date().toISOString(),
        exportType: 'categories',
        metadata: {
          source: 'Universe Bag Extension',
          totalBookmarks: 0,
          totalCategories: categories.length,
          totalTags: 0,
          exportOptions: options
        },
        categories: categories.map(category => {
          const categoryData: any = {
            name: category.name,
            description: category.description,
            color: category.color
          }
          
          if (options.includeHierarchy) {
            categoryData.parentId = category.parentId
          }
          
          if (options.includeStatistics) {
            categoryData.bookmarkCount = category.bookmarkCount
          }
          
          return categoryData
        })
      }
      
      progressCallback?.(90, '生成导出文件...')
      const jsonString = JSON.stringify(exportData, null, 2)
      
      progressCallback?.(100, '导出完成')
      
      return {
        success: true,
        data: jsonString,
        filename: `universe_bag_categories_${this.formatDate(new Date())}.json`,
        format: 'json',
        itemCount: categories.length
      }
      
    } catch (error) {
      console.error('导出分类数据失败:', error)
      throw error
    }
  }

  /**
   * 导出标签数据
   * @param options 导出选项
   * @param progressCallback 进度回调
   * @returns Promise<ExportResult>
   */
  async exportTags(
    options: ExportTagsOptions,
    progressCallback?: ProgressCallback
  ): Promise<ExportResult> {
    try {
      progressCallback?.(0, '开始导出标签数据...')
      
      // 获取标签数据
      progressCallback?.(30, '获取标签数据...')
      let tags = await tagService.getTags()
      
      // 按ID筛选
      if (options.tagIds && options.tagIds.length > 0) {
        tags = tags.filter(tag => 
          options.tagIds!.includes(tag.id)
        )
      }
      
      progressCallback?.(60, '处理标签数据...')
      
      const exportData: ImportData = {
        version: '2.0',
        exportDate: new Date().toISOString(),
        exportType: 'tags',
        metadata: {
          source: 'Universe Bag Extension',
          totalBookmarks: 0,
          totalCategories: 0,
          totalTags: tags.length,
          exportOptions: options
        },
        tags: tags.map(tag => {
          const tagData: any = {
            name: tag.name,
            color: tag.color
          }
          
          if (options.includeUsageStats) {
            tagData.usageCount = tag.usageCount
          }
          
          return tagData
        })
      }
      
      // 如果需要包含相关收藏
      if (options.includeRelatedBookmarks) {
        progressCallback?.(70, '获取相关收藏数据...')
        const bookmarks = await bookmarkService.getBookmarks()
        const relatedBookmarks = bookmarks.filter(bookmark =>
          bookmark.tags.some(tag => tags.some(t => t.name === tag))
        )
        
        exportData.bookmarks = relatedBookmarks.map(bookmark => ({
          type: bookmark.type,
          title: bookmark.title,
          url: bookmark.url,
          description: bookmark.description,
          category: bookmark.category,
          tags: bookmark.tags
        }))
        
        exportData.metadata.totalBookmarks = relatedBookmarks.length
      }
      
      progressCallback?.(90, '生成导出文件...')
      const jsonString = JSON.stringify(exportData, null, 2)
      
      progressCallback?.(100, '导出完成')
      
      return {
        success: true,
        data: jsonString,
        filename: `universe_bag_tags_${this.formatDate(new Date())}.json`,
        format: 'json',
        itemCount: tags.length
      }
      
    } catch (error) {
      console.error('导出标签数据失败:', error)
      throw error
    }
  }

  /**
   * 导出收藏数据（保持向后兼容）
   * @param options 导出选项
   * @param progressCallback 进度回调
   * @returns Promise<ExportResult>
   */
  async exportBookmarks(
    options: ExportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ExportResult> {
    try {
      progressCallback?.(0, '开始导出收藏数据...')
      
      // 获取收藏数据
      progressCallback?.(20, '获取收藏数据...')
      const bookmarks = await this.getBookmarksForExport(options)
      
      if (bookmarks.length === 0) {
        throw new Error('没有找到符合条件的收藏数据')
      }
      
      progressCallback?.(40, `准备导出 ${bookmarks.length} 个收藏...`)
      
      let result: ExportResult
      
      switch (options.format) {
        case 'json':
          result = await this.exportToJSON(bookmarks, options, progressCallback)
          break
        case 'csv':
          result = await this.exportToCSV(bookmarks, options, progressCallback)
          break
        case 'html':
          result = await this.exportToHTML(bookmarks, options, progressCallback)
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }
      
      progressCallback?.(100, '导出完成')
      return result
      
    } catch (error) {
      console.error('导出收藏失败:', error)
      throw error
    }
  }

  /**
   * 导入数据（支持全部数据类型）
   * @param data 导入数据
   * @param options 导入选项
   * @param progressCallback 进度回调
   * @returns Promise<ImportResult & { conflicts?: ConflictDetectionResult }>
   */
  async importData(
    data: string | File,
    options: ImportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ImportResult & { conflicts?: ConflictDetectionResult }> {
    try {
      progressCallback?.(0, '开始导入数据...')
      
      // 解析导入数据
      progressCallback?.(10, '解析导入数据...')
      const parsedData = await this.parseImportDataNew(data, options)
      
      progressCallback?.(30, `解析到数据: 收藏${parsedData.bookmarks?.length || 0}个, 分类${parsedData.categories?.length || 0}个, 标签${parsedData.tags?.length || 0}个`)
      
      // 验证数据
      if (options.validateData) {
        progressCallback?.(40, '验证数据格式...')
        await this.validateImportDataNew(parsedData)
      }
      
      // 检测冲突
      progressCallback?.(50, '检测数据冲突...')
      const conflicts = await conflictResolverService.detectConflicts(parsedData)
      
      if (conflicts.hasConflicts) {
        // 如果有冲突，返回冲突信息让用户处理
        return {
          success: false,
          totalItems: (parsedData.bookmarks?.length || 0) + (parsedData.categories?.length || 0) + (parsedData.tags?.length || 0),
          importedItems: 0,
          skippedItems: 0,
          errorItems: 0,
          errors: [],
          duplicates: [],
          conflicts
        }
      }
      
      // 执行导入
      progressCallback?.(60, '开始导入数据...')
      const result = await this.performImportNew(parsedData, options, progressCallback)
      
      progressCallback?.(100, '导入完成')
      return result
      
    } catch (error) {
      console.error('导入数据失败:', error)
      throw error
    }
  }

  /**
   * 解决冲突后导入数据
   * @param conflicts 冲突列表
   * @param resolutions 解决方案列表
   * @param originalData 原始导入数据
   * @param options 导入选项
   * @param progressCallback 进度回调
   * @returns Promise<ImportResult>
   */
  async importDataWithResolutions(
    conflicts: any[],
    resolutions: ConflictResolution[],
    originalData: ImportData,
    options: ImportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ImportResult> {
    try {
      progressCallback?.(0, '解决冲突中...')
      
      // 解决冲突
      const resolvedData = await conflictResolverService.resolveConflicts(conflicts, resolutions)
      
      // 合并未冲突的数据
      const finalData: ImportData = {
        ...originalData,
        bookmarks: [
          ...(resolvedData.bookmarks || []),
          ...(originalData.bookmarks?.filter(b => 
            !conflicts.some(c => c.type === 'bookmark' && c.importData === b)
          ) || [])
        ],
        categories: [
          ...(resolvedData.categories || []),
          ...(originalData.categories?.filter(c => 
            !conflicts.some(conf => conf.type === 'category' && conf.importData === c)
          ) || [])
        ],
        tags: [
          ...(resolvedData.tags || []),
          ...(originalData.tags?.filter(t => 
            !conflicts.some(conf => conf.type === 'tag' && conf.importData === t)
          ) || [])
        ]
      }
      
      progressCallback?.(50, '执行导入...')
      return await this.performImportNew(finalData, options, progressCallback)
      
    } catch (error) {
      console.error('解决冲突后导入失败:', error)
      throw error
    }
  }

  /**
   * 导入收藏数据（保持向后兼容）
   * @param data 导入数据
   * @param options 导入选项
   * @param progressCallback 进度回调
   * @returns Promise<ImportResult>
   */
  async importBookmarks(
    data: string | File,
    options: ImportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ImportResult> {
    try {
      progressCallback?.(0, '开始导入收藏数据...')
      
      // 解析导入数据
      progressCallback?.(10, '解析导入数据...')
      const parsedData = await this.parseImportData(data, options)
      
      progressCallback?.(30, `解析到 ${parsedData.length} 个收藏项...`)
      
      // 验证数据
      if (options.validateData) {
        progressCallback?.(40, '验证数据格式...')
        await this.validateImportData(parsedData)
      }
      
      // 执行导入
      progressCallback?.(50, '开始导入收藏...')
      const result = await this.performImport(parsedData, options, progressCallback)
      
      progressCallback?.(100, '导入完成')
      return result
      
    } catch (error) {
      console.error('导入收藏失败:', error)
      throw error
    }
  }

  /**
   * 检测重复收藏
   * @param importData 导入数据
   * @returns Promise<string[]> 重复项ID列表
   */
  async detectDuplicates(importData: BookmarkInput[]): Promise<string[]> {
    try {
      const existingBookmarks = await bookmarkService.getBookmarks()
      const duplicates: string[] = []
      
      for (const importItem of importData) {
        if (importItem.url) {
          const existing = existingBookmarks.find(bookmark => 
            bookmark.url === importItem.url
          )
          if (existing) {
            duplicates.push(existing.id)
          }
        }
      }
      
      return duplicates
    } catch (error) {
      console.error('检测重复收藏失败:', error)
      return []
    }
  }

  // ==================== 新的导入解析方法 ====================

  /**
   * 解析新格式的导入数据
   */
  async parseImportDataNew(
    data: string | File,
    options: ImportOptions
  ): Promise<ImportData> {
    let content: string
    
    if (data instanceof File) {
      content = await this.readFileContent(data)
    } else {
      content = data
    }
    
    if (options.source === 'json') {
      try {
        const parsedData = JSON.parse(content)
        
        // 检查是否是新的导出格式
        if (parsedData.version && parsedData.exportType && parsedData.metadata) {
          return parsedData as ImportData
        }
        
        // 兼容旧格式
        if (parsedData.bookmarks && Array.isArray(parsedData.bookmarks)) {
          return {
            version: '1.0',
            exportDate: parsedData.exportDate || new Date().toISOString(),
            exportType: 'bookmarks',
            metadata: {
              source: 'Legacy Format',
              totalBookmarks: parsedData.bookmarks.length,
              totalCategories: 0,
              totalTags: 0,
              exportOptions: {}
            },
            bookmarks: parsedData.bookmarks.map((item: any) => this.convertToBookmarkInput(item))
          }
        }
        
        // 简单数组格式
        if (Array.isArray(parsedData)) {
          return {
            version: '1.0',
            exportDate: new Date().toISOString(),
            exportType: 'bookmarks',
            metadata: {
              source: 'Array Format',
              totalBookmarks: parsedData.length,
              totalCategories: 0,
              totalTags: 0,
              exportOptions: {}
            },
            bookmarks: parsedData.map((item: any) => this.convertToBookmarkInput(item))
          }
        }
        
        throw new Error('无法识别的JSON格式')
      } catch (error) {
        throw new Error(`解析JSON失败: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }
    
    // 对于其他格式，转换为旧的解析方式
    const bookmarks = await this.parseImportData(data, options)
    return {
      version: '1.0',
      exportDate: new Date().toISOString(),
      exportType: 'bookmarks',
      metadata: {
        source: options.source,
        totalBookmarks: bookmarks.length,
        totalCategories: 0,
        totalTags: 0,
        exportOptions: {}
      },
      bookmarks
    }
  }

  /**
   * 验证新格式的导入数据
   */
  private async validateImportDataNew(data: ImportData): Promise<void> {
    const errors: string[] = []
    
    // 验证收藏夹数据
    if (data.bookmarks) {
      for (let i = 0; i < data.bookmarks.length; i++) {
        const item = data.bookmarks[i]
        const validation = ValidationUtils.validateBookmarkInput(item)
        
        if (!validation.isValid) {
          errors.push(`收藏第${i + 1}行: ${validation.errors.map(e => e.message).join(', ')}`)
        }
      }
    }
    
    // 验证分类数据
    if (data.categories) {
      for (let i = 0; i < data.categories.length; i++) {
        const item = data.categories[i]
        if (!item.name || item.name.trim() === '') {
          errors.push(`分类第${i + 1}行: 分类名称不能为空`)
        }
      }
    }
    
    // 验证标签数据
    if (data.tags) {
      for (let i = 0; i < data.tags.length; i++) {
        const item = data.tags[i]
        if (!item.name || item.name.trim() === '') {
          errors.push(`标签第${i + 1}行: 标签名称不能为空`)
        }
      }
    }
    
    if (errors.length > 0) {
      throw new Error(`数据验证失败:\n${errors.join('\n')}`)
    }
  }

  /**
   * 执行新格式的导入
   */
  private async performImportNew(
    data: ImportData,
    options: ImportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      totalItems: (data.bookmarks?.length || 0) + (data.categories?.length || 0) + (data.tags?.length || 0),
      importedItems: 0,
      skippedItems: 0,
      errorItems: 0,
      errors: [],
      duplicates: []
    }
    
    let currentProgress = 60
    const progressStep = 40 / result.totalItems
    
    // 导入分类数据
    if (data.categories) {
      for (let i = 0; i < data.categories.length; i++) {
        const category = data.categories[i]
        currentProgress += progressStep
        progressCallback?.(Math.floor(currentProgress), `导入分类: ${category.name}`)
        
        try {
          // 检查是否已存在同名分类
          const existing = await categoryService.getCategoryByName(category.name)
          if (existing && options.skipDuplicates) {
            result.skippedItems++
            continue
          }
          
          await categoryService.createCategory(category)
          result.importedItems++
        } catch (error) {
          result.errorItems++
          result.errors.push(`分类 ${category.name}: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      }
    }
    
    // 导入标签数据
    if (data.tags) {
      for (let i = 0; i < data.tags.length; i++) {
        const tag = data.tags[i]
        currentProgress += progressStep
        progressCallback?.(Math.floor(currentProgress), `导入标签: ${tag.name}`)
        
        try {
          // 检查是否已存在同名标签
          const existing = await tagService.getTagByName(tag.name)
          if (existing && options.skipDuplicates) {
            result.skippedItems++
            continue
          }
          
          await tagService.createTag(tag)
          result.importedItems++
        } catch (error) {
          result.errorItems++
          result.errors.push(`标签 ${tag.name}: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      }
    }
    
    // 导入收藏夹数据
    if (data.bookmarks) {
      for (let i = 0; i < data.bookmarks.length; i++) {
        const bookmark = data.bookmarks[i]
        currentProgress += progressStep
        progressCallback?.(Math.floor(currentProgress), `导入收藏: ${bookmark.title}`)
        
        try {
          // 检查是否跳过重复项
          if (options.skipDuplicates && bookmark.url) {
            const existing = await bookmarkService.findBookmarkByUrl(bookmark.url)
            if (existing) {
              result.skippedItems++
              continue
            }
          }
          
          // 设置默认分类
          if (options.defaultCategory && (!bookmark.category || bookmark.category === '默认分类')) {
            bookmark.category = options.defaultCategory
          }
          
          await bookmarkService.saveBookmark(bookmark)
          result.importedItems++
        } catch (error) {
          result.errorItems++
          result.errors.push(`收藏 ${bookmark.title}: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      }
    }
    
    return result
  }

  // ==================== 私有方法 ====================

  /**
   * 获取用于导出的收藏数据
   */
  private async getBookmarksForExport(options: ExportOptions): Promise<Bookmark[]> {
    let bookmarks = await bookmarkService.getBookmarks()
    
    // 按分类筛选
    if (options.categories && options.categories.length > 0) {
      bookmarks = bookmarks.filter(bookmark => 
        options.categories!.includes(bookmark.category)
      )
    }
    
    // 按日期范围筛选
    if (options.dateRange) {
      bookmarks = bookmarks.filter(bookmark => {
        const createdAt = new Date(bookmark.createdAt)
        return createdAt >= options.dateRange!.start && 
               createdAt <= options.dateRange!.end
      })
    }
    
    return bookmarks
  }

  /**
   * 导出为JSON格式
   */
  private async exportToJSON(
    bookmarks: Bookmark[],
    options: ExportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ExportResult> {
    progressCallback?.(60, '生成JSON数据...')
    
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      totalCount: bookmarks.length,
      bookmarks: bookmarks.map(bookmark => ({
        id: bookmark.id,
        type: bookmark.type,
        title: bookmark.title,
        url: bookmark.url,
        description: bookmark.description,
        category: bookmark.category,
        tags: bookmark.tags,
        favicon: bookmark.favicon,
        createdAt: bookmark.createdAt,
        updatedAt: bookmark.updatedAt,
        ...(options.includeContent && { content: bookmark.content }),
        ...(options.includeMetadata && { metadata: bookmark.metadata })
      }))
    }
    
    progressCallback?.(80, '格式化JSON数据...')
    const jsonString = JSON.stringify(exportData, null, 2)
    
    return {
      success: true,
      data: jsonString,
      filename: `bookmarks_${this.formatDate(new Date())}.json`,
      format: 'json',
      itemCount: bookmarks.length
    }
  }

  /**
   * 导出为CSV格式
   */
  private async exportToCSV(
    bookmarks: Bookmark[],
    options: ExportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ExportResult> {
    progressCallback?.(60, '生成CSV数据...')
    
    // CSV头部
    const headers = [
      'ID', '类型', '标题', 'URL', '描述', '分类', '标签', 
      '创建时间', '更新时间'
    ]
    
    if (options.includeContent) {
      headers.push('内容')
    }
    
    // 生成CSV行
    const rows = bookmarks.map(bookmark => {
      const row = [
        bookmark.id,
        bookmark.type,
        this.escapeCsvValue(bookmark.title),
        bookmark.url || '',
        this.escapeCsvValue(bookmark.description || ''),
        bookmark.category,
        bookmark.tags.join(';'),
        bookmark.createdAt.toISOString(),
        bookmark.updatedAt.toISOString()
      ]
      
      if (options.includeContent) {
        row.push(this.escapeCsvValue(bookmark.content || ''))
      }
      
      return row
    })
    
    progressCallback?.(80, '格式化CSV数据...')
    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n')
    
    return {
      success: true,
      data: csvContent,
      filename: `bookmarks_${this.formatDate(new Date())}.csv`,
      format: 'csv',
      itemCount: bookmarks.length
    }
  }

  /**
   * 导出为HTML格式
   */
  private async exportToHTML(
    bookmarks: Bookmark[],
    options: ExportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ExportResult> {
    progressCallback?.(60, '生成HTML数据...')
    
    // 按分类分组
    const bookmarksByCategory = bookmarks.reduce((acc, bookmark) => {
      const category = bookmark.category || '未分类'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(bookmark)
      return acc
    }, {} as Record<string, Bookmark[]>)
    
    progressCallback?.(70, '生成HTML结构...')
    
    let html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏导出 - ${this.formatDate(new Date())}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; }
        .header { border-bottom: 2px solid #e5e7eb; padding-bottom: 20px; margin-bottom: 30px; }
        .category { margin-bottom: 30px; }
        .category-title { font-size: 1.5em; font-weight: bold; color: #374151; margin-bottom: 15px; }
        .bookmark { margin-bottom: 15px; padding: 15px; border: 1px solid #e5e7eb; border-radius: 8px; }
        .bookmark-title { font-weight: bold; color: #1f2937; margin-bottom: 5px; }
        .bookmark-url { color: #3b82f6; text-decoration: none; font-size: 0.9em; }
        .bookmark-url:hover { text-decoration: underline; }
        .bookmark-description { color: #6b7280; margin-top: 8px; font-size: 0.9em; }
        .bookmark-meta { color: #9ca3af; font-size: 0.8em; margin-top: 8px; }
        .tags { margin-top: 8px; }
        .tag { background: #f3f4f6; color: #374151; padding: 2px 8px; border-radius: 4px; font-size: 0.8em; margin-right: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>收藏导出</h1>
        <p>导出时间: ${new Date().toLocaleString()}</p>
        <p>总计: ${bookmarks.length} 个收藏</p>
    </div>
`
    
    // 生成分类内容
    for (const [category, categoryBookmarks] of Object.entries(bookmarksByCategory)) {
      html += `    <div class="category">
        <h2 class="category-title">${category} (${categoryBookmarks.length})</h2>
`
      
      for (const bookmark of categoryBookmarks) {
        html += `        <div class="bookmark">
            <div class="bookmark-title">${this.escapeHtml(bookmark.title)}</div>
`
        
        if (bookmark.url) {
          html += `            <a href="${bookmark.url}" class="bookmark-url" target="_blank">${this.escapeHtml(bookmark.url)}</a>
`
        }
        
        if (bookmark.description) {
          html += `            <div class="bookmark-description">${this.escapeHtml(bookmark.description)}</div>
`
        }
        
        if (options.includeContent && bookmark.content) {
          html += `            <div class="bookmark-description"><strong>内容:</strong> ${this.escapeHtml(bookmark.content)}</div>
`
        }
        
        if (bookmark.tags.length > 0) {
          html += `            <div class="tags">
                ${bookmark.tags.map(tag => `<span class="tag">${this.escapeHtml(tag)}</span>`).join('')}
            </div>
`
        }
        
        html += `            <div class="bookmark-meta">创建时间: ${bookmark.createdAt.toLocaleString()}</div>
        </div>
`
      }
      
      html += `    </div>
`
    }
    
    html += `</body>
</html>`
    
    progressCallback?.(90, '完成HTML生成...')
    
    return {
      success: true,
      data: html,
      filename: `bookmarks_${this.formatDate(new Date())}.html`,
      format: 'html',
      itemCount: bookmarks.length
    }
  }

  /**
   * 解析导入数据
   */
  private async parseImportData(
    data: string | File,
    options: ImportOptions
  ): Promise<BookmarkInput[]> {
    let content: string
    
    if (data instanceof File) {
      content = await this.readFileContent(data)
    } else {
      content = data
    }
    
    switch (options.source) {
      case 'json':
        return this.parseJSONImport(content)
      case 'csv':
        return this.parseCSVImport(content)
      case 'html':
        return this.parseHTMLImport(content)
      case 'chrome':
      case 'firefox':
      case 'edge':
        return this.parseBrowserBookmarks(content, options.source)
      default:
        throw new Error(`不支持的导入来源: ${options.source}`)
    }
  }

  /**
   * 解析JSON导入
   */
  private parseJSONImport(content: string): BookmarkInput[] {
    try {
      const data = JSON.parse(content)
      
      // 检查是否是我们的导出格式
      if (data.bookmarks && Array.isArray(data.bookmarks)) {
        return data.bookmarks.map((item: any) => this.convertToBookmarkInput(item))
      }
      
      // 检查是否是简单的数组格式
      if (Array.isArray(data)) {
        return data.map((item: any) => this.convertToBookmarkInput(item))
      }
      
      throw new Error('无效的JSON格式')
    } catch (error) {
      throw new Error(`解析JSON失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 解析CSV导入
   */
  private parseCSVImport(content: string): BookmarkInput[] {
    try {
      const lines = content.split('\n').filter(line => line.trim())
      if (lines.length < 2) {
        throw new Error('CSV文件格式无效')
      }
      
      const headers = lines[0].split(',').map(h => h.trim())
      const bookmarks: BookmarkInput[] = []
      
      for (let i = 1; i < lines.length; i++) {
        const values = this.parseCSVLine(lines[i])
        if (values.length >= headers.length) {
          const bookmark = this.convertCSVRowToBookmarkInput(headers, values)
          if (bookmark) {
            bookmarks.push(bookmark)
          }
        }
      }
      
      return bookmarks
    } catch (error) {
      throw new Error(`解析CSV失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 解析HTML导入
   */
  private parseHTMLImport(content: string): BookmarkInput[] {
    try {
      const parser = new DOMParser()
      const doc = parser.parseFromString(content, 'text/html')
      const links = doc.querySelectorAll('a[href]')
      
      const bookmarks: BookmarkInput[] = []
      
      links.forEach(link => {
        const href = link.getAttribute('href')
        const title = link.textContent?.trim()
        
        if (href && title) {
          bookmarks.push({
            type: 'url',
            title,
            url: href,
            category: '导入分类',
            tags: []
          })
        }
      })
      
      return bookmarks
    } catch (error) {
      throw new Error(`解析HTML失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 解析浏览器书签
   */
  private parseBrowserBookmarks(content: string, browser: string): BookmarkInput[] {
    // 这里实现浏览器书签的解析逻辑
    // 不同浏览器的书签格式可能不同
    try {
      const parser = new DOMParser()
      const doc = parser.parseFromString(content, 'text/html')
      const bookmarks: BookmarkInput[] = []
      
      // 查找书签链接
      const links = doc.querySelectorAll('a[href]')
      
      links.forEach(link => {
        const href = link.getAttribute('href')
        const title = link.textContent?.trim()
        
        if (href && title && href.startsWith('http')) {
          bookmarks.push({
            type: 'url',
            title,
            url: href,
            category: `${browser}导入`,
            tags: []
          })
        }
      })
      
      return bookmarks
    } catch (error) {
      throw new Error(`解析${browser}书签失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 验证导入数据
   */
  private async validateImportData(data: BookmarkInput[]): Promise<void> {
    const errors: string[] = []
    
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      const validation = ValidationUtils.validateBookmarkInput(item)
      
      if (!validation.isValid) {
        errors.push(`第${i + 1}行: ${validation.errors.map(e => e.message).join(', ')}`)
      }
    }
    
    if (errors.length > 0) {
      throw new Error(`数据验证失败:\n${errors.join('\n')}`)
    }
  }

  /**
   * 执行导入
   */
  private async performImport(
    data: BookmarkInput[],
    options: ImportOptions,
    progressCallback?: ProgressCallback
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      totalItems: data.length,
      importedItems: 0,
      skippedItems: 0,
      errorItems: 0,
      errors: [],
      duplicates: []
    }
    
    // 检测重复项
    if (options.skipDuplicates) {
      result.duplicates = await this.detectDuplicates(data)
    }
    
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      const progress = Math.floor((i / data.length) * 50) + 50 // 50-100%
      
      progressCallback?.(progress, `导入第 ${i + 1}/${data.length} 个收藏...`)
      
      try {
        // 检查是否跳过重复项
        if (options.skipDuplicates && item.url) {
          const existing = await bookmarkService.findBookmarkByUrl(item.url)
          if (existing) {
            result.skippedItems++
            continue
          }
        }
        
        // 设置默认分类
        if (options.defaultCategory && (!item.category || item.category === '默认分类')) {
          item.category = options.defaultCategory
        }
        
        // 保存收藏
        await bookmarkService.saveBookmark(item)
        result.importedItems++
        
      } catch (error) {
        result.errorItems++
        result.errors.push(`第${i + 1}行: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }
    
    return result
  }

  // ==================== 工具方法 ====================

  /**
   * 读取文件内容
   */
  private readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('读取文件失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  /**
   * 转换为BookmarkInput格式
   */
  private convertToBookmarkInput(item: any): BookmarkInput {
    return {
      type: item.type || 'url',
      title: item.title || '无标题',
      url: item.url,
      description: item.description,
      content: item.content,
      category: item.category || '默认分类',
      tags: Array.isArray(item.tags) ? item.tags : [],
      favicon: item.favicon,
      metadata: item.metadata
    }
  }

  /**
   * 转换CSV行为BookmarkInput
   */
  private convertCSVRowToBookmarkInput(headers: string[], values: string[]): BookmarkInput | null {
    try {
      const getValueByHeader = (header: string) => {
        const index = headers.findIndex(h => h.toLowerCase().includes(header.toLowerCase()))
        return index >= 0 ? values[index]?.trim() : ''
      }
      
      const title = getValueByHeader('title') || getValueByHeader('标题')
      const url = getValueByHeader('url') || getValueByHeader('链接')
      
      if (!title && !url) {
        return null
      }
      
      return {
        type: 'url',
        title: title || '无标题',
        url: url,
        description: getValueByHeader('description') || getValueByHeader('描述'),
        category: getValueByHeader('category') || getValueByHeader('分类') || '导入分类',
        tags: (getValueByHeader('tags') || getValueByHeader('标签')).split(';').filter(Boolean)
      }
    } catch (error) {
      return null
    }
  }

  /**
   * 解析CSV行
   */
  private parseCSVLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  /**
   * 转义CSV值
   */
  private escapeCsvValue(value: string): string {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`
    }
    return value
  }

  /**
   * 转义HTML
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 格式化日期
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0].replace(/-/g, '')
  }
}

// 导出单例实例
export const importExportManagerService = new ImportExportManagerService()

// 保持向后兼容性
export const bookmarkImportExportService = importExportManagerService