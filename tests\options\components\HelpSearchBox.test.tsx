/**
 * HelpSearchBox 组件单元测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import HelpSearchBox from '../../../src/options/components/HelpSearchBox'
import type { HelpSection } from '../../../src/options/data/helpContent'

// 模拟 localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

// 测试数据
const mockSections: HelpSection[] = [
  {
    id: 'test-1',
    title: '如何添加收藏',
    content: '点击扩展图标，然后点击添加收藏按钮',
    category: 'guide',
    keywords: ['添加', '收藏', '扩展']
  },
  {
    id: 'test-2',
    title: '搜索功能使用',
    content: '在搜索框中输入关键词进行搜索',
    category: 'guide',
    keywords: ['搜索', '关键词', '查找']
  },
  {
    id: 'test-3',
    title: '常见问题解答',
    content: '这里是一些常见问题的解答',
    category: 'faq',
    keywords: ['问题', '解答', 'FAQ']
  }
]

describe('HelpSearchBox', () => {
  const mockOnSearchResults = vi.fn()
  const mockOnQueryChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(mockLocalStorage.getItem).mockReturnValue(null)
  })

  const renderComponent = (props = {}) => {
    return render(
      <HelpSearchBox
        sections={mockSections}
        onSearchResults={mockOnSearchResults}
        onQueryChange={mockOnQueryChange}
        {...props}
      />
    )
  }

  it('应该正确渲染搜索框', () => {
    renderComponent()
    
    expect(screen.getByPlaceholderText('搜索帮助内容...')).toBeInTheDocument()
    expect(screen.getByRole('textbox')).toBeInTheDocument()
  })

  it('应该处理输入变化', async () => {
    const user = userEvent.setup()
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.type(input, '收藏')
    
    expect(mockOnQueryChange).toHaveBeenCalledWith('收藏')
  })

  it('应该在输入时显示搜索建议', async () => {
    const user = userEvent.setup()
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.type(input, '收藏')
    
    // 等待防抖完成
    await waitFor(() => {
      expect(mockOnSearchResults).toHaveBeenCalled()
    })
  })

  it('应该显示清除按钮当有输入时', async () => {
    const user = userEvent.setup()
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.type(input, '测试')
    
    expect(screen.getByTitle('清除搜索')).toBeInTheDocument()
  })

  it('应该清除搜索当点击清除按钮', async () => {
    const user = userEvent.setup()
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.type(input, '测试')
    
    const clearButton = screen.getByTitle('清除搜索')
    await user.click(clearButton)
    
    expect(input).toHaveValue('')
    expect(mockOnQueryChange).toHaveBeenCalledWith('')
  })

  it('应该在按下 Enter 时执行搜索', async () => {
    const user = userEvent.setup()
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.type(input, '收藏')
    await user.keyboard('{Enter}')
    
    // 验证搜索历史被保存
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'help-search-history',
      JSON.stringify(['收藏'])
    )
  })

  it('应该显示搜索历史', async () => {
    const user = userEvent.setup()
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(['历史搜索1', '历史搜索2']))
    
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.click(input)
    
    expect(screen.getByText('搜索历史')).toBeInTheDocument()
    expect(screen.getByText('历史搜索1')).toBeInTheDocument()
    expect(screen.getByText('历史搜索2')).toBeInTheDocument()
  })

  it('应该处理搜索历史点击', async () => {
    const user = userEvent.setup()
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(['历史搜索']))
    
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.click(input)
    
    const historyItem = screen.getByText('历史搜索')
    await user.click(historyItem)
    
    expect(mockOnQueryChange).toHaveBeenCalledWith('历史搜索')
  })

  it('应该清除搜索历史', async () => {
    const user = userEvent.setup()
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(['历史搜索']))
    
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.click(input)
    
    const clearHistoryButton = screen.getByText('清除搜索历史')
    await user.click(clearHistoryButton)
    
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('help-search-history')
  })

  it('应该显示加载状态', async () => {
    const user = userEvent.setup()
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.type(input, '搜索')
    
    // 在防抖期间应该显示加载状态
    expect(screen.getByTitle('清除搜索')).toBeInTheDocument()
  })

  it('应该处理自定义占位符', () => {
    renderComponent({ placeholder: '自定义占位符' })
    
    expect(screen.getByPlaceholderText('自定义占位符')).toBeInTheDocument()
  })

  it('应该应用自定义样式类', () => {
    const { container } = renderComponent({ className: 'custom-class' })
    
    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('应该在按下 Escape 时关闭建议', async () => {
    const user = userEvent.setup()
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(['历史搜索']))
    
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.click(input)
    
    expect(screen.getByText('搜索历史')).toBeInTheDocument()
    
    await user.keyboard('{Escape}')
    
    expect(screen.queryByText('搜索历史')).not.toBeInTheDocument()
  })

  it('应该处理 localStorage 错误', () => {
    mockLocalStorage.getItem.mockImplementation(() => {
      throw new Error('localStorage error')
    })
    
    // 应该不会抛出错误
    expect(() => renderComponent()).not.toThrow()
  })

  it('应该限制搜索历史数量', async () => {
    const user = userEvent.setup()
    const longHistory = Array.from({ length: 15 }, (_, i) => `搜索${i}`)
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(longHistory))
    
    renderComponent()
    
    const input = screen.getByRole('textbox')
    await user.type(input, '新搜索')
    await user.keyboard('{Enter}')
    
    // 验证历史记录被限制在10个以内
    const savedHistory = JSON.parse(mockLocalStorage.setItem.mock.calls[0][1])
    expect(savedHistory).toHaveLength(10)
    expect(savedHistory[0]).toBe('新搜索')
  })
})