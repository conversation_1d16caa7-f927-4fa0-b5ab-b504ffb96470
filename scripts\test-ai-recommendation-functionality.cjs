/**
 * AI推荐功能测试脚本
 * 测试AI推荐功能的实际运行效果
 */

const fs = require('fs')
const path = require('path')

// 测试配置
const TEST_CONFIG = {
  colors: {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
  },
  icons: {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    test: '🧪',
    rocket: '🚀',
    gear: '⚙️'
  }
}

/**
 * 彩色日志输出
 */
function log(message, color = 'reset') {
  console.log(`${TEST_CONFIG.colors[color]}${message}${TEST_CONFIG.colors.reset}`)
}

/**
 * 检查构建产物中的AI功能集成
 */
function checkBuildAIIntegration() {
  log(`\n${TEST_CONFIG.icons.gear} 检查构建产物中的AI功能集成`, 'cyan')
  
  const backgroundPath = path.join(__dirname, '../dist/src/background/index.js')
  
  if (!fs.existsSync(backgroundPath)) {
    log(`${TEST_CONFIG.icons.error} 构建产物不存在: ${backgroundPath}`, 'red')
    return false
  }
  
  const backgroundContent = fs.readFileSync(backgroundPath, 'utf8')
  
  // 检查AI相关的消息处理器
  const aiHandlers = [
    'AI_GENERATE_DESCRIPTION',
    'AI_RECOMMEND_TAGS',
    'AI_RECOMMEND_FOLDERS',
    'AI_RECOMMEND_BOTH',
    'AI_GENERATE_TAGS',
    'AI_GENERATE_CATEGORY'
  ]
  
  let foundHandlers = 0
  aiHandlers.forEach(handler => {
    if (backgroundContent.includes(handler)) {
      foundHandlers++
      log(`${TEST_CONFIG.icons.success} 找到处理器: ${handler}`, 'green')
    } else {
      log(`${TEST_CONFIG.icons.warning} 未找到处理器: ${handler}`, 'yellow')
    }
  })
  
  log(`\n📊 AI处理器集成度: ${foundHandlers}/${aiHandlers.length} (${((foundHandlers/aiHandlers.length)*100).toFixed(1)}%)`, 
      foundHandlers === aiHandlers.length ? 'green' : 'yellow')
  
  return foundHandlers >= aiHandlers.length * 0.8 // 至少80%的处理器存在
}

/**
 * 检查Options页面的AI推荐集成
 */
function checkOptionsPageAIIntegration() {
  log(`\n${TEST_CONFIG.icons.gear} 检查Options页面的AI推荐集成`, 'cyan')
  
  // 查找包含AIRecommendations的构建文件
  const assetsDir = path.join(__dirname, '../dist/assets')
  
  if (!fs.existsSync(assetsDir)) {
    log(`${TEST_CONFIG.icons.error} assets目录不存在`, 'red')
    return false
  }
  
  const files = fs.readdirSync(assetsDir)
  const jsFiles = files.filter(f => f.endsWith('.js'))
  
  let foundAIRecommendations = false
  let foundDescriptionGeneration = false
  
  jsFiles.forEach(file => {
    const filePath = path.join(assetsDir, file)
    const content = fs.readFileSync(filePath, 'utf8')
    
    if (content.includes('AIRecommendations') || content.includes('AI推荐')) {
      foundAIRecommendations = true
      log(`${TEST_CONFIG.icons.success} 在 ${file} 中找到AI推荐组件`, 'green')
    }
    
    if (content.includes('generateDescription') || content.includes('描述生成')) {
      foundDescriptionGeneration = true
      log(`${TEST_CONFIG.icons.success} 在 ${file} 中找到描述生成功能`, 'green')
    }
  })
  
  if (!foundAIRecommendations) {
    log(`${TEST_CONFIG.icons.warning} 未在构建产物中找到AI推荐组件`, 'yellow')
  }
  
  if (!foundDescriptionGeneration) {
    log(`${TEST_CONFIG.icons.warning} 未在构建产物中找到描述生成功能`, 'yellow')
  }
  
  return foundAIRecommendations && foundDescriptionGeneration
}

/**
 * 生成测试报告
 */
function generateTestReport() {
  log(`\n${TEST_CONFIG.icons.rocket} 生成AI推荐功能测试报告`, 'cyan')
  
  const report = {
    timestamp: new Date().toISOString(),
    testResults: {
      buildIntegration: checkBuildAIIntegration(),
      optionsPageIntegration: checkOptionsPageAIIntegration()
    },
    recommendations: []
  }
  
  // 生成建议
  if (!report.testResults.buildIntegration) {
    report.recommendations.push('检查background script的AI处理器集成')
  }
  
  if (!report.testResults.optionsPageIntegration) {
    report.recommendations.push('检查Options页面的AI组件构建')
  }
  
  // 保存报告
  const reportPath = path.join(__dirname, '../docs/ai-recommendation-test-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  
  log(`\n📋 测试报告已保存: ${reportPath}`, 'blue')
  
  return report
}

/**
 * 输出使用指南
 */
function outputUsageGuide() {
  log(`\n${TEST_CONFIG.icons.info} AI推荐功能使用指南`, 'bright')
  log('=' * 50, 'cyan')
  
  log('\n📍 1. 加载扩展:', 'yellow')
  log('   - 打开Chrome浏览器')
  log('   - 访问 chrome://extensions/')
  log('   - 开启"开发者模式"')
  log('   - 点击"加载已解压的扩展程序"')
  log('   - 选择项目的 dist 文件夹')
  
  log('\n📍 2. 测试智能推荐:', 'yellow')
  log('   - 访问收藏管理页面: chrome-extension://[extension-id]/src/options/index.html#bookmarks')
  log('   - 点击"添加收藏"按钮')
  log('   - 输入标题和URL')
  log('   - 点击"智能推荐"按钮')
  log('   - 查看AI生成的标签、分类和描述推荐')
  
  log('\n📍 3. 测试单独生成按钮:', 'yellow')
  log('   - 在收藏表单中，每个字段都有独立的AI生成按钮')
  log('   - 标签字段: 点击"AI生成"按钮生成标签')
  log('   - 分类字段: 点击"AI生成"按钮生成分类')
  log('   - 描述字段: 使用AITextGenerator组件的内置生成功能')
  
  log('\n📍 4. 验证真实AI调用:', 'yellow')
  log('   - 确保在"默认AI模型"页面配置了可用的AI模型')
  log('   - 测试时观察控制台日志，确认调用的是真实AI API而非演示数据')
  log('   - 生成的内容应该与输入的标题和URL相关，而不是固定内容')
  
  log('\n📍 5. 故障排除:', 'yellow')
  log('   - 如果AI功能不工作，检查默认AI模型配置')
  log('   - 查看浏览器控制台的错误信息')
  log('   - 确认网络连接正常（如果使用云端AI服务）')
  
  log('\n' + '=' * 50, 'cyan')
}

/**
 * 主测试函数
 */
function runFunctionalityTests() {
  log(`\n${TEST_CONFIG.icons.rocket} AI推荐功能测试开始`, 'bright')
  
  const report = generateTestReport()
  
  // 输出测试结果
  log(`\n📊 功能测试结果:`, 'bright')
  log(`   构建集成: ${report.testResults.buildIntegration ? '✅ 通过' : '❌ 失败'}`, 
      report.testResults.buildIntegration ? 'green' : 'red')
  log(`   页面集成: ${report.testResults.optionsPageIntegration ? '✅ 通过' : '❌ 失败'}`, 
      report.testResults.optionsPageIntegration ? 'green' : 'red')
  
  const allPassed = report.testResults.buildIntegration && report.testResults.optionsPageIntegration
  
  if (allPassed) {
    log(`\n${TEST_CONFIG.icons.success} 所有功能测试通过！`, 'green')
    log('AI推荐功能已正确集成到构建产物中。', 'green')
  } else {
    log(`\n${TEST_CONFIG.icons.warning} 部分功能测试未通过`, 'yellow')
    if (report.recommendations.length > 0) {
      log('\n建议检查:', 'yellow')
      report.recommendations.forEach(rec => {
        log(`   - ${rec}`, 'yellow')
      })
    }
  }
  
  outputUsageGuide()
  
  return allPassed
}

// 执行测试
if (require.main === module) {
  runFunctionalityTests()
}

module.exports = {
  runFunctionalityTests,
  checkBuildAIIntegration,
  checkOptionsPageAIIntegration,
  generateTestReport
}
