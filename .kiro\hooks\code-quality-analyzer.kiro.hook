{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices. Generates suggestions for improving code quality while maintaining existing functionality.", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.js", "src/**/*.ts", "src/**/*.jsx", "src/**/*.tsx", "src/**/*.vue", "scripts/**/*.js", "scripts/**/*.cjs", "tests/**/*.js", "tests/**/*.ts", "tests/**/*.tsx", "*.js", "*.ts", "*.cjs"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code files for potential improvements. Focus on:\n\n1. **Code Smells Detection**: Identify long methods, large classes, duplicate code, complex conditionals, and other code smells\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Check for adherence to JavaScript/TypeScript best practices, proper error handling, and clean code principles\n4. **Performance Optimizations**: Identify potential performance bottlenecks and suggest optimizations\n5. **Readability & Maintainability**: Suggest improvements for code clarity, naming conventions, and documentation\n6. **Security Considerations**: Flag potential security issues or vulnerabilities\n\nFor each suggestion:\n- Explain WHY the change would be beneficial\n- Provide specific code examples when possible\n- Ensure suggestions maintain existing functionality\n- Prioritize suggestions by impact (high/medium/low)\n- Consider the existing codebase patterns and maintain consistency\n\nGenerate your analysis in Chinese as per the project requirements, and focus only on the modified files to avoid overwhelming feedback."}}