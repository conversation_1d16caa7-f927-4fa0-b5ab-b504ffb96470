// 智能标签输入组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import SmartTagInput from '../src/components/SmartTagInput'
import { tagService } from '../src/services/tagService'

// Mock tagService
vi.mock('../src/services/tagService', () => ({
  tagService: {
    getTags: vi.fn(),
    getTagByName: vi.fn(),
    createTag: vi.fn()
  }
}))

// Mock UI components
vi.mock('@/components/ui/input', () => ({
  Input: ({ ...props }) => <input {...props} />
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }) => <button {...props}>{children}</button>
}))

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }) => <span {...props}>{children}</span>
}))

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }) => <div {...props}>{children}</div>
}))

vi.mock('@/components/ui/separator', () => ({
  Separator: (props) => <hr {...props} />
}))

const mockTags = [
  { id: '1', name: '技术', usageCount: 10, color: '#blue', createdAt: new Date(), updatedAt: new Date() },
  { id: '2', name: '学习', usageCount: 5, color: '#green', createdAt: new Date(), updatedAt: new Date() },
  { id: '3', name: '工具', usageCount: 8, color: '#red', createdAt: new Date(), updatedAt: new Date() }
]

describe('SmartTagInput', () => {
  const defaultProps = {
    recommendedExistingTags: ['技术', '学习'],
    recommendedNewTags: ['AI', '机器学习'],
    selectedTags: [],
    onTagSelect: vi.fn(),
    onTagDeselect: vi.fn(),
    onCreateTag: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(tagService.getTags).mockResolvedValue(mockTags)
  })

  it('应该正确渲染组件', async () => {
    render(<SmartTagInput {...defaultProps} />)
    
    expect(screen.getByText('推荐标签')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('输入标签名称...')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('推荐现有标签')).toBeInTheDocument()
      expect(screen.getByText('新标签建议')).toBeInTheDocument()
    })
  })

  it('应该显示推荐的现有标签', async () => {
    render(<SmartTagInput {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('技术')).toBeInTheDocument()
      expect(screen.getByText('学习')).toBeInTheDocument()
    })
  })

  it('应该显示推荐的新标签', async () => {
    render(<SmartTagInput {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('AI')).toBeInTheDocument()
      expect(screen.getByText('机器学习')).toBeInTheDocument()
    })
  })

  it('应该处理标签输入和添加', async () => {
    const onTagSelect = vi.fn()
    render(<SmartTagInput {...defaultProps} onTagSelect={onTagSelect} />)
    
    const input = screen.getByPlaceholderText('输入标签名称...')
    const addButton = screen.getByRole('button')
    
    fireEvent.change(input, { target: { value: '新标签' } })
    fireEvent.click(addButton)
    
    await waitFor(() => {
      expect(defaultProps.onCreateTag).toHaveBeenCalledWith('新标签')
    })
  })

  it('应该在输入时显示下拉选择框', async () => {
    render(<SmartTagInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('输入标签名称...')
    
    await waitFor(() => {
      // 等待标签加载完成
      expect(tagService.getTags).toHaveBeenCalled()
    })
    
    fireEvent.change(input, { target: { value: '技' } })
    fireEvent.focus(input)
    
    await waitFor(() => {
      // 应该显示匹配的标签
      const dropdown = document.querySelector('.absolute.top-full')
      expect(dropdown).toBeInTheDocument()
    })
  })

  it('应该处理推荐标签点击', async () => {
    const onTagSelect = vi.fn()
    render(<SmartTagInput {...defaultProps} onTagSelect={onTagSelect} />)
    
    await waitFor(() => {
      const techTag = screen.getByText('技术')
      fireEvent.click(techTag)
      expect(onTagSelect).toHaveBeenCalledWith('技术')
    })
  })

  it('应该显示置信度信息', async () => {
    render(<SmartTagInput {...defaultProps} confidence={0.8} />)
    
    await waitFor(() => {
      expect(screen.getByText('置信度: 高')).toBeInTheDocument()
    })
  })

  it('应该显示错误信息', async () => {
    render(<SmartTagInput {...defaultProps} error="测试错误信息" />)
    
    expect(screen.getByText('测试错误信息')).toBeInTheDocument()
  })

  it('应该显示成功信息', async () => {
    render(<SmartTagInput {...defaultProps} success="标签创建成功" />)
    
    expect(screen.getByText('标签创建成功')).toBeInTheDocument()
  })

  it('应该在禁用状态下不响应交互', async () => {
    const onTagSelect = vi.fn()
    render(<SmartTagInput {...defaultProps} disabled={true} onTagSelect={onTagSelect} />)
    
    const input = screen.getByPlaceholderText('输入标签名称...')
    expect(input).toBeDisabled()
    
    await waitFor(() => {
      const techTag = screen.getByText('技术')
      fireEvent.click(techTag)
      expect(onTagSelect).not.toHaveBeenCalled()
    })
  })

  it('应该处理键盘事件', async () => {
    const onCreateTag = vi.fn()
    render(<SmartTagInput {...defaultProps} onCreateTag={onCreateTag} />)
    
    const input = screen.getByPlaceholderText('输入标签名称...')
    
    fireEvent.change(input, { target: { value: '新标签' } })
    fireEvent.keyDown(input, { key: 'Enter' })
    
    await waitFor(() => {
      expect(onCreateTag).toHaveBeenCalledWith('新标签')
    })
  })

  it('应该处理Escape键关闭下拉框', async () => {
    render(<SmartTagInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('输入标签名称...')
    
    fireEvent.change(input, { target: { value: '技' } })
    fireEvent.keyDown(input, { key: 'Escape' })
    
    expect(input.value).toBe('')
  })

  it('应该显示推荐理由', async () => {
    render(<SmartTagInput {...defaultProps} reasoning="基于内容分析推荐" />)
    
    await waitFor(() => {
      expect(screen.getByText('基于内容分析推荐')).toBeInTheDocument()
    })
  })

  it('应该处理已选中标签的显示', async () => {
    render(<SmartTagInput {...defaultProps} selectedTags={['技术']} />)

    await waitFor(() => {
      // 应该显示已选择标签区域
      expect(screen.getByText('已选择标签')).toBeInTheDocument()
      // 已选中的标签应该只显示在已选择区域，不在推荐区域
      const techTags = screen.getAllByText('技术')
      expect(techTags).toHaveLength(1) // 只在已选择区域显示一次

      // 检查包含标签的Badge元素
      const badgeElement = techTags[0].closest('span[variant="default"]')
      expect(badgeElement).toHaveClass('cursor-pointer')
    })
  })

  it('应该显示已选择标签的删除按钮', async () => {
    const onTagDeselect = vi.fn()
    render(<SmartTagInput {...defaultProps} selectedTags={['技术', '学习']} onTagDeselect={onTagDeselect} />)

    await waitFor(() => {
      // 应该显示已选择标签区域
      expect(screen.getByText('已选择标签')).toBeInTheDocument()

      // 每个标签都应该有删除按钮
      const deleteButtons = screen.getAllByLabelText(/删除标签/)
      expect(deleteButtons).toHaveLength(2)

      // 点击删除按钮应该调用onTagDeselect
      fireEvent.click(deleteButtons[0])
      expect(onTagDeselect).toHaveBeenCalledWith('技术')
    })
  })

  it('应该在没有选中标签时不显示已选择区域', async () => {
    render(<SmartTagInput {...defaultProps} selectedTags={[]} />)

    await waitFor(() => {
      // 不应该显示已选择标签区域
      expect(screen.queryByText('已选择标签')).not.toBeInTheDocument()
    })
  })
})
