/**
 * 测试Content Script消息处理修复
 * 验证GET_SELECTED_TEXT消息类型是否正确处理
 */

// 模拟Chrome扩展API
const mockChrome = {
  runtime: {
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    sendMessage: jest.fn()
  },
  tabs: {
    sendMessage: jest.fn()
  },
  contextMenus: {
    create: jest.fn(),
    removeAll: jest.fn(),
    onClicked: {
      addListener: jest.fn()
    }
  }
}

global.chrome = mockChrome

// 测试Content Script消息处理
describe('Content Script 消息处理修复', () => {
  let messageHandler
  
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks()
    
    // 模拟window.getSelection
    global.window = {
      getSelection: jest.fn(() => ({
        toString: () => '测试选中文字'
      })),
      location: {
        href: 'https://example.com',
        protocol: 'https:',
        host: 'example.com'
      }
    }
    
    global.document = {
      readyState: 'complete',
      title: '测试页面',
      addEventListener: jest.fn()
    }
    
    global.console = {
      log: jest.fn(),
      error: jest.fn()
    }
  })

  test('应该正确处理GET_SELECTED_TEXT消息', async () => {
    // 模拟handleContentMessage函数
    const handleContentMessage = async (message) => {
      switch (message.type) {
        case 'GET_SELECTED_TEXT':
          const selection = window.getSelection()
          const selectedText = selection ? selection.toString().trim() : ''
          return { selectedText }
        default:
          throw new Error(`未知的消息类型: ${message.type}`)
      }
    }

    // 测试GET_SELECTED_TEXT消息
    const message = { type: 'GET_SELECTED_TEXT' }
    const response = await handleContentMessage(message)
    
    expect(response).toEqual({ selectedText: '测试选中文字' })
    expect(window.getSelection).toHaveBeenCalled()
  })

  test('应该正确处理空选中文字', async () => {
    // 模拟空选中
    window.getSelection = jest.fn(() => ({
      toString: () => ''
    }))

    const handleContentMessage = async (message) => {
      switch (message.type) {
        case 'GET_SELECTED_TEXT':
          const selection = window.getSelection()
          const selectedText = selection ? selection.toString().trim() : ''
          return { selectedText }
        default:
          throw new Error(`未知的消息类型: ${message.type}`)
      }
    }

    const message = { type: 'GET_SELECTED_TEXT' }
    const response = await handleContentMessage(message)
    
    expect(response).toEqual({ selectedText: '' })
  })

  test('应该正确处理getSelection异常', async () => {
    // 模拟getSelection抛出异常
    window.getSelection = jest.fn(() => {
      throw new Error('Selection API不可用')
    })

    const handleContentMessage = async (message) => {
      switch (message.type) {
        case 'GET_SELECTED_TEXT':
          try {
            const selection = window.getSelection()
            const selectedText = selection ? selection.toString().trim() : ''
            return { selectedText }
          } catch (error) {
            console.error('获取选中文字失败:', error)
            throw error
          }
        default:
          throw new Error(`未知的消息类型: ${message.type}`)
      }
    }

    const message = { type: 'GET_SELECTED_TEXT' }
    
    await expect(handleContentMessage(message)).rejects.toThrow('Selection API不可用')
  })
})

// 测试右键菜单重复创建修复
describe('右键菜单重复创建修复', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('应该在创建菜单前先清除现有菜单', () => {
    const initializeContextMenus = () => {
      try {
        // 先清除所有现有菜单项，避免重复创建
        chrome.contextMenus.removeAll(() => {
          // 创建主菜单项
          chrome.contextMenus.create({
            id: 'universe-bag-main',
            title: 'Universe Bag',
            contexts: ['page', 'selection', 'link']
          })

          // 收藏当前页面
          chrome.contextMenus.create({
            id: 'bookmark-page',
            parentId: 'universe-bag-main',
            title: '收藏当前页面',
            contexts: ['page']
          })

          // 收藏选中文字
          chrome.contextMenus.create({
            id: 'bookmark-selection',
            parentId: 'universe-bag-main',
            title: '收藏选中文字',
            contexts: ['selection']
          })

          // 收藏链接
          chrome.contextMenus.create({
            id: 'bookmark-link',
            parentId: 'universe-bag-main',
            title: '收藏链接',
            contexts: ['link']
          })

          console.log('右键菜单初始化完成')
        })

        // 监听右键菜单点击事件
        chrome.contextMenus.onClicked.addListener(() => {})

      } catch (error) {
        console.error('初始化右键菜单失败:', error)
      }
    }

    // 执行初始化
    initializeContextMenus()

    // 验证先调用了removeAll
    expect(chrome.contextMenus.removeAll).toHaveBeenCalled()
    expect(chrome.contextMenus.onClicked.addListener).toHaveBeenCalled()
  })

  test('应该创建正确的菜单结构', () => {
    let removeAllCallback

    // 模拟removeAll的回调执行
    chrome.contextMenus.removeAll.mockImplementation((callback) => {
      removeAllCallback = callback
    })

    const initializeContextMenus = () => {
      chrome.contextMenus.removeAll(() => {
        chrome.contextMenus.create({
          id: 'universe-bag-main',
          title: 'Universe Bag',
          contexts: ['page', 'selection', 'link']
        })

        chrome.contextMenus.create({
          id: 'bookmark-page',
          parentId: 'universe-bag-main',
          title: '收藏当前页面',
          contexts: ['page']
        })

        chrome.contextMenus.create({
          id: 'bookmark-selection',
          parentId: 'universe-bag-main',
          title: '收藏选中文字',
          contexts: ['selection']
        })

        chrome.contextMenus.create({
          id: 'bookmark-link',
          parentId: 'universe-bag-main',
          title: '收藏链接',
          contexts: ['link']
        })
      })
    }

    // 执行初始化
    initializeContextMenus()
    
    // 执行removeAll的回调
    removeAllCallback()

    // 验证创建了正确的菜单项
    expect(chrome.contextMenus.create).toHaveBeenCalledTimes(4)
    expect(chrome.contextMenus.create).toHaveBeenCalledWith({
      id: 'universe-bag-main',
      title: 'Universe Bag',
      contexts: ['page', 'selection', 'link']
    })
    expect(chrome.contextMenus.create).toHaveBeenCalledWith({
      id: 'bookmark-page',
      parentId: 'universe-bag-main',
      title: '收藏当前页面',
      contexts: ['page']
    })
  })
})

console.log('Content Script 修复测试完成')