// 测试模块导入是否正常

console.log('开始测试模块导入...')

// 测试基本的ES模块导入
try {
  console.log('✓ 基本模块系统正常')
} catch (error) {
  console.error('✗ 基本模块系统错误:', error)
}

// 检查构建后的文件
const fs = require('fs')
const path = require('path')

const distPath = path.join(__dirname, 'dist')
const optionsJsPath = path.join(distPath, 'assets')

if (fs.existsSync(optionsJsPath)) {
  const files = fs.readdirSync(optionsJsPath)
  const optionsFile = files.find(file => file.startsWith('options-') && file.endsWith('.js'))
  
  if (optionsFile) {
    console.log('✓ 找到options文件:', optionsFile)
    
    const filePath = path.join(optionsJsPath, optionsFile)
    const content = fs.readFileSync(filePath, 'utf8')
    
    // 检查是否有明显的初始化问题
    if (content.includes('Cannot access') || content.includes('before initialization')) {
      console.log('✗ 发现初始化错误相关代码')
    } else {
      console.log('✓ 没有发现明显的初始化错误')
    }
    
    // 检查变量K的使用
    const kMatches = content.match(/\bK\b/g)
    if (kMatches) {
      console.log(`✓ 找到变量K的使用: ${kMatches.length} 次`)
      
      // 查找K的定义
      const kDefinitions = content.match(/\bK\s*=/g)
      if (kDefinitions) {
        console.log(`✓ 找到变量K的定义: ${kDefinitions.length} 次`)
      } else {
        console.log('✗ 没有找到变量K的定义，这可能是问题所在')
      }
      
      // 查找K的使用上下文
      const kUsageContext = content.match(/.{0,50}\bK\b.{0,50}/g)
      if (kUsageContext) {
        console.log('K的使用上下文:')
        kUsageContext.slice(0, 5).forEach((context, index) => {
          console.log(`  ${index + 1}: ${context.trim()}`)
        })
      }
    } else {
      console.log('✓ 没有找到变量K的使用')
    }
    
    // 检查是否有循环依赖的迹象
    if (content.includes('circular') || content.includes('dependency')) {
      console.log('⚠ 可能存在循环依赖问题')
    }
    
  } else {
    console.log('✗ 没有找到options JavaScript文件')
  }
} else {
  console.log('✗ dist/assets目录不存在')
}

console.log('模块导入测试完成')