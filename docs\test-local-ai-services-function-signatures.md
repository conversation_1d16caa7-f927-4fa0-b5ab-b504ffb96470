# 本地AI服务测试脚本函数签名

## 概述

本文档提供 `scripts/test-local-ai-services.js` 中所有函数的签名和类型定义。

## 导出函数

### runTests()
```typescript
async function runTests(): Promise<void>
```
**描述**: 执行完整的本地AI服务测试流程  
**返回值**: Promise<void>  
**异常**: 测试失败时抛出错误并退出进程

## 内部函数

### 服务加载

#### loadServices()
```typescript
async function loadServices(): Promise<{
  localAIServiceAdapter: LocalAIServiceAdapter,
  aiProviderService: AIProviderService
} | null>
```
**描述**: 动态导入AI服务模块  
**返回值**: 服务实例对象或null（加载失败时）

### 输出工具函数

#### colorLog()
```typescript
function colorLog(color: string, message: string): void
```
**参数**:
- `color`: 颜色名称 ('red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan')
- `message`: 要输出的消息

#### success()
```typescript
function success(message: string): void
```
**描述**: 输出绿色成功信息 ✅

#### error()
```typescript
function error(message: string): void
```
**描述**: 输出红色错误信息 ❌

#### info()
```typescript
function info(message: string): void
```
**描述**: 输出蓝色信息 ℹ️

#### warning()
```typescript
function warning(message: string): void
```
**描述**: 输出黄色警告信息 ⚠️

#### header()
```typescript
function header(message: string): void
```
**描述**: 输出带分隔线的标题

### 测试函数

#### testLocalServiceDiscovery()
```typescript
async function testLocalServiceDiscovery(
  adapter: LocalAIServiceAdapter
): Promise<LocalServiceConfig[]>
```
**描述**: 测试本地AI服务自动发现功能  
**参数**:
- `adapter`: 本地AI服务适配器实例
**返回值**: 发现的服务配置列表

#### testServiceConnection()
```typescript
async function testServiceConnection(
  adapter: LocalAIServiceAdapter,
  service: LocalServiceConfig
): Promise<AIConnectionResult>
```
**描述**: 测试指定服务的连接状态  
**参数**:
- `adapter`: 本地AI服务适配器实例
- `service`: 要测试的服务配置
**返回值**: 连接测试结果

#### testModelRetrieval()
```typescript
async function testModelRetrieval(
  adapter: LocalAIServiceAdapter,
  service: LocalServiceConfig
): Promise<AIModel[]>
```
**描述**: 测试从指定服务获取模型列表  
**参数**:
- `adapter`: 本地AI服务适配器实例
- `service`: 服务配置
**返回值**: 模型列表

#### testDefaultServices()
```typescript
async function testDefaultServices(
  adapter: LocalAIServiceAdapter
): Promise<LocalServiceConfig[]>
```
**描述**: 测试获取默认服务配置  
**参数**:
- `adapter`: 本地AI服务适配器实例
**返回值**: 默认服务配置列表

#### testCustomServiceConfig()
```typescript
async function testCustomServiceConfig(
  adapter: LocalAIServiceAdapter
): Promise<void>
```
**描述**: 测试创建自定义服务配置  
**参数**:
- `adapter`: 本地AI服务适配器实例

#### testAIProviderServiceIntegration()
```typescript
async function testAIProviderServiceIntegration(
  aiProviderService: AIProviderService
): Promise<void>
```
**描述**: 测试AI提供商服务的集成功能  
**参数**:
- `aiProviderService`: AI提供商服务实例

## 类型定义

### LocalServiceConfig
```typescript
interface LocalServiceConfig {
  name: string
  baseUrl: string
  port?: number
  protocol?: 'http' | 'https'
  apiPath?: string
  healthCheckPath?: string
  modelsPath?: string
  timeout?: number
  headers?: Record<string, string>
  status?: 'online' | 'offline' | 'unknown'
}
```

### AIConnectionResult
```typescript
interface AIConnectionResult {
  providerId: string
  success: boolean
  responseTime?: number
  modelCount?: number
  error?: string
  testedAt: Date
}
```

### AIModel
```typescript
interface AIModel {
  id: string
  name: string
  displayName: string
  description?: string
  size?: string
  parameters?: string
  capabilities?: string[]
  tags?: string[]
  providerId: string
  isRecommended?: boolean
  isPopular?: boolean
}
```

### LocalAIServiceAdapter
```typescript
interface LocalAIServiceAdapter {
  discoverLocalServices(customPorts?: number[]): Promise<{
    services: LocalServiceConfig[]
    errors: string[]
  }>
  
  testLocalServiceConnection(config: LocalServiceConfig): Promise<AIConnectionResult>
  
  getLocalServiceModels(config: LocalServiceConfig): Promise<AIModel[]>
  
  createCustomServiceConfig(
    name: string,
    baseUrl: string,
    options?: Partial<LocalServiceConfig>
  ): LocalServiceConfig
  
  getDefaultServices(): LocalServiceConfig[]
}
```

### AIProviderService
```typescript
interface AIProviderService {
  discoverLocalServices(customPorts?: number[]): Promise<LocalServiceConfig[]>
  
  getDefaultLocalServices(): LocalServiceConfig[]
  
  createCustomLocalService(
    name: string,
    baseUrl: string,
    options?: any
  ): LocalServiceConfig
}
```

## 常量定义

### 颜色常量
```typescript
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
} as const
```

### 测试配置
```typescript
const customPorts = [8888, 9999] // 自定义扫描端口

const customConfigs = [
  {
    name: 'Test HTTP Service',
    url: 'http://localhost:8080',
    options: { apiPath: '/api/v2', timeout: 15000 }
  },
  {
    name: 'Test HTTPS Service', 
    url: 'https://api.example.com:8443',
    options: { headers: { 'Authorization': 'Bearer token' } }
  }
]
```

## 使用示例

### 基本使用
```javascript
// 导入并运行测试
import { runTests } from './scripts/test-local-ai-services.js'

try {
  await runTests()
  console.log('测试完成')
} catch (error) {
  console.error('测试失败:', error)
}
```

### 单独使用测试函数
```javascript
import { loadServices } from './scripts/test-local-ai-services.js'

const services = await loadServices()
if (services) {
  const { localAIServiceAdapter } = services
  
  // 测试服务发现
  const discoveredServices = await testLocalServiceDiscovery(localAIServiceAdapter)
  
  // 测试每个服务
  for (const service of discoveredServices) {
    const result = await testServiceConnection(localAIServiceAdapter, service)
    console.log(`${service.name}: ${result.success ? '成功' : '失败'}`)
  }
}
```

## 错误处理

### 模块加载错误
```typescript
// loadServices() 返回 null 时的处理
const services = await loadServices()
if (!services) {
  console.error('无法加载服务模块')
  process.exit(1)
}
```

### 测试执行错误
```typescript
// runTests() 中的错误处理
try {
  await runTests()
} catch (error) {
  console.error(`测试过程中发生错误: ${error.message}`)
  console.error(error.stack)
  process.exit(1)
}
```

### 服务连接错误
```typescript
// testServiceConnection() 的错误处理
const result = await testServiceConnection(adapter, service)
if (!result.success) {
  console.error(`连接失败: ${result.error}`)
}
```

## 相关文档

- [本地AI服务测试脚本API文档](./test-local-ai-services-script-api.md)
- [本地AI服务测试使用示例](./test-local-ai-services-usage-examples.md)
- [本地AI服务适配器API文档](./localAIServiceAdapter-api.md)