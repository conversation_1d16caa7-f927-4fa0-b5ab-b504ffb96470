<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TagColorPicker 组件演示</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // 模拟ColorUtils类
        const ColorUtils = {
            PRESET_COLORS: [
                '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
                '#06B6D4', '#F97316', '#84CC16', '#EC4899', '#6B7280',
                '#14B8A6', '#F472B6', '#A855F7', '#22D3EE', '#FDE047',
                '#FB7185', '#34D399', '#FBBF24', '#F87171', '#60A5FA'
            ],
            
            isValidColor(color) {
                if (!color || typeof color !== 'string') return false;
                
                // 验证十六进制格式
                const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                if (hexPattern.test(color)) return true;
                
                // 验证RGB格式
                const rgbPattern = /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/;
                const rgbMatch = color.match(rgbPattern);
                if (rgbMatch) {
                    const [, r, g, b] = rgbMatch;
                    return parseInt(r) <= 255 && parseInt(g) <= 255 && parseInt(b) <= 255;
                }
                
                return false;
            },
            
            getContrastColor(backgroundColor) {
                // 简化的对比色计算
                const hex = backgroundColor.replace('#', '');
                const r = parseInt(hex.substr(0, 2), 16);
                const g = parseInt(hex.substr(2, 2), 16);
                const b = parseInt(hex.substr(4, 2), 16);
                const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
                return luminance > 0.5 ? '#000000' : '#ffffff';
            }
        };

        // TagColorPicker组件
        const TagColorPicker = ({ 
            value = '', 
            onChange, 
            presetColors = ColorUtils.PRESET_COLORS,
            allowCustom = true,
            className = '',
            disabled = false,
            placeholder = '选择颜色'
        }) => {
            const [showCustomInput, setShowCustomInput] = React.useState(false);
            const [customColor, setCustomColor] = React.useState('');
            const [validationError, setValidationError] = React.useState('');
            const colorInputRef = React.useRef(null);

            React.useEffect(() => {
                if (value && !presetColors.includes(value)) {
                    setCustomColor(value);
                    setShowCustomInput(true);
                }
            }, [value, presetColors]);

            const handlePresetColorSelect = (color) => {
                setValidationError('');
                setShowCustomInput(false);
                setCustomColor('');
                onChange(color);
            };

            const handleCustomColorChange = (inputValue) => {
                setCustomColor(inputValue);
                setValidationError('');

                if (inputValue.trim()) {
                    if (ColorUtils.isValidColor(inputValue)) {
                        onChange(inputValue);
                    } else {
                        setValidationError('无效的颜色格式');
                    }
                }
            };

            const handleCustomColorBlur = () => {
                if (customColor.trim() && !ColorUtils.isValidColor(customColor)) {
                    setValidationError('请输入有效的颜色格式（如：#ff0000, rgb(255,0,0)）');
                }
            };

            const toggleCustomInput = () => {
                if (disabled) return;
                
                const newShowCustomInput = !showCustomInput;
                setShowCustomInput(newShowCustomInput);
                
                if (newShowCustomInput) {
                    setTimeout(() => {
                        colorInputRef.current?.focus();
                    }, 100);
                } else {
                    setCustomColor('');
                    setValidationError('');
                }
            };

            const getColorPreviewStyle = (color) => ({
                backgroundColor: color,
                border: `2px solid ${ColorUtils.getContrastColor(color) === '#000000' ? '#e5e7eb' : '#374151'}`
            });

            const isColorSelected = (color) => value === color;

            const getTextColor = (backgroundColor) => ColorUtils.getContrastColor(backgroundColor);

            return React.createElement('div', { className: `tag-color-picker ${className}` },
                // 颜色预览区域
                React.createElement('div', { className: 'mb-3' },
                    React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-2' }, placeholder),
                    React.createElement('div', { className: 'flex items-center space-x-3' },
                        React.createElement('div', {
                            className: 'w-8 h-8 rounded-md border-2 border-gray-300 flex items-center justify-center',
                            style: value ? getColorPreviewStyle(value) : { backgroundColor: '#f3f4f6' }
                        }, !value && React.createElement('span', { className: 'text-xs text-gray-400' }, '?')),
                        React.createElement('div', { className: 'flex-1' },
                            value ? React.createElement('div', { className: 'text-sm' },
                                React.createElement('span', { className: 'font-mono text-gray-600' }, value)
                            ) : React.createElement('span', { className: 'text-sm text-gray-400' }, '未选择颜色')
                        )
                    )
                ),
                
                // 预设颜色网格
                React.createElement('div', { className: 'mb-4' },
                    React.createElement('h4', { className: 'text-sm font-medium text-gray-700 mb-2' }, '预设颜色'),
                    React.createElement('div', { className: 'grid grid-cols-10 gap-2' },
                        presetColors.map((color, index) =>
                            React.createElement('button', {
                                key: `preset-${index}`,
                                type: 'button',
                                disabled: disabled,
                                className: `w-8 h-8 rounded-md border-2 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 ${
                                    isColorSelected(color) 
                                        ? 'ring-2 ring-blue-500 ring-offset-2 scale-110' 
                                        : 'hover:ring-1 hover:ring-gray-300'
                                } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`,
                                style: getColorPreviewStyle(color),
                                onClick: () => handlePresetColorSelect(color),
                                title: `选择颜色: ${color}`
                            }, isColorSelected(color) && React.createElement('svg', {
                                className: 'w-4 h-4',
                                fill: 'currentColor',
                                viewBox: '0 0 20 20',
                                style: { color: getTextColor(color) }
                            }, React.createElement('path', {
                                fillRule: 'evenodd',
                                d: 'M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z',
                                clipRule: 'evenodd'
                            })))
                        )
                    )
                ),
                
                // 自定义颜色区域
                allowCustom && React.createElement('div', { className: 'border-t pt-4' },
                    React.createElement('div', { className: 'flex items-center justify-between mb-2' },
                        React.createElement('h4', { className: 'text-sm font-medium text-gray-700' }, '自定义颜色'),
                        React.createElement('button', {
                            type: 'button',
                            disabled: disabled,
                            onClick: toggleCustomInput,
                            className: `text-sm px-3 py-1 rounded-md transition-colors duration-200 ${
                                showCustomInput 
                                    ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                                    : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                            } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`
                        }, showCustomInput ? '取消' : '自定义')
                    ),
                    
                    showCustomInput && React.createElement('div', { className: 'space-y-2' },
                        React.createElement('div', { className: 'flex space-x-2' },
                            React.createElement('input', {
                                type: 'color',
                                value: customColor && ColorUtils.isValidColor(customColor) ? customColor : '#000000',
                                onChange: (e) => handleCustomColorChange(e.target.value),
                                disabled: disabled,
                                className: `w-10 h-10 rounded-md border border-gray-300 cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
                                title: '使用颜色选择器'
                            }),
                            React.createElement('input', {
                                ref: colorInputRef,
                                type: 'text',
                                value: customColor,
                                onChange: (e) => handleCustomColorChange(e.target.value),
                                onBlur: handleCustomColorBlur,
                                disabled: disabled,
                                placeholder: '输入颜色值 (如: #ff0000, rgb(255,0,0))',
                                className: `flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    validationError ? 'border-red-300 bg-red-50' : ''
                                } ${disabled ? 'opacity-50 cursor-not-allowed bg-gray-50' : ''}`
                            })
                        ),
                        
                        validationError && React.createElement('div', { className: 'text-sm text-red-600 bg-red-50 px-3 py-2 rounded-md' }, validationError),
                        
                        React.createElement('div', { className: 'text-xs text-gray-500' }, '支持格式：十六进制 (#ff0000)、RGB (rgb(255,0,0))')
                    )
                ),
                
                // 清除颜色按钮
                value && React.createElement('div', { className: 'mt-4 pt-4 border-t' },
                    React.createElement('button', {
                        type: 'button',
                        disabled: disabled,
                        onClick: () => {
                            onChange('');
                            setCustomColor('');
                            setShowCustomInput(false);
                            setValidationError('');
                        },
                        className: `w-full px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-md transition-colors duration-200 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`
                    }, '清除颜色')
                )
            );
        };

        // 演示应用
        const App = () => {
            const [selectedColor1, setSelectedColor1] = React.useState('#3B82F6');
            const [selectedColor2, setSelectedColor2] = React.useState('');
            const [selectedColor3, setSelectedColor3] = React.useState('#10B981');

            return React.createElement('div', { className: 'p-6 max-w-4xl mx-auto' },
                React.createElement('h1', { className: 'text-2xl font-bold mb-6' }, 'TagColorPicker 组件演示'),
                
                React.createElement('div', { className: 'space-y-8' },
                    // 基本用法
                    React.createElement('div', { className: 'bg-white p-6 rounded-lg shadow-md' },
                        React.createElement('h2', { className: 'text-lg font-semibold mb-4' }, '基本用法'),
                        React.createElement('div', { className: 'max-w-md' },
                            React.createElement(TagColorPicker, {
                                value: selectedColor1,
                                onChange: setSelectedColor1,
                                placeholder: '选择标签颜色'
                            })
                        ),
                        React.createElement('div', { className: 'mt-4 p-3 bg-gray-50 rounded' },
                            React.createElement('p', { className: 'text-sm' },
                                React.createElement('strong', null, '选中的颜色: '),
                                selectedColor1 || '未选择'
                            ),
                            selectedColor1 && React.createElement('div', { className: 'mt-2 flex items-center space-x-2' },
                                React.createElement('div', {
                                    className: 'w-6 h-6 rounded border',
                                    style: { backgroundColor: selectedColor1 }
                                }),
                                React.createElement('span', { className: 'text-sm font-mono' }, selectedColor1)
                            )
                        )
                    ),
                    
                    // 无初始值
                    React.createElement('div', { className: 'bg-white p-6 rounded-lg shadow-md' },
                        React.createElement('h2', { className: 'text-lg font-semibold mb-4' }, '无初始值'),
                        React.createElement('div', { className: 'max-w-md' },
                            React.createElement(TagColorPicker, {
                                value: selectedColor2,
                                onChange: setSelectedColor2,
                                placeholder: '请选择颜色'
                            })
                        ),
                        React.createElement('div', { className: 'mt-4 p-3 bg-gray-50 rounded' },
                            React.createElement('p', { className: 'text-sm' },
                                React.createElement('strong', null, '选中的颜色: '),
                                selectedColor2 || '未选择'
                            )
                        )
                    ),
                    
                    // 禁用自定义颜色
                    React.createElement('div', { className: 'bg-white p-6 rounded-lg shadow-md' },
                        React.createElement('h2', { className: 'text-lg font-semibold mb-4' }, '禁用自定义颜色'),
                        React.createElement('div', { className: 'max-w-md' },
                            React.createElement(TagColorPicker, {
                                value: '#F59E0B',
                                onChange: () => {},
                                allowCustom: false,
                                placeholder: '仅预设颜色'
                            })
                        )
                    ),
                    
                    // 禁用状态
                    React.createElement('div', { className: 'bg-white p-6 rounded-lg shadow-md' },
                        React.createElement('h2', { className: 'text-lg font-semibold mb-4' }, '禁用状态'),
                        React.createElement('div', { className: 'max-w-md' },
                            React.createElement(TagColorPicker, {
                                value: '#EF4444',
                                onChange: () => {},
                                disabled: true,
                                placeholder: '禁用的颜色选择器'
                            })
                        )
                    )
                )
            );
        };

        // 渲染应用
        ReactDOM.render(React.createElement(App), document.getElementById('root'));
    </script>
</body>
</html>