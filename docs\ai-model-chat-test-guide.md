# AI模型对话测试功能使用指南

## 功能概述

AI模型对话测试功能是Chrome扩展中的一个重要测试工具，允许用户直接与各种AI模型进行对话测试，包括本地部署的模型和云端API服务。

## 访问路径

在Chrome扩展的选项页面中：
1. 打开扩展选项页面：`chrome-extension://[extension-id]/src/options/index.html`
2. 导航到"本地AI测试"标签页：`#local-ai-test`
3. 选择"对话测试"标签

## 主要功能

### 1. 模型发现与选择

#### 自动模型发现
- 自动扫描本地AI服务（Ollama、LM Studio、Xinference等）
- 从配置的云端AI提供商获取可用模型
- 支持推荐模型和热门模型标识

#### 模型信息展示
- 模型名称和显示名称
- 提供商信息
- 模型描述和能力标签
- 推荐/热门标识

### 2. 对话测试

#### 实时对话
- 支持与选中的AI模型进行实时对话
- 显示消息发送时间和响应时间
- 支持多轮对话历史

#### 消息管理
- 用户消息和AI回复的清晰区分
- 消息复制功能
- 对话历史清空
- 对话记录导出

### 3. 对话设置

#### 基础参数
- **温度 (Temperature)**: 0-2，控制回复的随机性
- **最大令牌数**: 100-4000，限制AI回复的最大长度
- **系统提示词**: 定义AI的角色和行为

#### 高级设置
- 流式输出支持（计划中）
- Top-P、频率惩罚等参数（计划中）

### 4. 测试日志

#### 实时日志
- 模型加载过程记录
- API调用状态跟踪
- 错误信息详细记录
- 性能指标监控

## 支持的AI服务

### 本地AI服务

#### Ollama
- 默认端口：11434
- API格式：Ollama原生格式
- 支持功能：对话、补全

#### LM Studio
- 默认端口：1234
- API格式：OpenAI兼容
- 支持功能：对话、补全

#### Xinference
- 默认端口：9997
- API格式：OpenAI兼容
- 支持功能：对话、补全、嵌入

#### LocalAI
- 默认端口：8080
- API格式：OpenAI兼容
- 支持功能：对话、补全

### 云端AI服务

#### OpenAI
- GPT-3.5、GPT-4系列模型
- 需要API密钥

#### Anthropic Claude
- Claude-3系列模型
- 需要API密钥

#### Google Gemini
- Gemini Pro模型
- 需要API密钥

#### 国内AI服务
- DeepSeek、智谱AI、通义千问等
- 需要相应的API密钥

## 使用步骤

### 1. 准备工作

#### 本地AI服务
```bash
# 启动Ollama（示例）
ollama serve

# 拉取模型
ollama pull llama2
```

#### 云端AI服务
1. 在AI集成设置中配置提供商
2. 添加API密钥和基础URL
3. 启用相应的提供商

### 2. 开始测试

1. **刷新模型列表**
   - 点击"刷新模型"按钮
   - 等待模型加载完成

2. **选择测试模型**
   - 从下拉列表中选择要测试的模型
   - 查看模型信息和能力

3. **配置对话参数**
   - 点击"设置"按钮
   - 调整温度、最大令牌数等参数
   - 设置系统提示词

4. **开始对话**
   - 在输入框中输入消息
   - 按Enter发送或点击发送按钮
   - 查看AI回复和响应时间

### 3. 高级功能

#### 导出对话记录
```json
{
  "model": {
    "id": "ollama-llama2",
    "name": "llama2",
    "displayName": "Llama 2 (Ollama)",
    "providerName": "Ollama"
  },
  "settings": {
    "temperature": 0.7,
    "maxTokens": 2048,
    "systemPrompt": "你是一个有用的AI助手"
  },
  "messages": [
    {
      "id": "user-**********",
      "role": "user",
      "content": "你好",
      "timestamp": "2024-01-01T12:00:00.000Z"
    },
    {
      "id": "assistant-**********",
      "role": "assistant",
      "content": "你好！我是AI助手，很高兴为您服务。",
      "timestamp": "2024-01-01T12:00:02.000Z",
      "responseTime": 1500
    }
  ],
  "exportedAt": "2024-01-01T12:05:00.000Z"
}
```

## 故障排除

### 常见问题

#### 1. 没有找到可用模型
**可能原因：**
- 本地AI服务未启动
- 云端API配置错误
- 网络连接问题

**解决方案：**
- 检查本地AI服务状态
- 验证API密钥和配置
- 检查网络连接

#### 2. 对话请求失败
**可能原因：**
- 模型不支持对话功能
- API配额不足
- 请求参数错误

**解决方案：**
- 选择支持对话的模型
- 检查API配额和计费
- 调整对话参数

#### 3. 响应时间过长
**可能原因：**
- 模型计算复杂度高
- 网络延迟
- 服务器负载高

**解决方案：**
- 选择更快的模型
- 减少最大令牌数
- 降低温度参数

### 调试技巧

#### 1. 查看测试日志
- 实时监控API调用状态
- 分析错误信息和响应时间
- 跟踪模型加载过程

#### 2. 使用浏览器开发者工具
```javascript
// 在控制台中查看网络请求
// 检查API调用的详细信息
```

#### 3. 测试不同参数组合
- 尝试不同的温度设置
- 调整最大令牌数
- 修改系统提示词

## API接口说明

### 本地AI服务API

#### Ollama格式
```javascript
// 请求格式
{
  "model": "llama2",
  "messages": [
    {"role": "user", "content": "Hello"}
  ],
  "stream": false,
  "options": {
    "temperature": 0.7,
    "num_predict": 2048
  }
}

// 响应格式
{
  "model": "llama2",
  "message": {
    "role": "assistant",
    "content": "Hello! How can I help you today?"
  },
  "done": true
}
```

#### OpenAI兼容格式
```javascript
// 请求格式
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Hello"}
  ],
  "temperature": 0.7,
  "max_tokens": 2048
}

// 响应格式
{
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 15,
    "total_tokens": 25
  }
}
```

## 性能优化

### 1. 模型选择
- 优先选择推荐模型
- 根据任务选择合适大小的模型
- 考虑响应时间和质量平衡

### 2. 参数调优
- 合理设置最大令牌数
- 根据需求调整温度
- 优化系统提示词

### 3. 缓存策略
- 模型列表自动缓存
- 避免重复的模型发现请求
- 合理的缓存过期时间

## 扩展开发

### 添加新的AI提供商
1. 在`aiIntegrationService`中添加提供商信息
2. 在`aiChatService`中实现API调用逻辑
3. 更新请求和响应格式处理

### 自定义对话功能
1. 扩展`ChatSettings`接口
2. 添加新的UI控件
3. 实现相应的API参数传递

## 安全注意事项

### 1. API密钥保护
- 不要在日志中记录API密钥
- 使用安全的存储方式
- 定期轮换API密钥

### 2. 数据隐私
- 注意对话内容的隐私性
- 避免发送敏感信息
- 了解各AI服务的数据政策

### 3. 使用限制
- 遵守API使用条款
- 注意请求频率限制
- 监控API配额使用情况

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持本地和云端AI模型对话测试
- 基础的对话参数设置
- 对话记录导出功能

### 计划功能
- 流式对话支持
- 更多对话参数选项
- 对话模板和预设
- 批量测试功能
- 性能基准测试