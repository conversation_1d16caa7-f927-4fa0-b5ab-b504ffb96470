# 默认AI模型页面Select组件修复

## 问题描述

在默认AI模型页面中，Select组件出现了以下错误：

```
Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
```

## 问题原因

在`DefaultAIModelsTab.tsx`组件中，Select组件使用了空字符串(`""`)作为SelectItem的value，这违反了Radix UI Select组件的规则。

### 问题代码

```tsx
// 主要模型选择
<Select value={usage.selectedModelId || ''}>
  {/* ... */}
</Select>

// 备用模型选择
<Select value={usage.fallbackModelId || ''}>
  <SelectContent>
    <SelectItem value="">  {/* 这里使用了空字符串 */}
      <span className="text-muted-foreground">不设置备用模型</span>
    </SelectItem>
    {/* ... */}
  </SelectContent>
</Select>
```

## 解决方案

将空字符串替换为特殊的标识符`'none'`，并在处理逻辑中进行相应的转换。

### 修复后的代码

```tsx
// 主要模型选择
<Select 
  value={usage.selectedModelId || 'none'}
  onValueChange={(value) => 
    handleUpdateUsage(usage.id, value === 'none' ? null : value, usage.fallbackModelId)
  }
>
  <SelectContent>
    <SelectItem value="none">
      <span className="text-muted-foreground">未选择模型</span>
    </SelectItem>
    {/* 其他选项... */}
  </SelectContent>
</Select>

// 备用模型选择
<Select 
  value={usage.fallbackModelId || 'none'}
  onValueChange={(value) => 
    handleUpdateUsage(usage.id, usage.selectedModelId, value === 'none' ? null : value)
  }
>
  <SelectContent>
    <SelectItem value="none">
      <span className="text-muted-foreground">不设置备用模型</span>
    </SelectItem>
    {/* 其他选项... */}
  </SelectContent>
</Select>
```

## 修复要点

1. **使用特殊标识符**: 将空字符串`""`替换为`"none"`
2. **逻辑转换**: 在`onValueChange`回调中，将`"none"`转换回`null`
3. **显示处理**: 在显示时，将`null`值转换为`"none"`以匹配Select的value
4. **用户体验**: 为用户提供清晰的"未选择模型"和"不设置备用模型"选项

## 验证结果

修复后：
- ✅ Select组件不再抛出空字符串value错误
- ✅ 用户可以正常选择和清除模型配置
- ✅ 界面显示正常，用户体验良好
- ✅ 构建过程无错误

## 相关文件

- `src/components/DefaultAIModelsTab.tsx` - 主要修复文件
- `tests/defaultAIModelsTab.test.tsx` - 相关测试文件

## 技术说明

这个修复遵循了Radix UI Select组件的最佳实践：
- 避免使用空字符串作为SelectItem的value
- 使用有意义的标识符来表示"无选择"状态
- 在业务逻辑层面处理null值的转换

修复确保了组件的稳定性和用户体验的一致性。