/**
 * VirtualBookmarkList测试页面验证脚本
 * 验证测试页面是否正确集成到应用中
 */

const fs = require('fs')
const path = require('path')

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`
}

console.log(colors.cyan('🔍 VirtualBookmarkList测试页面验证'))
console.log('=' .repeat(50))

// 检查文件是否存在
function checkFileExists(filePath) {
  const fullPath = path.resolve(filePath)
  const exists = fs.existsSync(fullPath)
  console.log(`${exists ? colors.green('✓') : colors.red('✗')} ${filePath}`)
  return exists
}

// 检查文件内容
function checkFileContent(filePath, checks) {
  try {
    const content = fs.readFileSync(path.resolve(filePath), 'utf8')
    let allPassed = true
    
    console.log(colors.blue(`\n📄 检查 ${filePath}:`))
    
    checks.forEach(({ name, pattern, required = true }) => {
      const found = pattern.test(content)
      const status = found ? colors.green('✓') : (required ? colors.red('✗') : colors.yellow('⚠'))
      console.log(`  ${status} ${name}`)
      
      if (required && !found) {
        allPassed = false
      }
    })
    
    return allPassed
  } catch (error) {
    console.log(colors.red(`✗ 无法读取文件: ${filePath}`))
    return false
  }
}

let allTestsPassed = true

// 1. 检查测试页面文件是否存在
console.log(colors.yellow('\n1. 检查测试页面文件:'))
const testPageFiles = [
  'src/components/test/VirtualBookmarkListTest.tsx',
  'src/components/test/VirtualBookmarkListTestPage.tsx'
]

testPageFiles.forEach(file => {
  if (!checkFileExists(file)) {
    allTestsPassed = false
  }
})

// 2. 检查VirtualBookmarkListTest组件
console.log(colors.yellow('\n2. 检查VirtualBookmarkListTest组件:'))
const testComponentChecks = [
  {
    name: '导入VirtualBookmarkList组件',
    pattern: /import VirtualBookmarkList from ['"]\.\.\/VirtualBookmarkList['"]/
  },
  {
    name: '导入shadcn组件',
    pattern: /import.*Button.*from ['"]\.\.\/ui\/button['"]/
  },
  {
    name: '导入Badge组件',
    pattern: /import.*Badge.*from ['"]\.\.\/ui\/badge['"]/
  },
  {
    name: '导入Card组件',
    pattern: /import.*Card.*from ['"]\.\.\/ui\/card['"]/
  },
  {
    name: '导入Switch组件',
    pattern: /import.*Switch.*from ['"]\.\.\/ui\/switch['"]/
  },
  {
    name: '生成测试数据函数',
    pattern: /generateMockBookmarks/
  },
  {
    name: '使用VirtualBookmarkList组件',
    pattern: /<VirtualBookmarkList[\s\S]*?\/>/
  },
  {
    name: '视图模式切换功能',
    pattern: /setViewMode/
  },
  {
    name: '数据量控制功能',
    pattern: /setBookmarkCount/
  },
  {
    name: '容器高度控制',
    pattern: /setContainerHeight/
  },
  {
    name: '自动高亮功能',
    pattern: /autoHighlight/
  },
  {
    name: '统计信息显示',
    pattern: /stats\./
  }
]

if (!checkFileContent('src/components/test/VirtualBookmarkListTest.tsx', testComponentChecks)) {
  allTestsPassed = false
}

// 3. 检查VirtualBookmarkListTestPage组件
console.log(colors.yellow('\n3. 检查VirtualBookmarkListTestPage组件:'))
const testPageChecks = [
  {
    name: '导入VirtualBookmarkListTest组件',
    pattern: /import VirtualBookmarkListTest from ['"]\.\/VirtualBookmarkListTest['"]/
  },
  {
    name: '导入shadcn组件',
    pattern: /import.*Button.*from ['"]\.\.\/ui\/button['"]/
  },
  {
    name: '包含导航栏',
    pattern: /导航栏|返回/
  },
  {
    name: '包含测试说明',
    pattern: /测试说明/
  },
  {
    name: '包含测试项目状态',
    pattern: /测试项目状态/
  },
  {
    name: '包含操作指南',
    pattern: /操作指南/
  },
  {
    name: '使用VirtualBookmarkListTest组件',
    pattern: /<VirtualBookmarkListTest/
  },
  {
    name: '包含页脚信息',
    pattern: /页脚信息|成功重构/
  }
]

if (!checkFileContent('src/components/test/VirtualBookmarkListTestPage.tsx', testPageChecks)) {
  allTestsPassed = false
}

// 4. 检查OptionsApp.tsx集成
console.log(colors.yellow('\n4. 检查OptionsApp.tsx集成:'))
const optionsAppChecks = [
  {
    name: '导入VirtualBookmarkListTestPage',
    pattern: /import VirtualBookmarkListTestPage from ['"]\.\.\/components\/test\/VirtualBookmarkListTestPage['"]/
  },
  {
    name: '添加virtualbookmarklist-test标签页',
    pattern: /virtualbookmarklist-test/
  },
  {
    name: '在renderTabContent中处理测试页面',
    pattern: /case 'virtualbookmarklist-test':/
  },
  {
    name: '返回VirtualBookmarkListTestPage组件',
    pattern: /return <VirtualBookmarkListTestPage/
  }
]

if (!checkFileContent('src/options/OptionsApp.tsx', optionsAppChecks)) {
  allTestsPassed = false
}

// 5. 检查shadcn组件依赖
console.log(colors.yellow('\n5. 检查shadcn组件依赖:'))
const shadcnComponents = [
  'src/components/ui/switch.tsx',
  'src/components/ui/separator.tsx',
  'src/components/ui/button.tsx',
  'src/components/ui/badge.tsx',
  'src/components/ui/card.tsx',
  'src/components/ui/label.tsx'
]

shadcnComponents.forEach(component => {
  if (!checkFileExists(component)) {
    allTestsPassed = false
  }
})

// 6. 检查构建产物
console.log(colors.yellow('\n6. 检查构建产物:'))
const buildFiles = [
  'dist/src/options/index.html',
  'dist/assets/options-75ecdb85.js'
]

let buildExists = true
buildFiles.forEach(file => {
  if (!checkFileExists(file)) {
    buildExists = false
  }
})

if (buildExists) {
  console.log(colors.green('✓ 构建产物存在'))
} else {
  console.log(colors.yellow('⚠ 构建产物不存在，请运行 npm run build'))
}

// 7. 总结
console.log('\n' + '='.repeat(50))
if (allTestsPassed) {
  console.log(colors.green('🎉 所有检查通过！VirtualBookmarkList测试页面已成功集成'))
  console.log(colors.green('\n✅ 集成完成的功能:'))
  console.log(colors.green('  • VirtualBookmarkListTest测试组件'))
  console.log(colors.green('  • VirtualBookmarkListTestPage测试页面'))
  console.log(colors.green('  • OptionsApp.tsx路由集成'))
  console.log(colors.green('  • shadcn组件依赖完整'))
  console.log(colors.green('  • 构建系统兼容'))
  
  console.log(colors.cyan('\n📋 如何访问测试页面:'))
  console.log(colors.cyan('  1. 构建扩展: npm run build'))
  console.log(colors.cyan('  2. 加载扩展到Chrome'))
  console.log(colors.cyan('  3. 打开扩展选项页面'))
  console.log(colors.cyan('  4. 点击"VirtualBookmarkList测试"标签页'))
  console.log(colors.cyan('  5. 或直接访问: chrome-extension://[扩展ID]/src/options/index.html#virtualbookmarklist-test'))
} else {
  console.log(colors.red('❌ 部分检查未通过，请检查上述问题'))
  console.log(colors.yellow('\n🔧 建议的修复步骤:'))
  console.log(colors.yellow('  1. 确保所有测试页面文件都已创建'))
  console.log(colors.yellow('  2. 检查shadcn组件的导入和使用'))
  console.log(colors.yellow('  3. 验证OptionsApp.tsx的路由集成'))
  console.log(colors.yellow('  4. 运行构建确保没有编译错误'))
}

console.log(colors.cyan('\n📋 测试功能:'))
console.log(colors.cyan('  • 视图模式切换（行视图、紧凑视图、卡片视图）'))
console.log(colors.cyan('  • 数据量测试（10-1000个收藏项）'))
console.log(colors.cyan('  • 容器高度调整（400-1000px）'))
console.log(colors.cyan('  • 高亮和选择功能'))
console.log(colors.cyan('  • 自动高亮演示'))
console.log(colors.cyan('  • 虚拟滚动性能验证'))
console.log(colors.cyan('  • shadcn组件集成效果'))

process.exit(allTestsPassed ? 0 : 1)