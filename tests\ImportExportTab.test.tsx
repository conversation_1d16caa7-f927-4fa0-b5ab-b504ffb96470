// 导入导出标签页组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import '@testing-library/jest-dom'
import ImportExportTab from '../src/components/ImportExportTab'
import { bookmarkImportExportService } from '../src/services/BookmarkImportExportService'

// Mock 导入导出服务
vi.mock('../src/services/BookmarkImportExportService', () => ({
  bookmarkImportExportService: {
    exportBookmarks: vi.fn(),
    importBookmarks: vi.fn(),
    detectDuplicates: vi.fn()
  }
}))

// Mock URL.createObjectURL 和 URL.revokeObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-url')
global.URL.revokeObjectURL = vi.fn()

describe('ImportExportTab 组件测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('组件渲染测试', () => {
    it('应该正确渲染导入导出界面', () => {
      render(<ImportExportTab />)

      expect(screen.getByText('导入导出')).toBeInTheDocument()
      expect(screen.getByText('导出您的收藏数据或从其他来源导入收藏')).toBeInTheDocument()
      expect(screen.getByText('导出收藏')).toBeInTheDocument()
      expect(screen.getByText('导入收藏')).toBeInTheDocument()
    })

    it('应该显示导出格式选项', () => {
      render(<ImportExportTab />)

      expect(screen.getByText('JSON')).toBeInTheDocument()
      expect(screen.getByText('CSV')).toBeInTheDocument()
      expect(screen.getByText('HTML')).toBeInTheDocument()
      expect(screen.getByText('完整数据')).toBeInTheDocument()
      expect(screen.getByText('表格格式')).toBeInTheDocument()
      expect(screen.getByText('网页格式')).toBeInTheDocument()
    })

    it('应该显示导入来源选项', () => {
      render(<ImportExportTab />)

      const importSourceSelect = screen.getByDisplayValue('JSON 文件')
      expect(importSourceSelect).toBeInTheDocument()

      fireEvent.click(importSourceSelect)
      expect(screen.getByText('CSV 文件')).toBeInTheDocument()
      expect(screen.getByText('HTML 文件')).toBeInTheDocument()
      expect(screen.getByText('Chrome 书签')).toBeInTheDocument()
      expect(screen.getByText('Firefox 书签')).toBeInTheDocument()
      expect(screen.getByText('Edge 书签')).toBeInTheDocument()
    })

    it('应该显示导出选项复选框', () => {
      render(<ImportExportTab />)

      expect(screen.getByLabelText('包含收藏内容')).toBeInTheDocument()
      expect(screen.getByLabelText('包含元数据')).toBeInTheDocument()
      expect(screen.getByLabelText('包含收藏内容')).toBeChecked()
      expect(screen.getByLabelText('包含元数据')).not.toBeChecked()
    })

    it('应该显示导入选项复选框', () => {
      render(<ImportExportTab />)

      expect(screen.getByLabelText('跳过重复项')).toBeInTheDocument()
      expect(screen.getByLabelText('验证数据格式')).toBeInTheDocument()
      expect(screen.getByLabelText('跳过重复项')).toBeChecked()
      expect(screen.getByLabelText('验证数据格式')).toBeChecked()
    })

    it('应该显示使用说明', () => {
      render(<ImportExportTab />)

      expect(screen.getByText('使用说明')).toBeInTheDocument()
      expect(screen.getByText('JSON格式：包含完整的收藏数据，适合备份和迁移')).toBeInTheDocument()
      expect(screen.getByText('支持导入本工具导出的JSON、CSV、HTML文件')).toBeInTheDocument()
    })
  })

  describe('导出功能测试', () => {
    it('应该能够选择导出格式', () => {
      render(<ImportExportTab />)

      const jsonButton = screen.getByRole('button', { name: /JSON/ })
      const csvButton = screen.getByRole('button', { name: /CSV/ })
      const htmlButton = screen.getByRole('button', { name: /HTML/ })

      expect(jsonButton).toHaveClass('border-primary-500')
      expect(csvButton).not.toHaveClass('border-primary-500')
      expect(htmlButton).not.toHaveClass('border-primary-500')

      fireEvent.click(csvButton)
      expect(csvButton).toHaveClass('border-primary-500')
      expect(jsonButton).not.toHaveClass('border-primary-500')
    })

    it('应该能够切换导出选项', () => {
      render(<ImportExportTab />)

      const includeContentCheckbox = screen.getByLabelText('包含收藏内容')
      const includeMetadataCheckbox = screen.getByLabelText('包含元数据')

      expect(includeContentCheckbox).toBeChecked()
      expect(includeMetadataCheckbox).not.toBeChecked()

      fireEvent.click(includeContentCheckbox)
      fireEvent.click(includeMetadataCheckbox)

      expect(includeContentCheckbox).not.toBeChecked()
      expect(includeMetadataCheckbox).toBeChecked()
    })

    it('应该能够设置日期范围', () => {
      render(<ImportExportTab />)

      const startDateInput = screen.getByPlaceholderText('开始日期')
      const endDateInput = screen.getByPlaceholderText('结束日期')

      fireEvent.change(startDateInput, { target: { value: '2024-01-01' } })
      fireEvent.change(endDateInput, { target: { value: '2024-12-31' } })

      expect(startDateInput).toHaveValue('2024-01-01')
      expect(endDateInput).toHaveValue('2024-12-31')
    })

    it('应该能够执行导出操作', async () => {
      const mockExportResult = {
        success: true,
        data: '{"bookmarks": []}',
        filename: 'bookmarks_20240101.json',
        format: 'json' as const,
        itemCount: 0
      }

      vi.mocked(bookmarkImportExportService.exportBookmarks).mockResolvedValue(mockExportResult)

      render(<ImportExportTab />)

      const exportButton = screen.getByRole('button', { name: /开始导出/ })
      fireEvent.click(exportButton)

      expect(exportButton).toHaveTextContent('导出中...')
      expect(exportButton).toBeDisabled()

      await waitFor(() => {
        expect(bookmarkImportExportService.exportBookmarks).toHaveBeenCalledWith(
          expect.objectContaining({
            format: 'json',
            includeContent: true,
            includeMetadata: false
          }),
          expect.any(Function)
        )
      })

      await waitFor(() => {
        expect(screen.getByText('导出成功')).toBeInTheDocument()
        expect(screen.getByText('已导出 0 个收藏到 bookmarks_20240101.json')).toBeInTheDocument()
      })
    })

    it('应该显示导出进度', async () => {
      vi.mocked(bookmarkImportExportService.exportBookmarks).mockImplementation(
        async (options, callback) => {
          // 立即调用进度回调
          callback?.(50, '正在生成数据...')
          callback?.(100, '导出完成')

          return {
            success: true,
            data: '{}',
            filename: 'test.json',
            format: 'json',
            itemCount: 1
          }
        }
      )

      render(<ImportExportTab />)

      const exportButton = screen.getByRole('button', { name: /开始导出/ })
      fireEvent.click(exportButton)

      await waitFor(() => {
        expect(screen.getByText('导出成功')).toBeInTheDocument()
      })
    })

    it('应该处理导出错误', async () => {
      vi.mocked(bookmarkImportExportService.exportBookmarks).mockRejectedValue(
        new Error('导出失败')
      )

      render(<ImportExportTab />)

      const exportButton = screen.getByRole('button', { name: /开始导出/ })
      fireEvent.click(exportButton)

      await waitFor(() => {
        expect(screen.getByText('操作失败')).toBeInTheDocument()
        expect(screen.getByText('导出失败')).toBeInTheDocument()
      })
    })
  })

  describe('导入功能测试', () => {
    it('应该能够选择导入来源', () => {
      render(<ImportExportTab />)

      const importSourceSelect = screen.getByDisplayValue('JSON 文件')
      
      fireEvent.change(importSourceSelect, { target: { value: 'csv' } })
      expect(importSourceSelect).toHaveValue('csv')

      fireEvent.change(importSourceSelect, { target: { value: 'chrome' } })
      expect(importSourceSelect).toHaveValue('chrome')
    })

    it('应该能够选择导入文件', () => {
      render(<ImportExportTab />)

      const fileInput = screen.getByLabelText('选择文件')
      const mockFile = new File(['test content'], 'test.json', { type: 'application/json' })

      fireEvent.change(fileInput, { target: { files: [mockFile] } })

      expect(screen.getByText('已选择: test.json (0.0 KB)')).toBeInTheDocument()
    })

    it('应该能够切换导入选项', () => {
      render(<ImportExportTab />)

      const skipDuplicatesCheckbox = screen.getByLabelText('跳过重复项')
      const validateDataCheckbox = screen.getByLabelText('验证数据格式')

      expect(skipDuplicatesCheckbox).toBeChecked()
      expect(validateDataCheckbox).toBeChecked()

      fireEvent.click(skipDuplicatesCheckbox)
      fireEvent.click(validateDataCheckbox)

      expect(skipDuplicatesCheckbox).not.toBeChecked()
      expect(validateDataCheckbox).not.toBeChecked()
    })

    it('应该能够设置默认分类', () => {
      render(<ImportExportTab />)

      const defaultCategoryInput = screen.getByDisplayValue('导入分类')
      
      fireEvent.change(defaultCategoryInput, { target: { value: '自定义分类' } })
      expect(defaultCategoryInput).toHaveValue('自定义分类')
    })

    it('应该在未选择文件时禁用导入按钮', () => {
      render(<ImportExportTab />)

      const importButton = screen.getByRole('button', { name: /开始导入/ })
      expect(importButton).toBeDisabled()
    })

    it('应该能够执行导入操作', async () => {
      const mockImportResult = {
        success: true,
        totalItems: 2,
        importedItems: 2,
        skippedItems: 0,
        errorItems: 0,
        errors: [],
        duplicates: []
      }

      vi.mocked(bookmarkImportExportService.importBookmarks).mockResolvedValue(mockImportResult)

      render(<ImportExportTab />)

      // 选择文件
      const fileInput = screen.getByLabelText('选择文件')
      const mockFile = new File(['{"bookmarks": []}'], 'test.json', { type: 'application/json' })
      fireEvent.change(fileInput, { target: { files: [mockFile] } })

      // 点击导入
      const importButton = screen.getByRole('button', { name: /开始导入/ })
      fireEvent.click(importButton)

      expect(importButton).toHaveTextContent('导入中...')
      expect(importButton).toBeDisabled()

      await waitFor(() => {
        expect(bookmarkImportExportService.importBookmarks).toHaveBeenCalledWith(
          mockFile,
          expect.objectContaining({
            source: 'json',
            skipDuplicates: true,
            defaultCategory: '导入分类',
            validateData: true
          }),
          expect.any(Function)
        )
      })

      await waitFor(() => {
        expect(screen.getByText('导入完成')).toBeInTheDocument()
        expect(screen.getByText('总计: 2 个项目')).toBeInTheDocument()
        expect(screen.getByText('成功: 2 个')).toBeInTheDocument()
        expect(screen.getByText('跳过: 0 个')).toBeInTheDocument()
        expect(screen.getByText('错误: 0 个')).toBeInTheDocument()
      })
    })

    it('应该显示导入进度', async () => {
      vi.mocked(bookmarkImportExportService.importBookmarks).mockImplementation(
        async (data, options, callback) => {
          // 立即调用进度回调
          callback?.(30, '解析数据中...')
          callback?.(80, '导入收藏中...')

          return {
            success: true,
            totalItems: 1,
            importedItems: 1,
            skippedItems: 0,
            errorItems: 0,
            errors: [],
            duplicates: []
          }
        }
      )

      render(<ImportExportTab />)

      // 选择文件并导入
      const fileInput = screen.getByLabelText('选择文件')
      const mockFile = new File(['test'], 'test.json')
      fireEvent.change(fileInput, { target: { files: [mockFile] } })

      const importButton = screen.getByRole('button', { name: /开始导入/ })
      fireEvent.click(importButton)

      await waitFor(() => {
        expect(screen.getByText('导入完成')).toBeInTheDocument()
      })
    })

    it('应该处理导入错误', async () => {
      vi.mocked(bookmarkImportExportService.importBookmarks).mockRejectedValue(
        new Error('导入失败')
      )

      render(<ImportExportTab />)

      // 选择文件并导入
      const fileInput = screen.getByLabelText('选择文件')
      const mockFile = new File(['test'], 'test.json')
      fireEvent.change(fileInput, { target: { files: [mockFile] } })

      const importButton = screen.getByRole('button', { name: /开始导入/ })
      fireEvent.click(importButton)

      await waitFor(() => {
        expect(screen.getByText('操作失败')).toBeInTheDocument()
        expect(screen.getByText('导入失败')).toBeInTheDocument()
      })
    })

    it('应该显示导入结果详情', async () => {
      const mockImportResult = {
        success: true,
        totalItems: 5,
        importedItems: 3,
        skippedItems: 1,
        errorItems: 1,
        errors: ['第2行: 数据格式错误'],
        duplicates: ['duplicate-id']
      }

      vi.mocked(bookmarkImportExportService.importBookmarks).mockResolvedValue(mockImportResult)

      render(<ImportExportTab />)

      // 选择文件并导入
      const fileInput = screen.getByLabelText('选择文件')
      const mockFile = new File(['test'], 'test.json')
      fireEvent.change(fileInput, { target: { files: [mockFile] } })

      const importButton = screen.getByRole('button', { name: /开始导入/ })
      fireEvent.click(importButton)

      await waitFor(() => {
        expect(screen.getByText('导入完成')).toBeInTheDocument()
        expect(screen.getByText('总计: 5 个项目')).toBeInTheDocument()
        expect(screen.getByText('成功: 3 个')).toBeInTheDocument()
        expect(screen.getByText('跳过: 1 个')).toBeInTheDocument()
        expect(screen.getByText('错误: 1 个')).toBeInTheDocument()
        expect(screen.getByText('重复项: 1 个')).toBeInTheDocument()
        expect(screen.getByText('错误详情:')).toBeInTheDocument()
        expect(screen.getByText('第2行: 数据格式错误')).toBeInTheDocument()
      })
    })
  })

  describe('错误处理测试', () => {
    it('应该能够重置错误状态', async () => {
      vi.mocked(bookmarkImportExportService.exportBookmarks).mockRejectedValue(
        new Error('测试错误')
      )

      render(<ImportExportTab />)

      const exportButton = screen.getByRole('button', { name: /开始导出/ })
      fireEvent.click(exportButton)

      await waitFor(() => {
        expect(screen.getByText('操作失败')).toBeInTheDocument()
        expect(screen.getByText('测试错误')).toBeInTheDocument()
      })

      const resetButton = screen.getByText('重新开始')
      fireEvent.click(resetButton)

      expect(screen.queryByText('操作失败')).not.toBeInTheDocument()
      expect(screen.queryByText('测试错误')).not.toBeInTheDocument()
    })

    it('应该在未选择文件时显示错误', async () => {
      render(<ImportExportTab />)

      // 不选择文件，直接尝试导入
      const importButton = screen.getByRole('button', { name: /开始导入/ })
      
      // 按钮应该是禁用的，但我们可以测试内部逻辑
      expect(importButton).toBeDisabled()
    })
  })

  describe('文件下载测试', () => {
    it('应该触发文件下载', async () => {
      const mockExportResult = {
        success: true,
        data: '{"test": "data"}',
        filename: 'test.json',
        format: 'json' as const,
        itemCount: 1
      }

      vi.mocked(bookmarkImportExportService.exportBookmarks).mockResolvedValue(mockExportResult)

      render(<ImportExportTab />)

      const exportButton = screen.getByRole('button', { name: /开始导出/ })
      fireEvent.click(exportButton)

      await waitFor(() => {
        expect(screen.getByText('导出成功')).toBeInTheDocument()
        expect(screen.getByText('已导出 1 个收藏到 test.json')).toBeInTheDocument()
      })

      // 验证URL相关方法被调用
      expect(URL.createObjectURL).toHaveBeenCalled()
      expect(URL.revokeObjectURL).toHaveBeenCalled()
    })
  })
})