import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import BookmarksTab from '../src/components/BookmarksTab'

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// @ts-ignore
global.chrome = mockChrome

// Mock hooks
vi.mock('../src/hooks/useViewMode', () => ({
  useViewMode: () => ({
    viewMode: 'list',
    setViewMode: vi.fn(),
    isLoading: false
  })
}))

vi.mock('../src/utils/layoutStability', () => ({
  useViewSwitchStability: () => ({
    containerRef: { current: null },
    displayView: 'list',
    isTransitioning: false
  }),
  useScrollPositionLock: () => ({
    lockScrollPosition: vi.fn()
  })
}))

vi.mock('../src/hooks/useAdvancedSearch', () => ({
  useAdvancedSearch: () => ({
    query: '',
    setQuery: vi.fn(),
    results: [],
    suggestions: [],
    isSearching: false,
    searchTime: 0,
    addFilter: vi.fn(),
    clearFilters: vi.fn()
  })
}))

// Mock components
vi.mock('../src/components/BookmarkEditModal', () => ({
  default: () => null
}))

vi.mock('../src/components/AddBookmarkModal', () => ({
  default: () => null
}))

vi.mock('../src/components/DeleteConfirmModal', () => ({
  default: () => null
}))

vi.mock('../src/components/ViewModeSelector', () => ({
  default: () => <div data-testid="view-mode-selector">视图选择器</div>
}))

vi.mock('../src/components/VirtualBookmarkList', () => ({
  default: ({ bookmarks }: { bookmarks: any[] }) => (
    <div data-testid="virtual-bookmark-list">
      收藏列表 ({bookmarks.length} 项)
    </div>
  )
}))

describe('BookmarksTab - 基本功能测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock successful bookmark loading
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: [
        {
          id: '1',
          title: '测试收藏1',
          url: 'https://example.com',
          category: '技术',
          tags: ['React', 'TypeScript'],
          description: '测试描述'
        }
      ]
    })
  })

  it('应该成功渲染BookmarksTab组件', async () => {
    render(<BookmarksTab />)
    
    // 等待数据加载完成
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证基本元素存在
    expect(screen.getByText('收藏管理')).toBeInTheDocument()
    expect(screen.getByText('管理您的收藏内容，支持搜索、分类和多种视图模式')).toBeInTheDocument()
  })

  it('应该显示shadcn组件元素', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证shadcn Button组件
    expect(screen.getByText('添加收藏')).toBeInTheDocument()
    expect(screen.getByText('刷新')).toBeInTheDocument()
    
    // 验证shadcn Input组件（搜索框）
    expect(screen.getByPlaceholderText('搜索收藏...')).toBeInTheDocument()
    
    // 验证shadcn Select组件（分类选择器）
    expect(screen.getByText('选择分类')).toBeInTheDocument()
  })

  it('应该正确处理数据加载', async () => {
    render(<BookmarksTab />)
    
    // 验证初始加载状态
    expect(screen.getByText('加载收藏数据中...')).toBeInTheDocument()
    
    // 等待数据加载完成
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证Chrome API被调用
    expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
      type: 'GET_BOOKMARKS',
      data: {}
    })
    
    // 验证收藏列表显示
    expect(screen.getByTestId('virtual-bookmark-list')).toBeInTheDocument()
    expect(screen.getByText('收藏列表 (1 项)')).toBeInTheDocument()
  })

  it('应该显示空状态', async () => {
    // Mock empty data
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })
    
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.getByText('暂无收藏内容')).toBeInTheDocument()
    })
    
    expect(screen.getByText('开始收藏您感兴趣的网页和内容吧！')).toBeInTheDocument()
  })

  it('应该正确处理API错误', async () => {
    // Mock API error
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: false,
      error: '获取数据失败'
    })
    
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证显示空状态（因为错误时设置为空数组）
    expect(screen.getByText('暂无收藏内容')).toBeInTheDocument()
  })

  it('应该包含所有必要的shadcn组件', async () => {
    render(<BookmarksTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证Card组件结构
    expect(screen.getByText('收藏管理')).toBeInTheDocument()
    
    // 验证Button组件
    const buttons = screen.getAllByRole('button')
    expect(buttons.length).toBeGreaterThan(0)
    
    // 验证Input组件
    const searchInput = screen.getByPlaceholderText('搜索收藏...')
    expect(searchInput.tagName).toBe('INPUT')
    
    // 验证ViewModeSelector组件
    expect(screen.getByTestId('view-mode-selector')).toBeInTheDocument()
  })
})