import { describe, it, expect } from 'vitest'
import { ModelFactory } from '../src/utils/modelFactory.ts'

describe('ModelFactory', () => {
  describe('createBookmark', () => {
    it('应该创建有效的书签对象', () => {
      const input = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        description: '测试描述',
        tags: ['测试', '示例'],
        category: '工作'
      }

      const bookmark = ModelFactory.createBookmark(input)

      expect(bookmark.id).toBeDefined()
      expect(bookmark.type).toBe(input.type)
      expect(bookmark.title).toBe(input.title)
      expect(bookmark.url).toBe(input.url)
      expect(bookmark.description).toBe(input.description)
      expect(bookmark.tags).toEqual(input.tags)
      expect(bookmark.category).toBe(input.category)
      expect(bookmark.createdAt).toBeInstanceOf(Date)
      expect(bookmark.updatedAt).toBeInstanceOf(Date)
    })

    it('应该在验证失败时抛出错误', () => {
      const input = {
        type: 'url',
        title: '', // 空标题应该失败
        url: 'https://example.com'
      }

      expect(() => {
        ModelFactory.createBookmark(input)
      }).toThrow('书签数据验证失败')
    })

    it('应该在禁用验证时创建无效数据的书签', () => {
      const input = {
        type: 'url',
        title: '', // 空标题
        url: 'https://example.com'
      }

      const bookmark = ModelFactory.createBookmark(input, false)
      expect(bookmark.title).toBe('')
    })
  })

  describe('createBookmarkFromPageInfo', () => {
    it('应该从页面信息创建URL书签', () => {
      const pageInfo = {
        title: '示例页面',
        url: 'https://example.com',
        favicon: 'https://example.com/favicon.ico',
        timestamp: new Date('2023-01-01T12:00:00.000Z')
      }

      const bookmark = ModelFactory.createBookmarkFromPageInfo(pageInfo)

      expect(bookmark.type).toBe('url')
      expect(bookmark.title).toBe(pageInfo.title)
      expect(bookmark.url).toBe(pageInfo.url)
      expect(bookmark.favicon).toBe(pageInfo.favicon)
      expect(bookmark.metadata.pageTitle).toBe(pageInfo.title)
      expect(bookmark.metadata.siteName).toBe('example.com')
      expect(bookmark.metadata.publishDate).toEqual(pageInfo.timestamp)
    })

    it('应该从页面信息创建文本书签（当有选中文字时）', () => {
      const pageInfo = {
        title: '示例页面',
        url: 'https://example.com',
        selectedText: '这是选中的文字内容',
        timestamp: new Date('2023-01-01T12:00:00.000Z')
      }

      const bookmark = ModelFactory.createBookmarkFromPageInfo(pageInfo)

      expect(bookmark.type).toBe('text')
      expect(bookmark.content).toBe(pageInfo.selectedText)
      expect(bookmark.metadata.wordCount).toBe(pageInfo.selectedText.length)
    })

    it('应该合并额外数据', () => {
      const pageInfo = {
        title: '示例页面',
        url: 'https://example.com',
        timestamp: new Date()
      }

      const additionalData = {
        description: '自定义描述',
        tags: ['自定义标签'],
        category: '自定义分类'
      }

      const bookmark = ModelFactory.createBookmarkFromPageInfo(pageInfo, additionalData)

      expect(bookmark.description).toBe(additionalData.description)
      expect(bookmark.tags).toEqual(additionalData.tags)
      expect(bookmark.category).toBe(additionalData.category)
    })
  })

  describe('createCategory', () => {
    it('应该创建有效的分类对象', () => {
      const input = {
        name: '工作',
        description: '工作相关的书签',
        color: '#FF5733'
      }

      const category = ModelFactory.createCategory(input)

      expect(category.id).toBeDefined()
      expect(category.name).toBe(input.name)
      expect(category.description).toBe(input.description)
      expect(category.color).toBe(input.color)
      expect(category.bookmarkCount).toBe(0)
      expect(category.createdAt).toBeInstanceOf(Date)
      expect(category.updatedAt).toBeInstanceOf(Date)
    })

    it('应该在验证失败时抛出错误', () => {
      const input = {
        name: '' // 空名称应该失败
      }

      expect(() => {
        ModelFactory.createCategory(input)
      }).toThrow('分类数据验证失败')
    })
  })

  describe('createTag', () => {
    it('应该创建有效的标签对象', () => {
      const input = {
        name: '重要',
        color: '#FF5733'
      }

      const tag = ModelFactory.createTag(input)

      expect(tag.id).toBeDefined()
      expect(tag.name).toBe(input.name)
      expect(tag.color).toBe(input.color)
      expect(tag.usageCount).toBe(0)
      expect(tag.createdAt).toBeInstanceOf(Date)
      expect(tag.updatedAt).toBeInstanceOf(Date)
    })

    it('应该在验证失败时抛出错误', () => {
      const input = {
        name: '' // 空名称应该失败
      }

      expect(() => {
        ModelFactory.createTag(input)
      }).toThrow('标签数据验证失败')
    })
  })

  describe('createDefaultCategory', () => {
    it('应该创建默认分类', () => {
      const category = ModelFactory.createDefaultCategory()

      expect(category.name).toBe('默认分类')
      expect(category.description).toBe('系统默认分类')
      expect(category.color).toBe('#6B7280')
    })
  })

  describe('createTagsFromNames', () => {
    it('应该从名称数组创建标签', () => {
      const tagNames = ['标签1', '标签2', '标签3']
      const tags = ModelFactory.createTagsFromNames(tagNames)

      expect(tags).toHaveLength(3)
      expect(tags[0].name).toBe('标签1')
      expect(tags[1].name).toBe('标签2')
      expect(tags[2].name).toBe('标签3')
    })

    it('应该过滤空名称', () => {
      const tagNames = ['标签1', '', '  ', '标签2']
      const tags = ModelFactory.createTagsFromNames(tagNames)

      expect(tags).toHaveLength(2)
      expect(tags[0].name).toBe('标签1')
      expect(tags[1].name).toBe('标签2')
    })
  })

  describe('updateBookmark', () => {
    it('应该更新书签对象', async () => {
      const originalBookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '原标题',
        url: 'https://example.com',
        description: '原描述',
        tags: ['原标签'],
        category: '原分类'
      })

      // 等待1毫秒确保时间戳不同
      await new Promise(resolve => setTimeout(resolve, 1))

      const updates = {
        title: '新标题',
        description: '新描述',
        tags: ['新标签1', '新标签2']
      }

      const updatedBookmark = ModelFactory.updateBookmark(originalBookmark, updates)

      expect(updatedBookmark.title).toBe(updates.title)
      expect(updatedBookmark.description).toBe(updates.description)
      expect(updatedBookmark.tags).toEqual(updates.tags)
      expect(updatedBookmark.category).toBe(originalBookmark.category) // 未更新的字段保持不变
      expect(updatedBookmark.updatedAt.getTime()).toBeGreaterThanOrEqual(originalBookmark.updatedAt.getTime())
    })

    it('应该不修改原对象', () => {
      const originalBookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '原标题',
        url: 'https://example.com'
      })

      const originalTitle = originalBookmark.title
      const updates = { title: '新标题' }

      ModelFactory.updateBookmark(originalBookmark, updates)

      expect(originalBookmark.title).toBe(originalTitle) // 原对象未被修改
    })
  })

  describe('generateBookmarkSummary', () => {
    it('应该使用描述作为摘要', () => {
      const bookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        description: '这是书签的描述'
      })

      const summary = ModelFactory.generateBookmarkSummary(bookmark)
      expect(summary).toBe('这是书签的描述')
    })

    it('应该在没有描述时使用内容', () => {
      const bookmark = ModelFactory.createBookmark({
        type: 'text',
        title: '测试笔记',
        content: '这是笔记的内容'
      }, false)

      const summary = ModelFactory.generateBookmarkSummary(bookmark)
      expect(summary).toBe('这是笔记的内容')
    })

    it('应该截断过长的摘要', () => {
      const longDescription = 'a'.repeat(150)
      const bookmark = ModelFactory.createBookmark({
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        description: longDescription
      })

      const summary = ModelFactory.generateBookmarkSummary(bookmark, 100)
      expect(summary.length).toBe(100)
      expect(summary.endsWith('...')).toBe(true)
    })
  })

  describe('calculateBookmarkSimilarity', () => {
    it('应该检测相同URL的书签', () => {
      const bookmark1 = ModelFactory.createBookmark({
        type: 'url',
        title: '书签1',
        url: 'https://example.com'
      })

      const bookmark2 = ModelFactory.createBookmark({
        type: 'url',
        title: '书签2',
        url: 'https://example.com'
      })

      const similarity = ModelFactory.calculateBookmarkSimilarity(bookmark1, bookmark2)
      expect(similarity).toBeGreaterThan(0.8) // 应该有很高的相似度
    })

    it('应该检测相似标题的书签', () => {
      const bookmark1 = ModelFactory.createBookmark({
        type: 'url',
        title: '如何学习JavaScript',
        url: 'https://example1.com'
      })

      const bookmark2 = ModelFactory.createBookmark({
        type: 'url',
        title: '如何学习JavaScript编程',
        url: 'https://example2.com'
      })

      const similarity = ModelFactory.calculateBookmarkSimilarity(bookmark1, bookmark2)
      expect(similarity).toBeGreaterThan(0.4) // 应该有一定的相似度
    })

    it('应该检测完全不同的书签', () => {
      const bookmark1 = ModelFactory.createBookmark({
        type: 'url',
        title: 'JavaScript教程',
        url: 'https://js-tutorial.com'
      })

      const bookmark2 = ModelFactory.createBookmark({
        type: 'url',
        title: '美食食谱',
        url: 'https://food-recipes.com'
      })

      const similarity = ModelFactory.calculateBookmarkSimilarity(bookmark1, bookmark2)
      expect(similarity).toBeLessThan(0.3) // 应该有很低的相似度
    })
  })
})