// 验证图标修改的脚本
// 检查所有相关文件中的五角星是否已经改为对勾

const fs = require('fs')
const path = require('path')

console.log('🔍 验证图标修改...\n')

// 需要检查的文件列表
const filesToCheck = [
  'src/services/tabStatusManager.ts',
  'src/services/bookmarkStatusService.ts',
  'src/background/messageHandler.ts'
]

let allCorrect = true

filesToCheck.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8')
    
    // 检查是否还有五角星
    const hasStars = content.includes('★')
    const hasCheckmarks = content.includes('✓')
    
    console.log(`📁 ${filePath}:`)
    
    if (hasStars) {
      console.log('  ❌ 仍然包含五角星 (★)')
      allCorrect = false
    } else {
      console.log('  ✅ 已移除所有五角星')
    }
    
    if (hasCheckmarks) {
      console.log('  ✅ 包含对勾图标 (✓)')
    } else {
      console.log('  ❌ 未找到对勾图标')
      allCorrect = false
    }
    
    console.log('')
  } else {
    console.log(`❌ 文件不存在: ${filePath}`)
    allCorrect = false
  }
})

// 检查测试文件
const testFiles = [
  'tests/tabStatusManager.test.js',
  'tests/integration/ui-fixes.integration.test.js',
  'tests/bookmark-status-sync.test.js'
]

console.log('🧪 检查测试文件...\n')

testFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8')
    
    const hasStars = content.includes("'★'") || content.includes('"★"')
    const hasCheckmarks = content.includes("'✓'") || content.includes('"✓"')
    
    console.log(`📁 ${filePath}:`)
    
    if (hasStars) {
      console.log('  ❌ 测试中仍然期望五角星')
      allCorrect = false
    } else {
      console.log('  ✅ 测试已更新为期望对勾')
    }
    
    console.log('')
  }
})

if (allCorrect) {
  console.log('🎉 所有文件都已正确修改！插件图标现在使用对勾（✓）而不是五角星（★）')
} else {
  console.log('⚠️  还有一些文件需要修改')
}

console.log('\n📋 修改总结:')
console.log('- 插件栏收藏状态图标: ★ → ✓')
console.log('- 图标提示文字: "已收藏 ★" → "已收藏 ✓"')
console.log('- 控制台日志信息: "已收藏 ★" → "已收藏 ✓"')
console.log('- 测试期望值已相应更新')