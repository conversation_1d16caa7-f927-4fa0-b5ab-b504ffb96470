// CategoryModal组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import CategoryModal from '../src/components/CategoryModal'
import type { Category } from '../src/types'

// Mock CategoryForm组件
vi.mock('../src/components/CategoryForm', () => ({
  default: ({ onSubmit, onCancel, loading, mode }: any) => (
    <div data-testid="category-form">
      <div>Mode: {mode}</div>
      <div>Loading: {loading ? 'true' : 'false'}</div>
      <button onClick={() => onSubmit({ name: 'Test Category', color: '#3B82F6' })}>
        Submit
      </button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  )
}))

describe('CategoryModal', () => {
  const mockCategory: Category = {
    id: 'test-category-1',
    name: '测试分类',
    description: '测试分类描述',
    color: '#3B82F6',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    bookmarkCount: 5
  }

  const defaultProps = {
    isOpen: true,
    type: 'create' as const,
    onClose: vi.fn(),
    loading: false
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // 清理body的overflow样式
    document.body.style.overflow = 'unset'
  })

  describe('渲染测试', () => {
    it('应该在isOpen为false时不渲染任何内容', () => {
      render(
        <CategoryModal
          {...defaultProps}
          isOpen={false}
        />
      )

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })

    it('应该在isOpen为true时渲染模态窗口', () => {
      render(<CategoryModal {...defaultProps} />)

      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByText('创建新分类')).toBeInTheDocument()
    })

    it('应该根据type显示正确的标题', () => {
      const { rerender } = render(<CategoryModal {...defaultProps} type="create" />)
      expect(screen.getByText('创建新分类')).toBeInTheDocument()

      rerender(<CategoryModal {...defaultProps} type="edit" />)
      expect(screen.getByText('编辑分类')).toBeInTheDocument()

      rerender(<CategoryModal {...defaultProps} type="delete" />)
      expect(screen.getByText('删除分类')).toBeInTheDocument()
    })
  })

  describe('创建模式测试', () => {
    it('应该渲染CategoryForm组件', () => {
      const mockOnSave = vi.fn()
      
      render(
        <CategoryModal
          {...defaultProps}
          type="create"
          onSave={mockOnSave}
        />
      )

      expect(screen.getByTestId('category-form')).toBeInTheDocument()
      expect(screen.getByText('Mode: create')).toBeInTheDocument()
    })

    it('应该处理表单提交', async () => {
      const mockOnSave = vi.fn().mockResolvedValue(undefined)
      
      render(
        <CategoryModal
          {...defaultProps}
          type="create"
          onSave={mockOnSave}
        />
      )

      fireEvent.click(screen.getByText('Submit'))

      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalledWith({
          name: 'Test Category',
          color: '#3B82F6'
        })
      })
    })
  })

  describe('编辑模式测试', () => {
    it('应该渲染CategoryForm组件并传递分类数据', () => {
      const mockOnSave = vi.fn()
      
      render(
        <CategoryModal
          {...defaultProps}
          type="edit"
          category={mockCategory}
          onSave={mockOnSave}
        />
      )

      expect(screen.getByTestId('category-form')).toBeInTheDocument()
      expect(screen.getByText('Mode: edit')).toBeInTheDocument()
    })
  })

  describe('删除模式测试', () => {
    it('应该渲染删除确认内容', () => {
      const mockOnDelete = vi.fn()
      
      render(
        <CategoryModal
          {...defaultProps}
          type="delete"
          category={mockCategory}
          bookmarkCount={5}
          onDelete={mockOnDelete}
        />
      )

      expect(screen.getByText('确认删除分类')).toBeInTheDocument()
      expect(screen.getByText('测试分类')).toBeInTheDocument()
      expect(screen.getByText(/将 5 个书签移动到"默认分类"/)).toBeInTheDocument()
    })

    it('应该显示空分类的提示', () => {
      const mockOnDelete = vi.fn()
      
      render(
        <CategoryModal
          {...defaultProps}
          type="delete"
          category={mockCategory}
          bookmarkCount={0}
          onDelete={mockOnDelete}
        />
      )

      expect(screen.getByText(/不会影响任何书签（此分类为空）/)).toBeInTheDocument()
    })

    it('应该处理删除确认', async () => {
      const mockOnDelete = vi.fn().mockResolvedValue(undefined)
      
      render(
        <CategoryModal
          {...defaultProps}
          type="delete"
          category={mockCategory}
          onDelete={mockOnDelete}
        />
      )

      fireEvent.click(screen.getByText('确认删除'))

      await waitFor(() => {
        expect(mockOnDelete).toHaveBeenCalled()
      })
    })
  })

  describe('交互测试', () => {
    it('应该在点击关闭按钮时调用onClose', () => {
      const mockOnClose = vi.fn()
      
      render(
        <CategoryModal
          {...defaultProps}
          onClose={mockOnClose}
        />
      )

      fireEvent.click(screen.getByLabelText('关闭'))
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('应该在点击背景时调用onClose', () => {
      const mockOnClose = vi.fn()
      
      render(
        <CategoryModal
          {...defaultProps}
          onClose={mockOnClose}
        />
      )

      // 点击背景遮罩 - 找到具有onClick处理器的背景元素
      const backdrop = screen.getByRole('dialog').firstChild as HTMLElement
      fireEvent.click(backdrop)
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('应该在按ESC键时调用onClose', () => {
      const mockOnClose = vi.fn()
      
      render(
        <CategoryModal
          {...defaultProps}
          onClose={mockOnClose}
        />
      )

      fireEvent.keyDown(document, { key: 'Escape' })
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('应该在loading时禁用关闭操作', () => {
      const mockOnClose = vi.fn()
      
      render(
        <CategoryModal
          {...defaultProps}
          loading={true}
          onClose={mockOnClose}
        />
      )

      // 尝试点击关闭按钮
      const closeButton = screen.getByLabelText('关闭')
      expect(closeButton).toBeDisabled()

      // 尝试按ESC键
      fireEvent.keyDown(document, { key: 'Escape' })
      expect(mockOnClose).not.toHaveBeenCalled()
    })
  })

  describe('加载状态测试', () => {
    it('应该显示加载状态', () => {
      render(
        <CategoryModal
          {...defaultProps}
          type="delete"
          category={mockCategory}
          loading={true}
          onDelete={vi.fn()}
        />
      )

      expect(screen.getByText('删除中...')).toBeInTheDocument()
    })

    it('应该传递loading状态给CategoryForm', () => {
      render(
        <CategoryModal
          {...defaultProps}
          type="create"
          loading={true}
          onSave={vi.fn()}
        />
      )

      expect(screen.getByText('Loading: true')).toBeInTheDocument()
    })
  })

  describe('无障碍性测试', () => {
    it('应该设置正确的ARIA属性', () => {
      render(<CategoryModal {...defaultProps} />)

      const dialog = screen.getByRole('dialog')
      expect(dialog).toHaveAttribute('aria-modal', 'true')
      expect(dialog).toHaveAttribute('aria-labelledby', 'modal-title')
    })

    it('应该在打开时阻止背景滚动', () => {
      render(<CategoryModal {...defaultProps} />)
      expect(document.body.style.overflow).toBe('hidden')
    })

    it('应该在关闭时恢复背景滚动', () => {
      const { unmount } = render(<CategoryModal {...defaultProps} />)
      unmount()
      expect(document.body.style.overflow).toBe('unset')
    })
  })

  describe('错误处理测试', () => {
    it('应该在没有onSave时不渲染表单', () => {
      // 不传递onSave属性
      render(
        <CategoryModal
          {...defaultProps}
          type="create"
        />
      )

      expect(screen.queryByTestId('category-form')).not.toBeInTheDocument()
    })

    it('应该在删除模式没有category时不渲染内容', () => {
      render(
        <CategoryModal
          {...defaultProps}
          type="delete"
          onDelete={vi.fn()}
        />
      )

      expect(screen.queryByText('确认删除分类')).not.toBeInTheDocument()
    })
  })
})