// 标签工具函数单元测试

import { describe, it, expect } from 'vitest'
import { TagUtils } from '../src/utils/tagUtils'

describe('TagUtils', () => {
  // 测试数据
  const mockTags = [
    {
      id: 'tag1',
      name: '技术',
      color: '#3B82F6',
      usageCount: 25,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: 'tag2',
      name: '学习',
      color: '#10B981',
      usageCount: 18,
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02')
    },
    {
      id: 'tag3',
      name: '工具',
      color: '#F59E0B',
      usageCount: 12,
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-03')
    },
    {
      id: 'tag4',
      name: '新闻',
      color: '#EF4444',
      usageCount: 8,
      createdAt: new Date('2024-01-04'),
      updatedAt: new Date('2024-01-04')
    }
  ]

  describe('validateTagName', () => {
    it('应该验证有效的标签名称', () => {
      const result = TagUtils.validateTagName('有效标签')
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该拒绝空标签名称', () => {
      const result = TagUtils.validateTagName('')
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].code).toBe('NAME_REQUIRED')
    })

    it('应该拒绝只包含空白字符的标签名称', () => {
      const result = TagUtils.validateTagName('   ')
      
      expect(result.isValid).toBe(false)
      // 空白字符会被trim()处理，所以实际上会触发NAME_REQUIRED错误
      expect(result.errors.some(e => e.code === 'NAME_REQUIRED')).toBe(true)
    })

    it('应该拒绝过长的标签名称', () => {
      const longName = 'a'.repeat(51)
      const result = TagUtils.validateTagName(longName)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'NAME_TOO_LONG')).toBe(true)
    })

    it('应该拒绝包含特殊字符的标签名称', () => {
      const invalidNames = ['标签<script>', '标签"引号', "标签'单引号", '标签&符号']
      
      invalidNames.forEach(name => {
        const result = TagUtils.validateTagName(name)
        expect(result.isValid).toBe(false)
        expect(result.errors.some(e => e.code === 'INVALID_CHARACTERS')).toBe(true)
      })
    })

    it('应该接受包含数字和中文的标签名称', () => {
      const validNames = ['技术123', 'React开发', '前端-后端', '学习_笔记']
      
      validNames.forEach(name => {
        const result = TagUtils.validateTagName(name)
        expect(result.isValid).toBe(true)
      })
    })
  })

  describe('normalizeTagName', () => {
    it('应该去除首尾空白字符', () => {
      const result = TagUtils.normalizeTagName('  标签名称  ')
      expect(result).toBe('标签名称')
    })

    it('应该将多个空白字符替换为单个空格', () => {
      const result = TagUtils.normalizeTagName('标签   名称')
      expect(result).toBe('标签 名称')
    })

    it('应该转换为小写', () => {
      const result = TagUtils.normalizeTagName('JavaScript')
      expect(result).toBe('javascript')
    })

    it('应该处理空字符串', () => {
      const result = TagUtils.normalizeTagName('')
      expect(result).toBe('')
    })

    it('应该处理null和undefined', () => {
      expect(TagUtils.normalizeTagName(null)).toBe('')
      expect(TagUtils.normalizeTagName(undefined)).toBe('')
    })
  })

  describe('sortTags', () => {
    it('应该按名称升序排序', () => {
      const result = TagUtils.sortTags(mockTags, 'name-asc')
      
      expect(result[0].name).toBe('工具')
      expect(result[1].name).toBe('技术')
      expect(result[2].name).toBe('新闻')
      expect(result[3].name).toBe('学习')
    })

    it('应该按名称降序排序', () => {
      const result = TagUtils.sortTags(mockTags, 'name-desc')
      
      expect(result[0].name).toBe('学习')
      expect(result[1].name).toBe('新闻')
      expect(result[2].name).toBe('技术')
      expect(result[3].name).toBe('工具')
    })

    it('应该按使用次数升序排序', () => {
      const result = TagUtils.sortTags(mockTags, 'usage-asc')
      
      expect(result[0].usageCount).toBe(8)
      expect(result[1].usageCount).toBe(12)
      expect(result[2].usageCount).toBe(18)
      expect(result[3].usageCount).toBe(25)
    })

    it('应该按使用次数降序排序', () => {
      const result = TagUtils.sortTags(mockTags, 'usage-desc')
      
      expect(result[0].usageCount).toBe(25)
      expect(result[1].usageCount).toBe(18)
      expect(result[2].usageCount).toBe(12)
      expect(result[3].usageCount).toBe(8)
    })

    it('应该按创建时间排序', () => {
      const result = TagUtils.sortTags(mockTags, 'created-asc')
      
      expect(result[0].createdAt.getTime()).toBeLessThan(result[1].createdAt.getTime())
      expect(result[1].createdAt.getTime()).toBeLessThan(result[2].createdAt.getTime())
    })

    it('应该不修改原数组', () => {
      const originalLength = mockTags.length
      const originalFirst = mockTags[0].name
      
      TagUtils.sortTags(mockTags, 'name-desc')
      
      expect(mockTags).toHaveLength(originalLength)
      expect(mockTags[0].name).toBe(originalFirst)
    })
  })

  describe('filterTags', () => {
    it('应该根据名称筛选标签', () => {
      const result = TagUtils.filterTags(mockTags, '技')
      
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('技术')
    })

    it('应该忽略大小写进行筛选', () => {
      const tagsWithEnglish = [
        ...mockTags,
        {
          id: 'tag5',
          name: 'JavaScript',
          color: '#F59E0B',
          usageCount: 10,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]
      
      const result = TagUtils.filterTags(tagsWithEnglish, 'javascript')
      
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('JavaScript')
    })

    it('应该在空查询时返回所有标签', () => {
      const result = TagUtils.filterTags(mockTags, '')
      
      expect(result).toHaveLength(mockTags.length)
      expect(result).toEqual(mockTags)
    })

    it('应该在空白查询时返回所有标签', () => {
      const result = TagUtils.filterTags(mockTags, '   ')
      
      expect(result).toHaveLength(mockTags.length)
    })

    it('应该返回部分匹配的结果', () => {
      const result = TagUtils.filterTags(mockTags, '学')
      
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('学习')
    })
  })

  describe('getTagSuggestions', () => {
    it('应该返回匹配的标签建议', () => {
      const result = TagUtils.getTagSuggestions('技', mockTags)
      
      expect(result).toContain('技术')
    })

    it('应该按相关性排序建议', () => {
      const tagsWithSimilar = [
        ...mockTags,
        {
          id: 'tag5',
          name: '技术文档',
          color: '#F59E0B',
          usageCount: 5,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'tag6',
          name: '前端技术',
          color: '#F59E0B',
          usageCount: 3,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]
      
      const result = TagUtils.getTagSuggestions('技术', tagsWithSimilar)
      
      // 完全匹配优先，然后是开头匹配
      expect(result[0]).toBe('技术') // 完全匹配优先
      expect(result[1]).toBe('技术文档') // 开头匹配次之
    })

    it('应该限制返回的建议数量', () => {
      const result = TagUtils.getTagSuggestions('学', mockTags, 2)
      
      expect(result.length).toBeLessThanOrEqual(2)
    })

    it('应该在空输入时返回空数组', () => {
      const result = TagUtils.getTagSuggestions('', mockTags)
      
      expect(result).toEqual([])
    })

    it('应该在没有匹配时返回空数组', () => {
      const result = TagUtils.getTagSuggestions('不存在的标签', mockTags)
      
      expect(result).toEqual([])
    })
  })

  describe('deduplicateTags', () => {
    it('应该去除重复的标签', () => {
      const tags = ['技术', '学习', '技术', '工具', '学习']
      const result = TagUtils.deduplicateTags(tags)
      
      expect(result).toEqual(['技术', '学习', '工具'])
    })

    it('应该去除空白标签', () => {
      const tags = ['技术', '', '  ', '学习', null, undefined]
      const result = TagUtils.deduplicateTags(tags.filter(Boolean))
      
      expect(result).toEqual(['技术', '学习'])
    })

    it('应该保留原始格式但去除首尾空白', () => {
      const tags = ['  技术  ', '学习', ' 技术 ']
      const result = TagUtils.deduplicateTags(tags)
      
      expect(result).toEqual(['技术', '学习'])
    })

    it('应该处理空数组', () => {
      const result = TagUtils.deduplicateTags([])
      
      expect(result).toEqual([])
    })
  })

  describe('groupTagsByUsage', () => {
    it('应该按使用次数分组标签', () => {
      const result = TagUtils.groupTagsByUsage(mockTags)
      
      expect(result).toHaveLength(2) // 高频、中频（没有低频和未使用）
      
      // 验证高频组
      const highFreq = result.find(group => group.range.includes('20+'))
      expect(highFreq.tags).toHaveLength(1)
      expect(highFreq.tags[0].name).toBe('技术')
      
      // 验证中频组
      const midFreq = result.find(group => group.range.includes('5-19'))
      expect(midFreq.tags).toHaveLength(3)
    })

    it('应该过滤掉空分组', () => {
      const tagsWithoutZeroUsage = mockTags.filter(tag => tag.usageCount > 0)
      const result = TagUtils.groupTagsByUsage(tagsWithoutZeroUsage)
      
      // 不应该有未使用分组
      const zeroUsage = result.find(group => group.range.includes('未使用'))
      expect(zeroUsage).toBeUndefined()
    })

    it('应该处理空数组', () => {
      const result = TagUtils.groupTagsByUsage([])
      
      expect(result).toEqual([])
    })
  })

  describe('generateTagColor', () => {
    it('应该为相同名称生成相同颜色', () => {
      const color1 = TagUtils.generateTagColor('技术')
      const color2 = TagUtils.generateTagColor('技术')
      
      expect(color1).toBe(color2)
    })

    it('应该为不同名称生成不同颜色', () => {
      const color1 = TagUtils.generateTagColor('技术')
      const color2 = TagUtils.generateTagColor('学习')
      
      expect(color1).not.toBe(color2)
    })

    it('应该生成有效的十六进制颜色', () => {
      const color = TagUtils.generateTagColor('测试标签')
      
      expect(color).toMatch(/^#[0-9A-F]{6}$/i)
    })

    it('应该处理空字符串', () => {
      const color = TagUtils.generateTagColor('')
      
      expect(color).toMatch(/^#[0-9A-F]{6}$/i)
    })

    it('应该处理特殊字符', () => {
      const color = TagUtils.generateTagColor('标签@#$%')
      
      expect(color).toMatch(/^#[0-9A-F]{6}$/i)
    })
  })

  describe('calculateTagSimilarity', () => {
    it('应该计算标签相似度', () => {
      const tag1 = mockTags[0] // 技术
      const tag2 = {
        id: 'tag5',
        name: '技术文档',
        color: '#3B82F6',
        usageCount: 10,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      const similarity = TagUtils.calculateTagSimilarity(tag1, tag2)
      
      expect(similarity).toBeGreaterThan(0)
      expect(similarity).toBeLessThanOrEqual(1)
    })

    it('应该为相同标签返回高相似度', () => {
      const tag1 = mockTags[0]
      const tag2 = { ...mockTags[0], id: 'different-id' }
      
      const similarity = TagUtils.calculateTagSimilarity(tag1, tag2)
      
      expect(similarity).toBeGreaterThan(0.8)
    })

    it('应该为完全不同的标签返回低相似度', () => {
      const tag1 = mockTags[0] // 技术
      const tag2 = mockTags[3] // 新闻
      
      const similarity = TagUtils.calculateTagSimilarity(tag1, tag2)
      
      expect(similarity).toBeLessThan(0.5)
    })
  })

  describe('getSimilarTags', () => {
    it('应该返回相似的标签', () => {
      const targetTag = mockTags[0] // 技术
      const allTags = [
        ...mockTags,
        {
          id: 'tag5',
          name: '技术文档',
          color: '#3B82F6',
          usageCount: 10,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]
      
      const result = TagUtils.getSimilarTags(targetTag, allTags, 0.3)
      
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].tag.name).toBe('技术文档')
      expect(result[0].similarity).toBeGreaterThan(0.3)
    })

    it('应该排除目标标签本身', () => {
      const targetTag = mockTags[0]
      const result = TagUtils.getSimilarTags(targetTag, mockTags, 0.1)
      
      expect(result.every(item => item.tag.id !== targetTag.id)).toBe(true)
    })

    it('应该按相似度降序排序', () => {
      const targetTag = mockTags[0]
      const result = TagUtils.getSimilarTags(targetTag, mockTags, 0.1)
      
      for (let i = 1; i < result.length; i++) {
        expect(result[i-1].similarity).toBeGreaterThanOrEqual(result[i].similarity)
      }
    })

    it('应该限制返回数量', () => {
      const targetTag = mockTags[0]
      const result = TagUtils.getSimilarTags(targetTag, mockTags, 0.1, 2)
      
      expect(result.length).toBeLessThanOrEqual(2)
    })
  })

  describe('validateBatchOperation', () => {
    it('应该验证有效的批量删除操作', () => {
      const result = TagUtils.validateBatchOperation(['tag1', 'tag2'], 'delete')
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该验证有效的批量合并操作', () => {
      const result = TagUtils.validateBatchOperation(['tag1', 'tag2'], 'merge')
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该拒绝空的标签ID数组', () => {
      const result = TagUtils.validateBatchOperation([], 'delete')
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'NO_TAGS_SELECTED')).toBe(true)
    })

    it('应该拒绝合并操作中标签数量不足', () => {
      const result = TagUtils.validateBatchOperation(['tag1'], 'merge')
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'INSUFFICIENT_TAGS_FOR_MERGE')).toBe(true)
    })

    it('应该拒绝过多的标签', () => {
      const manyTags = Array.from({ length: 101 }, (_, i) => `tag${i}`)
      const result = TagUtils.validateBatchOperation(manyTags, 'delete')
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'TOO_MANY_TAGS')).toBe(true)
    })
  })
})