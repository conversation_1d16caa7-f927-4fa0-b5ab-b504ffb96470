// 收藏工具栏组件 - 可复用的搜索、筛选、排序工具栏

import React from 'react'
import { Search, Plus, RefreshCw } from 'lucide-react'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Card, CardContent } from './ui/card'
import BookmarkSortSelector from './BookmarkSortSelector'
import ViewModeSelector from './ViewModeSelector'
import type { BookmarkSortOption } from './BookmarkSortSelector'
import type { ViewMode } from './ViewModeSelector'

/**
 * 工具栏组件属性
 */
interface BookmarkToolbarProps {
  /** 搜索相关 */
  searchQuery: string
  onSearchChange: (query: string) => void
  isSearching?: boolean
  suggestions?: string[]
  onSuggestionClick?: (suggestion: string) => void
  
  /** 分类筛选相关 */
  selectedCategory: string
  onCategoryChange: (category: string) => void
  categories: string[]
  
  /** 排序相关 */
  sortOption: BookmarkSortOption
  onSortChange: (option: BookmarkSortOption) => void
  
  /** 视图模式相关 */
  viewMode: ViewMode
  onViewModeChange: (mode: ViewMode) => void
  viewModeLoading?: boolean
  
  /** 操作按钮相关 */
  onAddClick?: () => void
  onRefreshClick?: () => void
  showAddButton?: boolean
  showRefreshButton?: boolean
  
  /** 自定义样式 */
  className?: string
  
  /** 布局选项 */
  layout?: 'horizontal' | 'vertical'
  compact?: boolean
}

/**
 * 收藏工具栏组件
 * 提供搜索、筛选、排序、视图切换等功能的统一工具栏
 * 可在不同页面复用
 */
const BookmarkToolbar: React.FC<BookmarkToolbarProps> = ({
  searchQuery,
  onSearchChange,
  isSearching = false,
  suggestions = [],
  onSuggestionClick,
  
  selectedCategory,
  onCategoryChange,
  categories,
  
  sortOption,
  onSortChange,
  
  viewMode,
  onViewModeChange,
  viewModeLoading = false,
  
  onAddClick,
  onRefreshClick,
  showAddButton = true,
  showRefreshButton = true,
  
  className = '',
  layout = 'horizontal',
  compact = false
}) => {
  
  const toolbarContent = (
    <>
      {/* 搜索框 */}
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          type="text"
          placeholder="搜索收藏..."
          className={`pl-10 ${isSearching ? 'bg-muted' : ''}`}
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
        />
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        )}
        
        {/* 搜索建议下拉框 */}
        {suggestions.length > 0 && searchQuery.trim() && onSuggestionClick && (
          <Card className="absolute top-full left-0 right-0 mt-1 z-10 max-h-48 overflow-y-auto">
            <CardContent className="p-0">
              {suggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  className="w-full justify-start text-sm border-b last:border-b-0 rounded-none"
                  onClick={() => onSuggestionClick(suggestion)}
                >
                  {suggestion}
                </Button>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
      
      {/* 分类选择器 */}
      <Select value={selectedCategory} onValueChange={onCategoryChange}>
        <SelectTrigger className={compact ? "w-[120px]" : "w-[140px]"}>
          <SelectValue placeholder="选择分类" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">所有分类</SelectItem>
          {categories.map(category => (
            <SelectItem key={category} value={category}>{category}</SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* 排序选择器 */}
      <BookmarkSortSelector
        value={sortOption}
        onChange={onSortChange}
        className={compact ? "w-[120px]" : ""}
      />
      
      {/* 视图模式选择器 */}
      <div className="view-mode-selector-container">
        {viewModeLoading ? (
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            {!compact && <span className="text-sm text-muted-foreground">加载视图选项...</span>}
          </div>
        ) : (
          <ViewModeSelector
            currentMode={viewMode}
            onModeChange={onViewModeChange}
          />
        )}
      </div>
      
      {/* 操作按钮组 */}
      {(showAddButton || showRefreshButton) && (
        <div className="flex items-center gap-2">
          {showAddButton && onAddClick && (
            <Button onClick={onAddClick} className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              {!compact && "添加收藏"}
            </Button>
          )}
          
          {showRefreshButton && onRefreshClick && (
            <Button variant="outline" onClick={onRefreshClick}>
              <RefreshCw className="w-4 h-4" />
              {!compact && <span className="ml-2">刷新</span>}
            </Button>
          )}
        </div>
      )}
    </>
  )
  
  if (layout === 'vertical') {
    return (
      <div className={`space-y-4 ${className}`}>
        {/* 第一行：搜索框 */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              type="text"
              placeholder="搜索收藏..."
              className={`pl-10 ${isSearching ? 'bg-muted' : ''}`}
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
            />
            {isSearching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              </div>
            )}
          </div>
          
          {/* 操作按钮组 */}
          {(showAddButton || showRefreshButton) && (
            <div className="flex items-center gap-2">
              {showAddButton && onAddClick && (
                <Button onClick={onAddClick} className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  添加收藏
                </Button>
              )}
              
              {showRefreshButton && onRefreshClick && (
                <Button variant="outline" onClick={onRefreshClick}>
                  <RefreshCw className="w-4 h-4" />
                  <span className="ml-2">刷新</span>
                </Button>
              )}
            </div>
          )}
        </div>
        
        {/* 第二行：筛选和视图控件 */}
        <div className="flex items-center gap-4">
          {/* 分类选择器 */}
          <Select value={selectedCategory} onValueChange={onCategoryChange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有分类</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* 排序选择器 */}
          <BookmarkSortSelector
            value={sortOption}
            onChange={onSortChange}
          />
          
          {/* 视图模式选择器 */}
          <div className="view-mode-selector-container">
            {viewModeLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span className="text-sm text-muted-foreground">加载视图选项...</span>
              </div>
            ) : (
              <ViewModeSelector
                currentMode={viewMode}
                onModeChange={onViewModeChange}
              />
            )}
          </div>
        </div>
        
        {/* 搜索建议下拉框 */}
        {suggestions.length > 0 && searchQuery.trim() && onSuggestionClick && (
          <Card className="max-h-48 overflow-y-auto">
            <CardContent className="p-0">
              {suggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  className="w-full justify-start text-sm border-b last:border-b-0 rounded-none"
                  onClick={() => onSuggestionClick(suggestion)}
                >
                  {suggestion}
                </Button>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    )
  }
  
  // 水平布局（默认）
  return (
    <div className={`flex items-center gap-4 ${className}`}>
      {toolbarContent}
    </div>
  )
}

export default BookmarkToolbar

// 导出类型定义
export type { BookmarkToolbarProps }