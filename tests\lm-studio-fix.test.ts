/**
 * 测试LM Studio提供商类型修复
 */
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'
import type { AIProviderConfig } from '../src/types/ai'

// Mock fetch
global.fetch = vi.fn()

describe('LM Studio提供商类型修复测试', () => {
  let aiProviderService: AIProviderService
  
  beforeEach(() => {
    aiProviderService = new AIProviderService()
    vi.clearAllMocks()
  })

  it('应该正确处理lm-studio类型的提供商', async () => {
    const mockProvider: AIProviderConfig = {
      id: 'test-lm-studio',
      name: 'Test LM Studio',
      type: 'lm-studio',
      baseUrl: 'http://localhost:1234/v1',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const mockRequest = {
      modelId: 'test-model',
      messages: [{ role: 'user', content: 'Hello' }],
      maxTokens: 100,
      temperature: 0.7
    }

    const mockResponse = {
      ok: true,
      json: () => Promise.resolve({
        choices: [{
          message: {
            content: 'Hello! How can I help you?'
          }
        }]
      })
    }

    vi.mocked(fetch).mockResolvedValue(mockResponse as any)

    // 调用私有方法进行测试
    const result = await (aiProviderService as any).callProviderAPI(mockProvider, mockRequest)

    expect(result).toBe('Hello! How can I help you?')
    expect(fetch).toHaveBeenCalledWith(
      'http://localhost:1234/v1/chat/completions',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        }),
        body: JSON.stringify({
          model: 'test-model',
          messages: [{ role: 'user', content: 'Hello' }],
          max_tokens: 100,
          temperature: 0.7,
          stream: false
        })
      })
    )
  })

  it('应该正确提取lm-studio类型的响应内容', () => {
    const mockData = {
      choices: [{
        message: {
          content: 'Test response from LM Studio'
        }
      }]
    }

    // 调用私有方法进行测试
    const result = (aiProviderService as any).extractResponseContent(mockData, 'lm-studio')

    expect(result).toBe('Test response from LM Studio')
  })

  it('应该在generateText中正确处理lm-studio提供商', async () => {
    // 这个测试验证了修复后的代码不会抛出"不支持的提供商类型"错误
    const mockProvider: AIProviderConfig = {
      id: 'lm-studio-test',
      name: 'LM Studio Test',
      type: 'lm-studio',
      baseUrl: 'http://localhost:1234/v1',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const mockRequest = {
      modelId: 'test-model',
      messages: [{ role: 'user', content: 'Generate some text' }],
      maxTokens: 100,
      temperature: 0.7
    }

    const mockResponse = {
      ok: true,
      json: () => Promise.resolve({
        choices: [{
          message: {
            content: 'Generated text from LM Studio'
          }
        }]
      })
    }

    vi.mocked(fetch).mockResolvedValue(mockResponse as any)

    // 直接测试callProviderAPI方法，这是修复的核心
    const result = await (aiProviderService as any).callProviderAPI(mockProvider, mockRequest)

    expect(result).toBe('Generated text from LM Studio')

    // 验证请求URL和参数正确
    expect(fetch).toHaveBeenCalledWith(
      'http://localhost:1234/v1/chat/completions',
      expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({
          model: 'test-model',
          messages: [{ role: 'user', content: 'Generate some text' }],
          max_tokens: 100,
          temperature: 0.7,
          stream: false
        })
      })
    )
  })

  it('应该验证修复解决了原始错误', async () => {
    // 这个测试确保原来的"不支持的提供商类型: lm-studio"错误不再出现
    const mockProvider: AIProviderConfig = {
      id: 'lm-studio-test',
      name: 'LM Studio Test',
      type: 'lm-studio',
      baseUrl: 'http://localhost:1234/v1',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const mockRequest = {
      modelId: 'deepseek-r1-distill-qwen-32b',
      messages: [{ role: 'user', content: '你好，请简单回复一下确认你能正常工作。' }],
      maxTokens: 100,
      temperature: 0.7
    }

    const mockResponse = {
      ok: true,
      json: () => Promise.resolve({
        choices: [{
          message: {
            content: '你好！我是AI助手，我能正常工作。'
          }
        }]
      })
    }

    vi.mocked(fetch).mockResolvedValue(mockResponse as any)

    // 这个调用之前会抛出"不支持的提供商类型: lm-studio"错误
    // 现在应该正常工作
    const result = await (aiProviderService as any).callProviderAPI(mockProvider, mockRequest)

    expect(result).toBe('你好！我是AI助手，我能正常工作。')
    expect(fetch).toHaveBeenCalledWith(
      'http://localhost:1234/v1/chat/completions',
      expect.objectContaining({
        method: 'POST'
      })
    )
  })
})
