#!/usr/bin/env node

/**
 * BookmarkService 单元测试
 * 
 * 功能说明:
 * - 测试BookmarkService类的所有公有方法
 * - 验证数据保存、查询、更新、删除功能
 * - 测试错误处理和边界情况
 * - 确保业务逻辑的正确性
 * 
 * 测试覆盖:
 * - 收藏保存和创建功能
 * - 收藏查询和搜索功能
 * - 收藏更新和删除功能
 * - 重复检测和统计功能
 * - 错误处理和异常情况
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// ES模块中获取当前文件路径的标准方法
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 开始测试 BookmarkService...\n')

/**
 * 轻量级测试框架类
 * 
 * 提供基础的测试执行、断言验证和结果报告功能
 * 支持同步和异步测试函数，具有详细的错误报告机制
 */
class TestRunner {
  /**
   * 构造函数 - 初始化测试运行器
   */
  constructor() {
    this.tests = []      // 存储所有测试用例的数组
    this.passed = 0      // 通过的测试数量计数器
    this.failed = 0      // 失败的测试数量计数器
    this.skipped = 0     // 跳过的测试数量计数器
  }

  /**
   * 添加测试用例到测试套件
   * 
   * @param {string} name - 测试用例的描述性名称
   * @param {Function} testFn - 测试执行函数，可以是同步或异步函数
   */
  test(name, testFn) {
    this.tests.push({ name, testFn, skip: false })
  }

  /**
   * 添加跳过的测试用例
   * 
   * @param {string} name - 测试用例的描述性名称
   * @param {Function} testFn - 测试执行函数
   */
  skip(name, testFn) {
    this.tests.push({ name, testFn, skip: true })
  }

  /**
   * 断言验证函数
   * 
   * @param {boolean} condition - 要验证的条件，为false时抛出错误
   * @param {string} message - 条件不满足时的错误消息
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  /**
   * 断言相等
   * 
   * @param {any} actual - 实际值
   * @param {any} expected - 期望值
   * @param {string} message - 错误消息
   */
  assertEqual(actual, expected, message) {
    if (actual !== expected) {
      throw new Error(`${message} - 期望: ${expected}, 实际: ${actual}`)
    }
  }

  /**
   * 断言深度相等（用于对象比较）
   * 
   * @param {any} actual - 实际值
   * @param {any} expected - 期望值
   * @param {string} message - 错误消息
   */
  assertDeepEqual(actual, expected, message) {
    if (JSON.stringify(actual) !== JSON.stringify(expected)) {
      throw new Error(`${message} - 期望: ${JSON.stringify(expected)}, 实际: ${JSON.stringify(actual)}`)
    }
  }

  /**
   * 断言抛出错误
   * 
   * @param {Function} fn - 应该抛出错误的函数
   * @param {string} message - 错误消息
   */
  async assertThrows(fn, message) {
    try {
      await fn()
      throw new Error(`${message} - 期望抛出错误但没有抛出`)
    } catch (error) {
      if (error.message.includes('期望抛出错误但没有抛出')) {
        throw error
      }
      // 正确抛出了错误
    }
  }

  /**
   * 执行所有注册的测试用例
   * 
   * @returns {Promise<void>} 异步执行所有测试并输出结果
   */
  async run() {
    console.log(`运行 ${this.tests.length} 个测试用例...\n`)

    // 依次执行每个测试用例
    for (const test of this.tests) {
      if (test.skip) {
        console.log(`⏭️  ${test.name} (跳过)`)
        this.skipped++
        continue
      }

      try {
        // 支持异步测试函数
        await test.testFn()
        console.log(`✅ ${test.name}`)
        this.passed++
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`)
        this.failed++
      }
    }

    // 输出测试结果统计
    console.log('\n' + '='.repeat(50))
    console.log(`测试结果: ${this.passed} 通过, ${this.failed} 失败, ${this.skipped} 跳过`)

    // 如果有测试失败，设置进程退出码为1
    if (this.failed > 0) {
      process.exit(1)
    }
  }
}

// 创建测试运行器实例
const runner = new TestRunner()

/**
 * 模拟数据和工具函数
 */

// 模拟BookmarkInput数据
const mockBookmarkInput = {
  type: 'url',
  title: '测试网站',
  url: 'https://test.example.com',
  description: '这是一个测试网站',
  category: '测试分类',
  tags: ['测试', '示例'],
  metadata: {
    pageTitle: '测试网站',
    siteName: 'test.example.com',
    publishDate: new Date(),
    wordCount: 100,
    aiGenerated: false
  }
}

// 模拟PageInfo数据
const mockPageInfo = {
  title: '页面标题',
  url: 'https://page.example.com',
  description: '页面描述',
  favicon: 'https://page.example.com/favicon.ico',
  siteName: 'page.example.com',
  publishDate: new Date(),
  wordCount: 200
}

// 模拟BookmarkService（因为实际的服务依赖于浏览器环境）
class MockBookmarkService {
  constructor() {
    this.bookmarks = new Map()
    this.nextId = 1
  }

  async saveBookmark(input) {
    // 模拟数据验证
    if (!input.title || !input.url) {
      throw new Error('标题和URL是必需的')
    }

    // 检查重复
    for (const bookmark of this.bookmarks.values()) {
      if (bookmark.url === input.url) {
        throw new Error('URL已存在')
      }
    }

    const id = `bookmark_${this.nextId++}`
    const bookmark = {
      id,
      ...input,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.bookmarks.set(id, bookmark)
    return id
  }

  async getBookmark(id) {
    return this.bookmarks.get(id) || null
  }

  async getBookmarks(filter, sort) {
    let results = Array.from(this.bookmarks.values())

    // 应用筛选
    if (filter) {
      if (filter.query) {
        const query = filter.query.toLowerCase()
        results = results.filter(b => 
          b.title.toLowerCase().includes(query) ||
          (b.description && b.description.toLowerCase().includes(query))
        )
      }
      if (filter.categories) {
        results = results.filter(b => filter.categories.includes(b.category))
      }
      if (filter.tags) {
        results = results.filter(b => 
          filter.tags.some(tag => b.tags.includes(tag))
        )
      }
    }

    // 应用排序
    if (sort) {
      results.sort((a, b) => {
        let aValue = a[sort.field]
        let bValue = b[sort.field]
        
        if (sort.field === 'createdAt' || sort.field === 'updatedAt') {
          aValue = aValue.getTime()
          bValue = bValue.getTime()
        }
        
        if (sort.direction === 'desc') {
          return bValue - aValue
        }
        return aValue - bValue
      })
    }

    // 应用分页
    if (filter && filter.limit) {
      const offset = filter.offset || 0
      results = results.slice(offset, offset + filter.limit)
    }

    return results
  }

  async updateBookmark(id, updates) {
    const bookmark = this.bookmarks.get(id)
    if (!bookmark) {
      throw new Error(`收藏不存在: ${id}`)
    }

    const updatedBookmark = {
      ...bookmark,
      ...updates,
      id, // 确保ID不被修改
      updatedAt: new Date()
    }

    this.bookmarks.set(id, updatedBookmark)
  }

  async deleteBookmark(id) {
    if (!this.bookmarks.has(id)) {
      throw new Error(`收藏不存在: ${id}`)
    }
    this.bookmarks.delete(id)
  }

  async deleteBookmarks(ids) {
    for (const id of ids) {
      if (!this.bookmarks.has(id)) {
        throw new Error(`收藏不存在: ${id}`)
      }
    }
    for (const id of ids) {
      this.bookmarks.delete(id)
    }
  }

  async quickBookmark(title, url, favicon, selectedText) {
    const input = {
      type: selectedText ? 'text' : 'url',
      title,
      url,
      content: selectedText,
      favicon,
      category: '默认分类',
      tags: [],
      metadata: {
        pageTitle: title,
        siteName: this.extractSiteName(url),
        publishDate: new Date(),
        wordCount: selectedText ? selectedText.length : undefined,
        aiGenerated: false
      }
    }
    return await this.saveBookmark(input)
  }

  async bookmarkSelectedText(selectedText, url, title, context) {
    const input = {
      type: 'text',
      title: `摘录: ${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}`,
      url,
      content: selectedText,
      description: context,
      category: '文字摘录',
      tags: [],
      metadata: {
        pageTitle: title,
        siteName: this.extractSiteName(url),
        publishDate: new Date(),
        wordCount: selectedText.length,
        aiGenerated: false
      }
    }
    return await this.saveBookmark(input)
  }

  async findBookmarkByUrl(url) {
    for (const bookmark of this.bookmarks.values()) {
      if (bookmark.url === url) {
        return bookmark
      }
    }
    return null
  }

  async checkBookmarkStatus(url) {
    const bookmark = await this.findBookmarkByUrl(url)
    return {
      isBookmarked: !!bookmark,
      bookmarkId: bookmark?.id
    }
  }

  async searchBookmarks(query, filter) {
    const searchFilter = {
      ...filter,
      query: query.trim()
    }
    return await this.getBookmarks(searchFilter)
  }

  async detectDuplicates(threshold = 0.8) {
    // 简化的重复检测实现
    const bookmarks = Array.from(this.bookmarks.values())
    const duplicateGroups = []
    const processed = new Set()

    for (let i = 0; i < bookmarks.length; i++) {
      if (processed.has(bookmarks[i].id)) continue

      const currentBookmark = bookmarks[i]
      const similarBookmarks = [currentBookmark]

      for (let j = i + 1; j < bookmarks.length; j++) {
        if (processed.has(bookmarks[j].id)) continue

        // 简单的相似度计算
        if (bookmarks[j].url === currentBookmark.url ||
            bookmarks[j].title === currentBookmark.title) {
          similarBookmarks.push(bookmarks[j])
          processed.add(bookmarks[j].id)
        }
      }

      if (similarBookmarks.length > 1) {
        duplicateGroups.push({
          id: `dup_${Date.now()}_${i}`,
          bookmarks: similarBookmarks,
          similarity: threshold,
          type: 'url'
        })
      }

      processed.add(currentBookmark.id)
    }

    return duplicateGroups
  }

  async getBookmarkStats() {
    const bookmarks = Array.from(this.bookmarks.values())
    const now = new Date()
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    const stats = {
      totalCount: bookmarks.length,
      typeStats: {},
      categoryStats: {},
      tagStats: {},
      recentCount: 0
    }

    bookmarks.forEach(bookmark => {
      // 类型统计
      stats.typeStats[bookmark.type] = (stats.typeStats[bookmark.type] || 0) + 1
      
      // 分类统计
      stats.categoryStats[bookmark.category] = (stats.categoryStats[bookmark.category] || 0) + 1
      
      // 标签统计
      bookmark.tags.forEach(tag => {
        stats.tagStats[tag] = (stats.tagStats[tag] || 0) + 1
      })
      
      // 最近收藏统计
      if (bookmark.createdAt >= oneWeekAgo) {
        stats.recentCount++
      }
    })

    return stats
  }

  extractSiteName(url) {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace('www.', '')
    } catch {
      return '未知网站'
    }
  }

  // 清空所有数据（用于测试）
  clear() {
    this.bookmarks.clear()
    this.nextId = 1
  }
}

// 创建模拟服务实例
const mockService = new MockBookmarkService()

/**
 * 测试用例定义
 */

// 测试组：保存操作
runner.test('保存收藏 - 基本功能', async () => {
  mockService.clear()
  
  const bookmarkId = await mockService.saveBookmark(mockBookmarkInput)
  
  runner.assert(bookmarkId, '应该返回收藏ID')
  runner.assert(bookmarkId.startsWith('bookmark_'), 'ID格式应该正确')
  
  const savedBookmark = await mockService.getBookmark(bookmarkId)
  runner.assert(savedBookmark, '应该能够获取保存的收藏')
  runner.assertEqual(savedBookmark.title, mockBookmarkInput.title, '标题应该匹配')
  runner.assertEqual(savedBookmark.url, mockBookmarkInput.url, 'URL应该匹配')
})

runner.test('保存收藏 - 数据验证', async () => {
  mockService.clear()
  
  // 测试缺少必需字段
  await runner.assertThrows(async () => {
    await mockService.saveBookmark({ type: 'url' })
  }, '缺少必需字段时应该抛出错误')
  
  // 测试重复URL
  await mockService.saveBookmark(mockBookmarkInput)
  await runner.assertThrows(async () => {
    await mockService.saveBookmark(mockBookmarkInput)
  }, '重复URL应该抛出错误')
})

runner.test('快速收藏功能', async () => {
  mockService.clear()
  
  const bookmarkId = await mockService.quickBookmark(
    '快速收藏测试',
    'https://quick.example.com',
    'https://quick.example.com/favicon.ico'
  )
  
  runner.assert(bookmarkId, '应该返回收藏ID')
  
  const bookmark = await mockService.getBookmark(bookmarkId)
  runner.assertEqual(bookmark.title, '快速收藏测试', '标题应该正确')
  runner.assertEqual(bookmark.category, '默认分类', '应该使用默认分类')
  runner.assertEqual(bookmark.type, 'url', '类型应该是url')
})

runner.test('收藏选中文字功能', async () => {
  mockService.clear()
  
  const selectedText = '这是一段重要的文字内容，需要被收藏保存。'
  const bookmarkId = await mockService.bookmarkSelectedText(
    selectedText,
    'https://text.example.com',
    '文章标题',
    '这是上下文信息'
  )
  
  runner.assert(bookmarkId, '应该返回收藏ID')
  
  const bookmark = await mockService.getBookmark(bookmarkId)
  runner.assertEqual(bookmark.content, selectedText, '内容应该匹配')
  runner.assertEqual(bookmark.type, 'text', '类型应该是text')
  runner.assertEqual(bookmark.category, '文字摘录', '应该使用文字摘录分类')
  runner.assert(bookmark.title.startsWith('摘录:'), '标题应该以"摘录:"开头')
})

// 测试组：查询操作
runner.test('获取单个收藏', async () => {
  mockService.clear()
  
  const bookmarkId = await mockService.saveBookmark(mockBookmarkInput)
  const bookmark = await mockService.getBookmark(bookmarkId)
  
  runner.assert(bookmark, '应该能够获取收藏')
  runner.assertEqual(bookmark.id, bookmarkId, 'ID应该匹配')
  
  // 测试不存在的收藏
  const nonExistent = await mockService.getBookmark('non-existent-id')
  runner.assertEqual(nonExistent, null, '不存在的收藏应该返回null')
})

runner.test('获取收藏列表 - 基本功能', async () => {
  mockService.clear()
  
  // 添加多个收藏
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '收藏1',
    url: 'https://test1.example.com'
  })
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '收藏2',
    url: 'https://test2.example.com',
    category: '其他分类'
  })
  
  const bookmarks = await mockService.getBookmarks()
  runner.assertEqual(bookmarks.length, 2, '应该返回2个收藏')
})

runner.test('获取收藏列表 - 筛选功能', async () => {
  mockService.clear()
  
  // 添加测试数据
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '技术文章',
    url: 'https://tech.example.com',
    category: '技术',
    tags: ['编程', '技术']
  })
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '生活随笔',
    url: 'https://life.example.com',
    category: '生活',
    tags: ['生活', '随笔']
  })
  
  // 测试分类筛选
  const techBookmarks = await mockService.getBookmarks({
    categories: ['技术']
  })
  runner.assertEqual(techBookmarks.length, 1, '技术分类应该有1个收藏')
  runner.assertEqual(techBookmarks[0].title, '技术文章', '应该是技术文章')
  
  // 测试标签筛选
  const programmingBookmarks = await mockService.getBookmarks({
    tags: ['编程']
  })
  runner.assertEqual(programmingBookmarks.length, 1, '编程标签应该有1个收藏')
  
  // 测试文本搜索
  const searchResults = await mockService.getBookmarks({
    query: '技术'
  })
  runner.assertEqual(searchResults.length, 1, '搜索"技术"应该有1个结果')
})

runner.test('获取收藏列表 - 排序功能', async () => {
  mockService.clear()
  
  // 添加测试数据（延迟确保时间不同）
  const id1 = await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: 'A收藏',
    url: 'https://a.example.com'
  })
  
  // 稍微延迟
  await new Promise(resolve => setTimeout(resolve, 10))
  
  const id2 = await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: 'B收藏',
    url: 'https://b.example.com'
  })
  
  // 测试按创建时间降序排序
  const bookmarks = await mockService.getBookmarks(null, {
    field: 'createdAt',
    direction: 'desc'
  })
  
  runner.assertEqual(bookmarks.length, 2, '应该有2个收藏')
  runner.assertEqual(bookmarks[0].title, 'B收藏', '第一个应该是B收藏（最新）')
  runner.assertEqual(bookmarks[1].title, 'A收藏', '第二个应该是A收藏（较早）')
})

runner.test('搜索收藏功能', async () => {
  mockService.clear()
  
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: 'JavaScript教程',
    description: '学习JavaScript的基础知识'
  })
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: 'Python指南',
    url: 'https://python.example.com',
    description: 'Python编程语言指南'
  })
  
  // 搜索JavaScript
  const jsResults = await mockService.searchBookmarks('JavaScript')
  runner.assertEqual(jsResults.length, 1, '搜索JavaScript应该有1个结果')
  runner.assertEqual(jsResults[0].title, 'JavaScript教程', '应该找到JavaScript教程')
  
  // 搜索编程（在描述中）
  const programmingResults = await mockService.searchBookmarks('编程')
  runner.assertEqual(programmingResults.length, 1, '搜索编程应该有1个结果')
  runner.assertEqual(programmingResults[0].title, 'Python指南', '应该找到Python指南')
})

runner.test('根据URL查找收藏', async () => {
  mockService.clear()
  
  const testUrl = 'https://findme.example.com'
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    url: testUrl
  })
  
  const found = await mockService.findBookmarkByUrl(testUrl)
  runner.assert(found, '应该找到收藏')
  runner.assertEqual(found.url, testUrl, 'URL应该匹配')
  
  const notFound = await mockService.findBookmarkByUrl('https://notfound.example.com')
  runner.assertEqual(notFound, null, '不存在的URL应该返回null')
})

runner.test('检查收藏状态', async () => {
  mockService.clear()
  
  const testUrl = 'https://status.example.com'
  
  // 检查未收藏状态
  let status = await mockService.checkBookmarkStatus(testUrl)
  runner.assertEqual(status.isBookmarked, false, '应该显示未收藏')
  runner.assertEqual(status.bookmarkId, undefined, '不应该有收藏ID')
  
  // 添加收藏
  const bookmarkId = await mockService.saveBookmark({
    ...mockBookmarkInput,
    url: testUrl
  })
  
  // 检查已收藏状态
  status = await mockService.checkBookmarkStatus(testUrl)
  runner.assertEqual(status.isBookmarked, true, '应该显示已收藏')
  runner.assertEqual(status.bookmarkId, bookmarkId, '应该返回正确的收藏ID')
})

// 测试组：更新操作
runner.test('更新收藏', async () => {
  mockService.clear()
  
  const bookmarkId = await mockService.saveBookmark(mockBookmarkInput)
  
  const updates = {
    title: '更新后的标题',
    tags: ['新标签1', '新标签2'],
    category: '新分类'
  }
  
  await mockService.updateBookmark(bookmarkId, updates)
  
  const updatedBookmark = await mockService.getBookmark(bookmarkId)
  runner.assertEqual(updatedBookmark.title, updates.title, '标题应该已更新')
  runner.assertDeepEqual(updatedBookmark.tags, updates.tags, '标签应该已更新')
  runner.assertEqual(updatedBookmark.category, updates.category, '分类应该已更新')
  runner.assertEqual(updatedBookmark.url, mockBookmarkInput.url, 'URL应该保持不变')
})

runner.test('更新不存在的收藏', async () => {
  mockService.clear()
  
  await runner.assertThrows(async () => {
    await mockService.updateBookmark('non-existent-id', { title: '新标题' })
  }, '更新不存在的收藏应该抛出错误')
})

// 测试组：删除操作
runner.test('删除单个收藏', async () => {
  mockService.clear()
  
  const bookmarkId = await mockService.saveBookmark(mockBookmarkInput)
  
  // 确认收藏存在
  let bookmark = await mockService.getBookmark(bookmarkId)
  runner.assert(bookmark, '收藏应该存在')
  
  // 删除收藏
  await mockService.deleteBookmark(bookmarkId)
  
  // 确认收藏已删除
  bookmark = await mockService.getBookmark(bookmarkId)
  runner.assertEqual(bookmark, null, '收藏应该已被删除')
})

runner.test('删除不存在的收藏', async () => {
  mockService.clear()
  
  await runner.assertThrows(async () => {
    await mockService.deleteBookmark('non-existent-id')
  }, '删除不存在的收藏应该抛出错误')
})

runner.test('批量删除收藏', async () => {
  mockService.clear()
  
  // 添加多个收藏
  const id1 = await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '收藏1',
    url: 'https://test1.example.com'
  })
  const id2 = await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '收藏2',
    url: 'https://test2.example.com'
  })
  const id3 = await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '收藏3',
    url: 'https://test3.example.com'
  })
  
  // 批量删除前两个
  await mockService.deleteBookmarks([id1, id2])
  
  // 验证删除结果
  const bookmark1 = await mockService.getBookmark(id1)
  const bookmark2 = await mockService.getBookmark(id2)
  const bookmark3 = await mockService.getBookmark(id3)
  
  runner.assertEqual(bookmark1, null, '收藏1应该已删除')
  runner.assertEqual(bookmark2, null, '收藏2应该已删除')
  runner.assert(bookmark3, '收藏3应该仍然存在')
})

// 测试组：验证和统计操作
runner.test('检测重复收藏', async () => {
  mockService.clear()
  
  // 添加重复的收藏
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '原始收藏',
    url: 'https://duplicate.example.com'
  })
  
  // 修改服务以允许重复（用于测试）
  const originalSave = mockService.saveBookmark
  mockService.saveBookmark = async function(input) {
    const id = `bookmark_${this.nextId++}`
    const bookmark = {
      id,
      ...input,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    this.bookmarks.set(id, bookmark)
    return id
  }
  
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    title: '重复收藏',
    url: 'https://duplicate.example.com'
  })
  
  // 恢复原始方法
  mockService.saveBookmark = originalSave
  
  const duplicates = await mockService.detectDuplicates()
  runner.assert(duplicates.length > 0, '应该检测到重复收藏')
  runner.assertEqual(duplicates[0].bookmarks.length, 2, '重复组应该包含2个收藏')
})

runner.test('获取收藏统计信息', async () => {
  mockService.clear()
  
  // 添加不同类型的收藏
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    type: 'url',
    category: '技术',
    tags: ['JavaScript', '编程']
  })
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    type: 'text',
    url: 'https://text.example.com',
    category: '技术',
    tags: ['编程', '学习']
  })
  await mockService.saveBookmark({
    ...mockBookmarkInput,
    type: 'url',
    url: 'https://life.example.com',
    category: '生活',
    tags: ['生活']
  })
  
  const stats = await mockService.getBookmarkStats()
  
  runner.assertEqual(stats.totalCount, 3, '总数应该是3')
  runner.assertEqual(stats.typeStats.url, 2, 'URL类型应该有2个')
  runner.assertEqual(stats.typeStats.text, 1, 'TEXT类型应该有1个')
  runner.assertEqual(stats.categoryStats['技术'], 2, '技术分类应该有2个')
  runner.assertEqual(stats.categoryStats['生活'], 1, '生活分类应该有1个')
  runner.assertEqual(stats.tagStats['编程'], 2, '编程标签应该有2个')
  runner.assertEqual(stats.tagStats['JavaScript'], 1, 'JavaScript标签应该有1个')
})

// 测试组：边界情况和错误处理
runner.test('处理空数据', async () => {
  mockService.clear()
  
  const bookmarks = await mockService.getBookmarks()
  runner.assertEqual(bookmarks.length, 0, '空数据库应该返回空数组')
  
  const stats = await mockService.getBookmarkStats()
  runner.assertEqual(stats.totalCount, 0, '空数据库统计应该为0')
})

runner.test('处理大量数据', async () => {
  mockService.clear()
  
  // 添加大量收藏（模拟性能测试）
  const promises = []
  for (let i = 0; i < 100; i++) {
    promises.push(mockService.saveBookmark({
      ...mockBookmarkInput,
      title: `收藏${i}`,
      url: `https://test${i}.example.com`
    }))
  }
  
  await Promise.all(promises)
  
  const bookmarks = await mockService.getBookmarks()
  runner.assertEqual(bookmarks.length, 100, '应该保存100个收藏')
  
  // 测试分页
  const pagedBookmarks = await mockService.getBookmarks({
    limit: 10,
    offset: 0
  })
  runner.assertEqual(pagedBookmarks.length, 10, '分页应该返回10个收藏')
})

runner.test('URL工具函数', () => {
  // 测试网站名称提取
  runner.assertEqual(
    mockService.extractSiteName('https://www.example.com/path'),
    'example.com',
    '应该正确提取网站名称'
  )
  
  runner.assertEqual(
    mockService.extractSiteName('https://subdomain.example.com'),
    'subdomain.example.com',
    '应该保留子域名'
  )
  
  runner.assertEqual(
    mockService.extractSiteName('invalid-url'),
    '未知网站',
    '无效URL应该返回默认值'
  )
})

// 运行所有测试
runner.run().catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})