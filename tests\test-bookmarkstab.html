<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookmarksTab组件测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .badge {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        .test-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-checklist {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .checklist-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
        }
        .checklist-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .checklist-item ul {
            margin: 0;
            padding-left: 20px;
        }
        .checklist-item li {
            margin: 5px 0;
            color: #666;
        }
        .preview-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #fafafa;
        }
        .preview-header {
            text-align: center;
            margin-bottom: 15px;
        }
        .preview-badge {
            background: #fff3cd;
            color: #856404;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        .mock-component {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }
        .mock-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }
        .mock-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        .mock-description {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .mock-buttons {
            display: flex;
            gap: 10px;
        }
        .mock-button {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #ddd;
            background: #007bff;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .mock-button:hover {
            background: #0056b3;
        }
        .mock-button.outline {
            background: white;
            color: #007bff;
        }
        .mock-button.outline:hover {
            background: #f8f9fa;
        }
        .mock-controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .mock-input {
            flex: 1;
            min-width: 200px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .mock-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
        }
        .mock-content {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        .mock-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 BookmarksTab组件测试页面 <span class="badge">shadcn重构验证</span></h1>
            <p>验证重构后的BookmarksTab组件是否正确使用shadcn/ui组件，包括Input、Select、Button、Card等组件的集成效果</p>
        </div>

        <div class="test-info">
            <h3>✅ 已完成的shadcn组件重构</h3>
            <ul>
                <li><strong>shadcn Input组件</strong> - 搜索输入框，带搜索图标和加载状态</li>
                <li><strong>shadcn Select组件</strong> - 分类筛选器，支持下拉选择</li>
                <li><strong>shadcn Button组件</strong> - 所有操作按钮，包括不同变体</li>
                <li><strong>shadcn Card组件</strong> - 页面布局容器，包含Header、Content等</li>
                <li><strong>搜索建议下拉框</strong> - 使用Card + Button组合实现</li>
            </ul>
        </div>

        <div class="test-checklist">
            <div class="checklist-item">
                <h4>🎯 基础功能测试</h4>
                <ul>
                    <li>□ 搜索框输入和清除</li>
                    <li>□ 分类筛选器选择</li>
                    <li>□ 添加收藏按钮点击</li>
                    <li>□ 刷新按钮功能</li>
                    <li>□ 视图模式切换</li>
                </ul>
            </div>
            <div class="checklist-item">
                <h4>🎨 shadcn集成测试</h4>
                <ul>
                    <li>□ Input组件样式正确</li>
                    <li>□ Select组件交互正常</li>
                    <li>□ Button组件变体显示</li>
                    <li>□ Card组件布局合理</li>
                    <li>□ 主题颜色一致</li>
                </ul>
            </div>
        </div>

        <div class="preview-area">
            <div class="preview-header">
                <div class="preview-badge">组件预览区域</div>
                <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">
                    下方显示的是重构后的BookmarksTab组件模拟效果
                </p>
            </div>
            
            <!-- 模拟BookmarksTab组件 -->
            <div class="mock-component">
                <div class="mock-header">
                    <div>
                        <div class="mock-title">收藏管理</div>
                        <div class="mock-description">管理您的收藏内容，支持搜索、分类和多种视图模式</div>
                    </div>
                    <div class="mock-buttons">
                        <button class="mock-button">➕ 添加收藏</button>
                        <button class="mock-button outline">🔄 刷新</button>
                    </div>
                </div>
                
                <div class="mock-controls">
                    <input type="text" class="mock-input" placeholder="🔍 搜索收藏..." />
                    <select class="mock-select">
                        <option>所有分类</option>
                        <option>技术</option>
                        <option>学习</option>
                        <option>工具</option>
                    </select>
                    <button class="mock-button outline">📋 列表视图</button>
                </div>
                
                <div class="mock-content">
                    <div class="mock-icon">📚</div>
                    <h3>暂无收藏内容</h3>
                    <p>开始收藏您感兴趣的网页和内容吧！</p>
                </div>
            </div>
        </div>

        <div class="test-info">
            <h3>🧪 测试说明</h3>
            <p><strong>预期行为：</strong></p>
            <ul>
                <li>组件应该正常加载并显示"加载收藏数据中..."状态</li>
                <li>搜索框应该有搜索图标，支持输入和建议</li>
                <li>分类选择器应该显示"所有分类"选项</li>
                <li>按钮应该有正确的shadcn样式和hover效果</li>
                <li>如果没有数据，应该显示空状态提示</li>
                <li>所有交互元素应该响应用户操作</li>
            </ul>
            
            <p><strong>实际测试步骤：</strong></p>
            <ol>
                <li>构建项目：<code>npm run build</code></li>
                <li>在Chrome中加载扩展：选择 <code>dist</code> 文件夹</li>
                <li>打开扩展的选项页面</li>
                <li>点击"BookmarksTab测试"标签页</li>
                <li>验证组件的shadcn样式和交互功能</li>
            </ol>
        </div>
    </div>

    <script>
        // 简单的交互演示
        document.addEventListener('DOMContentLoaded', function() {
            const input = document.querySelector('.mock-input');
            const select = document.querySelector('.mock-select');
            const buttons = document.querySelectorAll('.mock-button');
            
            input.addEventListener('input', function() {
                console.log('搜索输入:', this.value);
            });
            
            select.addEventListener('change', function() {
                console.log('分类选择:', this.value);
            });
            
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('按钮点击:', this.textContent);
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });
            });
        });
    </script>
</body>
</html>