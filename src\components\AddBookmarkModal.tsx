// 添加收藏模态组件 - 使用shadcn/ui组件重构

import React, { useState, useEffect } from 'react'
import { Save, AlertCircle, Link, FileText, Tag, Folder, Plus, Sparkles } from 'lucide-react'
import { BookmarkInput } from '../types'

// shadcn/ui组件导入
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useForm } from "react-hook-form"
import { cn } from "@/lib/utils"

interface AddBookmarkModalProps {
  /** 是否显示模态窗口 */
  isOpen: boolean
  /** 保存回调函数 */
  onSave: (bookmark: BookmarkInput) => Promise<void>
  /** 取消回调函数 */
  onCancel: () => void
  /** 是否正在保存 */
  loading?: boolean
  /** 预填充的数据（可选） */
  initialData?: Partial<BookmarkInput>
}

interface FormData {
  type: 'url' | 'text'
  title: string
  url: string
  content: string
  description: string
  category: string
  tags: string[]
}

interface FormErrors {
  title?: string
  url?: string
  content?: string
  description?: string
  category?: string
  tags?: string
}

/**
 * 添加收藏模态组件
 * 提供手动添加收藏的功能，支持URL和文本两种类型
 * 使用shadcn/ui组件重构
 */
const AddBookmarkModal: React.FC<AddBookmarkModalProps> = React.memo(({
  isOpen,
  onSave,
  onCancel,
  loading = false,
  initialData
}) => {
  const [tagInput, setTagInput] = useState('')
  const [isAIGenerating, setIsAIGenerating] = useState(false)

  // 使用react-hook-form管理表单状态
  const form = useForm<FormData>({
    defaultValues: {
      type: 'url',
      title: '',
      url: '',
      content: '',
      description: '',
      category: '默认分类',
      tags: []
    }
  })

  const { watch, setValue, getValues, reset } = form
  const watchedType = watch('type')
  const watchedTags = watch('tags')

  // 当初始数据变化时更新表单
  useEffect(() => {
    if (initialData) {
      reset({
        type: initialData.type || 'url',
        title: initialData.title || '',
        url: initialData.url || '',
        content: initialData.content || '',
        description: initialData.description || '',
        category: initialData.category || '默认分类',
        tags: initialData.tags || []
      })
    }
  }, [initialData, reset])

  // 重置表单
  const resetForm = () => {
    reset({
      type: 'url',
      title: '',
      url: '',
      content: '',
      description: '',
      category: '默认分类',
      tags: []
    })
    setTagInput('')
  }

  // 当模态窗口打开时重置表单
  useEffect(() => {
    if (isOpen && !initialData) {
      resetForm()
    }
  }, [isOpen, initialData])

  // 表单验证规则
  const validateTitle = (value: string) => {
    if (!value.trim()) return '标题不能为空'
    if (value.length > 200) return '标题长度不能超过200个字符'
    return true
  }

  const validateUrl = (value: string, type: string) => {
    if (type === 'url') {
      if (!value.trim()) return 'URL不能为空'
      try {
        new URL(value)
        return true
      } catch {
        return '请输入有效的URL地址'
      }
    }
    return true
  }

  const validateContent = (value: string, type: string) => {
    if (type === 'text') {
      if (!value.trim()) return '文本内容不能为空'
      if (value.length > 10000) return '文本内容长度不能超过10000个字符'
    }
    return true
  }

  const validateDescription = (value: string) => {
    if (value && value.length > 1000) return '描述长度不能超过1000个字符'
    return true
  }

  const validateCategory = (value: string) => {
    if (!value.trim()) return '分类不能为空'
    return true
  }

  // 处理表单提交
  const handleSubmit = async (data: FormData) => {
    try {
      const bookmarkInput: BookmarkInput = {
        type: data.type,
        title: data.title.trim(),
        description: data.description.trim() || undefined,
        category: data.category.trim(),
        tags: data.tags.filter(tag => tag.trim()),
        metadata: {
          aiGenerated: false
        }
      }

      // 根据类型添加对应字段
      if (data.type === 'url') {
        bookmarkInput.url = data.url.trim()
      } else if (data.type === 'text') {
        bookmarkInput.content = data.content.trim()
        // 文本类型可能也有URL（来源链接）
        if (data.url.trim()) {
          bookmarkInput.url = data.url.trim()
        }
      }

      await onSave(bookmarkInput)
      resetForm()
    } catch (error) {
      console.error('保存收藏失败:', error)
      // 这里可以显示错误提示
    }
  }

  // 处理类型切换
  const handleTypeChange = (newType: 'url' | 'text') => {
    setValue('type', newType)
  }

  // 添加标签
  const handleAddTag = () => {
    const tag = tagInput.trim()
    const currentTags = getValues('tags')
    if (tag && !currentTags.includes(tag)) {
      setValue('tags', [...currentTags, tag])
      setTagInput('')
    }
  }

  // 删除标签
  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = getValues('tags')
    setValue('tags', currentTags.filter(tag => tag !== tagToRemove))
  }

  // 处理标签输入的回车键
  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  // 处理标签输入的键盘事件（包括回车）
  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  // AI辅助生成标签和分类
  const handleAIGenerate = async () => {
    const currentValues = getValues()
    if (!currentValues.title.trim() && !currentValues.content.trim() && !currentValues.url.trim()) {
      return
    }

    setIsAIGenerating(true)
    try {
      // 这里可以调用AI服务生成建议
      // 暂时模拟AI生成的逻辑
      const content = currentValues.content || currentValues.title
      const suggestedTags = generateSuggestedTags(content)
      const suggestedCategory = generateSuggestedCategory(content)

      // 添加建议的标签（不重复）
      const currentTags = currentValues.tags
      const newTags = suggestedTags.filter(tag => !currentTags.includes(tag))
      if (newTags.length > 0) {
        setValue('tags', [...currentTags, ...newTags.slice(0, 3)]) // 最多添加3个建议标签
      }

      // 如果当前分类是默认分类，则使用建议的分类
      if (currentValues.category === '默认分类' && suggestedCategory) {
        setValue('category', suggestedCategory)
      }
    } catch (error) {
      console.error('AI生成建议失败:', error)
    } finally {
      setIsAIGenerating(false)
    }
  }

  // 简单的标签建议逻辑（实际应该调用AI服务）
  const generateSuggestedTags = (content: string): string[] => {
    const keywords = content.toLowerCase()
    const suggestions: string[] = []

    if (keywords.includes('react') || keywords.includes('前端')) suggestions.push('前端开发')
    if (keywords.includes('javascript') || keywords.includes('js')) suggestions.push('JavaScript')
    if (keywords.includes('typescript') || keywords.includes('ts')) suggestions.push('TypeScript')
    if (keywords.includes('vue') || keywords.includes('angular')) suggestions.push('前端框架')
    if (keywords.includes('node') || keywords.includes('后端')) suggestions.push('后端开发')
    if (keywords.includes('数据库') || keywords.includes('sql')) suggestions.push('数据库')
    if (keywords.includes('设计') || keywords.includes('ui')) suggestions.push('设计')
    if (keywords.includes('工具') || keywords.includes('效率')) suggestions.push('工具')
    if (keywords.includes('学习') || keywords.includes('教程')) suggestions.push('学习资源')
    if (keywords.includes('新闻') || keywords.includes('资讯')) suggestions.push('新闻资讯')

    return suggestions
  }

  // 简单的分类建议逻辑（实际应该调用AI服务）
  const generateSuggestedCategory = (content: string): string => {
    const keywords = content.toLowerCase()

    if (keywords.includes('工作') || keywords.includes('项目') || keywords.includes('开发')) return '工作'
    if (keywords.includes('学习') || keywords.includes('教程') || keywords.includes('课程')) return '学习'
    if (keywords.includes('工具') || keywords.includes('软件') || keywords.includes('应用')) return '工具'
    if (keywords.includes('新闻') || keywords.includes('资讯') || keywords.includes('动态')) return '新闻'
    if (keywords.includes('娱乐') || keywords.includes('游戏') || keywords.includes('视频')) return '娱乐'

    return '默认分类'
  }

  // 检查重复
  const checkDuplicate = async () => {
    const currentUrl = getValues('url')
    if (!currentUrl.trim()) return

    try {
      // 这里应该调用BookmarkService检查重复
      // 暂时跳过实现
      console.log('检查重复:', currentUrl)
    } catch (error) {
      console.error('检查重复失败:', error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Plus className="w-5 h-5 mr-2" />
            添加收藏
          </DialogTitle>
          <DialogDescription>
            手动添加收藏，支持网页链接和文本摘录两种类型
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* 类型选择 */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    收藏类型 *
                  </FormLabel>
                  <FormControl>
                    <div className="flex space-x-4">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          name="type"
                          value="url"
                          checked={field.value === 'url'}
                          onChange={() => handleTypeChange('url')}
                          className="mr-2"
                          disabled={loading}
                        />
                        <Link className="w-4 h-4 mr-1" />
                        网页链接
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          name="type"
                          value="text"
                          checked={field.value === 'text'}
                          onChange={() => handleTypeChange('text')}
                          className="mr-2"
                          disabled={loading}
                        />
                        <FileText className="w-4 h-4 mr-1" />
                        文本摘录
                      </label>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 标题输入 */}
            <FormField
              control={form.control}
              name="title"
              rules={{
                validate: validateTitle
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <FileText className="w-4 h-4 mr-1" />
                    标题 *
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="请输入收藏标题"
                      disabled={loading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* URL输入（URL类型必填，文本类型可选） */}
            <FormField
              control={form.control}
              name="url"
              rules={{
                validate: (value) => validateUrl(value, watchedType)
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <Link className="w-4 h-4 mr-1" />
                    网址 {watchedType === 'url' ? '*' : '(可选)'}
                  </FormLabel>
                  <FormControl>
                    <div className="flex">
                      <Input
                        type="url"
                        placeholder="https://example.com"
                        disabled={loading}
                        onBlur={checkDuplicate}
                        className="rounded-r-none"
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={checkDuplicate}
                        disabled={loading || !field.value?.trim()}
                        className="rounded-l-none border-l-0"
                        size="sm"
                      >
                        检查
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 文本内容输入（仅文本类型显示） */}
            {watchedType === 'text' && (
              <FormField
                control={form.control}
                name="content"
                rules={{
                  validate: (value) => validateContent(value, watchedType)
                }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <FileText className="w-4 h-4 mr-1" />
                      文本内容 *
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入要收藏的文本内容"
                        disabled={loading}
                        rows={4}
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* 描述输入 */}
            <FormField
              control={form.control}
              name="description"
              rules={{
                validate: validateDescription
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请输入收藏描述（可选）"
                      disabled={loading}
                      rows={3}
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 分类选择 */}
            <FormField
              control={form.control}
              name="category"
              rules={{
                validate: validateCategory
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <Folder className="w-4 h-4 mr-1" />
                    分类 *
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择分类" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="默认分类">默认分类</SelectItem>
                      <SelectItem value="工作">工作</SelectItem>
                      <SelectItem value="学习">学习</SelectItem>
                      <SelectItem value="娱乐">娱乐</SelectItem>
                      <SelectItem value="工具">工具</SelectItem>
                      <SelectItem value="新闻">新闻</SelectItem>
                      <SelectItem value="文字摘录">文字摘录</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 标签输入 */}
            <FormItem>
              <div className="flex items-center justify-between mb-2">
                <FormLabel className="flex items-center">
                  <Tag className="w-4 h-4 mr-1" />
                  标签
                </FormLabel>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleAIGenerate}
                  disabled={loading || isAIGenerating || (!getValues('title').trim() && !getValues('content').trim())}
                  className="h-auto p-1 text-sm"
                >
                  <Sparkles className="w-4 h-4 mr-1" />
                  {isAIGenerating ? 'AI生成中...' : 'AI建议'}
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mb-2" data-testid="tags-container">
                {watchedTags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="flex items-center gap-1"
                    data-testid={`tag-${tag}`}
                  >
                    {tag}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveTag(tag)}
                      disabled={loading}
                      className="h-auto p-0 w-4 h-4 hover:bg-transparent"
                      aria-label={`删除标签 ${tag}`}
                    >
                      ×
                    </Button>
                  </Badge>
                ))}
              </div>
              <div className="flex">
                <Input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleTagInputKeyPress}
                  onKeyDown={handleTagInputKeyDown}
                  className="rounded-r-none"
                  placeholder="输入标签后按回车添加"
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddTag}
                  disabled={loading || !tagInput.trim()}
                  className="rounded-l-none border-l-0"
                  size="sm"
                >
                  添加
                </Button>
              </div>
            </FormItem>
          </form>
        </Form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="submit"
            disabled={loading}
            onClick={form.handleSubmit(handleSubmit)}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                保存中...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                保存收藏
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
})

// 设置显示名称便于调试
AddBookmarkModal.displayName = 'AddBookmarkModal'

export default AddBookmarkModal

// 导出类型定义
export type { AddBookmarkModalProps }