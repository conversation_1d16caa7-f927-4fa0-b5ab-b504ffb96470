// 冲突检测和解决服务类 - 负责处理导入数据的冲突检测和智能合并

import { 
  Bookmark, 
  BookmarkInput, 
  Category, 
  CategoryInput, 
  Tag, 
  TagInput,
  ConflictItem,
  ConflictResolution,
  ConflictDetectionResult,
  ResolvedData,
  ImportData
} from '../types'
import { bookmarkService } from './bookmarkService'
import { categoryService } from './categoryService'
import { tagService } from './tagService'

/**
 * 冲突检测和解决服务类
 * 提供智能的数据冲突检测和解决功能
 */
export class ConflictResolverService {
  
  /**
   * 检测导入数据中的所有冲突
   * @param importData 导入数据
   * @returns Promise<ConflictDetectionResult>
   */
  async detectConflicts(importData: ImportData): Promise<ConflictDetectionResult> {
    try {
      const conflicts: ConflictItem[] = []
      
      // 检测收藏夹冲突
      if (importData.bookmarks && importData.bookmarks.length > 0) {
        const bookmarkConflicts = await this.detectBookmarkConflicts(
          importData.bookmarks,
          await bookmarkService.getBookmarks()
        )
        conflicts.push(...bookmarkConflicts)
      }
      
      // 检测分类冲突
      if (importData.categories && importData.categories.length > 0) {
        const categoryConflicts = await this.detectCategoryConflicts(
          importData.categories,
          await categoryService.getCategories()
        )
        conflicts.push(...categoryConflicts)
      }
      
      // 检测标签冲突
      if (importData.tags && importData.tags.length > 0) {
        const tagConflicts = await this.detectTagConflicts(
          importData.tags,
          await tagService.getTags()
        )
        conflicts.push(...tagConflicts)
      }
      
      // 统计冲突数量
      const summary = {
        bookmarkConflicts: conflicts.filter(c => c.type === 'bookmark').length,
        categoryConflicts: conflicts.filter(c => c.type === 'category').length,
        tagConflicts: conflicts.filter(c => c.type === 'tag').length
      }
      
      return {
        hasConflicts: conflicts.length > 0,
        conflicts,
        summary
      }
      
    } catch (error) {
      console.error('检测冲突失败:', error)
      throw error
    }
  }

  /**
   * 检测收藏夹冲突
   * @param importBookmarks 导入的收藏夹数据
   * @param existingBookmarks 现有的收藏夹数据
   * @returns ConflictItem[]
   */
  async detectBookmarkConflicts(
    importBookmarks: BookmarkInput[], 
    existingBookmarks: Bookmark[]
  ): Promise<ConflictItem[]> {
    const conflicts: ConflictItem[] = []
    
    for (const importBookmark of importBookmarks) {
      // URL重复检测
      if (importBookmark.url) {
        const existingByUrl = existingBookmarks.find(b => b.url === importBookmark.url)
        if (existingByUrl) {
          conflicts.push({
            id: this.generateConflictId(),
            type: 'bookmark',
            conflictType: 'duplicate',
            existingData: existingByUrl,
            importData: importBookmark,
            conflictFields: ['url'],
            similarity: 1.0
          })
          continue
        }
      }
      
      // 标题和内容相似度检测
      for (const existing of existingBookmarks) {
        const similarity = this.calculateBookmarkSimilarity(importBookmark, existing)
        if (similarity > 0.8) {
          const conflictFields = this.determineBookmarkConflictFields(importBookmark, existing)
          // 即使没有明显的冲突字段，高相似度也应该被标记为冲突
          conflicts.push({
            id: this.generateConflictId(),
            type: 'bookmark',
            conflictType: 'data_mismatch',
            existingData: existing,
            importData: importBookmark,
            conflictFields: conflictFields.length > 0 ? conflictFields : ['similarity'],
            similarity
          })
        }
      }
    }
    
    return conflicts
  }

  /**
   * 检测分类冲突
   * @param importCategories 导入的分类数据
   * @param existingCategories 现有的分类数据
   * @returns ConflictItem[]
   */
  async detectCategoryConflicts(
    importCategories: CategoryInput[], 
    existingCategories: Category[]
  ): Promise<ConflictItem[]> {
    const conflicts: ConflictItem[] = []
    
    for (const importCategory of importCategories) {
      // 名称重复检测
      const existingByName = existingCategories.find(c => 
        c.name.toLowerCase() === importCategory.name.toLowerCase()
      )
      
      if (existingByName) {
        const conflictFields = this.determineCategoryConflictFields(importCategory, existingByName)
        
        conflicts.push({
          id: this.generateConflictId(),
          type: 'category',
          conflictType: conflictFields.length > 1 ? 'data_mismatch' : 'name_conflict',
          existingData: existingByName,
          importData: importCategory,
          conflictFields,
          similarity: this.calculateCategorySimilarity(importCategory, existingByName)
        })
      }
    }
    
    return conflicts
  }

  /**
   * 检测标签冲突
   * @param importTags 导入的标签数据
   * @param existingTags 现有的标签数据
   * @returns ConflictItem[]
   */
  async detectTagConflicts(
    importTags: TagInput[], 
    existingTags: Tag[]
  ): Promise<ConflictItem[]> {
    const conflicts: ConflictItem[] = []
    
    for (const importTag of importTags) {
      // 名称重复检测
      const existingByName = existingTags.find(t => 
        t.name.toLowerCase() === importTag.name.toLowerCase()
      )
      
      if (existingByName) {
        const conflictFields = this.determineTagConflictFields(importTag, existingByName)
        
        conflicts.push({
          id: this.generateConflictId(),
          type: 'tag',
          conflictType: conflictFields.length > 1 ? 'data_mismatch' : 'name_conflict',
          existingData: existingByName,
          importData: importTag,
          conflictFields,
          similarity: this.calculateTagSimilarity(importTag, existingByName)
        })
      }
    }
    
    return conflicts
  }

  /**
   * 解决冲突
   * @param conflicts 冲突列表
   * @param resolutions 解决方案列表
   * @returns Promise<ResolvedData>
   */
  async resolveConflicts(
    conflicts: ConflictItem[], 
    resolutions: ConflictResolution[]
  ): Promise<ResolvedData> {
    try {
      const resolvedData: ResolvedData = {
        bookmarks: [],
        categories: [],
        tags: []
      }
      
      for (const resolution of resolutions) {
        const conflict = conflicts.find(c => c.id === resolution.conflictId)
        if (!conflict) continue
        
        let resolvedItem: any
        
        switch (resolution.action) {
          case 'keep_existing':
            // 保留现有数据，不添加到解决结果中
            continue
            
          case 'use_imported':
            resolvedItem = conflict.importData
            break
            
          case 'merge':
            resolvedItem = this.mergeData(conflict)
            break
            
          case 'manual_edit':
            resolvedItem = resolution.manualData || conflict.importData
            break
            
          default:
            resolvedItem = conflict.importData
        }
        
        // 根据类型添加到对应的数组中
        switch (conflict.type) {
          case 'bookmark':
            resolvedData.bookmarks.push(resolvedItem)
            break
          case 'category':
            resolvedData.categories.push(resolvedItem)
            break
          case 'tag':
            resolvedData.tags.push(resolvedItem)
            break
        }
      }
      
      return resolvedData
      
    } catch (error) {
      console.error('解决冲突失败:', error)
      throw error
    }
  }

  /**
   * 智能合并数据
   * @param conflict 冲突项
   * @returns 合并后的数据
   */
  private mergeData(conflict: ConflictItem): any {
    switch (conflict.type) {
      case 'bookmark':
        return this.mergeBookmarkData(conflict.existingData, conflict.importData)
      case 'category':
        return this.mergeCategoryData(conflict.existingData, conflict.importData)
      case 'tag':
        return this.mergeTagData(conflict.existingData, conflict.importData)
      default:
        return conflict.importData
    }
  }

  /**
   * 合并收藏夹数据
   * @param existing 现有数据
   * @param imported 导入数据
   * @returns 合并后的数据
   */
  private mergeBookmarkData(existing: Bookmark, imported: BookmarkInput): BookmarkInput {
    return {
      ...imported,
      // 合并标签（去重）
      tags: Array.from(new Set([...existing.tags, ...(imported.tags || [])])),
      
      // 合并描述（优先使用更长的描述）
      description: this.selectBetterDescription(existing.description, imported.description),
      
      // 合并内容（优先使用更长的内容）
      content: this.selectBetterContent(existing.content, imported.content),
      
      // 合并元数据
      metadata: {
        ...existing.metadata,
        ...imported.metadata,
        // 保持AI生成标记的准确性
        aiGenerated: existing.metadata.aiGenerated || imported.metadata?.aiGenerated || false
      }
    }
  }

  /**
   * 合并分类数据
   * @param existing 现有数据
   * @param imported 导入数据
   * @returns 合并后的数据
   */
  private mergeCategoryData(existing: Category, imported: CategoryInput): CategoryInput {
    return {
      ...imported,
      // 优先使用更详细的描述
      description: this.selectBetterDescription(existing.description, imported.description),
      
      // 保留现有颜色，除非导入数据有更好的颜色
      color: imported.color || existing.color,
      
      // 保留层级关系
      parentId: imported.parentId || existing.parentId
    }
  }

  /**
   * 合并标签数据
   * @param existing 现有数据
   * @param imported 导入数据
   * @returns 合并后的数据
   */
  private mergeTagData(existing: Tag, imported: TagInput): TagInput {
    return {
      ...imported,
      // 保留现有颜色，除非导入数据有更好的颜色
      color: imported.color || existing.color
    }
  }

  // ==================== 相似度计算方法 ====================

  /**
   * 计算收藏夹相似度
   */
  private calculateBookmarkSimilarity(bookmark1: BookmarkInput, bookmark2: Bookmark): number {
    let similarity = 0
    let factors = 0
    
    // 标题相似度
    if (bookmark1.title && bookmark2.title) {
      const titleSim = this.calculateTextSimilarity(bookmark1.title, bookmark2.title)
      similarity += titleSim * 0.4
      factors += 0.4
    }
    
    // URL相似度
    if (bookmark1.url && bookmark2.url) {
      const urlSim = bookmark1.url === bookmark2.url ? 1 : 0
      similarity += urlSim * 0.3
      factors += 0.3
    }
    
    // 内容相似度
    if (bookmark1.content && bookmark2.content) {
      const contentSim = this.calculateTextSimilarity(bookmark1.content, bookmark2.content)
      similarity += contentSim * 0.3
      factors += 0.3
    }
    
    return factors > 0 ? similarity : 0
  }

  /**
   * 计算分类相似度
   */
  private calculateCategorySimilarity(category1: CategoryInput, category2: Category): number {
    let similarity = 0
    let factors = 0
    
    // 名称相似度
    similarity += this.calculateTextSimilarity(category1.name, category2.name) * 0.6
    factors += 0.6
    
    // 描述相似度
    if (category1.description && category2.description) {
      similarity += this.calculateTextSimilarity(category1.description, category2.description) * 0.4
      factors += 0.4
    }
    
    return factors > 0 ? similarity / factors : 0
  }

  /**
   * 计算标签相似度
   */
  private calculateTagSimilarity(tag1: TagInput, tag2: Tag): number {
    return this.calculateTextSimilarity(tag1.name, tag2.name)
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    if (!text1 && !text2) return 1 // 两个都为空时认为相同
    if (!text1 || !text2) return 0 // 一个为空时认为不同
    
    const str1 = text1.toLowerCase().trim()
    const str2 = text2.toLowerCase().trim()
    
    if (str1 === str2) return 1
    
    // 使用简单的编辑距离算法
    const maxLength = Math.max(str1.length, str2.length)
    if (maxLength === 0) return 1
    
    const distance = this.calculateLevenshteinDistance(str1, str2)
    return 1 - distance / maxLength
  }

  /**
   * 计算编辑距离
   */
  private calculateLevenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // 删除
          matrix[j - 1][i] + 1,     // 插入
          matrix[j - 1][i - 1] + indicator  // 替换
        )
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  // ==================== 冲突字段检测方法 ====================

  /**
   * 确定收藏夹冲突字段
   */
  private determineBookmarkConflictFields(imported: BookmarkInput, existing: Bookmark): string[] {
    const conflictFields: string[] = []
    
    if (imported.title !== existing.title) conflictFields.push('title')
    if (imported.description !== existing.description) conflictFields.push('description')
    if (imported.content !== existing.content) conflictFields.push('content')
    if (imported.category !== existing.category) conflictFields.push('category')
    
    // 检查标签差异
    const importedTags = new Set(imported.tags || [])
    const existingTags = new Set(existing.tags)
    if (importedTags.size !== existingTags.size || 
        ![...importedTags].every(tag => existingTags.has(tag))) {
      conflictFields.push('tags')
    }
    
    return conflictFields
  }

  /**
   * 确定分类冲突字段
   */
  private determineCategoryConflictFields(imported: CategoryInput, existing: Category): string[] {
    const conflictFields: string[] = ['name'] // 名称冲突是必然的
    
    if (imported.description !== existing.description) conflictFields.push('description')
    if (imported.color !== existing.color) conflictFields.push('color')
    if (imported.parentId !== existing.parentId) conflictFields.push('parentId')
    
    return conflictFields
  }

  /**
   * 确定标签冲突字段
   */
  private determineTagConflictFields(imported: TagInput, existing: Tag): string[] {
    const conflictFields: string[] = ['name'] // 名称冲突是必然的
    
    if (imported.color !== existing.color) conflictFields.push('color')
    
    return conflictFields
  }

  // ==================== 工具方法 ====================

  /**
   * 选择更好的描述
   */
  private selectBetterDescription(desc1?: string, desc2?: string): string | undefined {
    if (!desc1 && !desc2) return undefined
    if (!desc1) return desc2
    if (!desc2) return desc1
    
    // 优先选择更长的描述
    return desc1.length >= desc2.length ? desc1 : desc2
  }

  /**
   * 选择更好的内容
   */
  private selectBetterContent(content1?: string, content2?: string): string | undefined {
    if (!content1 && !content2) return undefined
    if (!content1) return content2
    if (!content2) return content1
    
    // 优先选择更长的内容
    return content1.length >= content2.length ? content1 : content2
  }

  /**
   * 生成冲突ID
   */
  private generateConflictId(): string {
    return `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 导出单例实例
export const conflictResolverService = new ConflictResolverService()