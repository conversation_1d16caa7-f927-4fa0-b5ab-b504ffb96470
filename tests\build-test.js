/**
 * 简单的构建测试脚本
 * 检查主要组件是否可以正确导入
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 检查文件是否存在
function checkFileExists(filePath) {
  const fullPath = path.join(__dirname, filePath);
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ 文件不存在: ${filePath}`);
    return false;
  }
  console.log(`✅ 文件存在: ${filePath}`);
  return true;
}

// 检查文件内容是否包含特定模式
function checkFileContent(filePath, pattern, description) {
  const fullPath = path.join(__dirname, filePath);
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ 文件不存在: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  if (!pattern.test(content)) {
    console.error(`❌ ${description}: ${filePath}`);
    return false;
  }
  console.log(`✅ ${description}: ${filePath}`);
  return true;
}

console.log('🔍 开始构建检查...\n');

// 检查核心文件
const coreFiles = [
  'src/options/OptionsApp.tsx',
  'src/options/components/AboutTab.tsx',
  'src/options/components/HelpCenterTab.tsx',
  'src/options/components/HelpSearchBox.tsx',
  'src/options/components/ThemeToggle.tsx',
  'src/options/components/PageErrorBoundary.tsx',
  'src/options/hooks/useTheme.ts',
  'src/options/hooks/useResponsive.ts',
  'src/options/hooks/useCache.ts',
  'src/options/hooks/useLazyLoad.ts',
  'src/options/data/aboutInfo.ts',
  'src/options/data/helpContent.ts',
  'src/options/utils/manifestReader.ts',
  'src/options/utils/helpSearch.ts',
  'src/options/utils/performance.ts'
];

console.log('📁 检查核心文件...');
let allFilesExist = true;
coreFiles.forEach(file => {
  if (!checkFileExists(file)) {
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.error('\n❌ 部分核心文件缺失，构建可能失败');
  process.exit(1);
}

console.log('\n📝 检查组件导入...');

// 检查组件是否正确导入 React
const reactImportChecks = [
  ['src/options/OptionsApp.tsx', /^import React.*from 'react'/, 'React 导入检查'],
  ['src/options/components/AboutTab.tsx', /^import React.*from 'react'/, 'React 导入检查'],
  ['src/options/components/HelpCenterTab.tsx', /^import React.*from 'react'/, 'React 导入检查'],
  ['src/options/components/HelpSearchBox.tsx', /^import React.*from 'react'/, 'React 导入检查'],
  ['src/options/components/ThemeToggle.tsx', /^import React.*from 'react'/, 'React 导入检查']
];

let allImportsCorrect = true;
reactImportChecks.forEach(([file, pattern, description]) => {
  if (!checkFileContent(file, pattern, description)) {
    allImportsCorrect = false;
  }
});

console.log('\n🔧 检查 TypeScript 导出...');

// 检查主要导出
const exportChecks = [
  ['src/options/hooks/useTheme.ts', /export.*useTheme/, 'useTheme Hook 导出'],
  ['src/options/hooks/useResponsive.ts', /export.*useResponsive/, 'useResponsive Hook 导出'],
  ['src/options/components/AboutTab.tsx', /export default AboutTab/, 'AboutTab 组件导出'],
  ['src/options/components/HelpCenterTab.tsx', /export default HelpCenterTab/, 'HelpCenterTab 组件导出']
];

exportChecks.forEach(([file, pattern, description]) => {
  if (!checkFileContent(file, pattern, description)) {
    allImportsCorrect = false;
  }
});

if (allImportsCorrect) {
  console.log('\n✅ 所有检查通过！构建应该可以成功。');
} else {
  console.error('\n❌ 发现问题，请修复后重试。');
  process.exit(1);
}

console.log('\n🎉 构建检查完成！');