# AIProviderService 立即改进建议

## 1. 添加输入验证工具类

```typescript
// src/utils/validation.ts
export class ValidationUtils {
  static validateUrl(url: string): void {
    if (!url || typeof url !== 'string') {
      throw new Error('URL不能为空')
    }
    
    try {
      new URL(url)
    } catch {
      throw new Error('URL格式无效')
    }
  }
  
  static validateApiKey(apiKey: string, required: boolean = true): void {
    if (required && (!apiKey || typeof apiKey !== 'string')) {
      throw new Error('API密钥不能为空')
    }
    
    if (apiKey && apiKey.length < 10) {
      throw new Error('API密钥格式无效')
    }
  }
  
  static validateProviderConfig(config: AIProviderConfig): void {
    if (!config || !config.type) {
      throw new Error('提供商配置无效')
    }
    
    this.validateUrl(config.baseUrl)
    
    const requiresApiKey = ['openai', 'claude', 'gemini', 'openrouter']
    if (requiresApiKey.includes(config.type)) {
      this.validateApiKey(config.apiKey || '', true)
    }
  }
}
```

## 2. 改进错误处理

```typescript
// src/utils/errorHandler.ts
export class AIProviderError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly provider: string,
    public readonly originalError?: Error
  ) {
    super(message)
    this.name = 'AIProviderError'
  }
}

export class ErrorHandler {
  static handleConnectionError(error: any, provider: string): AIProviderError {
    if (error.name === 'AbortError') {
      return new AIProviderError(
        '连接超时，请检查服务是否运行',
        'CONNECTION_TIMEOUT',
        provider,
        error
      )
    }
    
    if (error.message.includes('ECONNREFUSED')) {
      return new AIProviderError(
        '服务未运行或端口不正确',
        'CONNECTION_REFUSED',
        provider,
        error
      )
    }
    
    return new AIProviderError(
      `连接失败: ${error.message}`,
      'CONNECTION_FAILED',
      provider,
      error
    )
  }
}
```

## 3. 提取公共接口

```typescript
// src/types/aiProvider.ts
export interface ServiceStatus {
  isRunning: boolean
  version?: string
  error?: string
}

export interface ConnectionTestResult {
  success: boolean
  modelCount?: number
  error?: string
}

export interface ModelFormatter {
  formatName(model: any): string
  generateDescription(model: any): string
  extractTags(model: any): string[]
  determineCapabilities(model: any): string[]
}
```

## 4. 使用示例

```typescript
export class AIProviderService {
  async testConnection(config: AIProviderConfig): Promise<AIConnectionResult> {
    // 添加输入验证
    ValidationUtils.validateProviderConfig(config)
    
    const startTime = Date.now()
    
    try {
      const provider = this.getProvider(config.type)
      const result = await provider.testConnection(config)
      
      return {
        providerId: config.id,
        success: result.success,
        responseTime: Date.now() - startTime,
        modelCount: result.modelCount,
        error: result.error,
        testedAt: new Date()
      }
    } catch (error) {
      const aiError = ErrorHandler.handleConnectionError(error, config.type)
      
      return {
        providerId: config.id,
        success: false,
        responseTime: Date.now() - startTime,
        error: aiError.message,
        testedAt: new Date()
      }
    }
  }
}
```

## 优势
1. 提高代码健壮性
2. 统一错误处理
3. 改善用户体验
4. 便于调试和维护