/**
 * 浏览器 API 模拟
 * 提供浏览器环境相关的 API 模拟，用于测试环境
 */

import { vi } from 'vitest'
import { TEST_CONFIG } from './test-config'

/**
 * 设置 window.matchMedia 模拟
 */
export const setupMatchMediaMock = () => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })
}

/**
 * 设置 localStorage 模拟
 */
export const setupLocalStorageMock = () => {
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn()
  }
  
  Object.defineProperty(window, 'localStorage', { 
    value: localStorageMock,
    writable: true
  })
  
  return localStorageMock
}

/**
 * 设置 location 模拟
 */
export const setupLocationMock = () => {
  Object.defineProperty(window, 'location', {
    value: {
      hash: '',
      pathname: '/options.html',
      search: '',
      href: `chrome-extension://${TEST_CONFIG.EXTENSION_ID}/options.html`,
      origin: `chrome-extension://${TEST_CONFIG.EXTENSION_ID}`,
      protocol: 'chrome-extension:',
      host: TEST_CONFIG.EXTENSION_ID,
      hostname: TEST_CONFIG.EXTENSION_ID,
      port: '',
      assign: vi.fn(),
      replace: vi.fn(),
      reload: vi.fn()
    },
    writable: true
  })
}

/**
 * 设置 history 模拟
 */
export const setupHistoryMock = () => {
  Object.defineProperty(window, 'history', {
    value: {
      replaceState: vi.fn(),
      pushState: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      go: vi.fn(),
      length: 1,
      state: null
    },
    writable: true
  })
}

/**
 * 设置观察者 API 模拟
 */
export const setupObserverMocks = () => {
  // ResizeObserver 模拟
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // IntersectionObserver 模拟
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
    root: null,
    rootMargin: '',
    thresholds: []
  }))

  // MutationObserver 模拟
  global.MutationObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn().mockReturnValue([])
  }))
}

/**
 * 设置动画 API 模拟
 */
export const setupAnimationMocks = () => {
  global.requestAnimationFrame = vi.fn().mockImplementation(
    cb => setTimeout(cb, TEST_CONFIG.ANIMATION_FRAME_DELAY)
  )
  global.cancelAnimationFrame = vi.fn().mockImplementation(
    id => clearTimeout(id)
  )
}

/**
 * 设置样式计算 API 模拟
 */
export const setupStyleMocks = () => {
  global.getComputedStyle = vi.fn().mockImplementation(() => ({
    getPropertyValue: vi.fn().mockReturnValue(''),
    setProperty: vi.fn(),
    removeProperty: vi.fn()
  }))
}

/**
 * 设置所有浏览器 API 模拟
 */
export const setupBrowserApiMocks = () => {
  setupMatchMediaMock()
  const localStorageMock = setupLocalStorageMock()
  setupLocationMock()
  setupHistoryMock()
  setupObserverMocks()
  setupAnimationMocks()
  setupStyleMocks()
  
  return { localStorageMock }
}