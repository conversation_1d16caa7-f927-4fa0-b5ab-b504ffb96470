// shadcn/ui 组件相关的 TypeScript 类型定义

import { type VariantProps } from "class-variance-authority"
import { buttonVariants } from "@/components/ui/button"

// Button 组件类型
export type ButtonVariant = VariantProps<typeof buttonVariants>["variant"]
export type ButtonSize = VariantProps<typeof buttonVariants>["size"]

// 通用组件属性类型
export interface ComponentProps {
  className?: string
  children?: React.ReactNode
}

// Dialog 相关类型
export interface DialogProps extends ComponentProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export interface DialogContentProps extends ComponentProps {
  onEscapeKeyDown?: (event: KeyboardEvent) => void
  onPointerDownOutside?: (event: PointerEvent) => void
}

// Form 相关类型
export interface FormFieldProps extends ComponentProps {
  name: string
  control?: any
  render: ({ field }: { field: any }) => React.ReactNode
}

export interface FormItemProps extends ComponentProps {
  // Form item 特定属性
}

export interface FormLabelProps extends ComponentProps {
  htmlFor?: string
}

export interface FormControlProps extends ComponentProps {
  // Form control 特定属性
}

export interface FormDescriptionProps extends ComponentProps {
  // Form description 特定属性
}

export interface FormMessageProps extends ComponentProps {
  // Form message 特定属性
}

// Input 相关类型
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string
}

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  className?: string
}

// Card 相关类型
export interface CardProps extends ComponentProps {
  // Card 特定属性
}

export interface CardHeaderProps extends ComponentProps {
  // Card header 特定属性
}

export interface CardTitleProps extends ComponentProps {
  // Card title 特定属性
}

export interface CardDescriptionProps extends ComponentProps {
  // Card description 特定属性
}

export interface CardContentProps extends ComponentProps {
  // Card content 特定属性
}

export interface CardFooterProps extends ComponentProps {
  // Card footer 特定属性
}

// Badge 相关类型
export interface BadgeProps extends ComponentProps {
  variant?: "default" | "secondary" | "destructive" | "outline"
}

// Select 相关类型
export interface SelectProps extends ComponentProps {
  value?: string
  onValueChange?: (value: string) => void
  defaultValue?: string
  disabled?: boolean
}

export interface SelectTriggerProps extends ComponentProps {
  // Select trigger 特定属性
}

export interface SelectValueProps extends ComponentProps {
  placeholder?: string
}

export interface SelectContentProps extends ComponentProps {
  // Select content 特定属性
}

export interface SelectItemProps extends ComponentProps {
  value: string
  disabled?: boolean
}

// DropdownMenu 相关类型
export interface DropdownMenuProps extends ComponentProps {
  // DropdownMenu 特定属性
}

export interface DropdownMenuTriggerProps extends ComponentProps {
  asChild?: boolean
}

export interface DropdownMenuContentProps extends ComponentProps {
  align?: "start" | "center" | "end"
  side?: "top" | "right" | "bottom" | "left"
  sideOffset?: number
}

export interface DropdownMenuItemProps extends ComponentProps {
  disabled?: boolean
  onSelect?: (event: Event) => void
}

export interface DropdownMenuSeparatorProps extends ComponentProps {
  // DropdownMenu separator 特定属性
}

export interface DropdownMenuLabelProps extends ComponentProps {
  // DropdownMenu label 特定属性
}

// Tooltip 相关类型
export interface TooltipProps extends ComponentProps {
  // Tooltip 特定属性
}

export interface TooltipTriggerProps extends ComponentProps {
  asChild?: boolean
}

export interface TooltipContentProps extends ComponentProps {
  side?: "top" | "right" | "bottom" | "left"
  sideOffset?: number
}

// AlertDialog 相关类型
export interface AlertDialogProps extends ComponentProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export interface AlertDialogTriggerProps extends ComponentProps {
  asChild?: boolean
}

export interface AlertDialogContentProps extends ComponentProps {
  // AlertDialog content 特定属性
}

export interface AlertDialogHeaderProps extends ComponentProps {
  // AlertDialog header 特定属性
}

export interface AlertDialogTitleProps extends ComponentProps {
  // AlertDialog title 特定属性
}

export interface AlertDialogDescriptionProps extends ComponentProps {
  // AlertDialog description 特定属性
}

export interface AlertDialogFooterProps extends ComponentProps {
  // AlertDialog footer 特定属性
}

export interface AlertDialogActionProps extends ComponentProps {
  // AlertDialog action 特定属性
}

export interface AlertDialogCancelProps extends ComponentProps {
  // AlertDialog cancel 特定属性
}

// 主题相关类型
export type Theme = "light" | "dark" | "system"

export interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

// 通用事件处理器类型
export type EventHandler<T = Event> = (event: T) => void
export type ChangeHandler<T = string> = (value: T) => void

// 组件状态类型
export type ComponentState = "idle" | "loading" | "success" | "error"

// 尺寸类型
export type Size = "sm" | "md" | "lg" | "xl"

// 颜色变体类型
export type ColorVariant = "default" | "primary" | "secondary" | "success" | "warning" | "danger"

// 位置类型
export type Position = "top" | "right" | "bottom" | "left"
export type Alignment = "start" | "center" | "end"

// 响应式断点类型
export type Breakpoint = "sm" | "md" | "lg" | "xl" | "2xl"

// 动画类型
export type AnimationType = "fade" | "slide" | "scale" | "bounce"

// 组件引用类型
export type ComponentRef<T = HTMLElement> = React.RefObject<T>

// 渲染函数类型
export type RenderFunction<T = any> = (props: T) => React.ReactNode

// 组件工厂类型
export type ComponentFactory<T = any> = (props: T) => React.ComponentType

// 样式类名生成器类型
export type ClassNameGenerator = (...classes: (string | undefined | null | false)[]) => string

// 组件配置类型
export interface ComponentConfig {
  className?: string
  variant?: string
  size?: string
  disabled?: boolean
  loading?: boolean
}

// 表单字段配置类型
export interface FieldConfig {
  name: string
  label?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  validation?: any
}

// 数据表格相关类型
export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex?: string
  render?: (value: any, record: T, index: number) => React.ReactNode
  width?: number | string
  align?: "left" | "center" | "right"
  sortable?: boolean
  filterable?: boolean
}

export interface TableProps<T = any> {
  columns: TableColumn<T>[]
  data: T[]
  loading?: boolean
  pagination?: boolean
  pageSize?: number
  onRowClick?: (record: T, index: number) => void
  className?: string
}

// 虚拟滚动相关类型
export interface VirtualScrollProps {
  items: any[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: any, index: number) => React.ReactNode
  overscan?: number
}

// 无限滚动相关类型
export interface InfiniteScrollProps {
  hasMore: boolean
  loadMore: () => void
  loader?: React.ReactNode
  threshold?: number
  children: React.ReactNode
}