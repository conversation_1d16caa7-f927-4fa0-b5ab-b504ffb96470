// ValidationUtils 扩展功能测试

import { describe, it, expect, beforeEach } from 'vitest'
import { ValidationUtils } from '../src/utils/validation'
import { ImportData, ExportAllOptions, ConflictResolution } from '../src/types'

describe('ValidationUtils - 扩展功能', () => {
  describe('validateImportData', () => {
    it('应该验证有效的导入数据', () => {
      const validData: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 1,
          totalCategories: 1,
          totalTags: 1,
          exportOptions: {}
        },
        bookmarks: [{
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '测试分类',
          tags: ['测试标签']
        }],
        categories: [{
          name: '测试分类',
          description: '测试描述',
          color: '#3B82F6'
        }],
        tags: [{
          name: '测试标签',
          color: '#F59E0B'
        }]
      }

      const result = ValidationUtils.validateImportData(validData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测缺少必需字段的错误', () => {
      const invalidData = {
        // 缺少 version
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 0,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: {}
        }
      } as ImportData

      const result = ValidationUtils.validateImportData(invalidData)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.field === 'version')).toBe(true)
    })

    it('应该验证无效的版本号格式', () => {
      const invalidData: ImportData = {
        version: 'invalid-version',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 0,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: {}
        }
      }

      const result = ValidationUtils.validateImportData(invalidData)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'INVALID_VERSION')).toBe(true)
    })

    it('应该验证无效的导出类型', () => {
      const invalidData: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'invalid' as any,
        metadata: {
          source: 'Test',
          totalBookmarks: 0,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: {}
        }
      }

      const result = ValidationUtils.validateImportData(invalidData)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'INVALID_EXPORT_TYPE')).toBe(true)
    })
  })

  describe('validateExportOptions', () => {
    it('应该验证有效的全部数据导出选项', () => {
      const validOptions: ExportAllOptions = {
        format: 'json',
        includeBookmarks: true,
        includeCategories: true,
        includeTags: true,
        includeMetadata: true,
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-12-31')
        }
      }

      const result = ValidationUtils.validateExportOptions(validOptions, 'all')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测无效的日期范围', () => {
      const invalidOptions: ExportAllOptions = {
        format: 'json',
        includeBookmarks: true,
        includeCategories: true,
        includeTags: true,
        includeMetadata: true,
        dateRange: {
          start: new Date('2024-12-31'),
          end: new Date('2024-01-01') // 结束日期早于开始日期
        }
      }

      const result = ValidationUtils.validateExportOptions(invalidOptions, 'all')
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'INVALID_DATE_ORDER')).toBe(true)
    })

    it('应该检测无效的格式', () => {
      const invalidOptions = {
        format: 'xml', // 不支持的格式
        includeBookmarks: true,
        includeCategories: true,
        includeTags: true,
        includeMetadata: true
      } as ExportAllOptions

      const result = ValidationUtils.validateExportOptions(invalidOptions, 'all')
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'INVALID_FORMAT')).toBe(true)
    })
  })

  describe('validateConflictResolutions', () => {
    it('应该验证有效的冲突解决方案', () => {
      const validResolutions: ConflictResolution[] = [
        {
          conflictId: 'conflict1',
          action: 'keep_existing'
        },
        {
          conflictId: 'conflict2',
          action: 'use_imported'
        },
        {
          conflictId: 'conflict3',
          action: 'merge'
        },
        {
          conflictId: 'conflict4',
          action: 'manual_edit',
          manualData: { title: '手动编辑的标题' }
        }
      ]

      const result = ValidationUtils.validateConflictResolutions(validResolutions)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测缺少冲突ID的错误', () => {
      const invalidResolutions = [
        {
          // 缺少 conflictId
          action: 'keep_existing'
        }
      ] as ConflictResolution[]

      const result = ValidationUtils.validateConflictResolutions(invalidResolutions)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'CONFLICT_ID_REQUIRED')).toBe(true)
    })

    it('应该检测无效的解决方案类型', () => {
      const invalidResolutions = [
        {
          conflictId: 'conflict1',
          action: 'invalid_action' as any
        }
      ] as ConflictResolution[]

      const result = ValidationUtils.validateConflictResolutions(invalidResolutions)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'INVALID_RESOLUTION_ACTION')).toBe(true)
    })

    it('应该检测手动编辑模式缺少数据的错误', () => {
      const invalidResolutions: ConflictResolution[] = [
        {
          conflictId: 'conflict1',
          action: 'manual_edit'
          // 缺少 manualData
        }
      ]

      const result = ValidationUtils.validateConflictResolutions(invalidResolutions)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'MANUAL_DATA_REQUIRED')).toBe(true)
    })
  })

  describe('batchValidate', () => {
    it('应该批量验证多个项目', () => {
      const items = [
        { type: 'url', title: '有效收藏', url: 'https://example.com' },
        { type: 'url', title: '', url: 'https://example2.com' }, // 无效：标题为空
        { type: 'url', title: '另一个有效收藏', url: 'https://example3.com' }
      ]

      const result = ValidationUtils.batchValidate(items, ValidationUtils.validateBookmarkInput)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].field).toContain('[1]') // 第二个项目的错误
    })
  })

  describe('checkDataIntegrity', () => {
    it('应该检测数据数量不匹配', () => {
      const data: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 2, // 声明有2个收藏
          totalCategories: 1,
          totalTags: 1,
          exportOptions: {}
        },
        bookmarks: [{ // 实际只有1个收藏
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '测试分类',
          tags: ['测试标签']
        }],
        categories: [{
          name: '测试分类'
        }],
        tags: [{
          name: '测试标签'
        }]
      }

      const result = ValidationUtils.checkDataIntegrity(data)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'BOOKMARKS_COUNT_MISMATCH')).toBe(true)
    })

    it('应该检测引用完整性错误', () => {
      const data: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 1,
          totalCategories: 1,
          totalTags: 0,
          exportOptions: {}
        },
        bookmarks: [{
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '不存在的分类', // 引用了不存在的分类
          tags: []
        }],
        categories: [{
          name: '测试分类'
        }]
      }

      const result = ValidationUtils.checkDataIntegrity(data)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'CATEGORY_REFERENCE_INVALID')).toBe(true)
    })

    it('应该通过完整性检查', () => {
      const data: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 1,
          totalCategories: 1,
          totalTags: 1,
          exportOptions: {}
        },
        bookmarks: [{
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '测试分类',
          tags: ['测试标签']
        }],
        categories: [{
          name: '测试分类'
        }],
        tags: [{
          name: '测试标签'
        }]
      }

      const result = ValidationUtils.checkDataIntegrity(data)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })
})