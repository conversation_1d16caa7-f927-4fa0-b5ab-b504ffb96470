// 调试收藏功能问题的脚本

// 模拟浏览器环境
import FDBFactory from 'fake-indexeddb/lib/FDBFactory'
import FDBKeyRange from 'fake-indexeddb/lib/FDBKeyRange'

global.indexedDB = new FDBFactory()
global.IDBKeyRange = FDBKeyRange

// 动态导入 TypeScript 模块
const { bookmarkService } = await import('./src/services/bookmarkService.ts')

async function debugBookmarkIssue() {
  console.log('=== 开始调试收藏功能问题 ===')
  
  try {
    // 1. 测试快速收藏功能
    console.log('\n1. 测试快速收藏功能...')
    const bookmarkId = await bookmarkService.quickBookmark(
      'opendatalab/MinerU: A high-quality tool...',
      'https://github.com/opendatalab/MinerU',
      'https://github.com/favicon.ico'
    )
    console.log('收藏ID:', bookmarkId)
    
    // 2. 检查收藏是否真的保存了
    console.log('\n2. 检查收藏是否保存...')
    const savedBookmark = await bookmarkService.getBookmark(bookmarkId)
    console.log('保存的收藏:', savedBookmark ? '存在' : '不存在')
    if (savedBookmark) {
      console.log('收藏详情:', {
        id: savedBookmark.id,
        title: savedBookmark.title,
        url: savedBookmark.url,
        category: savedBookmark.category
      })
    }
    
    // 3. 获取所有收藏列表
    console.log('\n3. 获取所有收藏列表...')
    const allBookmarks = await bookmarkService.getBookmarks()
    console.log('收藏总数:', allBookmarks.length)
    allBookmarks.forEach((bookmark, index) => {
      console.log(`收藏 ${index + 1}:`, {
        id: bookmark.id,
        title: bookmark.title,
        url: bookmark.url,
        category: bookmark.category
      })
    })
    
    // 4. 检查收藏状态
    console.log('\n4. 检查收藏状态...')
    const status = await bookmarkService.checkBookmarkStatus('https://github.com/opendatalab/MinerU')
    console.log('收藏状态:', status)
    
    // 5. 测试数据库统计
    console.log('\n5. 获取数据库统计...')
    const stats = await bookmarkService.getBookmarkStats()
    console.log('统计信息:', stats)
    
  } catch (error) {
    console.error('调试过程中发生错误:', error)
    console.error('错误堆栈:', error.stack)
  }
}

// 运行调试
debugBookmarkIssue().then(() => {
  console.log('\n=== 调试完成 ===')
}).catch(error => {
  console.error('调试失败:', error)
})