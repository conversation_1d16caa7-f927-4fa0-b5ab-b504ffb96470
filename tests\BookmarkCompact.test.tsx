// BookmarkCompact组件单元测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import '@testing-library/jest-dom'
import BookmarkCompact from '../src/components/BookmarkCompact'
import type { Bookmark } from '../src/types'

// 模拟TruncatedTitle组件
vi.mock('../src/components/TruncatedTitle', () => ({
  default: function MockTruncatedTitle({ title, className }: any) {
    return <span className={className}>{title}</span>
  }
}))

// 创建测试用的收藏数据
const createMockBookmark = (overrides: Partial<Bookmark> = {}): Bookmark => ({
  id: 'test-bookmark-1',
  type: 'url',
  title: '测试收藏标题',
  url: 'https://example.com',
  description: '这是一个测试描述，用于验证紧凑视图的显示效果',
  tags: ['测试', '标签', '紧凑视图'],
  category: '测试分类',
  favicon: 'https://example.com/favicon.ico',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  metadata: {
    pageTitle: '测试页面标题',
    siteName: 'example.com',
    aiGenerated: false
  },
  ...overrides
})

describe('BookmarkCompact组件', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    // 模拟window.open
    global.open = vi.fn()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('基础渲染', () => {
    it('应该正确渲染紧凑视图', () => {
      const bookmark = createMockBookmark()
      
      render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onClick={mockOnClick}
        />
      )

      // 验证标题显示
      expect(screen.getByText('测试收藏标题')).toBeInTheDocument()
      
      // 验证URL显示
      expect(screen.getByText('https://example.com')).toBeInTheDocument()
      
      // 验证描述显示
      expect(screen.getByText('这是一个测试描述，用于验证紧凑视图的显示效果')).toBeInTheDocument()
      
      // 验证分类显示
      expect(screen.getByText('测试分类')).toBeInTheDocument()
      
      // 验证标签显示
      expect(screen.getByText('测试')).toBeInTheDocument()
      expect(screen.getByText('标签')).toBeInTheDocument()
      
      // 验证时间显示
      expect(screen.getByText('1月1日')).toBeInTheDocument()
    })

    it('应该正确显示网站图标', () => {
      const bookmark = createMockBookmark()
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      const favicon = screen.getByAltText('')
      expect(favicon).toHaveAttribute('src', 'https://example.com/favicon.ico')
    })

    it('当没有网站图标时应该显示默认图标', () => {
      const bookmark = createMockBookmark({ favicon: undefined })
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      // 应该显示Star图标
      const starIcon = container.querySelector('svg')
      expect(starIcon).toBeInTheDocument()
      expect(starIcon).not.toHaveClass('hidden')
    })

    it('应该正确处理无标题的情况', () => {
      const bookmark = createMockBookmark({ title: '' })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.getByText('无标题')).toBeInTheDocument()
    })

    it('应该正确处理无URL的情况', () => {
      const bookmark = createMockBookmark({ url: undefined })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      // URL不应该显示
      expect(screen.queryByText('https://example.com')).not.toBeInTheDocument()
      
      // 外部链接按钮不应该显示
      expect(screen.queryByTitle('在新标签页中打开')).not.toBeInTheDocument()
    })

    it('应该正确处理无描述的情况', () => {
      const bookmark = createMockBookmark({ description: undefined })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      // 描述不应该显示
      expect(screen.queryByText('这是一个测试描述，用于验证紧凑视图的显示效果')).not.toBeInTheDocument()
    })
  })

  describe('文本类型收藏', () => {
    it('应该显示文本内容预览', () => {
      const bookmark = createMockBookmark({
        type: 'text',
        content: '这是一段测试文本内容，用于验证文本类型收藏的显示效果'
      })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.getByText('这是一段测试文本内容，用于验证文本类型收藏的显示效果')).toBeInTheDocument()
    })

    it('非文本类型不应该显示内容预览', () => {
      const bookmark = createMockBookmark({
        type: 'url',
        content: '这是一段不应该显示的内容'
      })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.queryByText('这是一段不应该显示的内容')).not.toBeInTheDocument()
    })
  })

  describe('标签显示', () => {
    it('应该显示前两个标签', () => {
      const bookmark = createMockBookmark({
        tags: ['标签1', '标签2', '标签3', '标签4']
      })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.getByText('标签1')).toBeInTheDocument()
      expect(screen.getByText('标签2')).toBeInTheDocument()
      expect(screen.getByText('+2')).toBeInTheDocument()
    })

    it('当标签少于等于2个时不显示+号', () => {
      const bookmark = createMockBookmark({
        tags: ['标签1', '标签2']
      })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.getByText('标签1')).toBeInTheDocument()
      expect(screen.getByText('标签2')).toBeInTheDocument()
      expect(screen.queryByText(/^\+/)).not.toBeInTheDocument()
    })

    it('当没有标签时不显示标签区域', () => {
      const bookmark = createMockBookmark({ tags: [] })
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      // 不应该有Tag图标
      const tagIcon = container.querySelector('.lucide-tag')
      expect(tagIcon).not.toBeInTheDocument()
    })
  })

  describe('类型指示器', () => {
    it('文本类型应该显示蓝色指示器', () => {
      const bookmark = createMockBookmark({ type: 'text' })
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      const indicator = container.querySelector('.bg-blue-400')
      expect(indicator).toBeInTheDocument()
    })

    it('图片类型应该显示绿色指示器', () => {
      const bookmark = createMockBookmark({ type: 'image' })
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      const indicator = container.querySelector('.bg-green-400')
      expect(indicator).toBeInTheDocument()
    })

    it('URL类型不应该显示指示器', () => {
      const bookmark = createMockBookmark({ type: 'url' })
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      const indicator = container.querySelector('.bg-blue-400, .bg-green-400, .bg-gray-400')
      expect(indicator).not.toBeInTheDocument()
    })
  })

  describe('高亮状态', () => {
    it('应该正确应用高亮样式', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} isHighlighted={true} />
      )
      
      const cardElement = container.firstChild as HTMLElement
      expect(cardElement).toHaveClass('ring-2', 'ring-primary-500', 'border-primary-300', 'bg-primary-50')
    })

    it('默认情况下不应该有高亮样式', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} />
      )
      
      const cardElement = container.firstChild as HTMLElement
      expect(cardElement).not.toHaveClass('ring-2', 'ring-primary-500', 'border-primary-300', 'bg-primary-50')
    })
  })

  describe('交互功能', () => {
    it('点击卡片应该触发onClick回调', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onClick={mockOnClick}
        />
      )
      
      const card = container.firstChild as HTMLElement
      fireEvent.click(card)
      
      expect(mockOnClick).toHaveBeenCalledWith(bookmark)
    })

    it('点击编辑按钮应该触发onEdit回调', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        const editButton = screen.getByTitle('编辑收藏')
        fireEvent.click(editButton)
      })
      
      expect(mockOnEdit).toHaveBeenCalledWith(bookmark)
    })

    it('点击删除按钮应该触发onDelete回调', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        const deleteButton = screen.getByTitle('删除收藏')
        fireEvent.click(deleteButton)
      })
      
      expect(mockOnDelete).toHaveBeenCalledWith(bookmark)
    })

    it('点击外部链接按钮应该打开新窗口', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        const linkButton = screen.getByTitle('在新标签页中打开')
        fireEvent.click(linkButton)
      })
      
      expect(global.open).toHaveBeenCalledWith(
        'https://example.com', 
        '_blank', 
        'noopener,noreferrer'
      )
    })
  })

  describe('时间格式化', () => {
    it('应该正确显示今天的时间', () => {
      const today = new Date()
      const bookmark = createMockBookmark({ createdAt: today })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.getByText('今天')).toBeInTheDocument()
    })

    it('应该正确显示昨天的时间', () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const bookmark = createMockBookmark({ createdAt: yesterday })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.getByText('昨天')).toBeInTheDocument()
    })

    it('应该正确显示几天前的时间', () => {
      const threeDaysAgo = new Date()
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
      const bookmark = createMockBookmark({ createdAt: threeDaysAgo })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.getByText('3天前')).toBeInTheDocument()
    })

    it('应该正确显示较早的日期', () => {
      const longAgo = new Date('2023-06-15')
      const bookmark = createMockBookmark({ createdAt: longAgo })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      // 应该显示月日格式
      expect(screen.getByText(/6月|15/)).toBeInTheDocument()
    })
  })

  describe('图标错误处理', () => {
    it('当网站图标加载失败时应该显示默认图标', () => {
      const bookmark = createMockBookmark()
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      const favicon = screen.getByAltText('')
      
      // 模拟图标加载失败
      fireEvent.error(favicon)
      
      // 验证图标被隐藏
      expect(favicon).toHaveStyle('display: none')
    })
  })

  describe('可访问性', () => {
    it('操作按钮应该有正确的aria-label', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        expect(screen.getByLabelText('在新标签页中打开')).toBeInTheDocument()
        expect(screen.getByLabelText('编辑收藏')).toBeInTheDocument()
        expect(screen.getByLabelText('删除收藏')).toBeInTheDocument()
      })
    })

    it('类型指示器应该有正确的title属性', () => {
      const bookmark = createMockBookmark({ type: 'text' })
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      const indicator = container.querySelector('[title="类型: text"]')
      expect(indicator).toBeInTheDocument()
    })
  })

  describe('自定义样式', () => {
    it('应该正确应用自定义CSS类名', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} className="custom-class" />
      )
      
      const cardElement = container.firstChild as HTMLElement
      expect(cardElement).toHaveClass('custom-class')
    })
  })

  describe('响应式布局', () => {
    it('应该正确处理长标题的截断', () => {
      const bookmark = createMockBookmark({
        title: '这是一个非常非常长的标题，用于测试标题截断功能是否正常工作'
      })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      // TruncatedTitle组件应该被调用
      expect(screen.getByText('这是一个非常非常长的标题，用于测试标题截断功能是否正常工作')).toBeInTheDocument()
    })

    it('应该正确处理长URL的截断', () => {
      const bookmark = createMockBookmark({
        url: 'https://example.com/very/long/path/that/should/be/truncated/for/better/display'
      })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      expect(screen.getByText('https://example.com/very/long/path/that/should/be/truncated/for/better/display')).toBeInTheDocument()
    })
  })
})