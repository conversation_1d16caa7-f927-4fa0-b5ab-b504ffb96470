// 标签页导航逻辑的自定义Hook
import { useState, useEffect, useCallback } from 'react'

interface Tab {
  id: string
  name: string
  icon: any
  isTestPage?: boolean
}

interface UseTabNavigationProps {
  tabs: Tab[]
  defaultTab?: string
}

export const useTabNavigation = ({ tabs, defaultTab = 'bookmarks' }: UseTabNavigationProps) => {
  const [activeTab, setActiveTab] = useState(defaultTab)

  // 处理标签页切换
  const handleTabChange = useCallback((tabId: string) => {
    if (tabId !== activeTab) {
      setActiveTab(tabId)
      // 更新URL hash
      window.history.replaceState(null, '', `#${tabId}`)
      
      // 保存到本地存储
      try {
        localStorage.setItem('options-active-tab', tabId)
      } catch (error) {
        console.warn('无法保存标签页状态到本地存储:', error)
      }
    }
  }, [activeTab])

  // 检查URL hash来确定默认标签页
  useEffect(() => {
    const hash = window.location.hash.substring(1)
    const validTabIds = tabs.map(tab => tab.id)
    
    if (hash && validTabIds.includes(hash)) {
      setActiveTab(hash)
    }
  }, [tabs])

  // 监听浏览器前进后退
  useEffect(() => {
    const handlePopState = () => {
      const hash = window.location.hash.substring(1)
      if (hash && tabs.some(tab => tab.id === hash)) {
        setActiveTab(hash)
      }
    }

    window.addEventListener('popstate', handlePopState)
    return () => window.removeEventListener('popstate', handlePopState)
  }, [tabs])

  // 从本地存储恢复标签页状态
  useEffect(() => {
    try {
      const savedTab = localStorage.getItem('options-active-tab')
      if (savedTab && tabs.some(tab => tab.id === savedTab) && !window.location.hash) {
        setActiveTab(savedTab)
        window.history.replaceState(null, '', `#${savedTab}`)
      }
    } catch (error) {
      console.warn('无法从本地存储读取标签页状态:', error)
    }
  }, [tabs])

  // 键盘导航支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Alt + 数字键快速切换标签页
      if (e.altKey && e.key >= '1' && e.key <= '7') {
        e.preventDefault()
        const tabIndex = parseInt(e.key) - 1
        if (tabs[tabIndex]) {
          handleTabChange(tabs[tabIndex].id)
        }
      }
      
      // Ctrl/Cmd + K 快速搜索（如果在收藏管理页面）
      if ((e.ctrlKey || e.metaKey) && e.key === 'k' && activeTab === 'bookmarks') {
        e.preventDefault()
        // 聚焦到搜索框
        const searchInput = document.querySelector('input[placeholder*="搜索"]') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [activeTab, handleTabChange, tabs])

  return {
    activeTab,
    handleTabChange
  }
}