// 冲突列表面板组件

import React from 'react'
import { CheckCircle2, XCircle } from 'lucide-react'
import { ConflictItem, ConflictResolution } from '../../types'
import { getDataTypeLabel, getDataTypeColor, getConflictTypeLabel } from './utils'

export interface ConflictListPanelProps {
  conflicts: ConflictItem[]
  resolutions: ConflictResolution[]
  currentConflictIndex: number
  stats: {
    resolved: number
    total: number
    byType: Record<string, number>
  }
  onNavigateToConflict: (index: number) => void
}

const ConflictListPanel: React.FC<ConflictListPanelProps> = ({
  conflicts,
  resolutions,
  currentConflictIndex,
  stats,
  onNavigateToConflict
}) => {
  return (
    <div className="w-80 border-r border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-medium text-gray-900">冲突列表</h3>
        <div className="text-sm text-gray-600 mt-1">
          收藏 {stats.byType.bookmark || 0} · 
          分类 {stats.byType.category || 0} · 
          标签 {stats.byType.tag || 0}
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {conflicts.map((conflict, index) => {
          const isResolved = resolutions.some(r => r.conflictId === conflict.id)
          const isCurrent = index === currentConflictIndex
          
          return (
            <div
              key={conflict.id}
              onClick={() => onNavigateToConflict(index)}
              className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                isCurrent ? 'bg-blue-50 border-blue-200' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className={`w-2 h-2 rounded-full ${getDataTypeColor(conflict.type)}`} />
                  <span className="text-sm font-medium text-gray-900">
                    {getDataTypeLabel(conflict.type)}
                  </span>
                </div>
                
                {isResolved ? (
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-400" />
                )}
              </div>
              
              <div className="mt-1">
                <div className="text-sm text-gray-900 truncate">
                  {conflict.importData.title || conflict.importData.name}
                </div>
                <div className="text-xs text-gray-500">
                  {getConflictTypeLabel(conflict.conflictType)}
                  · 相似度 {Math.round(conflict.similarity * 100)}%
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default ConflictListPanel