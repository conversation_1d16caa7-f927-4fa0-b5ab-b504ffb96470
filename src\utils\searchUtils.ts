/**
 * 搜索和筛选工具
 * 提供高级搜索、模糊匹配、权重排序等功能
 */

import type { Bookmark } from '../types'

// 搜索配置接口
export interface SearchConfig {
  /** 是否启用模糊搜索 */
  fuzzySearch?: boolean
  /** 是否区分大小写 */
  caseSensitive?: boolean
  /** 最小搜索长度 */
  minSearchLength?: number
  /** 搜索权重配置 */
  weights?: SearchWeights
  /** 高亮配置 */
  highlight?: HighlightConfig
}

// 搜索权重配置
export interface SearchWeights {
  title?: number
  description?: number
  content?: number
  url?: number
  tags?: number
  category?: number
}

// 高亮配置
export interface HighlightConfig {
  enabled?: boolean
  className?: string
  tag?: string
}

// 搜索结果接口
export interface SearchResult<T = Bookmark> {
  item: T
  score: number
  matches: SearchMatch[]
  highlighted?: T
}

// 搜索匹配信息
export interface SearchMatch {
  field: keyof Bookmark
  value: string
  indices: [number, number][]
  score: number
}

// 筛选条件接口
export interface FilterCondition {
  field: keyof Bookmark
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'in' | 'range'
  value: any
  caseSensitive?: boolean
}

// 排序配置
export interface SortConfig {
  field: keyof Bookmark
  direction: 'asc' | 'desc'
  type?: 'string' | 'number' | 'date'
}

// 默认搜索配置
const DEFAULT_SEARCH_CONFIG: Required<SearchConfig> = {
  fuzzySearch: true,
  caseSensitive: false,
  minSearchLength: 1,
  weights: {
    title: 3,
    description: 2,
    content: 1,
    url: 1,
    tags: 2,
    category: 1
  },
  highlight: {
    enabled: true,
    className: 'search-highlight',
    tag: 'mark'
  }
}

/**
 * 高级搜索类
 * 提供模糊搜索、权重排序、高亮等功能
 */
export class AdvancedSearch {
  private config: Required<SearchConfig>

  constructor(config: SearchConfig = {}) {
    this.config = { ...DEFAULT_SEARCH_CONFIG, ...config }
  }

  /**
   * 搜索收藏项目
   * @param items 收藏项目列表
   * @param query 搜索查询
   * @returns 搜索结果
   */
  search(items: Bookmark[], query: string): SearchResult[] {
    if (!query || query.length < this.config.minSearchLength) {
      return items.map(item => ({
        item,
        score: 0,
        matches: []
      }))
    }

    const normalizedQuery = this.config.caseSensitive ? query : query.toLowerCase()
    const results: SearchResult[] = []

    for (const item of items) {
      const matches = this.findMatches(item, normalizedQuery)
      if (matches.length > 0) {
        const score = this.calculateScore(matches)
        const highlighted = this.config.highlight.enabled 
          ? this.highlightMatches(item, matches) 
          : undefined

        results.push({
          item,
          score,
          matches,
          highlighted
        })
      }
    }

    // 按分数排序
    return results.sort((a, b) => b.score - a.score)
  }

  /**
   * 查找匹配项
   * @param item 收藏项目
   * @param query 搜索查询
   * @returns 匹配信息列表
   */
  private findMatches(item: Bookmark, query: string): SearchMatch[] {
    const matches: SearchMatch[] = []
    const searchableFields: (keyof Bookmark)[] = ['title', 'description', 'content', 'url', 'category']

    for (const field of searchableFields) {
      const value = item[field]
      if (typeof value === 'string' && value) {
        const normalizedValue = this.config.caseSensitive ? value : value.toLowerCase()
        const indices = this.findStringMatches(normalizedValue, query)
        
        if (indices.length > 0) {
          matches.push({
            field,
            value: normalizedValue,
            indices,
            score: this.calculateFieldScore(field, indices, normalizedValue.length)
          })
        }
      }
    }

    // 搜索标签
    if (item.tags && Array.isArray(item.tags)) {
      for (const tag of item.tags) {
        const normalizedTag = this.config.caseSensitive ? tag : tag.toLowerCase()
        const indices = this.findStringMatches(normalizedTag, query)
        
        if (indices.length > 0) {
          matches.push({
            field: 'tags',
            value: normalizedTag,
            indices,
            score: this.calculateFieldScore('tags', indices, normalizedTag.length)
          })
        }
      }
    }

    return matches
  }

  /**
   * 在字符串中查找匹配位置
   * @param text 文本
   * @param query 查询
   * @returns 匹配位置数组
   */
  private findStringMatches(text: string, query: string): [number, number][] {
    const indices: [number, number][] = []
    
    if (this.config.fuzzySearch) {
      // 模糊搜索：支持部分匹配和字符间隔
      const words = query.split(/\s+/).filter(word => word.length > 0)
      
      for (const word of words) {
        let startIndex = 0
        let index = text.indexOf(word, startIndex)
        
        while (index !== -1) {
          indices.push([index, index + word.length])
          startIndex = index + 1
          index = text.indexOf(word, startIndex)
        }
      }
    } else {
      // 精确搜索
      let startIndex = 0
      let index = text.indexOf(query, startIndex)
      
      while (index !== -1) {
        indices.push([index, index + query.length])
        startIndex = index + 1
        index = text.indexOf(query, startIndex)
      }
    }

    return indices
  }

  /**
   * 计算字段分数
   * @param field 字段名
   * @param indices 匹配位置
   * @param textLength 文本长度
   * @returns 分数
   */
  private calculateFieldScore(field: keyof Bookmark, indices: [number, number][], textLength: number): number {
    const weight = this.config.weights[field] || 1
    const matchLength = indices.reduce((sum, [start, end]) => sum + (end - start), 0)
    const coverage = matchLength / textLength
    const matchCount = indices.length
    
    // 综合考虑权重、覆盖率和匹配次数
    return weight * (coverage * 0.7 + Math.min(matchCount / 10, 1) * 0.3)
  }

  /**
   * 计算总分数
   * @param matches 匹配列表
   * @returns 总分数
   */
  private calculateScore(matches: SearchMatch[]): number {
    return matches.reduce((sum, match) => sum + match.score, 0)
  }

  /**
   * 高亮匹配文本
   * @param item 收藏项目
   * @param matches 匹配信息
   * @returns 高亮后的项目
   */
  private highlightMatches(item: Bookmark, matches: SearchMatch[]): Bookmark {
    const highlighted = { ...item }
    const { className, tag } = this.config.highlight

    for (const match of matches) {
      const field = match.field
      const originalValue = item[field]
      
      if (typeof originalValue === 'string') {
        let highlightedValue = originalValue
        
        // 从后往前替换，避免位置偏移
        const sortedIndices = match.indices.sort((a, b) => b[0] - a[0])
        
        for (const [start, end] of sortedIndices) {
          const before = highlightedValue.substring(0, start)
          const matched = highlightedValue.substring(start, end)
          const after = highlightedValue.substring(end)
          
          highlightedValue = `${before}<${tag} class="${className}">${matched}</${tag}>${after}`
        }
        
        ;(highlighted as any)[field] = highlightedValue
      }
    }

    return highlighted
  }

  /**
   * 更新搜索配置
   * @param config 新配置
   */
  updateConfig(config: Partial<SearchConfig>): void {
    this.config = { ...this.config, ...config }
  }
}

/**
 * 高级筛选器类
 * 提供复杂的筛选条件组合
 */
export class AdvancedFilter {
  /**
   * 应用筛选条件
   * @param items 项目列表
   * @param conditions 筛选条件
   * @returns 筛选后的项目
   */
  filter(items: Bookmark[], conditions: FilterCondition[]): Bookmark[] {
    if (conditions.length === 0) {
      return items
    }

    return items.filter(item => {
      return conditions.every(condition => this.evaluateCondition(item, condition))
    })
  }

  /**
   * 评估单个筛选条件
   * @param item 项目
   * @param condition 筛选条件
   * @returns 是否匹配
   */
  private evaluateCondition(item: Bookmark, condition: FilterCondition): boolean {
    const fieldValue = item[condition.field]
    const { operator, value, caseSensitive = false } = condition

    // 处理空值
    if (fieldValue == null) {
      return operator === 'equals' && value == null
    }

    const normalizedFieldValue = caseSensitive 
      ? String(fieldValue) 
      : String(fieldValue).toLowerCase()
    const normalizedValue = caseSensitive 
      ? String(value) 
      : String(value).toLowerCase()

    switch (operator) {
      case 'equals':
        return normalizedFieldValue === normalizedValue

      case 'contains':
        return normalizedFieldValue.includes(normalizedValue)

      case 'startsWith':
        return normalizedFieldValue.startsWith(normalizedValue)

      case 'endsWith':
        return normalizedFieldValue.endsWith(normalizedValue)

      case 'regex':
        try {
          const regex = new RegExp(value, caseSensitive ? 'g' : 'gi')
          return regex.test(normalizedFieldValue)
        } catch {
          return false
        }

      case 'in':
        if (Array.isArray(value)) {
          return value.some(v => {
            const normalizedV = caseSensitive ? String(v) : String(v).toLowerCase()
            return normalizedFieldValue === normalizedV
          })
        }
        return false

      case 'range':
        if (Array.isArray(value) && value.length === 2) {
          const [min, max] = value
          const numValue = Number(fieldValue)
          return !isNaN(numValue) && numValue >= min && numValue <= max
        }
        return false

      default:
        return false
    }
  }
}

/**
 * 排序工具类
 */
export class AdvancedSorter {
  /**
   * 排序项目列表
   * @param items 项目列表
   * @param configs 排序配置
   * @returns 排序后的项目
   */
  sort(items: Bookmark[], configs: SortConfig[]): Bookmark[] {
    if (configs.length === 0) {
      return items
    }

    return [...items].sort((a, b) => {
      for (const config of configs) {
        const result = this.compareItems(a, b, config)
        if (result !== 0) {
          return result
        }
      }
      return 0
    })
  }

  /**
   * 比较两个项目
   * @param a 项目A
   * @param b 项目B
   * @param config 排序配置
   * @returns 比较结果
   */
  private compareItems(a: Bookmark, b: Bookmark, config: SortConfig): number {
    const { field, direction, type = 'string' } = config
    const valueA = a[field]
    const valueB = b[field]

    // 处理空值
    if (valueA == null && valueB == null) return 0
    if (valueA == null) return direction === 'asc' ? -1 : 1
    if (valueB == null) return direction === 'asc' ? 1 : -1

    let result = 0

    switch (type) {
      case 'number':
        result = Number(valueA) - Number(valueB)
        break

      case 'date':
        const dateA = new Date(valueA as string)
        const dateB = new Date(valueB as string)
        result = dateA.getTime() - dateB.getTime()
        break

      case 'string':
      default:
        result = String(valueA).localeCompare(String(valueB))
        break
    }

    return direction === 'asc' ? result : -result
  }
}

/**
 * 搜索建议生成器
 */
export class SearchSuggestionGenerator {
  private items: Bookmark[]

  constructor(items: Bookmark[]) {
    this.items = items
  }

  /**
   * 生成搜索建议
   * @param query 当前查询
   * @param maxSuggestions 最大建议数量
   * @returns 建议列表
   */
  generateSuggestions(query: string, maxSuggestions: number = 10): string[] {
    if (!query || query.length < 2) {
      return []
    }

    const suggestions = new Set<string>()
    const normalizedQuery = query.toLowerCase()

    // 从标题中提取建议
    for (const item of this.items) {
      if (item.title) {
        const words = item.title.toLowerCase().split(/\s+/)
        for (const word of words) {
          if (word.startsWith(normalizedQuery) && word.length > normalizedQuery.length) {
            suggestions.add(word)
          }
        }
      }

      // 从标签中提取建议
      if (item.tags) {
        for (const tag of item.tags) {
          const normalizedTag = tag.toLowerCase()
          if (normalizedTag.startsWith(normalizedQuery) && normalizedTag.length > normalizedQuery.length) {
            suggestions.add(tag)
          }
        }
      }

      // 从分类中提取建议
      if (item.category) {
        const normalizedCategory = item.category.toLowerCase()
        if (normalizedCategory.startsWith(normalizedQuery) && normalizedCategory.length > normalizedQuery.length) {
          suggestions.add(item.category)
        }
      }
    }

    return Array.from(suggestions).slice(0, maxSuggestions)
  }

  /**
   * 更新项目列表
   * @param items 新的项目列表
   */
  updateItems(items: Bookmark[]): void {
    this.items = items
  }
}

// 导出便捷函数
export const createAdvancedSearch = (config?: SearchConfig) => new AdvancedSearch(config)
export const createAdvancedFilter = () => new AdvancedFilter()
export const createAdvancedSorter = () => new AdvancedSorter()
export const createSuggestionGenerator = (items: Bookmark[]) => new SearchSuggestionGenerator(items)