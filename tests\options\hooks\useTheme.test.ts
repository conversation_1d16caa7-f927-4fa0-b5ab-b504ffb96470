/**
 * useTheme Hook 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useTheme } from '../../../src/options/hooks/useTheme'

// 模拟 localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

// 模拟 matchMedia
const mockMatchMedia = vi.fn()
Object.defineProperty(window, 'matchMedia', { value: mockMatchMedia })

describe('useTheme', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 重置 DOM
    document.documentElement.classList.remove('dark')
    
    // 默认模拟浅色主题
    mockMatchMedia.mockReturnValue({
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    })
    
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  it('应该返回默认的系统主题', () => {
    const { result } = renderHook(() => useTheme())
    
    expect(result.current.theme).toBe('system')
    expect(result.current.actualTheme).toBe('light')
    expect(result.current.isSystemTheme).toBe(true)
  })

  it('应该从本地存储恢复保存的主题', () => {
    mockLocalStorage.getItem.mockReturnValue('dark')
    
    const { result } = renderHook(() => useTheme())
    
    expect(result.current.theme).toBe('dark')
    expect(result.current.actualTheme).toBe('dark')
    expect(result.current.isSystemTheme).toBe(false)
  })

  it('应该检测系统深色主题', () => {
    mockMatchMedia.mockReturnValue({
      matches: true,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    })
    
    const { result } = renderHook(() => useTheme())
    
    expect(result.current.actualTheme).toBe('dark')
  })

  it('应该设置主题并保存到本地存储', () => {
    const { result } = renderHook(() => useTheme())
    
    act(() => {
      result.current.setTheme('dark')
    })
    
    expect(result.current.theme).toBe('dark')
    expect(result.current.actualTheme).toBe('dark')
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('options-theme', 'dark')
  })

  it('应该切换主题', () => {
    const { result } = renderHook(() => useTheme())
    
    // 初始为系统主题（浅色）
    expect(result.current.actualTheme).toBe('light')
    
    // 切换应该变为深色
    act(() => {
      result.current.toggleTheme()
    })
    
    expect(result.current.theme).toBe('dark')
    expect(result.current.actualTheme).toBe('dark')
    
    // 再次切换应该变为浅色
    act(() => {
      result.current.toggleTheme()
    })
    
    expect(result.current.theme).toBe('light')
    expect(result.current.actualTheme).toBe('light')
  })

  it('应该监听系统主题变化', () => {
    let mediaQueryListener: (e: MediaQueryListEvent) => void
    
    const mockMediaQuery = {
      matches: false,
      addEventListener: vi.fn((event, listener) => {
        if (event === 'change') {
          mediaQueryListener = listener
        }
      }),
      removeEventListener: vi.fn()
    }
    
    mockMatchMedia.mockReturnValue(mockMediaQuery)
    
    const { result } = renderHook(() => useTheme())
    
    // 初始为浅色
    expect(result.current.actualTheme).toBe('light')
    
    // 模拟系统主题变化为深色
    act(() => {
      mediaQueryListener({ matches: true } as MediaQueryListEvent)
    })
    
    expect(result.current.actualTheme).toBe('dark')
  })

  it('应该应用主题到 DOM', () => {
    const { result } = renderHook(() => useTheme())
    
    // 设置深色主题
    act(() => {
      result.current.setTheme('dark')
    })
    
    expect(document.documentElement.classList.contains('dark')).toBe(true)
    
    // 设置浅色主题
    act(() => {
      result.current.setTheme('light')
    })
    
    expect(document.documentElement.classList.contains('dark')).toBe(false)
  })

  it('应该更新 meta theme-color', () => {
    // 创建 meta 标签
    const metaTag = document.createElement('meta')
    metaTag.name = 'theme-color'
    metaTag.content = '#ffffff'
    document.head.appendChild(metaTag)
    
    const { result } = renderHook(() => useTheme())
    
    // 设置深色主题
    act(() => {
      result.current.setTheme('dark')
    })
    
    expect(metaTag.content).toBe('#1f2937')
    
    // 设置浅色主题
    act(() => {
      result.current.setTheme('light')
    })
    
    expect(metaTag.content).toBe('#ffffff')
    
    // 清理
    document.head.removeChild(metaTag)
  })

  it('应该处理本地存储错误', () => {
    mockLocalStorage.getItem.mockImplementation(() => {
      throw new Error('localStorage error')
    })
    
    // 应该不会抛出错误，使用默认主题
    expect(() => renderHook(() => useTheme())).not.toThrow()
    
    const { result } = renderHook(() => useTheme())
    expect(result.current.theme).toBe('system')
  })

  it('应该处理保存主题时的错误', () => {
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new Error('localStorage error')
    })
    
    const { result } = renderHook(() => useTheme())
    
    // 应该不会抛出错误
    expect(() => {
      act(() => {
        result.current.setTheme('dark')
      })
    }).not.toThrow()
    
    expect(result.current.theme).toBe('dark')
  })

  it('应该忽略无效的保存主题值', () => {
    mockLocalStorage.getItem.mockReturnValue('invalid-theme')
    
    const { result } = renderHook(() => useTheme())
    
    // 应该使用默认的系统主题
    expect(result.current.theme).toBe('system')
  })

  it('应该清理事件监听器', () => {
    const mockRemoveEventListener = vi.fn()
    const mockMediaQuery = {
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: mockRemoveEventListener
    }
    
    mockMatchMedia.mockReturnValue(mockMediaQuery)
    
    const { unmount } = renderHook(() => useTheme())
    
    unmount()
    
    expect(mockRemoveEventListener).toHaveBeenCalled()
  })
})