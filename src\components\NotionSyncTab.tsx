import React, { useState, useEffect } from 'react'
import { Database, Settings, RefreshCw, CheckCircle, AlertCircle, ExternalLink, Plus, Trash2 } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Switch } from './ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Separator } from './ui/separator'
import { Badge } from './ui/badge'

interface NotionDatabase {
  id: string
  name: string
  url: string
  lastSync: string
  status: 'connected' | 'error' | 'syncing'
  syncDirection: 'both' | 'to-notion' | 'from-notion'
}

interface NotionSyncTabProps {}

const NotionSyncTab: React.FC<NotionSyncTabProps> = () => {
  const [apiToken, setApiToken] = useState('')
  const [isConnected, setIsConnected] = useState(false)
  const [databases, setDatabases] = useState<NotionDatabase[]>([])
  const [loading, setLoading] = useState(false)
  const [autoSync, setAutoSync] = useState(true)
  const [syncInterval, setSyncInterval] = useState('30min')

  // 模拟数据库列表
  useEffect(() => {
    if (isConnected) {
      setDatabases([
        {
          id: '1',
          name: '我的收藏夹',
          url: 'https://notion.so/database/123',
          lastSync: '2024-01-15 14:30:00',
          status: 'connected',
          syncDirection: 'both'
        },
        {
          id: '2',
          name: '学习资源',
          url: 'https://notion.so/database/456',
          lastSync: '2024-01-15 14:25:00',
          status: 'connected',
          syncDirection: 'to-notion'
        }
      ])
    }
  }, [isConnected])

  // 连接Notion
  const handleConnect = async () => {
    if (!apiToken.trim()) {
      alert('请输入有效的API Token')
      return
    }

    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      setIsConnected(true)
      console.log('Notion连接成功')
    } catch (error) {
      console.error('连接失败:', error)
      alert('连接失败，请检查API Token是否正确')
    } finally {
      setLoading(false)
    }
  }

  // 断开连接
  const handleDisconnect = () => {
    setIsConnected(false)
    setDatabases([])
    setApiToken('')
  }

  // 手动同步
  const handleManualSync = async (databaseId?: string) => {
    setLoading(true)
    try {
      // 模拟同步过程
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      if (databaseId) {
        // 更新特定数据库的同步状态
        setDatabases(prev => prev.map(db => 
          db.id === databaseId 
            ? { ...db, lastSync: new Date().toLocaleString(), status: 'connected' as const }
            : db
        ))
      } else {
        // 更新所有数据库的同步状态
        setDatabases(prev => prev.map(db => ({
          ...db,
          lastSync: new Date().toLocaleString(),
          status: 'connected' as const
        })))
      }
      
      console.log('同步完成')
    } catch (error) {
      console.error('同步失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 添加数据库
  const handleAddDatabase = () => {
    // 这里应该打开一个对话框来选择Notion数据库
    console.log('添加数据库功能待实现')
  }

  // 删除数据库连接
  const handleRemoveDatabase = (databaseId: string) => {
    setDatabases(prev => prev.filter(db => db.id !== databaseId))
  }

  // 获取状态图标
  const getStatusIcon = (status: NotionDatabase['status']) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'syncing':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
      default:
        return null
    }
  }

  // 获取状态文本
  const getStatusText = (status: NotionDatabase['status']) => {
    switch (status) {
      case 'connected':
        return '已连接'
      case 'error':
        return '连接错误'
      case 'syncing':
        return '同步中'
      default:
        return '未知'
    }
  }

  return (
    <div className="p-6">
      {/* 头部区域 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <Database className="w-6 h-6 mr-3 text-primary" />
            Notion集成
          </CardTitle>
          <CardDescription className="mt-1">
            将您的收藏数据与Notion数据库同步，实现跨平台数据管理
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="space-y-6">
        {/* 连接配置 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              连接配置
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {!isConnected ? (
              <>
                <div className="space-y-2">
                  <Label htmlFor="api-token">Notion API Token</Label>
                  <Input
                    id="api-token"
                    type="password"
                    placeholder="输入您的Notion API Token"
                    value={apiToken}
                    onChange={(e) => setApiToken(e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    在Notion中创建集成并获取API Token。
                    <a 
                      href="https://developers.notion.com/docs/getting-started" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-primary hover:underline ml-1"
                    >
                      查看教程 <ExternalLink className="w-3 h-3 inline" />
                    </a>
                  </p>
                </div>
                <Button 
                  onClick={handleConnect} 
                  disabled={loading || !apiToken.trim()}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      连接中...
                    </>
                  ) : (
                    <>
                      <Database className="w-4 h-4 mr-2" />
                      连接Notion
                    </>
                  )}
                </Button>
              </>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-green-800 font-medium">已连接到Notion</span>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleDisconnect}
                  >
                    断开连接
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 同步设置 */}
        {isConnected && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">同步设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">自动同步</Label>
                  <p className="text-sm text-muted-foreground">启用后将定期自动同步数据</p>
                </div>
                <Switch
                  checked={autoSync}
                  onCheckedChange={setAutoSync}
                />
              </div>

              {autoSync && (
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">同步间隔</Label>
                    <p className="text-sm text-muted-foreground">设置自动同步的时间间隔</p>
                  </div>
                  <Select value={syncInterval} onValueChange={setSyncInterval}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15min">每15分钟</SelectItem>
                      <SelectItem value="30min">每30分钟</SelectItem>
                      <SelectItem value="1hour">每小时</SelectItem>
                      <SelectItem value="6hour">每6小时</SelectItem>
                      <SelectItem value="24hour">每天</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">手动同步</Label>
                  <p className="text-sm text-muted-foreground">立即同步所有已连接的数据库</p>
                </div>
                <Button 
                  onClick={() => handleManualSync()}
                  disabled={loading}
                  variant="outline"
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <RefreshCw className="w-4 h-4 mr-2" />
                  )}
                  立即同步
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 数据库管理 */}
        {isConnected && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">已连接的数据库</CardTitle>
                <Button onClick={handleAddDatabase} size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  添加数据库
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {databases.length === 0 ? (
                <div className="text-center py-8">
                  <Database className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">暂无连接的数据库</h3>
                  <p className="text-muted-foreground mb-4">
                    点击"添加数据库"按钮来连接您的Notion数据库
                  </p>
                  <Button onClick={handleAddDatabase}>
                    <Plus className="w-4 h-4 mr-2" />
                    添加数据库
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {databases.map((database) => (
                    <Card key={database.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="font-medium text-foreground">{database.name}</h4>
                            <div className="flex items-center space-x-1">
                              {getStatusIcon(database.status)}
                              <span className="text-sm text-muted-foreground">
                                {getStatusText(database.status)}
                              </span>
                            </div>
                            <Badge variant="outline">
                              {database.syncDirection === 'both' ? '双向同步' :
                               database.syncDirection === 'to-notion' ? '同步到Notion' : '从Notion同步'}
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>最后同步: {database.lastSync}</span>
                            <a 
                              href={database.url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-primary hover:underline flex items-center"
                            >
                              在Notion中打开 <ExternalLink className="w-3 h-3 ml-1" />
                            </a>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleManualSync(database.id)}
                            disabled={loading}
                          >
                            <RefreshCw className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRemoveDatabase(database.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">使用说明</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-sm text-muted-foreground space-y-2">
              <p><strong>1. 获取API Token:</strong> 在Notion中创建集成并获取API Token</p>
              <p><strong>2. 连接数据库:</strong> 添加您想要同步的Notion数据库</p>
              <p><strong>3. 配置同步:</strong> 选择同步方向和频率</p>
              <p><strong>4. 开始同步:</strong> 启用自动同步或手动触发同步</p>
            </div>
            <Separator />
            <div className="text-sm text-muted-foreground">
              <p><strong>注意:</strong> 请确保您的Notion数据库具有适当的权限，并且包含必要的属性字段。</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default NotionSyncTab