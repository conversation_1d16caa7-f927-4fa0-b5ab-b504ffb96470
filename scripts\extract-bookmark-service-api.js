#!/usr/bin/env node

/**
 * BookmarkService API提取工具
 * 从 bookmarkService.ts 文件中提取函数签名和文档
 * 
 * 功能说明:
 * - 自动提取BookmarkService类的所有方法签名
 * - 生成完整的API参考文档
 * - 验证文档与代码的一致性
 * - 支持中文注释和文档生成
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

console.log('🔍 提取BookmarkService API信息...')

/**
 * BookmarkService API提取器类
 * 
 * 负责从TypeScript文件中提取类、方法、接口等API信息
 * 并生成结构化的文档数据
 */
class BookmarkServiceAPIExtractor {
  constructor() {
    this.className = 'BookmarkService'
    this.methods = []           // 提取的方法信息
    this.imports = []           // 导入的类型和模块
    this.exports = []           // 导出的内容
    this.privateMethod = []     // 私有方法
    this.publicMethods = []     // 公有方法
  }

  /**
   * 从文件中提取API信息
   * 
   * @param {string} filePath - 要分析的文件路径
   */
  extractFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const fileName = path.basename(filePath)
      
      console.log(`📄 分析文件: ${fileName}`)
      
      // 提取导入信息
      this.extractImports(content)
      
      // 提取类方法
      this.extractMethods(content)
      
      // 提取导出信息
      this.extractExports(content)
      
      // 分类方法
      this.categorizeMethods()
      
    } catch (error) {
      console.error(`❌ 分析文件失败 ${filePath}:`, error.message)
    }
  }

  /**
   * 提取导入信息
   * 
   * @param {string} content - 文件内容
   */
  extractImports(content) {
    // 匹配import语句
    const importRegex = /import\s+{([^}]+)}\s+from\s+['"`]([^'"`]+)['"`]/g
    let match

    while ((match = importRegex.exec(content)) !== null) {
      const imports = match[1].split(',').map(imp => imp.trim())
      const from = match[2]
      
      this.imports.push({
        imports: imports,
        from: from,
        fullMatch: match[0]
      })
    }
  }

  /**
   * 提取类方法
   * 
   * @param {string} content - 文件内容
   */
  extractMethods(content) {
    // 匹配方法定义的正则表达式 - 改进版本以更好地处理复杂返回类型
    const methodRegex = /\/\*\*[\s\S]*?\*\/\s*(private\s+|public\s+)?(async\s+)?(\w+)\s*\([^)]*\)\s*:\s*([^{]+?)\s*{/g
    let match

    while ((match = methodRegex.exec(content)) !== null) {
      const isPrivate = !!match[1] && match[1].includes('private')
      const isAsync = !!match[2]
      const methodName = match[3]
      let returnType = match[4].trim()
      
      // 处理复杂的返回类型，如果截断了就尝试重新提取
      if (returnType.includes('<') && !returnType.includes('>')) {
        // 尝试从原始内容中提取完整的返回类型
        const methodStart = match.index + match[0].indexOf(methodName)
        const afterMethodName = content.substring(methodStart)
        const colonIndex = afterMethodName.indexOf(':')
        const braceIndex = afterMethodName.indexOf('{')
        
        if (colonIndex > 0 && braceIndex > colonIndex) {
          returnType = afterMethodName.substring(colonIndex + 1, braceIndex).trim()
        }
      }
      
      // 获取方法前的注释
      const beforeMethod = content.substring(0, match.index)
      const comment = this.extractMethodComment(beforeMethod)
      
      // 提取参数信息
      const parameters = this.extractParameters(match[0])
      
      this.methods.push({
        name: methodName,
        isPrivate: isPrivate,
        isAsync: isAsync,
        returnType: returnType,
        parameters: parameters,
        comment: comment,
        signature: this.buildMethodSignature(methodName, parameters, returnType, isAsync),
        category: this.categorizeMethod(methodName),
        fullMatch: match[0]
      })
    }
  }

  /**
   * 提取方法注释
   * 
   * @param {string} beforeMethod - 方法前的内容
   * @returns {object} 注释信息
   */
  extractMethodComment(beforeMethod) {
    const lines = beforeMethod.split('\n')
    const commentLines = []
    let description = ''
    let params = []
    let returns = ''
    
    // 从后往前查找注释
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim()
      
      if (line.startsWith('/**') || line.startsWith('*') || line.startsWith('*/')) {
        const cleanLine = line.replace(/^[\*\/\s]+/, '').trim()
        
        if (cleanLine.startsWith('@param')) {
          const paramMatch = cleanLine.match(/@param\s+(\w+)\s+(.+)/)
          if (paramMatch) {
            params.unshift({
              name: paramMatch[1],
              description: paramMatch[2]
            })
          }
        } else if (cleanLine.startsWith('@returns')) {
          returns = cleanLine.replace('@returns', '').trim()
        } else if (cleanLine && !cleanLine.startsWith('@')) {
          if (!description) {
            description = cleanLine
          }
        }
      } else if (line === '' || line.startsWith('//')) {
        continue
      } else {
        break
      }
    }
    
    return {
      description: description,
      parameters: params,
      returns: returns
    }
  }

  /**
   * 提取参数信息
   * 
   * @param {string} methodSignature - 方法签名
   * @returns {Array} 参数信息数组
   */
  extractParameters(methodSignature) {
    const paramMatch = methodSignature.match(/\(([^)]*)\)/)
    if (!paramMatch || !paramMatch[1].trim()) {
      return []
    }
    
    const paramString = paramMatch[1]
    const params = []
    
    // 简单的参数解析（可能需要更复杂的逻辑处理嵌套类型）
    const paramParts = paramString.split(',')
    
    paramParts.forEach(param => {
      const trimmed = param.trim()
      if (trimmed) {
        const colonIndex = trimmed.indexOf(':')
        if (colonIndex > 0) {
          const name = trimmed.substring(0, colonIndex).trim()
          const type = trimmed.substring(colonIndex + 1).trim()
          const isOptional = name.includes('?')
          
          params.push({
            name: name.replace('?', ''),
            type: type,
            optional: isOptional
          })
        }
      }
    })
    
    return params
  }

  /**
   * 构建方法签名
   * 
   * @param {string} methodName - 方法名
   * @param {Array} parameters - 参数数组
   * @param {string} returnType - 返回类型
   * @param {boolean} isAsync - 是否异步
   * @returns {string} 方法签名
   */
  buildMethodSignature(methodName, parameters, returnType, isAsync) {
    const paramStr = parameters.map(p => 
      `${p.name}${p.optional ? '?' : ''}: ${p.type}`
    ).join(', ')
    
    const asyncPrefix = isAsync ? 'async ' : ''
    return `${asyncPrefix}${methodName}(${paramStr}): ${returnType}`
  }

  /**
   * 方法分类
   * 
   * @param {string} methodName - 方法名
   * @returns {string} 方法类别
   */
  categorizeMethod(methodName) {
    if (methodName.startsWith('save') || methodName.startsWith('create')) {
      return '保存操作'
    }
    if (methodName.startsWith('get') || methodName.startsWith('find') || methodName.startsWith('search')) {
      return '查询操作'
    }
    if (methodName.startsWith('update') || methodName.startsWith('modify')) {
      return '更新操作'
    }
    if (methodName.startsWith('delete') || methodName.startsWith('remove')) {
      return '删除操作'
    }
    if (methodName.startsWith('check') || methodName.startsWith('detect') || methodName.startsWith('validate')) {
      return '验证操作'
    }
    if (methodName.includes('Stats') || methodName.includes('Count')) {
      return '统计操作'
    }
    return '工具方法'
  }

  /**
   * 提取导出信息
   * 
   * @param {string} content - 文件内容
   */
  extractExports(content) {
    // 匹配export语句
    const exportRegex = /export\s+(class|const|function)\s+(\w+)/g
    let match

    while ((match = exportRegex.exec(content)) !== null) {
      this.exports.push({
        type: match[1],
        name: match[2],
        fullMatch: match[0]
      })
    }
  }

  /**
   * 分类方法
   */
  categorizeMethods() {
    this.publicMethods = this.methods.filter(method => !method.isPrivate)
    this.privateMethod = this.methods.filter(method => method.isPrivate)
  }

  /**
   * 生成API文档
   * 
   * @returns {string} 文档内容
   */
  generateDocumentation() {
    let doc = '# BookmarkService API 文档\n\n'
    
    // 生成概览
    doc += '## 概览\n\n'
    doc += `BookmarkService 是负责收藏功能核心业务逻辑的服务类，提供完整的收藏数据管理功能。\n\n`
    doc += `- **类名**: ${this.className}\n`
    doc += `- **文件路径**: src/services/bookmarkService.ts\n`
    doc += `- **公有方法**: ${this.publicMethods.length} 个\n`
    doc += `- **私有方法**: ${this.privateMethod.length} 个\n`
    doc += `- **总方法数**: ${this.methods.length} 个\n\n`
    
    // 生成导入依赖
    if (this.imports.length > 0) {
      doc += '## 依赖导入\n\n'
      this.imports.forEach(imp => {
        doc += `**从 \`${imp.from}\` 导入**:\n`
        imp.imports.forEach(item => {
          doc += `- \`${item.trim()}\`\n`
        })
        doc += '\n'
      })
    }
    
    // 生成公有方法文档
    if (this.publicMethods.length > 0) {
      doc += '## 公有方法\n\n'
      
      // 按类别分组
      const groupedMethods = this.groupMethodsByCategory(this.publicMethods)
      
      Object.keys(groupedMethods).forEach(category => {
        doc += `### ${category}\n\n`
        
        groupedMethods[category].forEach(method => {
          doc += `#### ${method.name}\n\n`
          
          if (method.comment.description) {
            doc += `**功能**: ${method.comment.description}\n\n`
          }
          
          doc += `**签名**: \`${method.signature}\`\n\n`
          
          if (method.parameters.length > 0) {
            doc += '**参数**:\n'
            method.parameters.forEach(param => {
              const optionalText = param.optional ? ' (可选)' : ''
              doc += `- \`${param.name}\` (\`${param.type}\`)${optionalText}`
              
              const paramDoc = method.comment.parameters.find(p => p.name === param.name)
              if (paramDoc) {
                doc += ` - ${paramDoc.description}`
              }
              doc += '\n'
            })
            doc += '\n'
          }
          
          if (method.comment.returns) {
            doc += `**返回值**: ${method.comment.returns}\n\n`
          }
          
          if (method.isAsync) {
            doc += `**异步方法**: 是\n\n`
          }
          
          // 生成使用示例
          doc += this.generateUsageExample(method)
          
          doc += '---\n\n'
        })
      })
    }
    
    // 生成私有方法文档
    if (this.privateMethod.length > 0) {
      doc += '## 私有方法\n\n'
      doc += '以下方法为内部使用，不建议直接调用：\n\n'
      
      this.privateMethod.forEach(method => {
        doc += `### ${method.name}\n\n`
        
        if (method.comment.description) {
          doc += `**功能**: ${method.comment.description}\n\n`
        }
        
        doc += `**签名**: \`${method.signature}\`\n\n`
        
        doc += '---\n\n'
      })
    }
    
    // 生成导出信息
    if (this.exports.length > 0) {
      doc += '## 导出内容\n\n'
      this.exports.forEach(exp => {
        doc += `- **${exp.type}**: \`${exp.name}\`\n`
      })
      doc += '\n'
    }
    
    // 生成使用指南
    doc += this.generateUsageGuide()
    
    return doc
  }

  /**
   * 按类别分组方法
   * 
   * @param {Array} methods - 方法数组
   * @returns {Object} 分组后的方法
   */
  groupMethodsByCategory(methods) {
    const grouped = {}
    
    methods.forEach(method => {
      const category = method.category
      if (!grouped[category]) {
        grouped[category] = []
      }
      grouped[category].push(method)
    })
    
    return grouped
  }

  /**
   * 生成使用示例
   * 
   * @param {Object} method - 方法信息
   * @returns {string} 使用示例
   */
  generateUsageExample(method) {
    let example = '**使用示例**:\n```typescript\n'
    
    switch (method.name) {
      case 'saveBookmark':
        example += `// 保存收藏
const bookmarkInput: BookmarkInput = {
  type: 'url',
  title: '示例网站',
  url: 'https://example.com',
  category: '默认分类',
  tags: ['示例', '网站']
}

try {
  const bookmarkId = await bookmarkService.saveBookmark(bookmarkInput)
  console.log('收藏保存成功:', bookmarkId)
} catch (error) {
  console.error('保存失败:', error)
}`
        break
        
      case 'getBookmark':
        example += `// 获取单个收藏
try {
  const bookmark = await bookmarkService.getBookmark('bookmark-id')
  if (bookmark) {
    console.log('收藏信息:', bookmark)
  } else {
    console.log('收藏不存在')
  }
} catch (error) {
  console.error('获取失败:', error)
}`
        break
        
      case 'getBookmarks':
        example += `// 获取收藏列表
const filter: BookmarkFilter = {
  query: '搜索关键词',
  categories: ['技术'],
  limit: 10
}

const sort: SortOptions = {
  field: 'createdAt',
  direction: 'desc'
}

try {
  const bookmarks = await bookmarkService.getBookmarks(filter, sort)
  console.log('收藏列表:', bookmarks)
} catch (error) {
  console.error('获取失败:', error)
}`
        break
        
      case 'updateBookmark':
        example += `// 更新收藏
const updates: BookmarkUpdate = {
  title: '新标题',
  tags: ['新标签1', '新标签2'],
  category: '新分类'
}

try {
  await bookmarkService.updateBookmark('bookmark-id', updates)
  console.log('更新成功')
} catch (error) {
  console.error('更新失败:', error)
}`
        break
        
      case 'deleteBookmark':
        example += `// 删除收藏
try {
  await bookmarkService.deleteBookmark('bookmark-id')
  console.log('删除成功')
} catch (error) {
  console.error('删除失败:', error)
}`
        break
        
      case 'quickBookmark':
        example += `// 快速收藏
try {
  const bookmarkId = await bookmarkService.quickBookmark(
    '页面标题',
    'https://example.com',
    'https://example.com/favicon.ico',
    '选中的文字内容'
  )
  console.log('快速收藏成功:', bookmarkId)
} catch (error) {
  console.error('快速收藏失败:', error)
}`
        break
        
      case 'searchBookmarks':
        example += `// 搜索收藏
try {
  const results = await bookmarkService.searchBookmarks('关键词', {
    categories: ['技术'],
    type: 'url'
  })
  console.log('搜索结果:', results)
} catch (error) {
  console.error('搜索失败:', error)
}`
        break
        
      case 'checkBookmarkStatus':
        example += `// 检查收藏状态
try {
  const status = await bookmarkService.checkBookmarkStatus('https://example.com')
  if (status.isBookmarked) {
    console.log('已收藏，ID:', status.bookmarkId)
  } else {
    console.log('未收藏')
  }
} catch (error) {
  console.error('检查失败:', error)
}`
        break
        
      case 'detectDuplicates':
        example += `// 检测重复收藏
try {
  const duplicates = await bookmarkService.detectDuplicates(0.8)
  console.log('发现重复组:', duplicates.length)
  duplicates.forEach(group => {
    console.log('重复类型:', group.type)
    console.log('相似度:', group.similarity)
    console.log('收藏数量:', group.bookmarks.length)
  })
} catch (error) {
  console.error('检测失败:', error)
}`
        break
        
      case 'getBookmarkStats':
        example += `// 获取统计信息
try {
  const stats = await bookmarkService.getBookmarkStats()
  console.log('总收藏数:', stats.totalCount)
  console.log('类型统计:', stats.typeStats)
  console.log('分类统计:', stats.categoryStats)
  console.log('标签统计:', stats.tagStats)
  console.log('最近收藏:', stats.recentCount)
} catch (error) {
  console.error('获取统计失败:', error)
}`
        break
        
      default:
        example += `// 使用 ${method.name} 方法
try {
  const result = await bookmarkService.${method.name}(/* 参数 */)
  console.log('操作成功:', result)
} catch (error) {
  console.error('操作失败:', error)
}`
    }
    
    example += '\n```\n\n'
    return example
  }

  /**
   * 生成使用指南
   * 
   * @returns {string} 使用指南
   */
  generateUsageGuide() {
    return `## 使用指南

### 基本使用

\`\`\`typescript
import { bookmarkService } from '../services/bookmarkService'

// BookmarkService 提供单例实例，可直接使用
const result = await bookmarkService.someMethod()
\`\`\`

### 错误处理

所有异步方法都可能抛出错误，建议使用 try-catch 进行错误处理：

\`\`\`typescript
try {
  const result = await bookmarkService.saveBookmark(input)
  // 处理成功结果
} catch (error) {
  console.error('操作失败:', error.message)
  // 处理错误情况
}
\`\`\`

### 类型安全

BookmarkService 使用 TypeScript 编写，提供完整的类型支持：

\`\`\`typescript
import { BookmarkInput, BookmarkFilter, SortOptions } from '../types'

// 类型安全的参数传递
const input: BookmarkInput = {
  type: 'url',
  title: '标题',
  url: 'https://example.com',
  category: '分类',
  tags: ['标签1', '标签2']
}
\`\`\`

### 最佳实践

1. **数据验证**: 保存收藏前会自动进行数据验证
2. **重复检测**: 保存时会自动检查URL重复
3. **统计更新**: 操作会自动更新相关统计信息
4. **错误恢复**: 内部错误不会影响主要功能
5. **性能优化**: 支持分页和筛选以提高性能

### 依赖关系

BookmarkService 依赖以下模块：
- \`indexedDBService\`: 数据持久化
- \`ModelFactory\`: 数据模型创建
- \`ValidationUtils\`: 数据验证

### 单例模式

BookmarkService 使用单例模式，确保全局只有一个实例：

\`\`\`typescript
// 导出的是单例实例，不是类
export const bookmarkService = new BookmarkService()
\`\`\`

## 技术特性

- ✅ **类型安全**: 完整的 TypeScript 类型支持
- ✅ **异步操作**: 所有数据操作都是异步的
- ✅ **错误处理**: 完善的错误捕获和报告
- ✅ **数据验证**: 自动验证输入数据
- ✅ **重复检测**: 智能检测重复收藏
- ✅ **统计维护**: 自动维护统计信息
- ✅ **性能优化**: 支持分页、筛选和排序
- ✅ **模块化设计**: 低耦合的模块化架构

## 版本信息

- **当前版本**: 1.0.0
- **最后更新**: ${new Date().toLocaleDateString('zh-CN')}
- **兼容性**: Chrome Extension Manifest V3
- **依赖**: IndexedDB, Chrome Storage API
`
  }

  /**
   * 生成统计信息
   * 
   * @returns {Object} 统计信息
   */
  generateStats() {
    return {
      className: this.className,
      totalMethods: this.methods.length,
      publicMethods: this.publicMethods.length,
      privateMethods: this.privateMethod.length,
      imports: this.imports.length,
      exports: this.exports.length,
      categories: [...new Set(this.methods.map(m => m.category))].length
    }
  }
}

/**
 * 主函数
 */
function main() {
  const extractor = new BookmarkServiceAPIExtractor()
  
  // 要分析的服务文件
  const serviceFile = path.resolve(__dirname, '../src/services/bookmarkService.ts')
  
  if (fs.existsSync(serviceFile)) {
    extractor.extractFromFile(serviceFile)
  } else {
    console.log(`⚠️  服务文件不存在: ${serviceFile}`)
    return
  }
  
  // 生成文档
  const documentation = extractor.generateDocumentation()
  const stats = extractor.generateStats()
  
  // 保存文档
  const outputPath = path.resolve(__dirname, '../docs/bookmark-service-api.md')
  
  try {
    fs.writeFileSync(outputPath, documentation, 'utf8')
    console.log(`📝 文档已生成: ${outputPath}`)
  } catch (error) {
    console.error('❌ 保存文档失败:', error.message)
  }
  
  // 输出统计信息
  console.log('\n📊 提取统计:')
  console.log(`- 类名: ${stats.className}`)
  console.log(`- 公有方法: ${stats.publicMethods} 个`)
  console.log(`- 私有方法: ${stats.privateMethods} 个`)
  console.log(`- 总方法数: ${stats.totalMethods} 个`)
  console.log(`- 导入模块: ${stats.imports} 个`)
  console.log(`- 导出内容: ${stats.exports} 个`)
  console.log(`- 方法分类: ${stats.categories} 个`)
  
  console.log('\n✅ BookmarkService API提取完成!')
}

// 运行主函数
main()